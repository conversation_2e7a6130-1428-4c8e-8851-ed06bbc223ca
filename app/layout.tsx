import type { Metada<PERSON> } from 'next';
import { Inter, <PERSON>eist_Mono } from 'next/font/google';
import { ConvexClientProvider } from '../components/ConvexClientProvider';
import '../src/index.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const geistMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-geist-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: 'ARA Property Services - Bid Writing Studio',
  description: 'Multi-Agent Tender & Bid Writing Platform',
  keywords: ['bid writing', 'tender', 'property services', 'ARA'],
  authors: [{ name: 'ARA Property Services' }],
  robots: 'index, follow',
  icons: {
    icon: '/favicon.ico',
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  themeColor: '#0a0a0a',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={`${inter.variable} ${geistMono.variable}`}>
      <body className="min-h-screen bg-black text-white font-mono antialiased">
        <ConvexClientProvider>
          {children}
        </ConvexClientProvider>
      </body>
    </html>
  );
}