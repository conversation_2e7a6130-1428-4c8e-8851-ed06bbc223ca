"use client";

import { useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { SignInForm } from "@/components/SignInForm";
import TenderPlatform from "@/components/TenderPlatform";

export default function HomePage() {
  const user = useQuery(api.auth.loggedInUser);

  if (user === undefined) {
    // Loading state
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-text-primary">Loading...</div>
      </div>
    );
  }

  if (user === null) {
    // Not authenticated
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-surface border border-border rounded-lg p-8">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-text-primary mb-2">
              Welcome to Bid Writing Studio
            </h1>
            <p className="text-text-tertiary">
              ARA Property Services - Multi-Agent Tender Platform
            </p>
          </div>
          <SignInForm />
        </div>
      </div>
    );
  }

  // Authenticated
  return (
    <div className="min-h-screen bg-background">
      <TenderPlatform />
    </div>
  );
}