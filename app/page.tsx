"use client";

import { Authenticated, Unauthenticated } from "convex/react";
import { SignInForm } from "@/components/SignInForm";
import TenderPlatform from "@/components/TenderPlatform";

export default function HomePage() {
  return (
    <div className="min-h-screen bg-background">
      <Authenticated>
        <TenderPlatform />
      </Authenticated>
      <Unauthenticated>
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="w-full max-w-md bg-surface border border-border rounded-lg p-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold text-text-primary mb-2">
                Welcome to Bid Writing Studio
              </h1>
              <p className="text-text-tertiary">
                ARA Property Services - Multi-Agent Tender Platform
              </p>
            </div>
            <SignInForm />
          </div>
        </div>
      </Unauthenticated>
    </div>
  );
}