#!/bin/bash

echo "🔧 SSH Troubleshooting and Recovery Script"
echo "=========================================="
echo ""

TARGET_IP="*************"
LOCAL_IP="*************"

echo "🔍 Diagnosing SSH Connection Issues..."
echo ""

# Test 1: Basic network connectivity
echo "1️⃣ Testing Network Connectivity..."
echo ""

# Test Tailscale ping
echo "Testing Tailscale connectivity:"
if command -v tailscale &> /dev/null; then
    echo "   Tailscale installed ✅"
    
    # Check Tailscale status
    echo "   Tailscale status:"
    tailscale status | grep -E "(100\.93\.124\.60|pve)" || echo "   Machine not found in tailscale status"
    
    # Try ping
    echo "   Attempting ping..."
    timeout 10s tailscale ping $TARGET_IP || echo "   Ping failed ❌"
else
    echo "   Tailscale not installed ❌"
fi

echo ""

# Test direct IP connectivity
echo "Testing direct IP connectivity:"
if ping -c 3 -W 3000 $TARGET_IP &>/dev/null; then
    echo "   ✅ IP $TARGET_IP is reachable"
else
    echo "   ❌ IP $TARGET_IP is not reachable"
fi

if ping -c 3 -W 3000 $LOCAL_IP &>/dev/null; then
    echo "   ✅ Local IP $LOCAL_IP is reachable"
else
    echo "   ❌ Local IP $LOCAL_IP is not reachable"
fi

echo ""

# Test 2: Port accessibility
echo "2️⃣ Testing Port Accessibility..."
echo ""

# Test SSH port (22)
echo "Testing SSH port 22:"
for ip in $TARGET_IP $LOCAL_IP; do
    if timeout 5s bash -c "echo >/dev/tcp/$ip/22" &>/dev/null; then
        echo "   ✅ SSH port 22 open on $ip"
    else
        echo "   ❌ SSH port 22 closed/filtered on $ip"
    fi
done

echo ""

# Test GPU backend port (9092) - we know this works
echo "Testing GPU backend port 9092:"
if timeout 5s bash -c "echo >/dev/tcp/$TARGET_IP/9092" &>/dev/null; then
    echo "   ✅ GPU backend port 9092 open on $TARGET_IP"
else
    echo "   ❌ GPU backend port 9092 closed on $TARGET_IP"
fi

echo ""

# Test 3: SSH service diagnosis
echo "3️⃣ SSH Service Diagnosis..."
echo ""

# Try different SSH approaches
echo "Attempting various SSH connection methods:"

# Method 1: Standard SSH
echo "   Method 1: Standard SSH"
if timeout 10s ssh -o ConnectTimeout=5 -o BatchMode=yes root@$TARGET_IP "echo 'SSH OK'" 2>/dev/null; then
    echo "   ✅ Standard SSH working"
else
    echo "   ❌ Standard SSH failed"
fi

# Method 2: SSH with verbose output
echo "   Method 2: SSH verbose (checking for errors)"
SSH_DEBUG=$(timeout 10s ssh -v -o ConnectTimeout=5 root@$TARGET_IP "echo 'test'" 2>&1 | head -20)
if [[ $SSH_DEBUG == *"Connected to"* ]]; then
    echo "   ✅ SSH connection established"
else
    echo "   ❌ SSH connection failed"
    echo "   Debug info:"
    echo "$SSH_DEBUG" | grep -E "(connect|refused|timeout|host|key)" | head -5 | sed 's/^/     /'
fi

# Method 3: Try local IP
echo "   Method 3: Local IP SSH"
if timeout 10s ssh -o ConnectTimeout=5 -o BatchMode=yes root@$LOCAL_IP "echo 'SSH OK'" 2>/dev/null; then
    echo "   ✅ Local IP SSH working"
else
    echo "   ❌ Local IP SSH failed"
fi

echo ""

# Test 4: Alternative access methods
echo "4️⃣ Alternative Access Methods..."
echo ""

# Check if we can reach any other services
echo "Testing other services on the machine:"

# Test HTTP services
for port in 8006 3128 80 443; do
    if timeout 3s bash -c "echo >/dev/tcp/$TARGET_IP/$port" &>/dev/null; then
        echo "   ✅ Port $port open (possible service)"
        
        # Try to identify the service
        case $port in
            8006) echo "      → Likely Proxmox VE web interface" ;;
            3128) echo "      → Likely proxy service" ;;
            80) echo "      → HTTP service" ;;
            443) echo "      → HTTPS service" ;;
        esac
    fi
done

echo ""

# Test 5: Recovery suggestions
echo "5️⃣ Recovery Solutions..."
echo ""

# Check if Proxmox web interface is available
echo "🌐 Proxmox Web Interface Check:"
if timeout 5s bash -c "echo >/dev/tcp/$TARGET_IP/8006" &>/dev/null; then
    echo "   ✅ Proxmox web interface appears available"
    echo "   📱 Access via: https://$TARGET_IP:8006"
    echo "   🔑 Login with root credentials"
    echo "   💻 Open console to the VM/container"
else
    echo "   ❌ Proxmox web interface not accessible"
fi

echo ""

echo "🛠️ SSH Recovery Methods:"
echo ""

echo "Method A: Proxmox Console Access"
echo "  1. Open browser to https://$TARGET_IP:8006"
echo "  2. Login with root credentials"
echo "  3. Navigate to your VM/container"
echo "  4. Click 'Console' to get direct access"
echo "  5. Check SSH service: systemctl status ssh"
echo "  6. Restart SSH if needed: systemctl restart ssh"
echo ""

echo "Method B: Network Interface Reset"
echo "  If accessible via Proxmox console:"
echo "  1. Check network: ip addr show"
echo "  2. Restart networking: systemctl restart networking"
echo "  3. Check Tailscale: tailscale status"
echo "  4. Restart Tailscale: systemctl restart tailscaled"
echo ""

echo "Method C: SSH Service Recovery"
echo "  Commands to run via console:"
echo "  sudo systemctl status ssh"
echo "  sudo systemctl start ssh"
echo "  sudo systemctl enable ssh"
echo "  sudo ufw allow 22/tcp  # if firewall is active"
echo ""

echo "Method D: System Reboot"
echo "  If other methods fail:"
echo "  sudo reboot"
echo "  Wait 2-3 minutes for full startup"
echo ""

echo "Method E: Key/Permission Issues"
echo "  If SSH connects but authentication fails:"
echo "  # Reset authorized_keys via console"
echo "  mkdir -p ~/.ssh"
echo "  chmod 700 ~/.ssh"
echo "  # Re-add your public key"
echo "  chmod 600 ~/.ssh/authorized_keys"
echo ""

# Generate immediate action plan
echo "📋 IMMEDIATE ACTION PLAN:"
echo "========================"
echo ""

if timeout 5s bash -c "echo >/dev/tcp/$TARGET_IP/8006" &>/dev/null; then
    echo "🎯 RECOMMENDED: Use Proxmox Web Console"
    echo "1. Open: https://$TARGET_IP:8006"
    echo "2. Login and access VM console"
    echo "3. Run: systemctl status ssh"
    echo "4. If down: systemctl restart ssh"
    echo "5. Test: ssh root@$TARGET_IP"
    echo ""
elif ping -c 1 $TARGET_IP &>/dev/null; then
    echo "🎯 RECOMMENDED: Physical/Direct Access"
    echo "1. Machine is reachable but SSH is down"
    echo "2. Need direct console access"
    echo "3. Check SSH service status"
    echo "4. Restart SSH service"
    echo ""
else
    echo "🎯 RECOMMENDED: Check Machine Status"
    echo "1. Machine may be down or rebooting"
    echo "2. Check physical power/network cables"
    echo "3. Try power cycle if accessible"
    echo "4. Wait 5-10 minutes and retry"
fi

echo "🔄 Quick Retry Test:"
echo "Run this command to test SSH recovery:"
echo "ssh -o ConnectTimeout=5 root@$TARGET_IP 'echo \"SSH WORKING\" && nvidia-smi'"
echo ""

echo "📞 Support Information:"
echo "- GPU Backend still responding: ✅"
echo "- Tailscale network status: Check 'tailscale status'"
echo "- Proxmox access: https://$TARGET_IP:8006"
echo "- This script: ./fix-ssh-access.sh"