[package]
name = "zero-touch-tender-system"
version = "1.0.0"
description = "Zero-Touch Tender Processing System"
authors = ["ARA Property Services"]
license = ""
repository = ""
default-run = "zero-touch-tender-system"
edition = "2021"
rust-version = "1.60"

[build-dependencies]
tauri-build = { version = "1.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "1.0", features = [ "api-all", "devtools", "system-tray", "updater"] }

[features]
default = [ "custom-protocol" ]
custom-protocol = [ "tauri/custom-protocol" ]