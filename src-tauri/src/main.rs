// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{Manager, SystemTray, SystemTrayEvent, SystemTrayMenu, CustomMenuItem};
use std::process::Command;

// File operations
#[tauri::command]
async fn read_file(path: String) -> Result<String, String> {
    std::fs::read_to_string(path).map_err(|e| e.to_string())
}

#[tauri::command]
async fn write_file(path: String, content: String) -> Result<(), String> {
    std::fs::write(path, content).map_err(|e| e.to_string())
}

// System commands
#[tauri::command]
async fn execute_command(command: String, args: Vec<String>) -> Result<String, String> {
    let output = Command::new(command)
        .args(args)
        .output()
        .map_err(|e| e.to_string())?;
        
    String::from_utf8(output.stdout).map_err(|e| e.to_string())
}

// Zero-Touch workflow commands
#[tauri::command]
async fn start_workflow(tender_data: serde_json::Value) -> Result<String, String> {
    // This would integrate with your Convex backend
    // For now, return a mock workflow ID
    Ok("workflow_123456".to_string())
}

#[tauri::command]
async fn get_workflow_status(workflow_id: String) -> Result<serde_json::Value, String> {
    // Mock status response
    let status = serde_json::json!({
        "id": workflow_id,
        "stage": "CONTENT_READY",
        "status": "in_progress",
        "progress": 75,
        "estimated_completion": "2024-01-15T14:30:00Z"
    });
    
    Ok(status)
}

// Agent management
#[tauri::command]
async fn get_agent_status() -> Result<serde_json::Value, String> {
    let agents = serde_json::json!({
        "document_parser": {
            "status": "active",
            "success_rate": 95.2,
            "avg_processing_time": 42
        },
        "scheduler": {
            "status": "active", 
            "success_rate": 98.1,
            "avg_processing_time": 18
        },
        "content_builder": {
            "status": "active",
            "success_rate": 92.7,
            "avg_processing_time": 87
        },
        "voice_bot": {
            "status": "standby",
            "success_rate": 94.3,
            "avg_processing_time": 0
        }
    });
    
    Ok(agents)
}

fn main() {
    let quit = CustomMenuItem::new("quit".to_string(), "Quit");
    let show = CustomMenuItem::new("show".to_string(), "Show");
    let hide = CustomMenuItem::new("hide".to_string(), "Hide");
    
    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(tauri::SystemTrayMenuItem::Separator)
        .add_item(quit);

    let system_tray = SystemTray::new().with_menu(tray_menu);

    tauri::Builder::default()
        .system_tray(system_tray)
        .on_system_tray_event(|app, event| match event {
            SystemTrayEvent::LeftClick {
                position: _,
                size: _,
                ..
            } => {
                let window = app.get_window("main").unwrap();
                window.show().unwrap();
                window.set_focus().unwrap();
            }
            SystemTrayEvent::MenuItemClick { id, .. } => {
                match id.as_str() {
                    "quit" => {
                        std::process::exit(0);
                    }
                    "show" => {
                        let window = app.get_window("main").unwrap();
                        window.show().unwrap();
                        window.set_focus().unwrap();
                    }
                    "hide" => {
                        let window = app.get_window("main").unwrap();
                        window.hide().unwrap();
                    }
                    _ => {}
                }
            }
            _ => {}
        })
        .invoke_handler(tauri::generate_handler![
            read_file,
            write_file,
            execute_command,
            start_workflow,
            get_workflow_status,
            get_agent_status
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}