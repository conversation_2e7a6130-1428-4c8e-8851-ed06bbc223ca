#!/bin/bash

# Zero-Touch Tender System - Tauri Desktop App Builder
# This script builds the complete system into a standalone desktop application

set -e

echo "🖥️  Zero-Touch Tender System - Tauri Desktop Builder"
echo "====================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if Rust and <PERSON><PERSON> are installed
check_prerequisites() {
    echo -e "${BLUE}🔍 Checking prerequisites...${NC}"
    
    if ! command -v cargo &> /dev/null; then
        echo -e "${RED}❌ Rust/Cargo not found. Installing...${NC}"
        curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
        source "$HOME/.cargo/env"
    fi
    
    if ! command -v cargo tauri &> /dev/null; then
        echo -e "${YELLOW}📦 Installing Tauri CLI...${NC}"
        cargo install tauri-cli
    fi
    
    echo -e "${GREEN}✅ Prerequisites ready${NC}"
}

# Install Node dependencies
install_dependencies() {
    echo -e "${BLUE}📦 Installing Node dependencies...${NC}"
    
    if command -v bun &> /dev/null; then
        bun install
    elif command -v pnpm &> /dev/null; then
        pnpm install
    else
        npm install
    fi
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Create app icons (basic placeholder - you can replace with actual icons)
create_icons() {
    echo -e "${BLUE}🎨 Creating app icons...${NC}"
    
    mkdir -p src-tauri/icons
    
    # Create a simple SVG icon and convert to required formats
    # This is a placeholder - replace with your actual logo
    cat > src-tauri/icons/icon.svg << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" fill="#2196F3" rx="64"/>
  <text x="256" y="280" font-family="Arial, sans-serif" font-size="120" fill="white" text-anchor="middle">ZT</text>
  <text x="256" y="360" font-family="Arial, sans-serif" font-size="32" fill="#E3F2FD" text-anchor="middle">TENDER</text>
</svg>
EOF
    
    echo -e "${GREEN}✅ Icons created${NC}"
}

# Build for development
build_dev() {
    echo -e "${BLUE}🛠️  Building development version...${NC}"
    cargo tauri dev
}

# Build for production
build_prod() {
    echo -e "${BLUE}🏗️  Building production version...${NC}"
    
    # Build Next.js app first
    echo -e "${BLUE}📦 Building Next.js application...${NC}"
    if command -v bun &> /dev/null; then
        bun run build
    elif command -v pnpm &> /dev/null; then
        pnpm build
    else
        npm run build
    fi
    
    # Build Tauri app
    echo -e "${BLUE}🖥️  Building Tauri desktop application...${NC}"
    cargo tauri build
    
    echo -e "${GREEN}✅ Production build complete!${NC}"
    echo -e "${GREEN}📱 Desktop app created in: src-tauri/target/release/bundle/${NC}"
}

# Main execution
main() {
    echo -e "${GREEN}🎯 Building Zero-Touch Tender System Desktop App...${NC}"
    echo ""
    
    check_prerequisites
    install_dependencies
    create_icons
    
    # Check for build type
    if [[ "$1" == "--dev" || "$1" == "-d" ]]; then
        build_dev
    else
        build_prod
    fi
}

# Check for help flag
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Zero-Touch Tender System - Tauri Desktop Builder"
    echo ""
    echo "Usage: ./scripts/build-tauri.sh [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  -d, --dev      Run in development mode"
    echo ""
    echo "Build outputs:"
    echo "  macOS:    src-tauri/target/release/bundle/dmg/"
    echo "  Windows:  src-tauri/target/release/bundle/msi/"
    echo "  Linux:    src-tauri/target/release/bundle/deb/"
    echo ""
    exit 0
fi

# Run main function
main "$@"