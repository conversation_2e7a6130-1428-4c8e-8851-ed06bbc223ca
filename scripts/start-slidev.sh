#!/bin/bash

# Zero-Touch Tender System - Slidev Presentation Launcher
# Starts the Slidev presentation server

set -e

echo "📊 Zero-Touch Tender System - Presentation Launcher"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Slidev presentation...${NC}"

# Check if Slidev is installed
if ! command -v slidev &> /dev/null; then
    echo -e "${BLUE}📦 Installing Slidev CLI...${NC}"
    npm install -g @slidev/cli @slidev/theme-default
fi

# Ensure clean presentation without icon errors
echo -e "${BLUE}🎨 Ensuring compatibility...${NC}"

# Start Slidev
echo -e "${GREEN}📱 Presentation will be available at: http://localhost:3030${NC}"
echo -e "${GREEN}🎤 Presenter mode: http://localhost:3030/presenter${NC}"
echo ""

slidev slides.md --open
