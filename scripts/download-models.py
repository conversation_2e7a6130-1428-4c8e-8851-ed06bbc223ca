#!/usr/bin/env python3
"""
Download all required models for the document processing pipeline
This ensures models are cached in RunPod's persistent storage
"""

import os
import sys
from huggingface_hub import snapshot_download
import torch

# Models to download
MODELS = {
    "surya": [
        "vikp/surya_det3",
        "vikp/surya_rec",
        "vikp/surya_layout3"
    ],
    "qwen": [
        "Qwen/Qwen2.5-VL-7B"
    ],
    "layoutlm": [
        "microsoft/layoutlmv3-base"
    ],
    "minimax": [
        "MiniMax-01-M1"  # This might need special access
    ],
    "phi4": [
        "microsoft/phi-4"
    ]
}

def download_model(model_name, cache_dir):
    """Download a model to the cache directory"""
    try:
        print(f"📥 Downloading {model_name}...")
        
        # Special handling for different model types
        if "surya" in model_name:
            # Surya models are smaller, download directly
            snapshot_download(
                repo_id=model_name,
                cache_dir=cache_dir,
                resume_download=True
            )
        else:
            # For larger models, download with specific options
            snapshot_download(
                repo_id=model_name,
                cache_dir=cache_dir,
                resume_download=True,
                ignore_patterns=["*.bin", "*.onnx"],  # Skip unnecessary files
                token=os.getenv("HF_TOKEN")
            )
        
        print(f"✅ Successfully downloaded {model_name}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download {model_name}: {str(e)}")
        return False

def main():
    # Get cache directory from environment or use default
    cache_dir = os.getenv("HF_HOME", "/workspace/models")
    
    print(f"🚀 Model Downloader for Document Processing Pipeline")
    print(f"📁 Cache directory: {cache_dir}")
    print(f"🔧 CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"🎮 GPU: {torch.cuda.get_device_name(0)}")
    print("")
    
    # Check if HuggingFace token is set for gated models
    hf_token = os.getenv("HF_TOKEN")
    if not hf_token:
        print("⚠️  Warning: HF_TOKEN not set. Some models may fail to download.")
        print("   Set with: export HF_TOKEN='your_token_here'")
        print("")
    
    # Download each model
    total_models = sum(len(models) for models in MODELS.values())
    downloaded = 0
    failed = []
    
    for category, model_list in MODELS.items():
        print(f"\n📦 Downloading {category} models...")
        category_dir = os.path.join(cache_dir, category)
        os.makedirs(category_dir, exist_ok=True)
        
        for model in model_list:
            if download_model(model, category_dir):
                downloaded += 1
            else:
                failed.append(model)
    
    # Summary
    print("\n" + "="*50)
    print(f"📊 Download Summary:")
    print(f"   Total models: {total_models}")
    print(f"   ✅ Downloaded: {downloaded}")
    print(f"   ❌ Failed: {len(failed)}")
    
    if failed:
        print(f"\n⚠️  Failed models:")
        for model in failed:
            print(f"   - {model}")
        print("\n   These models may need manual download or special access.")
    
    # Check disk usage
    try:
        import shutil
        usage = shutil.disk_usage(cache_dir)
        print(f"\n💾 Disk Usage:")
        print(f"   Total: {usage.total / (1024**3):.1f} GB")
        print(f"   Used: {usage.used / (1024**3):.1f} GB")
        print(f"   Free: {usage.free / (1024**3):.1f} GB")
    except:
        pass
    
    print("\n✅ Model download complete!")
    
    # Create a marker file to indicate models are downloaded
    marker_file = os.path.join(cache_dir, ".models_downloaded")
    with open(marker_file, "w") as f:
        f.write(f"Models downloaded on {os.environ.get('HOSTNAME', 'unknown')}\n")
    
    return 0 if len(failed) == 0 else 1

if __name__ == "__main__":
    sys.exit(main())