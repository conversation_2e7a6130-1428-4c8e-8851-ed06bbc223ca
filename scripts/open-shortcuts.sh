#!/bin/bash

# Quick launcher to open the shortcuts folder
# This makes it easy to access all the app shortcuts

echo "🚀 Opening Zero-Touch Tender System shortcuts..."

# Get the absolute path of the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SHORTCUTS_DIR="$PROJECT_DIR/shortcuts"

# Check if shortcuts exist
if [ ! -d "$SHORTCUTS_DIR" ]; then
    echo "📦 Shortcuts not found. Creating them now..."
    "$PROJECT_DIR/scripts/create-shortcuts.sh"
fi

# Open shortcuts folder based on platform
case "$(uname -s)" in
    Darwin)
        # macOS
        open "$SHORTCUTS_DIR"
        echo "📱 Shortcuts opened in Finder!"
        echo ""
        echo "Available app bundles:"
        echo "  🚀 Zero-Touch Dev Mode.app"
        echo "  🖥️  Zero-Touch Desktop Builder.app" 
        echo "  📊 Zero-Touch Presentation.app"
        ;;
    Linux)
        # Linux
        if command -v xdg-open &> /dev/null; then
            xdg-open "$SHORTCUTS_DIR"
        elif command -v nautilus &> /dev/null; then
            nautilus "$SHORTCUTS_DIR"
        else
            echo "📁 Shortcuts location: $SHORTCUTS_DIR"
        fi
        ;;
    CYGWIN*|MINGW*|MSYS*)
        # Windows
        explorer.exe "$SHORTCUTS_DIR"
        ;;
    *)
        echo "📁 Shortcuts location: $SHORTCUTS_DIR"
        ;;
esac

echo "✨ Double-click any app to launch the corresponding tool!"