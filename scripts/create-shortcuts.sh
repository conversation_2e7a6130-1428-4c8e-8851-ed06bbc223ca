#!/bin/bash

# Zero-Touch Tender System - Icon Shortcuts Creator
# Creates desktop shortcuts and app bundles for easy script access

set -e

echo "🎯 Zero-Touch Tender System - Creating Icon Shortcuts"
echo "===================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the absolute path of the project directory
PROJECT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SHORTCUTS_DIR="$PROJECT_DIR/shortcuts"

# Create shortcuts directory
mkdir -p "$SHORTCUTS_DIR"

# Create icon files (SVG format for scalability)
create_icons() {
    echo -e "${BLUE}🎨 Creating app icons...${NC}"
    
    mkdir -p "$SHORTCUTS_DIR/icons"
    
    # Development Mode Icon (Blue)
    cat > "$SHORTCUTS_DIR/icons/dev-mode.svg" << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="devGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#devGrad)" rx="64"/>
  <circle cx="256" cy="200" r="80" fill="white" opacity="0.9"/>
  <text x="256" y="220" font-family="Arial, sans-serif" font-size="60" fill="#1565C0" text-anchor="middle" font-weight="bold">⚡</text>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="36" fill="white" text-anchor="middle" font-weight="bold">DEV</text>
  <text x="256" y="360" font-family="Arial, sans-serif" font-size="24" fill="#E3F2FD" text-anchor="middle">MODE</text>
</svg>
EOF

    # Desktop App Icon (Purple)
    cat > "$SHORTCUTS_DIR/icons/desktop-app.svg" << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="desktopGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6A1B9A;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#desktopGrad)" rx="64"/>
  <rect x="128" y="160" width="256" height="160" fill="white" opacity="0.9" rx="16"/>
  <rect x="220" y="320" width="72" height="40" fill="white" opacity="0.7" rx="8"/>
  <rect x="180" y="360" width="152" height="24" fill="white" opacity="0.5" rx="12"/>
  <text x="256" y="250" font-family="Arial, sans-serif" font-size="48" fill="#6A1B9A" text-anchor="middle" font-weight="bold">🖥️</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">DESKTOP</text>
</svg>
EOF

    # Tauri Build Icon (Orange)
    cat > "$SHORTCUTS_DIR/icons/tauri-build.svg" << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="buildGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#buildGrad)" rx="64"/>
  <polygon points="256,120 320,200 320,280 256,360 192,280 192,200" fill="white" opacity="0.9"/>
  <text x="256" y="250" font-family="Arial, sans-serif" font-size="60" fill="#E65100" text-anchor="middle" font-weight="bold">⚙️</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">BUILD</text>
</svg>
EOF

    # Presentation Icon (Green)
    cat > "$SHORTCUTS_DIR/icons/presentation.svg" << 'EOF'
<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="presentGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#presentGrad)" rx="64"/>
  <rect x="100" y="140" width="312" height="200" fill="white" opacity="0.9" rx="16"/>
  <circle cx="256" cy="240" r="40" fill="#2E7D32"/>
  <text x="256" y="255" font-family="Arial, sans-serif" font-size="30" fill="white" text-anchor="middle">▶</text>
  <text x="256" y="400" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">SLIDES</text>
</svg>
EOF

    echo -e "${GREEN}✅ Icons created${NC}"
}

# Convert SVG to PNG for better compatibility
convert_icons() {
    echo -e "${BLUE}🔄 Converting icons to PNG format...${NC}"
    
    # Check if ImageMagick or rsvg-convert is available
    if command -v convert &> /dev/null; then
        CONVERTER="convert"
    elif command -v rsvg-convert &> /dev/null; then
        CONVERTER="rsvg-convert"
    else
        echo -e "${YELLOW}⚠️  No image converter found. Installing rsvg-convert...${NC}"
        if command -v brew &> /dev/null; then
            brew install librsvg
            CONVERTER="rsvg-convert"
        else
            echo -e "${RED}❌ Please install ImageMagick or librsvg manually${NC}"
            return 1
        fi
    fi
    
    cd "$SHORTCUTS_DIR/icons"
    
    for svg_file in *.svg; do
        png_file="${svg_file%.svg}.png"
        if [ "$CONVERTER" = "convert" ]; then
            convert -background none -size 512x512 "$svg_file" "$png_file"
        else
            rsvg-convert -w 512 -h 512 "$svg_file" -o "$png_file"
        fi
        echo -e "${GREEN}✓ Created $png_file${NC}"
    done
    
    cd "$PROJECT_DIR"
    echo -e "${GREEN}✅ Icons converted${NC}"
}

# Create macOS App Bundles
create_macos_apps() {
    echo -e "${BLUE}🍎 Creating macOS app bundles...${NC}"
    
    # Development Mode App
    create_app_bundle "Zero-Touch Dev Mode" "dev-mode" "dev-runner.sh" "Start Zero-Touch development environment"
    
    # Desktop Build App  
    create_app_bundle "Zero-Touch Desktop Builder" "tauri-build" "build-tauri.sh" "Build Zero-Touch desktop application"
    
    # Presentation App
    create_app_bundle "Zero-Touch Presentation" "presentation" "start-slidev.sh" "Launch Zero-Touch presentation"
    
    echo -e "${GREEN}✅ macOS app bundles created${NC}"
}

# Helper function to create individual app bundle
create_app_bundle() {
    local app_name="$1"
    local icon_name="$2"
    local script_name="$3"
    local description="$4"
    
    local app_dir="$SHORTCUTS_DIR/$app_name.app"
    
    # Create app bundle structure
    mkdir -p "$app_dir/Contents/MacOS"
    mkdir -p "$app_dir/Contents/Resources"
    
    # Create Info.plist
    cat > "$app_dir/Contents/Info.plist" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>CFBundleExecutable</key>
    <string>launcher</string>
    <key>CFBundleIdentifier</key>
    <string>com.araproperty.zerotouchsystem.${icon_name}</string>
    <key>CFBundleName</key>
    <string>${app_name}</string>
    <key>CFBundleDisplayName</key>
    <string>${app_name}</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleSignature</key>
    <string>ZTTS</string>
    <key>CFBundleIconFile</key>
    <string>icon</string>
    <key>LSMinimumSystemVersion</key>
    <string>10.13</string>
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <true/>
    </dict>
</dict>
</plist>
EOF

    # Copy icon (if PNG exists, otherwise use SVG)
    if [ -f "$SHORTCUTS_DIR/icons/${icon_name}.png" ]; then
        cp "$SHORTCUTS_DIR/icons/${icon_name}.png" "$app_dir/Contents/Resources/icon.png"
    else
        cp "$SHORTCUTS_DIR/icons/${icon_name}.svg" "$app_dir/Contents/Resources/icon.svg"
    fi
    
    # Create launcher script
    cat > "$app_dir/Contents/MacOS/launcher" << EOF
#!/bin/bash
cd "$PROJECT_DIR"
osascript -e 'tell application "Terminal" to do script "cd \\"$PROJECT_DIR\\" && ./scripts/$script_name"'
EOF
    
    chmod +x "$app_dir/Contents/MacOS/launcher"
    
    echo -e "${GREEN}✓ Created $app_name.app${NC}"
}

# Create Slidev launcher script if it doesn't exist
create_slidev_script() {
    echo -e "${BLUE}📊 Creating Slidev launcher script...${NC}"
    
    cat > "$PROJECT_DIR/scripts/start-slidev.sh" << 'EOF'
#!/bin/bash

# Zero-Touch Tender System - Slidev Presentation Launcher
# Starts the Slidev presentation server

set -e

echo "📊 Zero-Touch Tender System - Presentation Launcher"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting Slidev presentation...${NC}"

# Check if Slidev is installed
if ! command -v slidev &> /dev/null; then
    echo -e "${BLUE}📦 Installing Slidev CLI...${NC}"
    npm install -g @slidev/cli @slidev/theme-default
fi

# Start Slidev
echo -e "${GREEN}📱 Presentation will be available at: http://localhost:3030${NC}"
echo -e "${GREEN}🎤 Presenter mode: http://localhost:3030/presenter${NC}"
echo ""

slidev slides.md --open
EOF
    
    chmod +x "$PROJECT_DIR/scripts/start-slidev.sh"
    echo -e "${GREEN}✅ Slidev launcher created${NC}"
}

# Create desktop shortcuts for other platforms
create_desktop_shortcuts() {
    echo -e "${BLUE}🖥️  Creating desktop shortcuts...${NC}"
    
    # Create .desktop files for Linux
    create_desktop_file "zero-touch-dev" "Zero-Touch Dev Mode" "dev-mode" "dev-runner.sh" "Development environment"
    create_desktop_file "zero-touch-build" "Zero-Touch Desktop Builder" "tauri-build" "build-tauri.sh" "Build desktop app"
    create_desktop_file "zero-touch-slides" "Zero-Touch Presentation" "presentation" "start-slidev.sh" "Presentation slides"
    
    echo -e "${GREEN}✅ Desktop shortcuts created${NC}"
}

# Helper function to create .desktop files
create_desktop_file() {
    local file_name="$1"
    local display_name="$2"
    local icon_name="$3"
    local script_name="$4"
    local description="$5"
    
    cat > "$SHORTCUTS_DIR/${file_name}.desktop" << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=${display_name}
Comment=${description}
Exec=${PROJECT_DIR}/scripts/${script_name}
Icon=${SHORTCUTS_DIR}/icons/${icon_name}.png
Path=${PROJECT_DIR}
Terminal=true
Categories=Development;Office;
StartupNotify=true
EOF
    
    chmod +x "$SHORTCUTS_DIR/${file_name}.desktop"
    echo -e "${GREEN}✓ Created ${file_name}.desktop${NC}"
}

# Create Windows batch files
create_windows_shortcuts() {
    echo -e "${BLUE}🪟 Creating Windows shortcuts...${NC}"
    
    # Development Mode
    cat > "$SHORTCUTS_DIR/Zero-Touch Dev Mode.bat" << EOF
@echo off
cd /d "%~dp0.."
bash scripts/dev-runner.sh
pause
EOF

    # Desktop Builder
    cat > "$SHORTCUTS_DIR/Zero-Touch Desktop Builder.bat" << EOF
@echo off
cd /d "%~dp0.."
bash scripts/build-tauri.sh
pause
EOF

    # Presentation
    cat > "$SHORTCUTS_DIR/Zero-Touch Presentation.bat" << EOF
@echo off
cd /d "%~dp0.."
bash scripts/start-slidev.sh
pause
EOF
    
    echo -e "${GREEN}✅ Windows batch files created${NC}"
}

# Main execution
main() {
    echo -e "${GREEN}🎯 Creating icon shortcuts for Zero-Touch Tender System...${NC}"
    echo ""
    
    create_icons
    convert_icons || echo -e "${YELLOW}⚠️  Icon conversion skipped${NC}"
    create_slidev_script
    
    # Detect platform and create appropriate shortcuts
    case "$(uname -s)" in
        Darwin)
            create_macos_apps
            ;;
        Linux)
            create_desktop_shortcuts
            ;;
        CYGWIN*|MINGW*|MSYS*)
            create_windows_shortcuts
            ;;
        *)
            echo -e "${YELLOW}⚠️  Unknown platform. Creating generic shortcuts...${NC}"
            create_desktop_shortcuts
            create_windows_shortcuts
            ;;
    esac
    
    echo ""
    echo -e "${GREEN}🎉 Icon shortcuts created successfully!${NC}"
    echo -e "${BLUE}📁 Location: $SHORTCUTS_DIR${NC}"
    echo ""
    echo -e "${GREEN}Available shortcuts:${NC}"
    echo -e "  🚀 ${GREEN}Zero-Touch Dev Mode${NC} - Start development environment"
    echo -e "  🖥️  ${GREEN}Zero-Touch Desktop Builder${NC} - Build desktop application"
    echo -e "  📊 ${GREEN}Zero-Touch Presentation${NC} - Launch Slidev presentation"
    echo ""
    echo -e "${BLUE}Usage:${NC}"
    echo -e "  • Double-click app bundles (macOS)"
    echo -e "  • Right-click → Open .desktop files (Linux)"
    echo -e "  • Double-click .bat files (Windows)"
}

# Check for help flag
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Zero-Touch Tender System - Icon Shortcuts Creator"
    echo ""
    echo "Usage: ./scripts/create-shortcuts.sh"
    echo ""
    echo "Creates desktop shortcuts and app bundles for:"
    echo "  • Development mode launcher"
    echo "  • Desktop app builder"
    echo "  • Presentation viewer"
    echo ""
    echo "Output: shortcuts/ directory with platform-specific files"
    echo ""
    exit 0
fi

# Run main function
main