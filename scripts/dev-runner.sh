#!/bin/bash

# Zero-Touch Tender System - Development Runner Script
# This script sets up and runs the complete Zero-Touch system

set -e

echo "🚀 Zero-Touch Tender System - Development Runner"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if required environment files exist
check_env() {
    echo -e "${BLUE}📋 Checking environment configuration...${NC}"
    
    if [ ! -f .env.local ]; then
        echo -e "${YELLOW}⚠️  .env.local not found. Creating from template...${NC}"
        cat > .env.local << 'EOF'
# Core Configuration
CONVEX_DEPLOYMENT=dev:your-deployment
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# AI Integration
CONVEX_OPENAI_API_KEY=your_openai_key

# Gmail Integration
GMAIL_CLIENT_ID=your_gmail_client_id
GMAIL_CLIENT_SECRET=your_gmail_secret
GMAIL_WEBHOOK_SECRET=your_webhook_secret

# Calendar Integration
GOOGLE_CALENDAR_API_KEY=your_calendar_key

# SMS Notifications
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token
TWILIO_PHONE_NUMBER=your_twilio_number

# Meeting Platforms
TEAMS_CLIENT_ID=your_teams_client_id
ZOOM_API_KEY=your_zoom_key

# Development
NODE_ENV=development
EOF
        echo -e "${RED}❌ Please configure .env.local with your API keys and run again${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ Environment configuration found${NC}"
}

# Install dependencies
install_deps() {
    echo -e "${BLUE}📦 Installing dependencies...${NC}"
    
    if command -v bun &> /dev/null; then
        echo -e "${GREEN}🟢 Using Bun for package management${NC}"
        bun install
    elif command -v pnpm &> /dev/null; then
        echo -e "${GREEN}🟢 Using pnpm for package management${NC}"
        pnpm install
    else
        echo -e "${YELLOW}🟡 Using npm for package management${NC}"
        npm install
    fi
    
    echo -e "${GREEN}✅ Dependencies installed${NC}"
}

# Setup Convex
setup_convex() {
    echo -e "${BLUE}🗄️  Setting up Convex backend...${NC}"
    
    if ! command -v convex &> /dev/null; then
        echo -e "${YELLOW}📦 Installing Convex CLI...${NC}"
        npm install -g convex
    fi
    
    echo -e "${GREEN}✅ Convex ready${NC}"
}

# Start development servers
start_servers() {
    echo -e "${BLUE}🚀 Starting development servers...${NC}"
    echo ""
    echo -e "${GREEN}📱 Frontend:${NC} http://localhost:3000"
    echo -e "${GREEN}🗄️  Backend:${NC} Convex development dashboard"
    echo -e "${GREEN}⚡ Zero-Touch:${NC} Available at /zero-touch endpoint"
    echo ""
    echo -e "${YELLOW}Press Ctrl+C to stop all servers${NC}"
    echo ""
    
    # Start both frontend and backend in parallel
    if command -v bun &> /dev/null; then
        bun run dev
    elif command -v pnpm &> /dev/null; then
        pnpm dev
    else
        npm run dev
    fi
}

# Open development URLs
open_browser() {
    sleep 3
    echo -e "${BLUE}🌐 Opening development environment...${NC}"
    
    if command -v open &> /dev/null; then
        # macOS
        open http://localhost:3000
    elif command -v xdg-open &> /dev/null; then
        # Linux
        xdg-open http://localhost:3000
    elif command -v start &> /dev/null; then
        # Windows
        start http://localhost:3000
    fi
}

# Main execution
main() {
    echo -e "${GREEN}🎯 Starting Zero-Touch Tender System...${NC}"
    echo ""
    
    check_env
    install_deps
    setup_convex
    
    # Start servers in background and open browser
    start_servers &
    SERVER_PID=$!
    
    open_browser
    
    # Wait for servers
    wait $SERVER_PID
}

# Handle script termination
cleanup() {
    echo -e "\n${YELLOW}🛑 Shutting down servers...${NC}"
    kill $SERVER_PID 2>/dev/null || true
    exit 0
}

trap cleanup SIGINT SIGTERM

# Check for help flag
if [[ "$1" == "--help" || "$1" == "-h" ]]; then
    echo "Zero-Touch Tender System - Development Runner"
    echo ""
    echo "Usage: ./scripts/dev-runner.sh [options]"
    echo ""
    echo "Options:"
    echo "  -h, --help     Show this help message"
    echo "  --no-browser   Don't automatically open browser"
    echo ""
    echo "Environment setup:"
    echo "  1. Configure .env.local with your API keys"
    echo "  2. Run this script to start development servers"
    echo "  3. Access the application at http://localhost:3000"
    echo ""
    exit 0
fi

# Run main function
main