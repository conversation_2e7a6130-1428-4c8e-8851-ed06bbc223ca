#!/bin/bash

echo "🚀 Deploying Document Processing Pipeline to GPU Machine"
echo "======================================================="

# Configuration
GPU_HOST="root@*************"
REMOTE_DIR="/root/doc-processor"

echo "Target: $GPU_HOST"
echo "Remote directory: $REMOTE_DIR"
echo ""

# Test connection
echo "1️⃣ Testing connection to GPU machine..."
if ! ssh -o ConnectTimeout=5 $GPU_HOST "echo 'Connection successful'"; then
    echo "❌ Cannot connect to $GPU_HOST"
    echo "   Check: SSH service running, network connectivity, firewall"
    exit 1
fi
echo "✅ Connection successful"

# Create remote directory
echo ""
echo "2️⃣ Creating remote directory..."
ssh $GPU_HOST "mkdir -p $REMOTE_DIR"

# Copy all files
echo ""
echo "3️⃣ Copying files to GPU machine..."
rsync -avz --progress \
    --exclude='.git' \
    --exclude='node_modules' \
    --exclude='.next' \
    --exclude='*.log' \
    ./ $GPU_HOST:$REMOTE_DIR/

# Copy and run the RTX 4090 fix script
echo ""
echo "4️⃣ Running RTX 4090 fix script..."
ssh $GPU_HOST "cd $REMOTE_DIR && chmod +x rtx4090-fix-2025.sh && ./rtx4090-fix-2025.sh"

# Check if reboot is needed
echo ""
echo "5️⃣ Checking if reboot is needed..."
REBOOT_NEEDED=$(ssh $GPU_HOST "if [ -f /var/run/reboot-required ]; then echo 'yes'; else echo 'no'; fi")

if [ "$REBOOT_NEEDED" = "yes" ]; then
    echo "⚠️  Reboot required for driver installation"
    read -p "Reboot now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "Rebooting GPU machine..."
        ssh $GPU_HOST "reboot"
        echo "Waiting for machine to come back online..."
        sleep 30
        
        # Wait for machine to come back
        for i in {1..20}; do
            if ssh -o ConnectTimeout=5 $GPU_HOST "echo 'Back online'" 2>/dev/null; then
                echo "✅ Machine is back online"
                break
            fi
            echo "   Waiting... ($i/20)"
            sleep 10
        done
    fi
fi

# Test GPU after reboot/fix
echo ""
echo "6️⃣ Testing GPU functionality..."
GPU_TEST=$(ssh $GPU_HOST "nvidia-smi --query-gpu=name --format=csv,noheader" 2>/dev/null)
if [[ $GPU_TEST == *"RTX 4090"* ]]; then
    echo "✅ RTX 4090 detected and working"
else
    echo "❌ GPU test failed: $GPU_TEST"
    echo "Manual intervention may be required"
    exit 1
fi

# Test Docker GPU access
echo ""
echo "7️⃣ Testing Docker GPU access..."
DOCKER_TEST=$(ssh $GPU_HOST "timeout 30 docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi --query-gpu=name --format=csv,noheader" 2>/dev/null)
if [[ $DOCKER_TEST == *"RTX 4090"* ]]; then
    echo "✅ Docker GPU access working"
else
    echo "❌ Docker GPU test failed"
    echo "Output: $DOCKER_TEST"
    # Continue anyway as we can fix this later
fi

# Start the document processing services
echo ""
echo "8️⃣ Starting document processing services..."
ssh $GPU_HOST "cd $REMOTE_DIR && docker-compose -f runpod-docker-compose.yml down || true"
ssh $GPU_HOST "cd $REMOTE_DIR && docker-compose -f runpod-docker-compose.yml pull"
ssh $GPU_HOST "cd $REMOTE_DIR && docker-compose -f runpod-docker-compose.yml up -d"

# Wait for services to start
echo ""
echo "9️⃣ Waiting for services to start..."
sleep 30

# Test the gateway
echo ""
echo "🔟 Testing document processing gateway..."
GATEWAY_TEST=$(ssh $GPU_HOST "curl -s http://localhost:8000/health" 2>/dev/null)
if [[ $GATEWAY_TEST == *"healthy"* ]]; then
    echo "✅ Gateway is responding"
else
    echo "❌ Gateway test failed"
    echo "Checking service status..."
    ssh $GPU_HOST "cd $REMOTE_DIR && docker-compose ps"
fi

# Show final status
echo ""
echo "🎉 Deployment Summary"
echo "===================="
echo "GPU Machine: $GPU_HOST"
echo "Services directory: $REMOTE_DIR"
echo ""

# Get service status
echo "Service Status:"
ssh $GPU_HOST "cd $REMOTE_DIR && docker-compose ps"

echo ""
echo "GPU Status:"
ssh $GPU_HOST "nvidia-smi --query-gpu=name,temperature.gpu,power.draw,memory.used,memory.total --format=csv"

echo ""
echo "🌐 Access URLs:"
echo "Gateway API: http://*************:8000"
echo "Health Check: http://*************:8000/health"
echo "API Docs: http://*************:8000/docs"

echo ""
echo "🔧 Management Commands:"
echo "ssh $GPU_HOST 'cd $REMOTE_DIR && docker-compose logs -f'"
echo "ssh $GPU_HOST 'cd $REMOTE_DIR && docker-compose ps'"
echo "ssh $GPU_HOST 'cd $REMOTE_DIR && docker-compose restart'"
echo "ssh $GPU_HOST 'gpu-test'"

# Test document upload from local machine
echo ""
echo "🧪 Testing document upload from local machine..."
if command -v curl >/dev/null 2>&1; then
    echo "This is a test document for RTX 4090 processing" > test-upload.txt
    UPLOAD_RESPONSE=$(curl -s -X POST -F "file=@test-upload.txt" http://*************:8000/api/upload 2>/dev/null)
    if [[ $UPLOAD_RESPONSE == *"document_id"* ]]; then
        echo "✅ Document upload test successful"
        DOC_ID=$(echo $UPLOAD_RESPONSE | grep -o '"document_id":"[^"]*"' | cut -d'"' -f4)
        echo "   Document ID: $DOC_ID"
    else
        echo "❌ Document upload test failed"
        echo "   Response: $UPLOAD_RESPONSE"
    fi
    rm -f test-upload.txt
else
    echo "curl not available for testing upload"
fi

echo ""
echo "✅ Deployment complete! Your RTX 4090 is ready for document processing."