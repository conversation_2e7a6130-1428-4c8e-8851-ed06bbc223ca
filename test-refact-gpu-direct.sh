#!/bin/bash

echo "🔍 Testing Refact GPU Access"
echo "============================"
echo ""

# Try to connect via SSH with a shorter timeout
echo "Attempting to test Refact GPU access..."

# Test SSH with very short timeout
if timeout 5s ssh -o ConnectTimeout=3 -o BatchMode=yes root@100.93.124.60 "echo 'Connected'" 2>/dev/null; then
    echo "✅ SSH connection successful"
    
    # Test Refact GPU access
    echo ""
    echo "Testing Refact GPU capabilities..."
    
    ssh root@100.93.124.60 "
        cd /root/refact-setup 2>/dev/null || { echo 'Directory not found'; exit 1; }
        
        if [ -f venv/bin/activate ]; then
            source venv/bin/activate
            echo '✅ Virtual environment activated'
            
            # Test PyTorch CUDA
            echo ''
            echo '🔥 Testing PyTorch CUDA:'
            python3 -c '
import torch
print(f\"PyTorch Version: {torch.__version__}\")
print(f\"CUDA Available: {torch.cuda.is_available()}\")
if torch.cuda.is_available():
    print(f\"CUDA Version: {torch.version.cuda}\")
    print(f\"GPU Count: {torch.cuda.device_count()}\")
    print(f\"GPU 0: {torch.cuda.get_device_name(0)}\")
    print(f\"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB\")
    
    # Test tensor operations on GPU
    try:
        x = torch.randn(100, 100).cuda()
        y = torch.matmul(x, x)
        print(f\"GPU Tensor Test: SUCCESS (shape: {y.shape})\")
        print(f\"Current GPU Memory: {torch.cuda.memory_allocated() / 1024**2:.1f}MB\")
    except Exception as e:
        print(f\"GPU Tensor Test: FAILED - {e}\")
else:
    print(\"❌ CUDA not available\")
            '
            
            echo ''
            echo '🤖 Testing Refact:'
            # Test if refact command exists
            if command -v refact &> /dev/null; then
                echo '✅ Refact command found'
                refact --version 2>/dev/null || echo 'Version check failed'
                
                # Check Refact configuration
                echo ''
                echo '📝 Refact Configuration:'
                if [ -f ~/.cache/refact/cli.yaml ]; then
                    echo '✅ CLI config exists:'
                    cat ~/.cache/refact/cli.yaml
                else
                    echo '❌ CLI config not found'
                fi
                
                if [ -f ~/.cache/refact/bring-your-own-key.yaml ]; then
                    echo '✅ BYOK config exists:'
                    cat ~/.cache/refact/bring-your-own-key.yaml
                else
                    echo '❌ BYOK config not found'
                fi
            else
                echo '❌ Refact command not found'
                echo 'Available commands:'
                ls venv/bin/ | grep -i refact || echo 'No refact binaries found'
            fi
            
        else
            echo '❌ Virtual environment not found'
        fi
    " 2>/dev/null
    
else
    echo "❌ SSH connection failed"
    echo ""
    echo "Alternative diagnostic approach:"
    echo "1. Check if GPU backend can provide process information"
    
    # Get GPU processes via API
    echo ""
    echo "Current GPU processes via API:"
    PROCESSES=$(curl -s http://100.93.124.60:9092/api/gpu/status | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    processes = data.get('processes', [])
    if processes:
        for p in processes:
            print(f\"  PID {p['pid']}: {p['name']} ({p['memory_usage']}MB)\")
    else:
        print('  No GPU processes currently running')
except:
    print('  Failed to parse GPU status')
    ")
    echo "$PROCESSES"
    
    echo ""
    echo "🎯 Recommended Actions:"
    echo "1. Try SSH connection from different terminal"
    echo "2. Check if machine needs reboot: tailscale ping 100.93.124.60"
    echo "3. Access Proxmox console directly if available"
    echo "4. Wait for SSH service to restore"
fi

echo ""
echo "📊 Current GPU Status:"
curl -s http://100.93.124.60:9092/api/gpu/status | python3 -c "
import json, sys
try:
    data = json.load(sys.stdin)
    print(f\"GPU: {data['name']}\")
    print(f\"Temperature: {data['temperature']}°C\")
    print(f\"Memory: {data['memory_used']}/{data['memory_total']}MB\")
    print(f\"Utilization: {data['utilization']}%\")
    print(f\"Processes: {len(data['processes'])}\")
except:
    print('Failed to get GPU status')
"

echo ""
echo "🌐 Access URLs:"
echo "GPU Dashboard: http://100.93.124.60:9092"
echo "Health Check: curl http://100.93.124.60:9092/health"