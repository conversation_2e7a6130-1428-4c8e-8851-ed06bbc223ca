# RunPod-optimized Docker Compose configuration
version: '3.9'

services:
  # Gateway with RunPod network configuration
  gateway:
    build: ./gateway
    container_name: doc-gateway
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=******************************************/docdb
      - ENABLE_METRICS=true
      - RUNPOD_POD_ID=${RUNPOD_POD_ID}
      - RUNPOD_GPU_COUNT=${RUNPOD_GPU_COUNT:-1}
    depends_on:
      - redis
      - postgres
    volumes:
      - /workspace/uploads:/app/uploads
      - /workspace/outputs:/app/outputs
    networks:
      - doc-network
    restart: unless-stopped

  # Surya - OCR and Layout Detection
  surya:
    build: 
      context: ./services/surya
      dockerfile: Dockerfile
    container_name: surya-ocr
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - SURY<PERSON>_BATCH_SIZE=${SURYA_BATCH_SIZE:-8}  # Increased for RunPod
      - MODEL_CACHE=/workspace/models/surya
      - ENABLE_FP16=true
      - TORCH_CUDA_ARCH_LIST="7.5;8.0;8.6;8.9;9.0"  # Support various GPU architectures
    volumes:
      - /workspace/models/surya:/models
      - /workspace/shared:/shared
    networks:
      - doc-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 16G

  # Qwen2.5-VL - Using vLLM for efficient serving
  qwen:
    image: vllm/vllm-openai:latest
    container_name: qwen-vl
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=Qwen/Qwen2.5-VL-7B
      - TENSOR_PARALLEL_SIZE=${QWEN_TP_SIZE:-1}
      - GPU_MEMORY_UTILIZATION=0.9
      - MAX_MODEL_LEN=32768
      - DOWNLOAD_DIR=/workspace/models
      - HF_TOKEN=${HF_TOKEN}
    volumes:
      - /workspace/models:/workspace/models
      - /workspace/shared:/shared
    ports:
      - "8001:8000"
    networks:
      - doc-network
    restart: unless-stopped
    command: >
      --model Qwen/Qwen2.5-VL-7B
      --dtype float16
      --max-model-len 32768
      --gpu-memory-utilization 0.9
      --download-dir /workspace/models

  # LayoutLMv3 - Document Structure Analysis
  layoutlm:
    build: 
      context: ./services/layoutlm
      dockerfile: Dockerfile
    container_name: layoutlm-v3
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=microsoft/layoutlmv3-base
      - BATCH_SIZE=${LAYOUTLM_BATCH:-16}  # Increased for RunPod
      - MODEL_CACHE=/workspace/models/layoutlm
      - TORCH_CUDA_ARCH_LIST="7.5;8.0;8.6;8.9;9.0"
    volumes:
      - /workspace/models/layoutlm:/models
      - /workspace/shared:/shared
    networks:
      - doc-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G

  # MiniMax-M1 - Long Context Processing (using multiple GPUs if available)
  minimax:
    image: vllm/vllm-openai:latest
    container_name: minimax-m1
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all  # Use all available GPUs
      - MODEL_NAME=MiniMax-01-M1
      - TENSOR_PARALLEL_SIZE=${MINIMAX_TP_SIZE:-${RUNPOD_GPU_COUNT:-1}}
      - GPU_MEMORY_UTILIZATION=0.95
      - QUANTIZATION=awq
      - MAX_MODEL_LEN=1000000
      - DOWNLOAD_DIR=/workspace/models
      - HF_TOKEN=${HF_TOKEN}
    volumes:
      - /workspace/models:/workspace/models
      - /workspace/shared:/shared
    ports:
      - "8002:8000"
    networks:
      - doc-network
    restart: unless-stopped
    command: >
      --model MiniMax-01-M1
      --dtype float16
      --quantization awq
      --max-model-len 1000000
      --gpu-memory-utilization 0.95
      --tensor-parallel-size ${MINIMAX_TP_SIZE:-${RUNPOD_GPU_COUNT:-1}}
      --download-dir /workspace/models

  # Phi-4 - Financial Analysis
  phi4:
    image: vllm/vllm-openai:latest
    container_name: phi-4
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=0
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=microsoft/phi-4
      - GPU_MEMORY_UTILIZATION=0.8
      - MAX_MODEL_LEN=16384
      - DOWNLOAD_DIR=/workspace/models
      - HF_TOKEN=${HF_TOKEN}
    volumes:
      - /workspace/models:/workspace/models
      - /workspace/shared:/shared
    ports:
      - "8003:8000"
    networks:
      - doc-network
    restart: unless-stopped
    command: >
      --model microsoft/phi-4
      --dtype float16
      --max-model-len 16384
      --gpu-memory-utilization 0.8
      --download-dir /workspace/models

  # Redis with persistence
  redis:
    image: redis:7-alpine
    container_name: doc-redis
    command: >
      redis-server 
      --appendonly yes 
      --maxmemory 4gb 
      --maxmemory-policy allkeys-lru
      --save 60 1000
    volumes:
      - /workspace/redis-data:/data
    networks:
      - doc-network
    restart: unless-stopped

  # PostgreSQL with optimizations
  postgres:
    image: postgres:16-alpine
    container_name: doc-postgres
    environment:
      - POSTGRES_USER=docuser
      - POSTGRES_PASSWORD=docpass
      - POSTGRES_DB=docdb
      - POSTGRES_INITDB_ARGS=--encoding=UTF8
      - POSTGRES_HOST_AUTH_METHOD=md5
    volumes:
      - /workspace/postgres-data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - doc-network
    restart: unless-stopped
    command: >
      postgres
      -c shared_buffers=2GB
      -c effective_cache_size=6GB
      -c maintenance_work_mem=512MB
      -c work_mem=32MB

  # Nginx reverse proxy for RunPod
  nginx:
    image: nginx:alpine
    container_name: doc-nginx
    ports:
      - "80:80"
    volumes:
      - ./runpod-nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - gateway
    networks:
      - doc-network
    restart: unless-stopped

  # Model downloader service
  model-downloader:
    image: python:3.11-slim
    container_name: model-downloader
    environment:
      - HF_TOKEN=${HF_TOKEN}
      - HF_HOME=/workspace/models
    volumes:
      - /workspace/models:/workspace/models
      - ./scripts/download-models.py:/download-models.py
    command: python /download-models.py
    networks:
      - doc-network
    profiles: ["setup"]

networks:
  doc-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16