{"name": "flex-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "build": "next build", "start": "next start", "lint": "tsc -p convex -noEmit --pretty false && tsc -p . -noEmit --pretty false && convex dev --once && next build", "type-check": "tsc --noEmit", "dev:app": "./scripts/dev-runner.sh", "tauri": "tauri", "tauri:dev": "./scripts/build-tauri.sh --dev", "tauri:build": "./scripts/build-tauri.sh", "desktop": "npm run tauri:build", "shortcuts": "./scripts/create-shortcuts.sh", "present": "./scripts/start-slidev.sh"}, "dependencies": {"@convex-dev/auth": "^0.0.80", "@langchain/langgraph": "^0.2.38", "@langchain/core": "^0.3.26", "@langchain/openai": "^0.3.17", "@langchain/anthropic": "^0.3.13", "@tiptap/extension-character-count": "^2.24.2", "@tiptap/extension-color": "^2.24.2", "@tiptap/extension-focus": "^2.24.2", "@tiptap/extension-highlight": "^2.24.2", "@tiptap/extension-history": "^2.24.2", "@tiptap/extension-list-item": "^2.24.2", "@tiptap/extension-placeholder": "^2.24.2", "@tiptap/extension-text-align": "^2.24.2", "@tiptap/extension-text-style": "^2.24.2", "@tiptap/extension-typography": "^2.24.2", "@tiptap/react": "^2.24.2", "@tiptap/starter-kit": "^2.24.2", "@types/pdf-parse": "^1.1.5", "clsx": "^2.1.1", "convex": "^1.24.2", "date-fns": "^4.1.0", "googleapis": "^144.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.525.0", "mammoth": "^1.8.0", "next": "^15.3.5", "openai": "^5.8.2", "pdf-parse": "^1.1.1", "pdf2json": "^3.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-dropzone": "^14.3.8", "react-hotkeys-hook": "^5.1.0", "sharp": "^0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^3.1.0", "tesseract.js": "^5.1.1", "winston": "^3.17.0", "xlsx": "^0.18.5", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/js": "^9.21.0", "@tauri-apps/cli": "^1.6.0", "@types/node": "^22.13.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "autoprefixer": "^10.4.21", "dotenv": "^16.4.7", "eslint": "^9.21.0", "eslint-plugin-react-hooks": "^5.1.0", "globals": "^15.15.0", "npm-run-all": "^4.1.5", "postcss": "^8.5.6", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.24.1"}}