<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="presentGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#presentGrad)" rx="64"/>
  <rect x="100" y="140" width="312" height="200" fill="white" opacity="0.9" rx="16"/>
  <circle cx="256" cy="240" r="40" fill="#2E7D32"/>
  <text x="256" y="255" font-family="Arial, sans-serif" font-size="30" fill="white" text-anchor="middle">▶</text>
  <text x="256" y="400" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">SLIDES</text>
</svg>
