<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="desktopGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6A1B9A;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#desktopGrad)" rx="64"/>
  <rect x="128" y="160" width="256" height="160" fill="white" opacity="0.9" rx="16"/>
  <rect x="220" y="320" width="72" height="40" fill="white" opacity="0.7" rx="8"/>
  <rect x="180" y="360" width="152" height="24" fill="white" opacity="0.5" rx="12"/>
  <text x="256" y="250" font-family="Arial, sans-serif" font-size="48" fill="#6A1B9A" text-anchor="middle" font-weight="bold">🖥️</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">DESKTOP</text>
</svg>
