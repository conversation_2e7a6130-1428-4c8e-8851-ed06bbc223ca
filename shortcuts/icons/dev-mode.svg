<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="devGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1565C0;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#devGrad)" rx="64"/>
  <circle cx="256" cy="200" r="80" fill="white" opacity="0.9"/>
  <text x="256" y="220" font-family="Arial, sans-serif" font-size="60" fill="#1565C0" text-anchor="middle" font-weight="bold">⚡</text>
  <text x="256" y="320" font-family="Arial, sans-serif" font-size="36" fill="white" text-anchor="middle" font-weight="bold">DEV</text>
  <text x="256" y="360" font-family="Arial, sans-serif" font-size="24" fill="#E3F2FD" text-anchor="middle">MODE</text>
</svg>
