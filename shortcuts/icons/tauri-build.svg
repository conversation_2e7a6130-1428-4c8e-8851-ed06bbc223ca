<svg width="512" height="512" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="buildGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E65100;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="512" height="512" fill="url(#buildGrad)" rx="64"/>
  <polygon points="256,120 320,200 320,280 256,360 192,280 192,200" fill="white" opacity="0.9"/>
  <text x="256" y="250" font-family="Arial, sans-serif" font-size="60" fill="#E65100" text-anchor="middle" font-weight="bold">⚙️</text>
  <text x="256" y="420" font-family="Arial, sans-serif" font-size="28" fill="white" text-anchor="middle" font-weight="bold">BUILD</text>
</svg>
