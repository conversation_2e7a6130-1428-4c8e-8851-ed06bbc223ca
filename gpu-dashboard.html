<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTX 4090 GPU Monitor Dashboard</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 1rem 2rem;
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #2d3748;
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .header p {
            color: #718096;
        }
        
        .status-badge {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-left: 1rem;
        }
        
        .status-online {
            background: #c6f6d5;
            color: #22543d;
        }
        
        .status-offline {
            background: #fed7d7;
            color: #742a2a;
        }
        
        .container {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 0 1rem;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 25px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
        }
        
        .card h3 {
            color: #2d3748;
            margin-bottom: 1rem;
            font-size: 1.25rem;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.75rem;
            border-bottom: 1px solid #e2e8f0;
        }
        
        .metric:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .metric-label {
            color: #718096;
            font-weight: 500;
        }
        
        .metric-value {
            font-weight: 700;
            font-size: 1.1rem;
        }
        
        .temp { color: #e53e3e; }
        .util { color: #3182ce; }
        .memory { color: #805ad5; }
        .power { color: #d69e2e; }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e2e8f0;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            transition: width 0.3s ease;
            border-radius: 4px;
        }
        
        .chart-container {
            grid-column: 1 / -1;
            height: 400px;
        }
        
        .console {
            background: #1a202c;
            color: #e2e8f0;
            padding: 1rem;
            border-radius: 0.5rem;
            height: 300px;
            overflow-y: auto;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 0.875rem;
            line-height: 1.4;
        }
        
        .console-line {
            margin-bottom: 0.25rem;
        }
        
        .timestamp {
            color: #718096;
        }
        
        .error { color: #fc8181; }
        .warn { color: #f6ad55; }
        .info { color: #63b3ed; }
        
        .refresh-btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            cursor: pointer;
            font-weight: 600;
            transition: background 0.2s ease;
            margin-bottom: 1rem;
        }
        
        .refresh-btn:hover {
            background: #3182ce;
        }
        
        .loading {
            text-align: center;
            color: #718096;
            padding: 2rem;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 0 0.5rem;
            }
            
            .header {
                padding: 1rem;
            }
            
            .header h1 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎮 RTX 4090 GPU Monitor</h1>
        <p>Real-time monitoring and control dashboard</p>
        <span id="connectionStatus" class="status-badge status-offline">Connecting...</span>
    </div>
    
    <div class="container">
        <button class="refresh-btn" onclick="refreshData()">🔄 Refresh Data</button>
        
        <div class="grid">
            <!-- GPU Status Card -->
            <div class="card">
                <h3>🖥️ GPU Status</h3>
                <div id="gpuStatus" class="loading">Loading GPU status...</div>
            </div>
            
            <!-- Performance Metrics -->
            <div class="card">
                <h3>📊 Performance Metrics</h3>
                <div id="performanceMetrics" class="loading">Loading metrics...</div>
            </div>
            
            <!-- System Info -->
            <div class="card">
                <h3>⚙️ System Information</h3>
                <div id="systemInfo" class="loading">Loading system info...</div>
            </div>
        </div>
        
        <!-- Charts -->
        <div class="card chart-container">
            <h3>📈 GPU Utilization Over Time</h3>
            <canvas id="utilizationChart"></canvas>
        </div>
        
        <!-- Console Output -->
        <div class="card">
            <h3>🖥️ Live Console Output</h3>
            <div id="console" class="console">
                <div class="console-line">
                    <span class="timestamp">[Loading]</span> 
                    <span class="info">Connecting to GPU monitor...</span>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://*************:9092';
        let chart;
        let wsConnection;
        let gpuHistory = [];
        
        // Initialize chart
        function initChart() {
            const ctx = document.getElementById('utilizationChart').getContext('2d');
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'GPU Utilization (%)',
                        data: [],
                        borderColor: '#4299e1',
                        backgroundColor: 'rgba(66, 153, 225, 0.1)',
                        tension: 0.4,
                        fill: true
                    }, {
                        label: 'Temperature (°C)',
                        data: [],
                        borderColor: '#e53e3e',
                        backgroundColor: 'rgba(229, 62, 62, 0.1)',
                        tension: 0.4,
                        fill: false
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    },
                    plugins: {
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }
        
        // Fetch GPU status
        async function fetchGPUStatus() {
            try {
                const response = await fetch(`${API_BASE}/api/gpu/status`);
                const data = await response.json();
                
                updateGPUStatus(data);
                updateChart(data);
                updateConnectionStatus(true);
                
                return data;
            } catch (error) {
                console.error('Failed to fetch GPU status:', error);
                updateConnectionStatus(false);
                return null;
            }
        }
        
        // Update GPU status display
        function updateGPUStatus(data) {
            const statusEl = document.getElementById('gpuStatus');
            const metricsEl = document.getElementById('performanceMetrics');
            const systemEl = document.getElementById('systemInfo');
            
            statusEl.innerHTML = `
                <div class="metric">
                    <span class="metric-label">GPU Name</span>
                    <span class="metric-value">${data.name}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Driver Version</span>
                    <span class="metric-value">${data.driver_version}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">CUDA Version</span>
                    <span class="metric-value">${data.cuda_version}</span>
                </div>
            `;
            
            metricsEl.innerHTML = `
                <div class="metric">
                    <span class="metric-label">🌡️ Temperature</span>
                    <span class="metric-value temp">${data.temperature}°C</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(data.temperature / 90) * 100}%; background: #e53e3e;"></div>
                </div>
                
                <div class="metric">
                    <span class="metric-label">⚡ Utilization</span>
                    <span class="metric-value util">${data.utilization}%</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${data.utilization}%; background: #3182ce;"></div>
                </div>
                
                <div class="metric">
                    <span class="metric-label">💾 Memory</span>
                    <span class="metric-value memory">${(data.memory_used / 1024).toFixed(1)}GB / ${(data.memory_total / 1024).toFixed(1)}GB</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(data.memory_used / data.memory_total) * 100}%; background: #805ad5;"></div>
                </div>
                
                <div class="metric">
                    <span class="metric-label">🔋 Power</span>
                    <span class="metric-value power">${data.power_draw}W / ${data.power_limit}W</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${(data.power_draw / data.power_limit) * 100}%; background: #d69e2e;"></div>
                </div>
            `;
            
            systemEl.innerHTML = `
                <div class="metric">
                    <span class="metric-label">Compute Mode</span>
                    <span class="metric-value">${data.compute_mode}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Active Processes</span>
                    <span class="metric-value">${data.processes.length}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Last Update</span>
                    <span class="metric-value">${new Date(data.timestamp).toLocaleTimeString()}</span>
                </div>
            `;
        }
        
        // Update chart with new data
        function updateChart(data) {
            const now = new Date().toLocaleTimeString();
            
            gpuHistory.push({
                time: now,
                utilization: data.utilization,
                temperature: data.temperature
            });
            
            // Keep only last 20 points
            if (gpuHistory.length > 20) {
                gpuHistory.shift();
            }
            
            chart.data.labels = gpuHistory.map(h => h.time);
            chart.data.datasets[0].data = gpuHistory.map(h => h.utilization);
            chart.data.datasets[1].data = gpuHistory.map(h => h.temperature);
            chart.update();
        }
        
        // Update connection status
        function updateConnectionStatus(connected) {
            const statusEl = document.getElementById('connectionStatus');
            if (connected) {
                statusEl.textContent = 'Connected';
                statusEl.className = 'status-badge status-online';
            } else {
                statusEl.textContent = 'Offline';
                statusEl.className = 'status-badge status-offline';
            }
        }
        
        // Add console log
        function addConsoleLog(level, message) {
            const consoleEl = document.getElementById('console');
            const timestamp = new Date().toLocaleTimeString();
            const logLine = document.createElement('div');
            logLine.className = 'console-line';
            logLine.innerHTML = `<span class="timestamp">[${timestamp}]</span> <span class="${level}">${message}</span>`;
            
            consoleEl.appendChild(logLine);
            consoleEl.scrollTop = consoleEl.scrollHeight;
            
            // Keep only last 50 logs
            while (consoleEl.children.length > 50) {
                consoleEl.removeChild(consoleEl.firstChild);
            }
        }
        
        // Initialize WebSocket connection
        function initWebSocket() {
            try {
                wsConnection = new WebSocket('ws://*************:9092/ws/gpu-monitor');
                
                wsConnection.onopen = () => {
                    addConsoleLog('info', 'WebSocket connected to GPU monitor');
                    updateConnectionStatus(true);
                };
                
                wsConnection.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    
                    if (data.type === 'gpu_status') {
                        updateGPUStatus(data.payload);
                        updateChart(data.payload);
                    } else if (data.type === 'console_log') {
                        addConsoleLog(data.payload.level, `${data.payload.service}: ${data.payload.message}`);
                    }
                };
                
                wsConnection.onclose = () => {
                    addConsoleLog('warn', 'WebSocket connection closed');
                    updateConnectionStatus(false);
                    
                    // Retry connection after 3 seconds
                    setTimeout(initWebSocket, 3000);
                };
                
                wsConnection.onerror = (error) => {
                    addConsoleLog('error', 'WebSocket error: ' + error);
                };
                
            } catch (error) {
                addConsoleLog('error', 'Failed to connect WebSocket: ' + error);
                setTimeout(initWebSocket, 5000);
            }
        }
        
        // Refresh data manually
        async function refreshData() {
            addConsoleLog('info', 'Refreshing GPU data...');
            const data = await fetchGPUStatus();
            if (data) {
                addConsoleLog('info', 'GPU data refreshed successfully');
            } else {
                addConsoleLog('error', 'Failed to refresh GPU data');
            }
        }
        
        // Initialize dashboard
        async function init() {
            addConsoleLog('info', 'Initializing GPU dashboard...');
            
            // Initialize chart
            initChart();
            
            // Fetch initial data
            await fetchGPUStatus();
            
            // Start WebSocket connection
            initWebSocket();
            
            // Set up periodic refresh as fallback
            setInterval(fetchGPUStatus, 5000);
            
            addConsoleLog('info', 'GPU dashboard initialized');
        }
        
        // Start when page loads
        window.addEventListener('load', init);
    </script>
</body>
</html>