"""Surya OCR Service for document processing"""

import sys
import os
sys.path.append('/app')

from typing import Dict, Any, List
import numpy as np
from PIL import Image
import structlog
from surya.ocr import run_ocr
from surya.detection import batch_text_detection
from surya.layout import batch_layout_detection
from surya.model.detection import segformer
from surya.model.recognition.model import load_model as load_rec_model
from surya.model.recognition.processor import load_processor
from services.base.model_service import BaseModelService
import torch

logger = structlog.get_logger()

class SuryaService(BaseModelService):
    """Surya OCR service for document processing"""
    
    def __init__(self):
        super().__init__("Surya OCR", "1.0.0")
        self.det_model = None
        self.det_processor = None
        self.rec_model = None
        self.rec_processor = None
        self.batch_size = int(os.getenv("SURYA_BATCH_SIZE", "4"))
    
    async def load_model(self):
        """Load Surya models"""
        logger.info("Loading Surya models...")
        
        # Load detection model
        self.det_model = segformer.load_model()
        self.det_processor = segformer.load_processor()
        
        # Load recognition model
        self.rec_model = load_rec_model()
        self.rec_processor = load_processor()
        
        # Move models to device
        if self.device.type == "cuda":
            self.det_model = self.det_model.to(self.device)
            self.rec_model = self.rec_model.to(self.device)
        
        logger.info("Surya models loaded successfully")
    
    async def process_document(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Process document with Surya OCR"""
        
        # Load image
        image = Image.open(file_path)
        if image.mode != "RGB":
            image = image.convert("RGB")
        
        # Get processing options
        detect_layout = options.get("detect_layout", True)
        languages = options.get("languages", ["en"])
        
        results = {
            "text": "",
            "lines": [],
            "layout": {},
            "confidence": 0.0,
            "metadata": {
                "width": image.width,
                "height": image.height,
                "format": image.format
            }
        }
        
        # Run text detection
        logger.info("Running text detection...")
        predictions = batch_text_detection(
            [image], 
            self.det_model, 
            self.det_processor,
            batch_size=self.batch_size
        )
        
        # Run OCR
        logger.info("Running OCR...")
        ocr_results = run_ocr(
            [image],
            [languages],
            self.det_model,
            self.det_processor,
            self.rec_model,
            self.rec_processor,
            batch_size=self.batch_size
        )
        
        # Process OCR results
        if ocr_results and len(ocr_results) > 0:
            ocr_result = ocr_results[0]
            
            # Extract text and lines
            all_text = []
            for text_line in ocr_result.text_lines:
                line_data = {
                    "text": text_line.text,
                    "confidence": text_line.confidence,
                    "bbox": text_line.bbox
                }
                results["lines"].append(line_data)
                all_text.append(text_line.text)
            
            results["text"] = "\n".join(all_text)
            
            # Calculate average confidence
            if results["lines"]:
                confidences = [line["confidence"] for line in results["lines"]]
                results["confidence"] = np.mean(confidences)
        
        # Run layout detection if requested
        if detect_layout:
            logger.info("Running layout detection...")
            layout_predictions = batch_layout_detection(
                [image],
                self.det_model,
                self.det_processor,
                batch_size=self.batch_size
            )
            
            if layout_predictions and len(layout_predictions) > 0:
                layout_result = layout_predictions[0]
                
                # Extract layout elements
                layout_data = {
                    "elements": []
                }
                
                for element in layout_result.bboxes:
                    layout_data["elements"].append({
                        "type": element.label,
                        "bbox": element.bbox,
                        "confidence": element.confidence
                    })
                
                results["layout"] = layout_data
        
        # Extract tables if detected
        tables = self._extract_tables(results)
        if tables:
            results["tables"] = tables
        
        return results
    
    def _extract_tables(self, ocr_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Extract tables from layout detection results"""
        tables = []
        
        layout = ocr_results.get("layout", {})
        elements = layout.get("elements", [])
        
        # Find table elements
        table_elements = [e for e in elements if e["type"] == "table"]
        
        for table_elem in table_elements:
            # Get lines within table bbox
            table_bbox = table_elem["bbox"]
            table_lines = []
            
            for line in ocr_results.get("lines", []):
                line_bbox = line["bbox"]
                # Check if line is within table
                if (line_bbox[0] >= table_bbox[0] and 
                    line_bbox[1] >= table_bbox[1] and
                    line_bbox[2] <= table_bbox[2] and
                    line_bbox[3] <= table_bbox[3]):
                    table_lines.append(line)
            
            if table_lines:
                tables.append({
                    "bbox": table_bbox,
                    "lines": table_lines,
                    "confidence": table_elem["confidence"]
                })
        
        return tables
    
    def get_capabilities(self) -> List[str]:
        """Get service capabilities"""
        return [
            "ocr",
            "text_detection",
            "layout_detection",
            "table_extraction",
            "multi_language",
            "batch_processing"
        ]
    
    def _process_batch(self, batch: List[Any]) -> List[Any]:
        """Process a batch of images"""
        # This is implemented in the process_document method using Surya's batch functions
        # This method is here to satisfy the abstract base class requirement
        return []

# Create and run the service
if __name__ == "__main__":
    import uvicorn
    
    service = SuryaService()
    app = service.get_app()
    
    uvicorn.run(app, host="0.0.0.0", port=8000)