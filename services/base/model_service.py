"""Base class for model services"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
import torch
from fastapi import FastAP<PERSON>, UploadFile, File, HTTPException
from pydantic import BaseModel
import structlog
import os
import tempfile
from contextlib import asynccontextmanager
import time
import psutil
import GPUtil

logger = structlog.get_logger()

class ProcessingRequest(BaseModel):
    options: Optional[Dict[str, Any]] = {}

class ProcessingResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    error: Optional[str] = None
    processing_time: float
    gpu_memory_used: Optional[float] = None

class BaseModelService(ABC):
    """Base class for all model services"""
    
    def __init__(self, model_name: str, model_version: str = "1.0.0"):
        self.model_name = model_name
        self.model_version = model_version
        self.model = None
        self.device = None
        self.app = FastAPI(
            title=f"{model_name} Service",
            version=model_version
        )
        self._setup_routes()
        
    @abstractmethod
    async def load_model(self):
        """Load the model - must be implemented by subclasses"""
        pass
    
    @abstractmethod
    async def process_document(self, file_path: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """Process a document - must be implemented by subclasses"""
        pass
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.on_event("startup")
        async def startup():
            await self.initialize()
        
        @self.app.on_event("shutdown")
        async def shutdown():
            await self.cleanup()
        
        @self.app.get("/health")
        async def health():
            return await self.health_check()
        
        @self.app.post("/process", response_model=ProcessingResponse)
        async def process(
            file: UploadFile = File(...),
            options: str = "{}"
        ):
            return await self.process_file(file, options)
        
        @self.app.get("/info")
        async def info():
            return await self.get_info()
    
    async def initialize(self):
        """Initialize the service"""
        logger.info(f"Initializing {self.model_name} service")
        
        # Setup device
        if torch.cuda.is_available():
            self.device = torch.device("cuda")
            logger.info(f"Using GPU: {torch.cuda.get_device_name(0)}")
            logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.2f} GB")
        else:
            self.device = torch.device("cpu")
            logger.warning("GPU not available, using CPU")
        
        # Load model
        start_time = time.time()
        await self.load_model()
        load_time = time.time() - start_time
        
        logger.info(f"{self.model_name} initialized in {load_time:.2f}s")
    
    async def cleanup(self):
        """Cleanup resources"""
        logger.info(f"Cleaning up {self.model_name} service")
        
        if self.model is not None:
            del self.model
            
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
    
    async def health_check(self) -> Dict[str, Any]:
        """Health check endpoint"""
        health_status = {
            "status": "healthy",
            "model": self.model_name,
            "version": self.model_version,
            "device": str(self.device),
            "model_loaded": self.model is not None
        }
        
        # Add GPU stats if available
        if torch.cuda.is_available():
            try:
                gpu = GPUtil.getGPUs()[0]
                health_status.update({
                    "gpu_name": gpu.name,
                    "gpu_memory_used": f"{gpu.memoryUsed}MB",
                    "gpu_memory_total": f"{gpu.memoryTotal}MB",
                    "gpu_utilization": f"{gpu.load * 100:.1f}%"
                })
            except:
                pass
        
        # Add CPU and memory stats
        health_status.update({
            "cpu_percent": psutil.cpu_percent(),
            "memory_percent": psutil.virtual_memory().percent
        })
        
        return health_status
    
    async def process_file(self, file: UploadFile, options: str) -> ProcessingResponse:
        """Process an uploaded file"""
        start_time = time.time()
        gpu_memory_before = None
        
        try:
            # Parse options
            try:
                options_dict = json.loads(options)
            except:
                options_dict = {}
            
            # Save uploaded file
            with tempfile.NamedTemporaryFile(delete=False, suffix=file.filename) as tmp_file:
                content = await file.read()
                tmp_file.write(content)
                tmp_file_path = tmp_file.name
            
            # Get GPU memory before processing
            if torch.cuda.is_available():
                gpu_memory_before = torch.cuda.memory_allocated() / 1e9
            
            # Process document
            result = await self.process_document(tmp_file_path, options_dict)
            
            # Calculate GPU memory used
            gpu_memory_used = None
            if torch.cuda.is_available() and gpu_memory_before is not None:
                gpu_memory_after = torch.cuda.memory_allocated() / 1e9
                gpu_memory_used = gpu_memory_after - gpu_memory_before
            
            processing_time = time.time() - start_time
            
            logger.info(
                f"Processed document",
                filename=file.filename,
                processing_time=f"{processing_time:.2f}s",
                gpu_memory_used=f"{gpu_memory_used:.2f}GB" if gpu_memory_used else "N/A"
            )
            
            return ProcessingResponse(
                success=True,
                data=result,
                processing_time=processing_time,
                gpu_memory_used=gpu_memory_used
            )
            
        except Exception as e:
            logger.error(f"Processing failed", error=str(e), filename=file.filename)
            return ProcessingResponse(
                success=False,
                data={},
                error=str(e),
                processing_time=time.time() - start_time
            )
        
        finally:
            # Cleanup temporary file
            if 'tmp_file_path' in locals():
                try:
                    os.unlink(tmp_file_path)
                except:
                    pass
    
    async def get_info(self) -> Dict[str, Any]:
        """Get service information"""
        info = {
            "model": self.model_name,
            "version": self.model_version,
            "device": str(self.device),
            "capabilities": self.get_capabilities()
        }
        
        # Add model-specific info
        if hasattr(self, 'get_model_info'):
            info.update(self.get_model_info())
        
        return info
    
    def get_capabilities(self) -> List[str]:
        """Get model capabilities - can be overridden by subclasses"""
        return ["document_processing"]
    
    def batch_process(self, items: List[Any], batch_size: int = 32) -> List[Any]:
        """Process items in batches - useful for GPU efficiency"""
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            # Process batch on GPU
            with torch.cuda.amp.autocast(enabled=self.device.type == "cuda"):
                batch_results = self._process_batch(batch)
            
            results.extend(batch_results)
            
            # Clear GPU cache periodically
            if self.device.type == "cuda" and i % (batch_size * 10) == 0:
                torch.cuda.empty_cache()
        
        return results
    
    @abstractmethod
    def _process_batch(self, batch: List[Any]) -> List[Any]:
        """Process a batch of items - must be implemented if using batch_process"""
        pass
    
    def get_app(self) -> FastAPI:
        """Get the FastAPI app instance"""
        return self.app

# Import json at the top (was missing)
import json