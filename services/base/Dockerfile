FROM nvidia/cuda:12.1.0-runtime-ubuntu22.04

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    CUDA_VISIBLE_DEVICES=0

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3.11 \
    python3.11-pip \
    python3.11-dev \
    git \
    wget \
    curl \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgfortran5 \
    && rm -rf /var/lib/apt/lists/*

# Create symlinks for Python
RUN ln -s /usr/bin/python3.11 /usr/bin/python && \
    ln -s /usr/bin/pip3.11 /usr/bin/pip

# Upgrade pip
RUN pip install --upgrade pip setuptools wheel

# Install PyTorch with CUDA support
RUN pip install torch==2.3.0 torchvision==0.18.0 torchaudio==2.3.0 --index-url https://download.pytorch.org/whl/cu121

# Install common ML dependencies
RUN pip install \
    transformers==4.44.0 \
    accelerate==0.34.0 \
    bitsandbytes==0.44.0 \
    scipy==1.14.1 \
    numpy==1.26.4 \
    pandas==2.2.3 \
    Pillow==10.4.0 \
    opencv-python==********* \
    fastapi==0.115.5 \
    uvicorn[standard]==0.32.1 \
    python-multipart==0.0.12 \
    pydantic==2.9.2 \
    tenacity==9.0.0 \
    structlog==24.4.0

# Create app directory
WORKDIR /app

# Create model cache directory
RUN mkdir -p /models

# Default healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Default command (override in child images)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]