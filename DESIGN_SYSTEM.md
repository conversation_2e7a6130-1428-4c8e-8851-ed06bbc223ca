# Bid Writing Studio - Dark Theme Design System

## Overview

This document outlines the comprehensive dark theme design system for the Bid Writing Studio platform. The system is built using Tailwind CSS with custom design tokens and provides a professional, accessible, and consistent user experience.

## Color Palette

### Primary Colors (Blue)
- **Primary 50**: `#eff6ff` - Light tint for backgrounds
- **Primary 600**: `#2563eb` - **DEFAULT** - Main brand color
- **Primary 700**: `#1d4ed8` - Hover states and emphasis
- **Primary 900**: `#1e3a8a` - Deep accent color

### Accent Colors (Green)
- **Accent 50**: `#ecfdf5` - Light tint for success states
- **Accent 600**: `#059669` - **DEFAULT** - Secondary brand color
- **Accent 700**: `#047857` - Hover states for accent elements
- **Accent 900**: `#064e3b` - Deep accent color

### Secondary Colors (Teal)
- **Secondary 500**: `#14b8a6` - Supporting color
- **Secondary 600**: `#0d9488` - **DEFAULT** - Supporting brand color
- **Secondary 700**: `#0f766e` - Hover states

### Background Colors
- **Background**: `#0a0a0a` - Primary background
- **Background Secondary**: `#111111` - Card backgrounds
- **Background Tertiary**: `#1a1a1a` - Elevated surfaces
- **Background Elevated**: `#222222` - Modal and drawer backgrounds

### Surface Colors
- **Surface**: `#1e1e1e` - Component backgrounds
- **Surface Secondary**: `#2a2a2a` - Interactive elements
- **Surface Tertiary**: `#333333` - Borders and dividers
- **Surface Hover**: `#3a3a3a` - Hover states
- **Surface Active**: `#444444` - Active states

### Text Colors
- **Text Primary**: `#ffffff` - Headings and primary text
- **Text Secondary**: `#e5e5e5` - Body text and descriptions
- **Text Tertiary**: `#b3b3b3` - Placeholder text and labels
- **Text Quaternary**: `#808080` - Muted text and timestamps
- **Text Disabled**: `#525252` - Disabled state text

### Semantic Colors
- **Error**: `#dc2626` - Error states and destructive actions
- **Success**: `#059669` - Success states and confirmations
- **Warning**: `#d97706` - Warning states and cautions
- **Info**: `#0ea5e9` - Information and neutral actions

## Typography

### Font Families
- **Sans**: Inter (primary) - Clean, readable sans-serif for UI text
- **Mono**: Geist Mono (secondary) - Technical text, code, and data display

### Font Sizes
- **xs**: 12px - Small labels and captions
- **sm**: 14px - Secondary text and form labels
- **base**: 16px - Body text and paragraphs
- **lg**: 18px - Emphasized text and small headings
- **xl**: 20px - Section headings
- **2xl**: 24px - Page headings
- **3xl**: 30px - Major headings
- **4xl**: 36px - Display headings

### Line Heights
- **Tight**: 1.25 - Headings and compact text
- **Normal**: 1.5 - Body text and paragraphs
- **Loose**: 1.75 - Expanded text blocks

## Spacing Scale

- **xs**: 4px - Tight spacing
- **sm**: 8px - Close spacing
- **md**: 12px - Default spacing
- **lg**: 16px - Comfortable spacing
- **xl**: 24px - Generous spacing
- **2xl**: 32px - Section spacing
- **3xl**: 48px - Large gaps
- **4xl**: 64px - Major sections
- **5xl**: 96px - Layout spacing
- **6xl**: 128px - Maximum spacing

## Component Utilities

### Buttons
```html
<!-- Primary Button -->
<button class="btn-primary">Submit Bid</button>

<!-- Secondary Button -->
<button class="btn-secondary">Save Draft</button>

<!-- Accent Button -->
<button class="btn-accent">Generate Content</button>

<!-- Outline Button -->
<button class="btn-outline">Cancel</button>

<!-- Ghost Button -->
<button class="btn-ghost">More Options</button>

<!-- Destructive Button -->
<button class="btn-destructive">Delete</button>
```

### Form Controls
```html
<!-- Input Field -->
<input type="text" class="input-field" placeholder="Enter text">

<!-- Error State -->
<input type="email" class="input-field input-field-error" placeholder="Email">

<!-- Success State -->
<input type="password" class="input-field input-field-success" placeholder="Password">
```

### Cards
```html
<!-- Basic Card -->
<div class="card">
  <h3>Card Title</h3>
  <p>Card content...</p>
</div>

<!-- Elevated Card -->
<div class="card-elevated">
  <h3>Important Content</h3>
  <p>Enhanced visibility...</p>
</div>

<!-- Interactive Card -->
<div class="card-interactive">
  <h3>Clickable Card</h3>
  <p>Hover for interaction...</p>
</div>
```

### Status Badges
```html
<!-- Primary Badge -->
<span class="badge badge-primary">In Progress</span>

<!-- Success Badge -->
<span class="badge badge-success">Completed</span>

<!-- Warning Badge -->
<span class="badge badge-warning">Review Required</span>

<!-- Error Badge -->
<span class="badge badge-error">Failed</span>
```

## Accessibility Features

### Color Contrast Ratios
All color combinations meet WCAG 2.1 AA standards:

- **Primary text on dark backgrounds**: 21:1 contrast ratio
- **Secondary text on dark backgrounds**: 12.63:1 contrast ratio
- **Primary buttons**: 4.5:1 minimum contrast ratio
- **Interactive elements**: 3:1 minimum contrast ratio for non-text elements

### Focus Management
- **Focus Ring**: Consistent 2px focus outline with primary color
- **Focus Offset**: 2px offset for visual separation
- **Keyboard Navigation**: All interactive elements support keyboard access

### Motion Preferences
- **Reduced Motion**: Respects user's motion preferences
- **Smooth Transitions**: 300ms default with easing functions
- **Loading States**: Clear visual feedback for async operations

## Utility Classes

### Gradients
```html
<!-- Text Gradient -->
<h1 class="text-gradient">Gradient Text</h1>

<!-- Background Gradients -->
<div class="bg-gradient-primary">Primary Gradient</div>
<div class="bg-gradient-accent">Accent Gradient</div>
```

### Glass Effects
```html
<!-- Glass Effect -->
<div class="glass-effect">Frosted glass appearance</div>

<!-- Dark Glass Effect -->
<div class="glass-effect-dark">Dark frosted glass</div>
```

### Shadows and Glows
```html
<!-- Glow Effects -->
<div class="shadow-glow">Primary glow</div>
<div class="shadow-glow-accent">Accent glow</div>
```

### Transitions
```html
<!-- Smooth Transitions -->
<div class="transition-smooth">Smooth animation</div>
<div class="transition-fast">Fast animation</div>
<div class="transition-slow">Slow animation</div>
```

## Animations

### Available Animations
- **fade-in**: Gentle entrance animation
- **slide-in**: Horizontal entrance animation
- **slide-up**: Vertical entrance animation
- **scale-in**: Scaling entrance animation
- **shimmer**: Loading state animation

### Usage
```html
<div class="animate-fade-in">Fading in content</div>
<div class="animate-slide-up">Sliding up content</div>
<div class="animate-shimmer">Loading shimmer</div>
```

## Loading States

### Spinner
```html
<div class="loading-spinner" aria-label="Loading..."></div>
```

### Pulse Effect
```html
<div class="loading-pulse h-4 w-32"></div>
```

## Layout Patterns

### Container Spacing
```html
<div class="p-section">Section padding</div>
<div class="m-form-field">Form field margin</div>
```

### Z-Index Scale
- **dropdown**: 1000
- **sticky**: 1020
- **fixed**: 1030
- **modal-backdrop**: 1040
- **modal**: 1050
- **popover**: 1060
- **tooltip**: 1070
- **toast**: 1080

## Best Practices

### Color Usage
1. Use primary colors for main actions and branding
2. Use accent colors for secondary actions and success states
3. Use semantic colors consistently (error, warning, success, info)
4. Maintain contrast ratios for accessibility

### Typography
1. Use font hierarchy consistently
2. Limit font sizes to the defined scale
3. Use mono font for technical content and data
4. Ensure readable line heights

### Spacing
1. Use consistent spacing scale
2. Prefer larger spacing for better readability
3. Group related elements with appropriate spacing
4. Use section spacing for major layout divisions

### Components
1. Use component utilities for consistency
2. Combine base classes with modifiers
3. Follow the established naming conventions
4. Test interactive states (hover, focus, active)

### Performance
1. Leverage CSS custom properties for theming
2. Use backdrop-blur sparingly for performance
3. Prefer CSS animations over JavaScript
4. Optimize for both light and dark themes

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Implementation Notes

### CSS Variables
All design tokens are available as CSS custom properties in the `:root` scope, allowing for dynamic theming and easy customization.

### Tailwind Integration
The design system is fully integrated with Tailwind CSS, providing both utility classes and component classes for maximum flexibility.

### Legacy Compatibility
Legacy class names (`auth-input-field`, `auth-button`) are maintained for backward compatibility while new components should use the updated class names.

---

*This design system ensures a consistent, accessible, and professional appearance across the Bid Writing Studio platform while maintaining flexibility for future enhancements.*