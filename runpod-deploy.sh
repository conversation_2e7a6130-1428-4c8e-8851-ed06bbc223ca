#!/bin/bash

# RunPod Deployment Script for Document Processing Pipeline

set -e

echo "🚀 RunPod Document Processing Pipeline Deployment"
echo "================================================"

# Check if RunPod CLI is installed
if ! command -v runpodctl &> /dev/null; then
    echo "❌ RunPod CLI not found. Installing..."
    curl -L https://github.com/runpod/runpodctl/releases/latest/download/runpodctl-linux-amd64 -o runpodctl
    chmod +x runpodctl
    sudo mv runpodctl /usr/local/bin/
fi

# Configuration
RUNPOD_API_KEY=${RUNPOD_API_KEY:-""}
PROJECT_NAME="doc-processor"
GPU_TYPE=${GPU_TYPE:-"RTX_4090"}  # RTX_4090, RTX_A5000, A100_40GB, A100_80GB
GPU_COUNT=${GPU_COUNT:-1}
DISK_SIZE=${DISK_SIZE:-100}  # GB
REGION=${REGION:-"US"}  # US, EU

# Validate API key
if [ -z "$RUNPOD_API_KEY" ]; then
    echo "❌ Error: RUNPOD_API_KEY environment variable not set"
    echo "Please set: export RUNPOD_API_KEY='your-api-key'"
    exit 1
fi

echo "📋 Configuration:"
echo "  GPU Type: $GPU_TYPE"
echo "  GPU Count: $GPU_COUNT"
echo "  Disk Size: ${DISK_SIZE}GB"
echo "  Region: $REGION"
echo ""

# Create RunPod template
echo "📝 Creating RunPod template..."
cat > runpod-template.json <<EOF
{
  "name": "${PROJECT_NAME}-template",
  "imageName": "nvidia/cuda:12.1.0-runtime-ubuntu22.04",
  "dockerArgs": "",
  "ports": "8000/http,8001/http,8002/http,8003/http,9090/http,3001/http",
  "volumeInGb": $DISK_SIZE,
  "volumeMountPath": "/workspace",
  "env": [
    {
      "key": "CUDA_VISIBLE_DEVICES",
      "value": "0"
    },
    {
      "key": "MODEL_CACHE",
      "value": "/workspace/models"
    },
    {
      "key": "ENABLE_FP16",
      "value": "true"
    }
  ],
  "startupScript": "#!/bin/bash\n\
apt-get update && apt-get install -y git curl docker.io docker-compose\n\
cd /workspace\n\
git clone https://github.com/your-repo/doc-processor.git\n\
cd doc-processor\n\
docker-compose up -d"
}
EOF

# Create pod configuration
echo "🔧 Creating pod configuration..."
cat > runpod-pod.json <<EOF
{
  "cloudType": "SECURE",
  "gpuType": "$GPU_TYPE",
  "gpuCount": $GPU_COUNT,
  "volumeInGb": $DISK_SIZE,
  "containerDiskInGb": 20,
  "minMemoryInGb": 32,
  "minVcpuCount": 8,
  "name": "${PROJECT_NAME}-pod",
  "templateId": "your-template-id",
  "networkVolumeId": null,
  "startJupyter": false,
  "startSSH": true,
  "env": {
    "RUNPOD_API_KEY": "$RUNPOD_API_KEY"
  }
}
EOF

echo "✅ Configuration files created!"
echo ""
echo "📤 Next steps:"
echo "1. Upload your Docker images to a registry (Docker Hub, GitHub Container Registry, etc.)"
echo "2. Update the image names in docker-compose.yml"
echo "3. Create RunPod template: runpodctl template create -f runpod-template.json"
echo "4. Create RunPod pod: runpodctl pod create -f runpod-pod.json"
echo ""
echo "Or use the RunPod web interface for easier deployment!"