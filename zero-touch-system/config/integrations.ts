import { z } from "zod";

// Base integration config schema
export const BaseIntegrationConfigSchema = z.object({
  name: z.string(),
  type: z.string(),
  enabled: z.boolean().default(true),
  credentials: z.object({
    encrypted: z.boolean().default(true),
    keyId: z.string(),
  }),
  rateLimit: z.object({
    requests: z.number(),
    period: z.enum(["second", "minute", "hour", "day"]),
  }).optional(),
  retryPolicy: z.object({
    maxRetries: z.number().default(3),
    backoffType: z.enum(["exponential", "linear", "constant"]).default("exponential"),
    initialDelay: z.number().default(1000),
  }),
  webhooks: z.array(z.object({
    url: z.string().url(),
    events: z.array(z.string()),
    secret: z.string(),
    isActive: z.boolean().default(true),
  })).default([]),
});

// Gmail Integration Config
export const GmailConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("gmail"),
  config: z.object({
    clientId: z.string(),
    clientSecret: z.string(),
    redirectUri: z.string().url(),
    scopes: z.array(z.string()).default([
      "https://www.googleapis.com/auth/gmail.readonly",
      "https://www.googleapis.com/auth/gmail.send",
      "https://www.googleapis.com/auth/gmail.modify",
    ]),
    watchLabels: z.array(z.string()).default(["INBOX", "UNREAD"]),
    pollInterval: z.number().default(60000), // 1 minute
    maxResults: z.number().default(100),
    filters: z.object({
      from: z.array(z.string()).optional(),
      to: z.array(z.string()).optional(),
      subject: z.array(z.string()).optional(),
      hasAttachment: z.boolean().optional(),
    }).optional(),
  }),
});

// Google Calendar Integration Config
export const CalendarConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("calendar"),
  config: z.object({
    clientId: z.string(),
    clientSecret: z.string(),
    redirectUri: z.string().url(),
    scopes: z.array(z.string()).default([
      "https://www.googleapis.com/auth/calendar.readonly",
      "https://www.googleapis.com/auth/calendar.events",
    ]),
    calendars: z.array(z.object({
      id: z.string(),
      name: z.string(),
      color: z.string().optional(),
      notifications: z.boolean().default(true),
    })),
    syncInterval: z.number().default(300000), // 5 minutes
    lookAheadDays: z.number().default(30),
    reminderMinutes: z.array(z.number()).default([15, 60, 1440]), // 15 min, 1 hour, 1 day
    autoCreateEvents: z.boolean().default(true),
    eventDefaults: z.object({
      duration: z.number().default(3600000), // 1 hour
      color: z.string().default("blue"),
      reminders: z.boolean().default(true),
    }),
  }),
});

// Twilio Integration Config
export const TwilioConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("twilio"),
  config: z.object({
    accountSid: z.string(),
    authToken: z.string(),
    fromNumbers: z.array(z.object({
      number: z.string(),
      name: z.string(),
      capabilities: z.array(z.enum(["sms", "voice", "whatsapp"])),
      isDefault: z.boolean().default(false),
    })),
    webhookUrl: z.string().url().optional(),
    statusCallbackUrl: z.string().url().optional(),
    smsDefaults: z.object({
      maxSegments: z.number().default(3),
      validityPeriod: z.number().default(14400), // 4 hours
      attemptRetry: z.boolean().default(true),
    }),
    voiceDefaults: z.object({
      voice: z.enum(["man", "woman", "alice", "polly.amy"]).default("woman"),
      language: z.string().default("en-US"),
      timeout: z.number().default(30),
    }),
  }),
});

// Slack Integration Config
export const SlackConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("slack"),
  config: z.object({
    botToken: z.string(),
    appToken: z.string(),
    signingSecret: z.string(),
    clientId: z.string(),
    clientSecret: z.string(),
    redirectUri: z.string().url(),
    scopes: z.array(z.string()).default([
      "chat:write",
      "chat:write.public",
      "channels:read",
      "groups:read",
      "im:read",
      "users:read",
      "files:write",
    ]),
    defaultChannel: z.string().optional(),
    botUserId: z.string().optional(),
    socketMode: z.boolean().default(true),
    eventSubscriptions: z.array(z.string()).default([
      "message.channels",
      "message.groups",
      "message.im",
      "app_mention",
    ]),
    interactiveComponents: z.boolean().default(true),
    slashCommands: z.array(z.object({
      command: z.string(),
      description: z.string(),
      usageHint: z.string().optional(),
    })).default([]),
  }),
});

// Microsoft Teams Integration Config
export const TeamsConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("teams"),
  config: z.object({
    appId: z.string(),
    appPassword: z.string(),
    tenantId: z.string(),
    botEndpoint: z.string().url(),
    notificationUrl: z.string().url(),
    scopes: z.array(z.string()).default([
      "https://graph.microsoft.com/User.Read",
      "https://graph.microsoft.com/ChannelMessage.Send",
      "https://graph.microsoft.com/Chat.ReadWrite",
    ]),
    defaultTeams: z.array(z.object({
      id: z.string(),
      name: z.string(),
      channels: z.array(z.object({
        id: z.string(),
        name: z.string(),
      })),
    })).default([]),
    adaptiveCards: z.boolean().default(true),
    proactiveMessaging: z.boolean().default(true),
    conversationReferences: z.record(z.any()).default({}),
  }),
});

// GitHub Integration Config
export const GitHubConfigSchema = BaseIntegrationConfigSchema.extend({
  type: z.literal("github"),
  config: z.object({
    appId: z.number(),
    privateKey: z.string(),
    clientId: z.string(),
    clientSecret: z.string(),
    webhookSecret: z.string(),
    installationId: z.number().optional(),
    repositories: z.array(z.object({
      owner: z.string(),
      repo: z.string(),
      permissions: z.array(z.string()),
    })).default([]),
    eventTypes: z.array(z.string()).default([
      "push",
      "pull_request",
      "issues",
      "issue_comment",
      "pull_request_review",
    ]),
    autoCreateIssues: z.boolean().default(false),
    autoCreatePRs: z.boolean().default(false),
    branchProtection: z.object({
      enabled: z.boolean().default(true),
      requiredReviews: z.number().default(1),
      dismissStaleReviews: z.boolean().default(true),
    }),
  }),
});

// Combined integration config type
export const IntegrationConfigSchema = z.discriminatedUnion("type", [
  GmailConfigSchema,
  CalendarConfigSchema,
  TwilioConfigSchema,
  SlackConfigSchema,
  TeamsConfigSchema,
  GitHubConfigSchema,
]);

export type IntegrationConfig = z.infer<typeof IntegrationConfigSchema>;

// Integration factory
export class IntegrationConfigFactory {
  static createDefault(type: string): IntegrationConfig {
    switch (type) {
      case "gmail":
        return {
          name: "Gmail Integration",
          type: "gmail",
          enabled: true,
          credentials: {
            encrypted: true,
            keyId: "default",
          },
          retryPolicy: {
            maxRetries: 3,
            backoffType: "exponential",
            initialDelay: 1000,
          },
          webhooks: [],
          config: {
            clientId: "",
            clientSecret: "",
            redirectUri: "http://localhost:3000/api/auth/gmail/callback",
            scopes: [
              "https://www.googleapis.com/auth/gmail.readonly",
              "https://www.googleapis.com/auth/gmail.send",
            ],
            watchLabels: ["INBOX", "UNREAD"],
            pollInterval: 60000,
            maxResults: 100,
          },
        };

      case "calendar":
        return {
          name: "Google Calendar Integration",
          type: "calendar",
          enabled: true,
          credentials: {
            encrypted: true,
            keyId: "default",
          },
          retryPolicy: {
            maxRetries: 3,
            backoffType: "exponential",
            initialDelay: 1000,
          },
          webhooks: [],
          config: {
            clientId: "",
            clientSecret: "",
            redirectUri: "http://localhost:3000/api/auth/calendar/callback",
            scopes: [
              "https://www.googleapis.com/auth/calendar.readonly",
              "https://www.googleapis.com/auth/calendar.events",
            ],
            calendars: [],
            syncInterval: 300000,
            lookAheadDays: 30,
            reminderMinutes: [15, 60, 1440],
            autoCreateEvents: true,
            eventDefaults: {
              duration: 3600000,
              color: "blue",
              reminders: true,
            },
          },
        };

      case "twilio":
        return {
          name: "Twilio Integration",
          type: "twilio",
          enabled: true,
          credentials: {
            encrypted: true,
            keyId: "default",
          },
          retryPolicy: {
            maxRetries: 3,
            backoffType: "exponential",
            initialDelay: 1000,
          },
          webhooks: [],
          config: {
            accountSid: "",
            authToken: "",
            fromNumbers: [],
            smsDefaults: {
              maxSegments: 3,
              validityPeriod: 14400,
              attemptRetry: true,
            },
            voiceDefaults: {
              voice: "woman",
              language: "en-US",
              timeout: 30,
            },
          },
        };

      default:
        throw new Error(`Unknown integration type: ${type}`);
    }
  }

  static validate(config: any): IntegrationConfig {
    return IntegrationConfigSchema.parse(config);
  }
}

// Environment-based configuration loader
export async function loadIntegrationConfig(type: string): Promise<IntegrationConfig> {
  const envPrefix = type.toUpperCase();
  const config = IntegrationConfigFactory.createDefault(type);

  // Override with environment variables
  switch (type) {
    case "gmail":
      if (process.env[`${envPrefix}_CLIENT_ID`]) {
        (config as any).config.clientId = process.env[`${envPrefix}_CLIENT_ID`];
      }
      if (process.env[`${envPrefix}_CLIENT_SECRET`]) {
        (config as any).config.clientSecret = process.env[`${envPrefix}_CLIENT_SECRET`];
      }
      break;

    case "twilio":
      if (process.env.TWILIO_ACCOUNT_SID) {
        (config as any).config.accountSid = process.env.TWILIO_ACCOUNT_SID;
      }
      if (process.env.TWILIO_AUTH_TOKEN) {
        (config as any).config.authToken = process.env.TWILIO_AUTH_TOKEN;
      }
      break;

    // Add more environment variable mappings as needed
  }

  return IntegrationConfigFactory.validate(config);
}