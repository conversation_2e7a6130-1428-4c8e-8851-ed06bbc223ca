import { ConvexClient } from "convex/browser";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { EventEmitter } from "events";

export interface AuditContext {
  userId?: string;
  agentId?: Id<"agents">;
  tenderId?: Id<"tenders">;
  workflowInstanceId?: Id<"workflow_instances">;
  eventId?: Id<"events">;
  requestId?: string;
  sessionId?: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface AuditEntry {
  action: string;
  entityType: string;
  entityId: string;
  success: boolean;
  changes?: {
    before?: any;
    after?: any;
    diff?: Array<{
      field: string;
      oldValue: any;
      newValue: any;
    }>;
  };
  metadata?: {
    tags: string[];
    context: Record<string, any>;
  };
  error?: {
    message: string;
    code: string;
    details: Record<string, any>;
  };
}

export class AuditLogger extends EventEmitter {
  private convexClient: ConvexClient;
  private context: AuditContext;
  private buffer: AuditEntry[];
  private flushInterval: NodeJS.Timeout | null;
  private maxBufferSize: number;
  private flushIntervalMs: number;

  constructor(
    convexClient: ConvexClient,
    context: AuditContext = {},
    options: {
      maxBufferSize?: number;
      flushIntervalMs?: number;
    } = {}
  ) {
    super();
    this.convexClient = convexClient;
    this.context = context;
    this.buffer = [];
    this.maxBufferSize = options.maxBufferSize || 100;
    this.flushIntervalMs = options.flushIntervalMs || 5000;
    this.flushInterval = null;

    this.startFlushInterval();
  }

  // Update context
  updateContext(updates: Partial<AuditContext>): void {
    this.context = { ...this.context, ...updates };
  }

  // Log an action
  async log(entry: AuditEntry): Promise<void> {
    const auditLog = {
      ...entry,
      timestamp: Date.now(),
      ...this.context,
      metadata: {
        ...entry.metadata,
        ...this.context,
      },
    };

    this.buffer.push(auditLog);
    this.emit("logged", auditLog);

    // Flush if buffer is full
    if (this.buffer.length >= this.maxBufferSize) {
      await this.flush();
    }
  }

  // Log success action
  async logSuccess(
    action: string,
    entityType: string,
    entityId: string,
    changes?: AuditEntry["changes"],
    metadata?: Record<string, any>
  ): Promise<void> {
    await this.log({
      action,
      entityType,
      entityId,
      success: true,
      changes,
      metadata: {
        tags: [],
        context: metadata || {},
      },
    });
  }

  // Log failure action
  async logFailure(
    action: string,
    entityType: string,
    entityId: string,
    error: Error | string,
    metadata?: Record<string, any>
  ): Promise<void> {
    const errorObj = typeof error === "string" 
      ? { message: error, code: "UNKNOWN_ERROR", details: {} }
      : {
          message: error.message,
          code: error.name || "UNKNOWN_ERROR",
          details: { stack: error.stack },
        };

    await this.log({
      action,
      entityType,
      entityId,
      success: false,
      error: errorObj,
      metadata: {
        tags: [],
        context: metadata || {},
      },
    });
  }

  // Log entity change
  async logChange(
    action: string,
    entityType: string,
    entityId: string,
    before: any,
    after: any,
    metadata?: Record<string, any>
  ): Promise<void> {
    const diff = this.calculateDiff(before, after);

    await this.log({
      action,
      entityType,
      entityId,
      success: true,
      changes: {
        before,
        after,
        diff,
      },
      metadata: {
        tags: [],
        context: metadata || {},
      },
    });
  }

  // Calculate diff between two objects
  private calculateDiff(before: any, after: any): Array<{
    field: string;
    oldValue: any;
    newValue: any;
  }> {
    const diff: Array<{ field: string; oldValue: any; newValue: any }> = [];
    const allKeys = new Set([
      ...Object.keys(before || {}),
      ...Object.keys(after || {}),
    ]);

    for (const key of allKeys) {
      const beforeValue = before?.[key];
      const afterValue = after?.[key];

      if (JSON.stringify(beforeValue) !== JSON.stringify(afterValue)) {
        diff.push({
          field: key,
          oldValue: beforeValue,
          newValue: afterValue,
        });
      }
    }

    return diff;
  }

  // Flush buffer to database
  async flush(): Promise<void> {
    if (this.buffer.length === 0) return;

    const entries = [...this.buffer];
    this.buffer = [];

    try {
      // Batch insert audit logs
      await Promise.all(
        entries.map(entry =>
          this.convexClient.mutation(api.zeroTouch.createAuditLog, entry)
        )
      );

      this.emit("flushed", { count: entries.length });
    } catch (error) {
      // Re-add entries to buffer on failure
      this.buffer.unshift(...entries);
      this.emit("flushError", { error, entries });
      throw error;
    }
  }

  // Start flush interval
  private startFlushInterval(): void {
    this.flushInterval = setInterval(() => {
      this.flush().catch(error => {
        this.emit("error", { error, phase: "flush" });
      });
    }, this.flushIntervalMs);
  }

  // Stop flush interval
  stopFlushInterval(): void {
    if (this.flushInterval) {
      clearInterval(this.flushInterval);
      this.flushInterval = null;
    }
  }

  // Destroy logger
  async destroy(): Promise<void> {
    this.stopFlushInterval();
    await this.flush();
    this.removeAllListeners();
  }

  // Audit decorators for methods
  static auditMethod(
    action: string,
    entityType: string,
    getEntityId: (args: any[], result?: any) => string
  ) {
    return function (
      target: any,
      propertyKey: string,
      descriptor: PropertyDescriptor
    ) {
      const originalMethod = descriptor.value;

      descriptor.value = async function (...args: any[]) {
        const logger = (this as any).auditLogger as AuditLogger;
        if (!logger) {
          return originalMethod.apply(this, args);
        }

        const entityId = getEntityId(args);
        const startTime = Date.now();

        try {
          const result = await originalMethod.apply(this, args);
          
          await logger.logSuccess(
            action,
            entityType,
            entityId,
            undefined,
            {
              method: propertyKey,
              duration: Date.now() - startTime,
              args: args.length > 0 ? args[0] : undefined,
              result: result?.id || result,
            }
          );

          return result;
        } catch (error) {
          await logger.logFailure(
            action,
            entityType,
            entityId,
            error,
            {
              method: propertyKey,
              duration: Date.now() - startTime,
              args: args.length > 0 ? args[0] : undefined,
            }
          );

          throw error;
        }
      };

      return descriptor;
    };
  }

  // Create audit logger for specific contexts
  static forWorkflow(
    convexClient: ConvexClient,
    workflowInstanceId: Id<"workflow_instances">
  ): AuditLogger {
    return new AuditLogger(convexClient, { workflowInstanceId });
  }

  static forAgent(
    convexClient: ConvexClient,
    agentId: Id<"agents">
  ): AuditLogger {
    return new AuditLogger(convexClient, { agentId });
  }

  static forTender(
    convexClient: ConvexClient,
    tenderId: Id<"tenders">
  ): AuditLogger {
    return new AuditLogger(convexClient, { tenderId });
  }

  static forUser(
    convexClient: ConvexClient,
    userId: string,
    sessionId?: string
  ): AuditLogger {
    return new AuditLogger(convexClient, { userId, sessionId });
  }
}

// Audit middleware for Express/Next.js
export function auditMiddleware(auditLogger: AuditLogger) {
  return async (req: any, res: any, next: any) => {
    const requestId = req.headers["x-request-id"] || 
                     `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Update audit context
    auditLogger.updateContext({
      requestId,
      ipAddress: req.ip || req.connection.remoteAddress,
      userAgent: req.headers["user-agent"],
      userId: req.user?.id,
      sessionId: req.session?.id,
    });

    // Log request
    await auditLogger.logSuccess(
      "api_request",
      "http_request",
      requestId,
      undefined,
      {
        method: req.method,
        path: req.path,
        query: req.query,
        headers: {
          ...req.headers,
          authorization: req.headers.authorization ? "[REDACTED]" : undefined,
        },
      }
    );

    // Capture response
    const originalSend = res.send;
    res.send = function (data: any) {
      res.send = originalSend;

      // Log response
      auditLogger.logSuccess(
        "api_response",
        "http_response",
        requestId,
        undefined,
        {
          statusCode: res.statusCode,
          duration: Date.now() - req.startTime,
        }
      ).catch(console.error);

      return res.send(data);
    };

    req.startTime = Date.now();
    next();
  };
}