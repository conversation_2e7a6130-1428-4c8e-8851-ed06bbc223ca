{"name": "zero-touch-tender-system", "version": "1.0.0", "description": "Automated tender workflow orchestration system", "main": "index.ts", "scripts": {"dev": "tsx watch index.ts", "start": "tsx index.ts", "build": "tsc", "test": "vitest", "lint": "eslint . --ext .ts", "format": "prettier --write ."}, "dependencies": {"@langchain/anthropic": "^0.1.21", "@langchain/core": "^0.1.63", "@langchain/langgraph": "^0.0.34", "@langchain/openai": "^0.0.34", "convex": "^1.11.2", "zod": "^3.23.8", "events": "^3.3.0", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.12.12", "typescript": "^5.4.5", "tsx": "^4.11.0", "vitest": "^1.6.0", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^7.11.0", "@typescript-eslint/parser": "^7.11.0", "prettier": "^3.2.5"}, "engines": {"node": ">=18.0.0"}}