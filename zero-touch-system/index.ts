import { ConvexClient } from "convex/browser";
import { MessageQueue } from "./queue/message-queue";
import { EventBus } from "./queue/event-bus";
import { AuditLogger } from "./audit/logger";
import { GlobalError<PERSON><PERSON>ler, ErrorCode, ZeroTouchError } from "./utils/error-handling";

// Zero-Touch System Configuration
export interface ZeroTouchConfig {
  convexUrl: string;
  environment: "development" | "staging" | "production";
  features: {
    autoKickoff: boolean;
    emailIntegration: boolean;
    calendarIntegration: boolean;
    smsNotifications: boolean;
    slackIntegration: boolean;
    teamsIntegration: boolean;
  };
  logging: {
    level: "debug" | "info" | "warn" | "error";
    auditBuffer: number;
    flushInterval: number;
  };
  queue: {
    defaultConcurrency: number;
    pollInterval: number;
  };
  monitoring: {
    healthCheckInterval: number;
    metricsInterval: number;
  };
}

// Default configuration
const defaultConfig: ZeroTouchConfig = {
  convexUrl: process.env.CONVEX_URL || "",
  environment: (process.env.NODE_ENV as any) || "development",
  features: {
    autoKickoff: true,
    emailIntegration: true,
    calendarIntegration: true,
    smsNotifications: true,
    slackIntegration: false,
    teamsIntegration: false,
  },
  logging: {
    level: "info",
    auditBuffer: 100,
    flushInterval: 5000,
  },
  queue: {
    defaultConcurrency: 5,
    pollInterval: 1000,
  },
  monitoring: {
    healthCheckInterval: 60000,
    metricsInterval: 300000,
  },
};

// Zero-Touch System Main Class
export class ZeroTouchSystem {
  private config: ZeroTouchConfig;
  private convexClient: ConvexClient;
  private messageQueue: MessageQueue;
  private eventBus: EventBus;
  private auditLogger: AuditLogger;
  private isRunning: boolean = false;
  private healthCheckInterval?: NodeJS.Timeout;
  private metricsInterval?: NodeJS.Timeout;

  constructor(config: Partial<ZeroTouchConfig> = {}) {
    this.config = { ...defaultConfig, ...config };
    
    // Initialize Convex client
    this.convexClient = new ConvexClient(this.config.convexUrl);
    
    // Initialize core components
    this.messageQueue = new MessageQueue(this.convexClient);
    this.eventBus = new EventBus(this.convexClient);
    this.auditLogger = new AuditLogger(this.convexClient, {}, {
      maxBufferSize: this.config.logging.auditBuffer,
      flushIntervalMs: this.config.logging.flushInterval,
    });

    // Setup global error handling
    this.setupErrorHandling();
  }

  // Initialize the system
  async initialize(): Promise<void> {
    try {
      console.log("🚀 Initializing Zero-Touch Tender System...");

      // Initialize audit logging
      await this.auditLogger.logSuccess(
        "system_initialization",
        "system",
        "zero-touch",
        undefined,
        {
          config: this.config,
          timestamp: new Date().toISOString(),
        }
      );

      // Register message queues
      this.registerQueues();

      // Register event handlers
      this.registerEventHandlers();

      // Initialize integrations
      await this.initializeIntegrations();

      // Load workflows
      await this.loadWorkflows();

      // Initialize agents
      await this.initializeAgents();

      console.log("✅ Zero-Touch System initialized successfully");
    } catch (error) {
      console.error("❌ Failed to initialize Zero-Touch System:", error);
      await this.auditLogger.logFailure(
        "system_initialization_failed",
        "system",
        "zero-touch",
        error
      );
      throw error;
    }
  }

  // Start the system
  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error("System is already running");
    }

    try {
      console.log("🏁 Starting Zero-Touch System...");

      // Start message queue processing
      await this.messageQueue.start();

      // Start event bus processing
      await this.eventBus.start();

      // Start health monitoring
      this.startHealthMonitoring();

      // Start metrics collection
      this.startMetricsCollection();

      this.isRunning = true;

      await this.auditLogger.logSuccess(
        "system_started",
        "system",
        "zero-touch"
      );

      console.log("✅ Zero-Touch System is running");
    } catch (error) {
      console.error("❌ Failed to start Zero-Touch System:", error);
      throw error;
    }
  }

  // Stop the system
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      console.log("🛑 Stopping Zero-Touch System...");

      // Stop health monitoring
      this.stopHealthMonitoring();

      // Stop metrics collection
      this.stopMetricsCollection();

      // Stop message queue
      await this.messageQueue.stop();

      // Stop event bus
      await this.eventBus.stop();

      // Flush audit logs
      await this.auditLogger.flush();

      this.isRunning = false;

      await this.auditLogger.logSuccess(
        "system_stopped",
        "system",
        "zero-touch"
      );

      console.log("✅ Zero-Touch System stopped");
    } catch (error) {
      console.error("❌ Error stopping Zero-Touch System:", error);
      throw error;
    }
  }

  // Register message queues
  private registerQueues(): void {
    // Tender processing queue
    this.messageQueue.registerQueue({
      name: "tender-processing",
      concurrency: this.config.queue.defaultConcurrency,
      retryPolicy: {
        maxRetries: 3,
        backoffType: "exponential",
        initialDelay: 1000,
      },
      visibilityTimeout: 300000, // 5 minutes
      deadLetterQueue: "tender-processing-dlq",
    });

    // Email processing queue
    this.messageQueue.registerQueue({
      name: "email-processing",
      concurrency: 3,
      retryPolicy: {
        maxRetries: 2,
        backoffType: "exponential",
        initialDelay: 500,
      },
      visibilityTimeout: 60000, // 1 minute
    });

    // Notification queue
    this.messageQueue.registerQueue({
      name: "notifications",
      concurrency: 10,
      retryPolicy: {
        maxRetries: 3,
        backoffType: "linear",
        initialDelay: 1000,
      },
      visibilityTimeout: 30000, // 30 seconds
    });

    // Agent task queue
    this.messageQueue.registerQueue({
      name: "agent-tasks",
      concurrency: 5,
      retryPolicy: {
        maxRetries: 2,
        backoffType: "exponential",
        initialDelay: 2000,
      },
      visibilityTimeout: 600000, // 10 minutes
    });
  }

  // Register event handlers
  private registerEventHandlers(): void {
    // Handle tender creation events
    this.eventBus.on("tender.created", async (event) => {
      console.log("📋 New tender created:", event.payload);
      
      // Queue tender for processing
      await this.messageQueue.enqueue({
        queue: "tender-processing",
        payload: {
          action: "kickoff",
          tenderId: event.payload.tenderId,
        },
        priority: 8,
      });
    });

    // Handle email received events
    this.eventBus.on("integration.email.received", async (event) => {
      console.log("📧 Email received:", event.payload.subject);
      
      // Queue email for processing
      await this.messageQueue.enqueue({
        queue: "email-processing",
        payload: event.payload,
        priority: 5,
      });
    });

    // Handle workflow completion events
    this.eventBus.on("workflow.completed", async (event) => {
      console.log("✅ Workflow completed:", event.payload.workflowId);
      
      // Send notifications
      await this.messageQueue.enqueue({
        queue: "notifications",
        payload: {
          type: "workflow_completed",
          recipients: event.payload.notifyUsers,
          data: event.payload,
        },
      });
    });

    // Handle system errors
    this.eventBus.on("system.error", async (event) => {
      console.error("❌ System error:", event.payload);
      
      // Log critical errors
      await this.auditLogger.logFailure(
        "system_error",
        "system",
        "zero-touch",
        event.payload.error,
        event.payload
      );
    });
  }

  // Initialize integrations
  private async initializeIntegrations(): Promise<void> {
    const integrations = [];

    if (this.config.features.emailIntegration) {
      integrations.push(this.initializeEmailIntegration());
    }

    if (this.config.features.calendarIntegration) {
      integrations.push(this.initializeCalendarIntegration());
    }

    if (this.config.features.smsNotifications) {
      integrations.push(this.initializeSmsIntegration());
    }

    if (this.config.features.slackIntegration) {
      integrations.push(this.initializeSlackIntegration());
    }

    if (this.config.features.teamsIntegration) {
      integrations.push(this.initializeTeamsIntegration());
    }

    await Promise.all(integrations);
  }

  // Initialize email integration
  private async initializeEmailIntegration(): Promise<void> {
    // Implementation will be added by Integration Agent
    console.log("📧 Email integration initialized");
  }

  // Initialize calendar integration
  private async initializeCalendarIntegration(): Promise<void> {
    // Implementation will be added by Integration Agent
    console.log("📅 Calendar integration initialized");
  }

  // Initialize SMS integration
  private async initializeSmsIntegration(): Promise<void> {
    // Implementation will be added by Integration Agent
    console.log("📱 SMS integration initialized");
  }

  // Initialize Slack integration
  private async initializeSlackIntegration(): Promise<void> {
    // Implementation will be added by Integration Agent
    console.log("💬 Slack integration initialized");
  }

  // Initialize Teams integration
  private async initializeTeamsIntegration(): Promise<void> {
    // Implementation will be added by Integration Agent
    console.log("👥 Teams integration initialized");
  }

  // Load workflows
  private async loadWorkflows(): Promise<void> {
    // Implementation will be added by Workflow Agent
    console.log("📊 Workflows loaded");
  }

  // Initialize agents
  private async initializeAgents(): Promise<void> {
    // Implementation will be added by Agent Coordinator
    console.log("🤖 Agents initialized");
  }

  // Setup error handling
  private setupErrorHandling(): void {
    // Register error handlers
    GlobalErrorHandler.register(ErrorCode.WORKFLOW_EXECUTION_ERROR, (error) => {
      console.error("Workflow execution error:", error);
      this.eventBus.emit({
        type: "system.error",
        source: "workflow",
        payload: { error: error.toJSON() },
      });
    });

    GlobalErrorHandler.register(ErrorCode.AGENT_TASK_FAILED, (error) => {
      console.error("Agent task failed:", error);
      this.eventBus.emit({
        type: "system.error",
        source: "agent",
        payload: { error: error.toJSON() },
      });
    });

    GlobalErrorHandler.register(ErrorCode.INTEGRATION_API_ERROR, (error) => {
      console.error("Integration API error:", error);
      this.eventBus.emit({
        type: "system.error",
        source: "integration",
        payload: { error: error.toJSON() },
      });
    });

    // Global uncaught exception handler
    process.on("uncaughtException", (error) => {
      console.error("Uncaught exception:", error);
      this.auditLogger.logFailure(
        "uncaught_exception",
        "system",
        "process",
        error
      ).then(() => {
        process.exit(1);
      });
    });

    // Global unhandled rejection handler
    process.on("unhandledRejection", (reason, promise) => {
      console.error("Unhandled rejection at:", promise, "reason:", reason);
      this.auditLogger.logFailure(
        "unhandled_rejection",
        "system",
        "process",
        reason as Error
      );
    });
  }

  // Start health monitoring
  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(async () => {
      try {
        const health = await this.checkSystemHealth();
        
        await this.convexClient.mutation(api.zeroTouch.updateSystemHealth, {
          component: "zero-touch-system",
          status: health.status,
          metrics: health.metrics,
          checks: health.checks,
        });

        if (health.status === "unhealthy" || health.status === "critical") {
          await this.eventBus.emit({
            type: "system.health.alert",
            source: "health-monitor",
            payload: health,
          });
        }
      } catch (error) {
        console.error("Health check failed:", error);
      }
    }, this.config.monitoring.healthCheckInterval);
  }

  // Stop health monitoring
  private stopHealthMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = undefined;
    }
  }

  // Check system health
  private async checkSystemHealth(): Promise<any> {
    const checks = [];
    let overallStatus = "healthy";

    // Check message queue
    try {
      const queueStats = await this.messageQueue.getQueueStats("tender-processing");
      checks.push({
        name: "message_queue",
        status: queueStats.pending > 100 ? "degraded" : "healthy",
        message: `${queueStats.pending} messages pending`,
      });
    } catch (error) {
      checks.push({
        name: "message_queue",
        status: "unhealthy",
        message: error.message,
      });
      overallStatus = "unhealthy";
    }

    // Check database connectivity
    try {
      await this.convexClient.query(api.zeroTouch.healthCheck, {});
      checks.push({
        name: "database",
        status: "healthy",
        message: "Database connection OK",
      });
    } catch (error) {
      checks.push({
        name: "database",
        status: "critical",
        message: error.message,
      });
      overallStatus = "critical";
    }

    return {
      status: overallStatus,
      checks,
      metrics: {
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
        cpuUsage: process.cpuUsage().user / 1000000,
      },
    };
  }

  // Start metrics collection
  private startMetricsCollection(): void {
    this.metricsInterval = setInterval(async () => {
      try {
        await this.collectAndStoreMetrics();
      } catch (error) {
        console.error("Metrics collection failed:", error);
      }
    }, this.config.monitoring.metricsInterval);
  }

  // Stop metrics collection
  private stopMetricsCollection(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = undefined;
    }
  }

  // Collect and store metrics
  private async collectAndStoreMetrics(): Promise<void> {
    // Implementation will be added by Monitoring Agent
    console.log("📊 Metrics collected");
  }

  // Get system status
  getStatus(): {
    isRunning: boolean;
    config: ZeroTouchConfig;
    uptime: number;
  } {
    return {
      isRunning: this.isRunning,
      config: this.config,
      uptime: process.uptime(),
    };
  }
}

// Export main function to start the system
export async function startZeroTouchSystem(config?: Partial<ZeroTouchConfig>): Promise<ZeroTouchSystem> {
  const system = new ZeroTouchSystem(config);
  await system.initialize();
  await system.start();
  return system;
}