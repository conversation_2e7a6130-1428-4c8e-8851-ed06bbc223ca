import { ConvexClient } from "convex/browser";
import { api } from "../../../convex/_generated/api";
import { Id } from "../../../convex/_generated/dataModel";
import { EventEmitter } from "events";
import { z } from "zod";
import { ChatOpenAI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { BaseChatModel } from "@langchain/core/language_models/chat_models";
import { BaseMessage, HumanMessage, SystemMessage, AIMessage } from "@langchain/core/messages";
import { AuditLogger } from "../../audit/logger";
import { EventBus, EventTypes } from "../../queue/event-bus";

// Agent configuration schema
export const AgentConfigSchema = z.object({
  name: z.string(),
  description: z.string(),
  type: z.string(),
  specializations: z.array(z.string()),
  model: z.object({
    provider: z.enum(["openai", "anthropic", "custom"]),
    name: z.string(),
    temperature: z.number().min(0).max(2).default(0.7),
    maxTokens: z.number().default(4000),
  }),
  systemPrompt: z.string(),
  capabilities: z.array(z.string()),
  maxConcurrentTasks: z.number().default(3),
  autoAssign: z.boolean().default(true),
  qualityThreshold: z.number().min(0).max(1).default(0.8),
  maxWordCount: z.number().optional(),
  retryPolicy: z.object({
    maxRetries: z.number().default(3),
    backoffType: z.enum(["exponential", "linear", "constant"]).default("exponential"),
    initialDelay: z.number().default(1000),
  }),
});

export type AgentConfig = z.infer<typeof AgentConfigSchema>;

// Task input/output schemas
export const TaskInputSchema = z.object({
  content: z.string().optional(),
  instructions: z.string(),
  context: z.record(z.any()),
  parameters: z.record(z.any()).optional(),
});

export const TaskOutputSchema = z.object({
  content: z.string(),
  wordCount: z.number(),
  confidence: z.number(),
  suggestions: z.array(z.string()).optional(),
  warnings: z.array(z.string()).optional(),
  processingTime: z.number(),
  revisionsNeeded: z.boolean().optional(),
  qualityMetrics: z.object({
    clarity: z.number(),
    relevance: z.number(),
    completeness: z.number(),
    persuasiveness: z.number(),
  }).optional(),
});

export type TaskInput = z.infer<typeof TaskInputSchema>;
export type TaskOutput = z.infer<typeof TaskOutputSchema>;

// Base Agent Abstract Class
export abstract class BaseAgent extends EventEmitter {
  protected convexClient: ConvexClient;
  protected config: AgentConfig;
  protected llm: BaseChatModel;
  protected auditLogger: AuditLogger;
  protected eventBus: EventBus;
  protected agentId?: Id<"agents">;
  protected currentTasks: Set<Id<"agent_tasks">>;
  protected isActive: boolean;

  constructor(
    convexClient: ConvexClient,
    config: AgentConfig,
    auditLogger: AuditLogger,
    eventBus: EventBus
  ) {
    super();
    this.convexClient = convexClient;
    this.config = config;
    this.auditLogger = auditLogger;
    this.eventBus = eventBus;
    this.currentTasks = new Set();
    this.isActive = false;

    // Initialize LLM based on config
    this.llm = this.initializeLLM(config.model);
  }

  // Abstract methods to be implemented by specific agents
  abstract processTask(input: TaskInput): Promise<TaskOutput>;
  abstract validateInput(input: TaskInput): Promise<boolean>;
  abstract getSpecializations(): string[];

  // Initialize LLM
  private initializeLLM(modelConfig: AgentConfig["model"]): BaseChatModel {
    switch (modelConfig.provider) {
      case "openai":
        return new ChatOpenAI({
          modelName: modelConfig.name,
          temperature: modelConfig.temperature,
          maxTokens: modelConfig.maxTokens,
          openAIApiKey: process.env.OPENAI_API_KEY,
        });

      case "anthropic":
        return new ChatAnthropic({
          modelName: modelConfig.name,
          temperature: modelConfig.temperature,
          maxTokens: modelConfig.maxTokens,
          anthropicApiKey: process.env.ANTHROPIC_API_KEY,
        });

      default:
        throw new Error(`Unsupported model provider: ${modelConfig.provider}`);
    }
  }

  // Initialize agent
  async initialize(): Promise<void> {
    try {
      // Register agent in database
      this.agentId = await this.convexClient.mutation(api.zeroTouch.createAgent, {
        name: this.config.name,
        description: this.config.description,
        type: this.config.type,
        capabilities: this.config.capabilities,
        specializations: this.config.specializations,
        model: this.config.model.name,
        temperature: this.config.model.temperature,
        maxTokens: this.config.model.maxTokens,
        systemPrompt: this.config.systemPrompt,
        maxConcurrentTasks: this.config.maxConcurrentTasks,
        autoAssign: this.config.autoAssign,
        qualityThreshold: this.config.qualityThreshold,
        maxWordCount: this.config.maxWordCount,
      });

      // Update audit logger context
      this.auditLogger.updateContext({ agentId: this.agentId });

      // Register event handlers
      this.registerEventHandlers();

      this.isActive = true;
      this.emit("initialized", { agentId: this.agentId });

      await this.auditLogger.logSuccess(
        "agent_initialized",
        "agent",
        this.agentId,
        undefined,
        { config: this.config }
      );
    } catch (error) {
      this.emit("error", { error, phase: "initialization" });
      throw error;
    }
  }

  // Register event handlers
  private registerEventHandlers(): void {
    // Listen for task assignments
    this.eventBus.on(
      EventTypes.AGENT_TASK_ASSIGNED,
      async (event) => {
        if (event.payload.agentId === this.agentId) {
          await this.handleTaskAssignment(event.payload.taskId);
        }
      },
      { priority: 10 }
    );

    // Listen for task cancellations
    this.eventBus.on(
      "agent.task.cancelled",
      async (event) => {
        if (event.payload.agentId === this.agentId) {
          await this.cancelTask(event.payload.taskId);
        }
      }
    );
  }

  // Handle task assignment
  private async handleTaskAssignment(taskId: Id<"agent_tasks">): Promise<void> {
    if (this.currentTasks.size >= this.config.maxConcurrentTasks) {
      await this.convexClient.mutation(api.zeroTouch.updateAgentTaskStatus, {
        taskId,
        status: "queued",
      });
      return;
    }

    this.currentTasks.add(taskId);
    
    try {
      await this.executeTask(taskId);
    } finally {
      this.currentTasks.delete(taskId);
      await this.checkQueuedTasks();
    }
  }

  // Execute task
  private async executeTask(taskId: Id<"agent_tasks">): Promise<void> {
    const startTime = Date.now();

    try {
      // Fetch task details
      const task = await this.convexClient.query(api.zeroTouch.getAgentTask, { taskId });
      if (!task) {
        throw new Error(`Task ${taskId} not found`);
      }

      // Update task status
      await this.convexClient.mutation(api.zeroTouch.updateAgentTaskStatus, {
        taskId,
        status: "processing",
        startedAt: Date.now(),
      });

      // Emit event
      await this.eventBus.event(EventTypes.AGENT_TASK_STARTED)
        .payload({ agentId: this.agentId, taskId })
        .tender(task.tenderId)
        .emit();

      // Validate input
      const isValid = await this.validateInput(task.input);
      if (!isValid) {
        throw new Error("Invalid task input");
      }

      // Process task with retry logic
      const output = await this.executeWithRetry(
        () => this.processTask(task.input),
        this.config.retryPolicy
      );

      // Validate output quality
      const qualityCheck = await this.checkOutputQuality(output);
      if (qualityCheck.score < this.config.qualityThreshold) {
        output.warnings = [...(output.warnings || []), ...qualityCheck.issues];
        output.revisionsNeeded = true;
      }

      // Update task with results
      await this.convexClient.mutation(api.zeroTouch.completeAgentTask, {
        taskId,
        output: {
          ...output,
          processingTime: Date.now() - startTime,
        },
        qualityScore: qualityCheck.score,
      });

      // Log success
      await this.auditLogger.logSuccess(
        "task_completed",
        "agent_task",
        taskId,
        undefined,
        {
          agentId: this.agentId,
          duration: Date.now() - startTime,
          wordCount: output.wordCount,
          qualityScore: qualityCheck.score,
        }
      );

      // Emit completion event
      await this.eventBus.event(EventTypes.AGENT_TASK_COMPLETED)
        .payload({
          agentId: this.agentId,
          taskId,
          output,
          qualityScore: qualityCheck.score,
        })
        .tender(task.tenderId)
        .emit();

      this.emit("taskCompleted", { taskId, output });
    } catch (error) {
      // Update task with error
      await this.convexClient.mutation(api.zeroTouch.failAgentTask, {
        taskId,
        error: error.message,
      });

      // Log failure
      await this.auditLogger.logFailure(
        "task_failed",
        "agent_task",
        taskId,
        error,
        {
          agentId: this.agentId,
          duration: Date.now() - startTime,
        }
      );

      // Emit failure event
      await this.eventBus.event(EventTypes.AGENT_TASK_FAILED)
        .payload({
          agentId: this.agentId,
          taskId,
          error: error.message,
        })
        .emit();

      this.emit("taskFailed", { taskId, error });
      throw error;
    }
  }

  // Execute with retry logic
  private async executeWithRetry<T>(
    fn: () => Promise<T>,
    retryPolicy: AgentConfig["retryPolicy"]
  ): Promise<T> {
    let lastError: any;

    for (let attempt = 0; attempt <= retryPolicy.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error;

        if (attempt < retryPolicy.maxRetries) {
          const delay = this.calculateBackoffDelay(
            attempt + 1,
            retryPolicy.backoffType,
            retryPolicy.initialDelay
          );

          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    throw lastError;
  }

  // Calculate backoff delay
  private calculateBackoffDelay(
    retryCount: number,
    backoffType: string,
    initialDelay: number
  ): number {
    switch (backoffType) {
      case "exponential":
        return initialDelay * Math.pow(2, retryCount - 1);
      case "linear":
        return initialDelay * retryCount;
      case "constant":
      default:
        return initialDelay;
    }
  }

  // Check output quality
  protected async checkOutputQuality(output: TaskOutput): Promise<{
    score: number;
    issues: string[];
  }> {
    const issues: string[] = [];
    let totalScore = 0;
    let weightSum = 0;

    // Basic quality checks
    if (output.wordCount < 50) {
      issues.push("Content is too short");
    }

    if (output.confidence < 0.7) {
      issues.push("Low confidence score");
    }

    // Use quality metrics if available
    if (output.qualityMetrics) {
      const metrics = output.qualityMetrics;
      totalScore = (
        metrics.clarity * 0.25 +
        metrics.relevance * 0.35 +
        metrics.completeness * 0.25 +
        metrics.persuasiveness * 0.15
      );

      if (metrics.clarity < 0.7) issues.push("Content lacks clarity");
      if (metrics.relevance < 0.7) issues.push("Content may not be fully relevant");
      if (metrics.completeness < 0.7) issues.push("Content appears incomplete");
    } else {
      // Default score based on confidence
      totalScore = output.confidence;
    }

    return {
      score: totalScore,
      issues,
    };
  }

  // Check for queued tasks
  private async checkQueuedTasks(): Promise<void> {
    if (this.currentTasks.size >= this.config.maxConcurrentTasks) {
      return;
    }

    const queuedTasks = await this.convexClient.query(api.zeroTouch.getQueuedTasksForAgent, {
      agentId: this.agentId!,
      limit: this.config.maxConcurrentTasks - this.currentTasks.size,
    });

    for (const task of queuedTasks) {
      await this.handleTaskAssignment(task._id);
    }
  }

  // Cancel task
  private async cancelTask(taskId: Id<"agent_tasks">): Promise<void> {
    if (this.currentTasks.has(taskId)) {
      this.currentTasks.delete(taskId);
      await this.convexClient.mutation(api.zeroTouch.updateAgentTaskStatus, {
        taskId,
        status: "cancelled",
      });

      this.emit("taskCancelled", { taskId });
    }
  }

  // Get agent statistics
  async getStatistics(): Promise<any> {
    if (!this.agentId) {
      throw new Error("Agent not initialized");
    }

    return await this.convexClient.query(api.zeroTouch.getAgentStatistics, {
      agentId: this.agentId,
    });
  }

  // Update agent status
  async updateStatus(status: "active" | "inactive" | "maintenance"): Promise<void> {
    if (!this.agentId) {
      throw new Error("Agent not initialized");
    }

    await this.convexClient.mutation(api.zeroTouch.updateAgentStatus, {
      agentId: this.agentId,
      status,
    });

    this.isActive = status === "active";
    this.emit("statusChanged", { status });
  }

  // Shutdown agent
  async shutdown(): Promise<void> {
    this.isActive = false;

    // Wait for current tasks to complete
    const timeout = 30000; // 30 seconds
    const startTime = Date.now();

    while (this.currentTasks.size > 0 && Date.now() - startTime < timeout) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // Force cancel remaining tasks
    for (const taskId of this.currentTasks) {
      await this.cancelTask(taskId);
    }

    // Update agent status
    if (this.agentId) {
      await this.updateStatus("inactive");
    }

    // Cleanup
    this.removeAllListeners();
    await this.auditLogger.destroy();

    this.emit("shutdown");
  }

  // Helper method to create chat messages
  protected createMessages(systemPrompt: string, userInput: string): BaseMessage[] {
    return [
      new SystemMessage(systemPrompt),
      new HumanMessage(userInput),
    ];
  }

  // Helper method to format context
  protected formatContext(context: Record<string, any>): string {
    return Object.entries(context)
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join("\n");
  }
}