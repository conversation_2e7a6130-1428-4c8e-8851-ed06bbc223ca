# Zero-Touch Tender System

## Overview

The Zero-Touch Tender System is an automated workflow orchestration platform designed to streamline the tender management process from initial email receipt to final submission. Built on top of LangGraph and Convex, it provides intelligent automation, real-time event processing, and comprehensive audit logging.

## Architecture

### Core Components

1. **Orchestrator** (`/orchestrator`)
   - Base orchestration framework using LangGraph
   - State management and workflow execution
   - Step coordination and error handling

2. **Agents** (`/agents`)
   - Base agent framework for specialized AI agents
   - Task processing and quality validation
   - Concurrent task management

3. **Message Queue** (`/queue`)
   - Asynchronous message processing
   - Priority-based queue management
   - Dead letter queue support
   - Retry mechanisms

4. **Event Bus** (`/queue`)
   - Real-time event processing
   - Event correlation and replay
   - Handler registration and filtering

5. **Audit Logger** (`/audit`)
   - Comprehensive audit trail
   - Buffered logging for performance
   - Context-aware logging

6. **Integrations** (`/integrations`)
   - Gmail integration for email monitoring
   - Google Calendar for deadline tracking
   - Twilio for SMS notifications
   - Slack/Teams for team collaboration

## Database Schema

The system extends the existing Convex schema with specialized tables:

- `workflows` - Workflow definitions
- `workflow_instances` - Running workflow instances
- `events` - System events and processing
- `message_queue` - Asynchronous message queue
- `audit_logs` - Comprehensive audit trail
- `integration_configs` - Integration configurations
- `notifications` - Multi-channel notifications
- `agent_coordination` - Agent task coordination
- `system_health` - Health monitoring

## Key Features

### Workflow Orchestration
- State-based workflow execution using LangGraph
- Conditional branching and parallel execution
- Retry policies and timeout handling
- Progress tracking and context management

### Event-Driven Architecture
- Real-time event processing
- Event correlation and causation tracking
- Flexible event handlers with priorities
- Event replay capabilities

### Message Queue System
- Priority-based message processing
- Configurable concurrency limits
- Automatic retry with backoff
- Dead letter queue for failed messages

### Agent Framework
- Base class for specialized AI agents
- Quality validation and scoring
- Concurrent task management
- Performance tracking

### Comprehensive Audit Logging
- All actions logged with context
- Change tracking with diffs
- Buffered writes for performance
- Multiple audit contexts (user, agent, workflow)

### Error Handling
- Custom error hierarchy
- Retry mechanisms with configurable policies
- Circuit breaker pattern
- Global error handling

### Integration Support
- Standardized integration configuration
- OAuth2 support for Google services
- Webhook management
- Rate limiting and retry policies

## Getting Started

### Installation

```bash
# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
```

### Configuration

Create a configuration file or use environment variables:

```typescript
const config = {
  convexUrl: process.env.CONVEX_URL,
  environment: "development",
  features: {
    autoKickoff: true,
    emailIntegration: true,
    calendarIntegration: true,
    smsNotifications: true,
  },
  logging: {
    level: "info",
    auditBuffer: 100,
    flushInterval: 5000,
  },
  queue: {
    defaultConcurrency: 5,
    pollInterval: 1000,
  },
};
```

### Starting the System

```typescript
import { startZeroTouchSystem } from "./zero-touch-system";

const system = await startZeroTouchSystem(config);
```

## Usage

### Creating a Workflow

```typescript
class TenderKickoffOrchestrator extends BaseOrchestrator {
  buildGraph(): void {
    // Add workflow steps
    this.addStep({
      id: "parse_email",
      name: "Parse Tender Email",
      handler: async (state) => {
        // Implementation
        return { ...state };
      },
    });

    // Set up edges
    this.setEntryPoint("parse_email");
    this.addEdge("parse_email", "extract_requirements");
  }
}
```

### Creating an Agent

```typescript
class RequirementsAgent extends BaseAgent {
  async processTask(input: TaskInput): Promise<TaskOutput> {
    // Process requirements extraction
    const messages = this.createMessages(
      this.config.systemPrompt,
      input.instructions
    );
    
    const response = await this.llm.invoke(messages);
    
    return {
      content: response.content,
      wordCount: response.content.split(" ").length,
      confidence: 0.95,
      processingTime: Date.now() - startTime,
    };
  }
}
```

### Handling Events

```typescript
// Register event handler
eventBus.on("tender.created", async (event) => {
  console.log("New tender:", event.payload);
  
  // Queue for processing
  await messageQueue.enqueue({
    queue: "tender-processing",
    payload: event.payload,
    priority: 8,
  });
});

// Emit event
await eventBus.event("tender.created")
  .payload({ tenderId: "123", name: "New Tender" })
  .tender(tenderId)
  .emit();
```

### Message Queue Processing

```typescript
// Register queue handler
messageQueue.registerHandler("tender-processing", async (message) => {
  const { tenderId } = message.payload;
  
  // Process tender
  const orchestrator = new TenderKickoffOrchestrator(convexClient);
  await orchestrator.initialize(workflowId, { tenderId });
  await orchestrator.execute();
});

// Start processing
await messageQueue.start();
```

### Audit Logging

```typescript
// Create contextual logger
const auditLogger = AuditLogger.forWorkflow(convexClient, workflowInstanceId);

// Log actions
await auditLogger.logSuccess(
  "tender_parsed",
  "tender",
  tenderId,
  undefined,
  { extractedFields: 10 }
);

// Log changes
await auditLogger.logChange(
  "tender_updated",
  "tender",
  tenderId,
  oldTender,
  newTender
);
```

## Integration Configuration

### Gmail Integration

```typescript
const gmailConfig = {
  name: "Gmail Integration",
  type: "gmail",
  config: {
    clientId: process.env.GMAIL_CLIENT_ID,
    clientSecret: process.env.GMAIL_CLIENT_SECRET,
    scopes: ["gmail.readonly", "gmail.send"],
    watchLabels: ["INBOX", "UNREAD"],
  },
};
```

### Calendar Integration

```typescript
const calendarConfig = {
  name: "Google Calendar",
  type: "calendar",
  config: {
    clientId: process.env.CALENDAR_CLIENT_ID,
    clientSecret: process.env.CALENDAR_CLIENT_SECRET,
    lookAheadDays: 30,
    reminderMinutes: [15, 60, 1440],
  },
};
```

## Monitoring

### Health Checks

The system automatically performs health checks on:
- Message queue depth
- Database connectivity
- Integration status
- Agent availability

### Metrics Collection

Metrics are collected for:
- Workflow execution times
- Agent performance
- Queue processing rates
- Error rates

## Error Handling

The system implements comprehensive error handling:

- **Retry Policies**: Configurable per component
- **Circuit Breakers**: Prevent cascading failures
- **Dead Letter Queues**: For unprocessable messages
- **Error Classification**: Severity levels and retry eligibility

## Security

- Encrypted credential storage
- API key rotation support
- Audit trail for all actions
- Role-based access control (planned)

## Development

### Running Tests

```bash
npm test
```

### Adding New Integrations

1. Create integration config schema
2. Implement integration client
3. Register with integration manager
4. Add event handlers

### Adding New Agents

1. Extend `BaseAgent` class
2. Implement required methods
3. Define configuration
4. Register with agent coordinator

## Deployment

The system is designed to run as a long-running service:

```bash
# Production mode
NODE_ENV=production npm start
```

## Future Enhancements

- [ ] Web dashboard for monitoring
- [ ] Advanced workflow designer
- [ ] Machine learning for optimization
- [ ] Multi-tenant support
- [ ] Horizontal scaling support

## Support

For issues or questions, please refer to the main project documentation or create an issue in the repository.