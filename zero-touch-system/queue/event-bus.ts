import { ConvexClient } from "convex/browser";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { EventEmitter } from "events";
import { z } from "zod";

// Event schemas
export const BaseEventSchema = z.object({
  id: z.string().optional(),
  type: z.string(),
  source: z.string(),
  timestamp: z.number().default(() => Date.now()),
  correlationId: z.string().optional(),
  causationId: z.string().optional(),
  metadata: z.object({
    tenderId: z.string().optional(),
    workflowInstanceId: z.string().optional(),
    userId: z.string().optional(),
    tags: z.array(z.string()).default([]),
  }).default({}),
});

export const EventPayloadSchema = z.record(z.any());

export const EventSchema = BaseEventSchema.extend({
  payload: EventPayloadSchema,
});

export type Event = z.infer<typeof EventSchema>;

// Event handler type
export type EventHandler<T = any> = (event: Event & { payload: T }) => Promise<void>;

// Event handler registration
export interface HandlerRegistration {
  id: string;
  eventType: string | RegExp;
  handler: EventHandler;
  options?: {
    priority?: number;
    filter?: (event: Event) => boolean;
    maxRetries?: number;
    timeout?: number;
  };
}

// Event bus class
export class EventBus extends EventEmitter {
  private convexClient: ConvexClient;
  private handlers: Map<string, HandlerRegistration[]>;
  private isProcessing: boolean;
  private processingInterval: NodeJS.Timeout | null;
  private correlationMap: Map<string, string[]>;

  constructor(convexClient: ConvexClient) {
    super();
    this.convexClient = convexClient;
    this.handlers = new Map();
    this.isProcessing = false;
    this.processingInterval = null;
    this.correlationMap = new Map();
  }

  // Register event handler
  on(eventType: string | RegExp, handler: EventHandler, options?: HandlerRegistration["options"]): string {
    const id = `handler_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const registration: HandlerRegistration = {
      id,
      eventType,
      handler,
      options,
    };

    const key = typeof eventType === "string" ? eventType : "regex_handlers";
    const registrations = this.handlers.get(key) || [];
    registrations.push(registration);
    
    // Sort by priority (higher priority first)
    registrations.sort((a, b) => (b.options?.priority || 0) - (a.options?.priority || 0));
    
    this.handlers.set(key, registrations);
    
    super.emit("handlerRegistered", { id, eventType });
    return id;
  }

  // Remove event handler
  off(handlerId: string): boolean {
    for (const [key, registrations] of this.handlers) {
      const index = registrations.findIndex(r => r.id === handlerId);
      if (index >= 0) {
        registrations.splice(index, 1);
        super.emit("handlerRemoved", { handlerId });
        return true;
      }
    }
    return false;
  }

  // Emit event
  async emit(event: Event | Omit<Event, "timestamp">): Promise<Id<"events">> {
    const validatedEvent = EventSchema.parse({
      ...event,
      timestamp: event.timestamp || Date.now(),
    });

    // Store event in database
    const eventId = await this.convexClient.mutation(api.zeroTouch.createEvent, {
      type: validatedEvent.type,
      source: validatedEvent.source,
      payload: validatedEvent.payload,
      correlationId: validatedEvent.correlationId,
      metadata: validatedEvent.metadata,
    });

    // Track correlation
    if (validatedEvent.correlationId) {
      const events = this.correlationMap.get(validatedEvent.correlationId) || [];
      events.push(eventId);
      this.correlationMap.set(validatedEvent.correlationId, events);
    }

    super.emit("eventEmitted", { eventId, event: validatedEvent });
    return eventId;
  }

  // Emit and wait for handlers
  async emitAndWait(event: Event | Omit<Event, "timestamp">, timeout = 30000): Promise<void> {
    const eventId = await this.emit(event);
    
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Event processing timeout for ${event.type}`));
      }, timeout);

      const checkInterval = setInterval(async () => {
        const status = await this.convexClient.query(api.zeroTouch.getEventStatus, { eventId });
        if (status === "processed") {
          clearTimeout(timer);
          clearInterval(checkInterval);
          resolve();
        } else if (status === "failed") {
          clearTimeout(timer);
          clearInterval(checkInterval);
          reject(new Error(`Event processing failed for ${event.type}`));
        }
      }, 1000);
    });
  }

  // Start processing events
  async start(pollInterval = 1000): Promise<void> {
    if (this.isProcessing) {
      throw new Error("Event bus is already processing");
    }

    this.isProcessing = true;
    super.emit("started");

    this.processingInterval = setInterval(async () => {
      if (!this.isProcessing) return;

      try {
        await this.processEvents();
      } catch (error) {
        super.emit("processingError", { error });
      }
    }, pollInterval);
  }

  // Stop processing events
  async stop(): Promise<void> {
    this.isProcessing = false;

    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }

    super.emit("stopped");
  }

  // Process pending events
  private async processEvents(): Promise<void> {
    // Fetch pending events
    const events = await this.convexClient.query(api.zeroTouch.getPendingEvents, {
      limit: 10,
    });

    // Process each event
    for (const event of events) {
      if (!this.isProcessing) break;

      try {
        await this.processEvent(event);
      } catch (error) {
        super.emit("eventError", { event, error });
      }
    }
  }

  // Process single event
  private async processEvent(eventData: any): Promise<void> {
    const event: Event = {
      id: eventData._id,
      type: eventData.type,
      source: eventData.source,
      payload: eventData.payload,
      timestamp: eventData.createdAt,
      correlationId: eventData.correlationId,
      metadata: eventData.metadata || {},
    };

    // Update event status to processing
    await this.convexClient.mutation(api.zeroTouch.updateEventStatus, {
      eventId: eventData._id,
      status: "processing",
    });

    const matchingHandlers = this.getMatchingHandlers(event);
    const handlerResults: Array<{
      name: string;
      status: string;
      error?: string;
    }> = [];

    // Execute handlers
    for (const registration of matchingHandlers) {
      const startTime = Date.now();
      let status = "completed";
      let error: string | undefined;

      try {
        // Apply filter if exists
        if (registration.options?.filter && !registration.options.filter(event)) {
          continue;
        }

        // Execute handler with timeout
        const timeout = registration.options?.timeout || 30000;
        await this.executeWithTimeout(
          registration.handler(event),
          timeout
        );
      } catch (err) {
        status = "failed";
        error = err.message;
        super.emit("handlerError", { event, handler: registration.id, error: err });
      }

      handlerResults.push({
        name: registration.id,
        status,
        startedAt: startTime,
        completedAt: Date.now(),
        error,
      } as any);
    }

    // Update event status
    const hasFailures = handlerResults.some(r => r.status === "failed");
    await this.convexClient.mutation(api.zeroTouch.completeEvent, {
      eventId: eventData._id,
      status: hasFailures ? "failed" : "processed",
      handlers: handlerResults,
    });

    super.emit("eventProcessed", { event, results: handlerResults });
  }

  // Get matching handlers for event
  private getMatchingHandlers(event: Event): HandlerRegistration[] {
    const handlers: HandlerRegistration[] = [];

    // Exact match handlers
    const exactHandlers = this.handlers.get(event.type) || [];
    handlers.push(...exactHandlers);

    // Regex match handlers
    const regexHandlers = this.handlers.get("regex_handlers") || [];
    for (const registration of regexHandlers) {
      if (registration.eventType instanceof RegExp && registration.eventType.test(event.type)) {
        handlers.push(registration);
      }
    }

    // Wildcard handlers
    const wildcardHandlers = this.handlers.get("*") || [];
    handlers.push(...wildcardHandlers);

    // Sort by priority
    handlers.sort((a, b) => (b.options?.priority || 0) - (a.options?.priority || 0));

    return handlers;
  }

  // Execute with timeout
  private async executeWithTimeout(promise: Promise<any>, timeout: number): Promise<any> {
    return Promise.race([
      promise,
      new Promise((_, reject) => {
        setTimeout(() => reject(new Error("Handler timeout")), timeout);
      }),
    ]);
  }

  // Get correlated events
  async getCorrelatedEvents(correlationId: string): Promise<Event[]> {
    const eventIds = this.correlationMap.get(correlationId) || [];
    const events = await Promise.all(
      eventIds.map(id => this.convexClient.query(api.zeroTouch.getEvent, { eventId: id }))
    );
    
    return events.filter(Boolean).map(e => ({
      id: e._id,
      type: e.type,
      source: e.source,
      payload: e.payload,
      timestamp: e.createdAt,
      correlationId: e.correlationId,
      metadata: e.metadata || {},
    }));
  }

  // Replay events
  async replayEvents(filter: {
    types?: string[];
    source?: string;
    startTime?: number;
    endTime?: number;
    correlationId?: string;
  }): Promise<void> {
    const events = await this.convexClient.query(api.zeroTouch.queryEvents, filter);
    
    for (const event of events) {
      await this.processEvent(event);
    }
  }

  // Create event builder
  event(type: string): EventBuilder {
    return new EventBuilder(this, type);
  }
}

// Event builder for fluent API
export class EventBuilder {
  private eventBus: EventBus;
  private event: Partial<Event>;

  constructor(eventBus: EventBus, type: string) {
    this.eventBus = eventBus;
    this.event = {
      type,
      source: "system",
      timestamp: Date.now(),
      metadata: {
        tags: [],
      },
    };
  }

  source(source: string): this {
    this.event.source = source;
    return this;
  }

  payload(payload: any): this {
    this.event.payload = payload;
    return this;
  }

  correlate(correlationId: string): this {
    this.event.correlationId = correlationId;
    return this;
  }

  cause(causationId: string): this {
    this.event.causationId = causationId;
    return this;
  }

  tender(tenderId: Id<"tenders">): this {
    this.event.metadata!.tenderId = tenderId;
    return this;
  }

  workflow(workflowInstanceId: Id<"workflow_instances">): this {
    this.event.metadata!.workflowInstanceId = workflowInstanceId;
    return this;
  }

  user(userId: string): this {
    this.event.metadata!.userId = userId;
    return this;
  }

  tag(...tags: string[]): this {
    this.event.metadata!.tags!.push(...tags);
    return this;
  }

  async emit(): Promise<Id<"events">> {
    return this.eventBus.emit(this.event as Event);
  }

  async emitAndWait(timeout?: number): Promise<void> {
    return this.eventBus.emitAndWait(this.event as Event, timeout);
  }
}

// Common event types
export const EventTypes = {
  // Tender events
  TENDER_CREATED: "tender.created",
  TENDER_UPDATED: "tender.updated",
  TENDER_ASSIGNED: "tender.assigned",
  TENDER_DEADLINE_APPROACHING: "tender.deadline.approaching",
  TENDER_COMPLETED: "tender.completed",

  // Workflow events
  WORKFLOW_STARTED: "workflow.started",
  WORKFLOW_STEP_COMPLETED: "workflow.step.completed",
  WORKFLOW_FAILED: "workflow.failed",
  WORKFLOW_COMPLETED: "workflow.completed",

  // Agent events
  AGENT_TASK_ASSIGNED: "agent.task.assigned",
  AGENT_TASK_STARTED: "agent.task.started",
  AGENT_TASK_COMPLETED: "agent.task.completed",
  AGENT_TASK_FAILED: "agent.task.failed",

  // Integration events
  EMAIL_RECEIVED: "integration.email.received",
  CALENDAR_EVENT_CREATED: "integration.calendar.event.created",
  SMS_SENT: "integration.sms.sent",
  SLACK_MESSAGE_RECEIVED: "integration.slack.message.received",

  // System events
  SYSTEM_ERROR: "system.error",
  SYSTEM_WARNING: "system.warning",
  SYSTEM_HEALTH_CHECK: "system.health.check",
} as const;