import { ConvexClient } from "convex/browser";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { EventEmitter } from "events";

export interface QueueMessage {
  id?: Id<"message_queue">;
  queue: string;
  payload: any;
  headers?: Record<string, any>;
  priority?: number;
  scheduledFor?: number;
  metadata?: {
    correlationId?: string;
    causationId?: string;
    tenderId?: Id<"tenders">;
    workflowInstanceId?: Id<"workflow_instances">;
    tags?: string[];
  };
}

export interface QueueConfig {
  name: string;
  concurrency: number;
  retryPolicy: {
    maxRetries: number;
    backoffType: "exponential" | "linear" | "constant";
    initialDelay: number;
  };
  visibilityTimeout: number;
  deadLetterQueue?: string;
  deadLetterThreshold?: number;
}

export type MessageHandler = (message: QueueMessage) => Promise<any>;

export class MessageQueue extends EventEmitter {
  private convexClient: ConvexClient;
  private handlers: Map<string, MessageHandler>;
  private pollingIntervals: Map<string, NodeJS.Timeout>;
  private processingMessages: Map<string, Set<Id<"message_queue">>>;
  private config: Map<string, QueueConfig>;
  private isRunning: boolean;

  constructor(convexClient: ConvexClient) {
    super();
    this.convexClient = convexClient;
    this.handlers = new Map();
    this.pollingIntervals = new Map();
    this.processingMessages = new Map();
    this.config = new Map();
    this.isRunning = false;
  }

  // Register queue configuration
  registerQueue(config: QueueConfig): void {
    this.config.set(config.name, config);
    this.processingMessages.set(config.name, new Set());
  }

  // Register message handler
  registerHandler(queue: string, handler: MessageHandler): void {
    this.handlers.set(queue, handler);
    this.emit("handlerRegistered", { queue });
  }

  // Enqueue message
  async enqueue(message: QueueMessage): Promise<Id<"message_queue">> {
    try {
      const messageId = await this.convexClient.mutation(api.zeroTouch.enqueueMessage, {
        queue: message.queue,
        payload: message.payload,
        headers: message.headers || {},
        priority: message.priority || 5,
        scheduledFor: message.scheduledFor,
        metadata: message.metadata || {},
      });

      this.emit("messageEnqueued", { messageId, queue: message.queue });
      return messageId;
    } catch (error) {
      this.emit("enqueueError", { error, message });
      throw error;
    }
  }

  // Batch enqueue messages
  async enqueueBatch(messages: QueueMessage[]): Promise<Id<"message_queue">[]> {
    const results = await Promise.allSettled(
      messages.map(msg => this.enqueue(msg))
    );

    const messageIds: Id<"message_queue">[] = [];
    const errors: any[] = [];

    results.forEach((result, index) => {
      if (result.status === "fulfilled") {
        messageIds.push(result.value);
      } else {
        errors.push({ message: messages[index], error: result.reason });
      }
    });

    if (errors.length > 0) {
      this.emit("batchEnqueueErrors", { errors });
    }

    return messageIds;
  }

  // Start processing messages
  async start(): Promise<void> {
    if (this.isRunning) {
      throw new Error("Message queue is already running");
    }

    this.isRunning = true;
    this.emit("started");

    // Start polling for each registered queue
    for (const [queueName, config] of this.config) {
      this.startQueuePolling(queueName, config);
    }
  }

  // Stop processing messages
  async stop(): Promise<void> {
    this.isRunning = false;

    // Clear all polling intervals
    for (const [queue, interval] of this.pollingIntervals) {
      clearInterval(interval);
    }
    this.pollingIntervals.clear();

    // Wait for all processing messages to complete
    const waitPromises: Promise<void>[] = [];
    for (const [queue, messageIds] of this.processingMessages) {
      if (messageIds.size > 0) {
        waitPromises.push(this.waitForQueueCompletion(queue));
      }
    }

    await Promise.all(waitPromises);
    this.emit("stopped");
  }

  // Start polling for a specific queue
  private startQueuePolling(queueName: string, config: QueueConfig): void {
    const pollInterval = setInterval(async () => {
      if (!this.isRunning) return;

      try {
        await this.processQueue(queueName, config);
      } catch (error) {
        this.emit("pollError", { queue: queueName, error });
      }
    }, 1000); // Poll every second

    this.pollingIntervals.set(queueName, pollInterval);
  }

  // Process messages from a queue
  private async processQueue(queueName: string, config: QueueConfig): Promise<void> {
    const processingSet = this.processingMessages.get(queueName)!;
    const availableSlots = config.concurrency - processingSet.size;

    if (availableSlots <= 0) return;

    // Fetch messages from the queue
    const messages = await this.convexClient.query(api.zeroTouch.dequeueMessages, {
      queue: queueName,
      limit: availableSlots,
      visibilityTimeout: config.visibilityTimeout,
    });

    // Process each message
    for (const message of messages) {
      if (!this.isRunning) break;

      processingSet.add(message._id);
      this.processMessage(queueName, message, config)
        .finally(() => {
          processingSet.delete(message._id);
        });
    }
  }

  // Process a single message
  private async processMessage(
    queueName: string,
    message: any,
    config: QueueConfig
  ): Promise<void> {
    const handler = this.handlers.get(queueName);
    if (!handler) {
      await this.moveToDeadLetter(message, "No handler registered");
      return;
    }

    const startTime = Date.now();

    try {
      // Update message status to processing
      await this.convexClient.mutation(api.zeroTouch.updateMessageStatus, {
        messageId: message._id,
        status: "processing",
      });

      // Execute handler
      const result = await handler({
        id: message._id,
        queue: message.queue,
        payload: message.payload,
        headers: message.headers,
        priority: message.priority,
        metadata: message.metadata,
      });

      // Mark message as completed
      await this.convexClient.mutation(api.zeroTouch.completeMessage, {
        messageId: message._id,
        result,
      });

      this.emit("messageProcessed", {
        messageId: message._id,
        queue: queueName,
        duration: Date.now() - startTime,
      });
    } catch (error) {
      await this.handleProcessingError(message, error, config);
    }
  }

  // Handle message processing error
  private async handleProcessingError(
    message: any,
    error: any,
    config: QueueConfig
  ): Promise<void> {
    const retryCount = message.retryCount || 0;

    if (retryCount >= config.retryPolicy.maxRetries) {
      // Move to dead letter queue
      await this.moveToDeadLetter(message, error);
    } else {
      // Calculate backoff delay
      const delay = this.calculateBackoffDelay(
        retryCount + 1,
        config.retryPolicy.backoffType,
        config.retryPolicy.initialDelay
      );

      // Schedule retry
      await this.convexClient.mutation(api.zeroTouch.retryMessage, {
        messageId: message._id,
        error: {
          message: error.message,
          code: error.code || "PROCESSING_ERROR",
          stack: error.stack,
        },
        scheduledFor: Date.now() + delay,
      });

      this.emit("messageRetryScheduled", {
        messageId: message._id,
        queue: message.queue,
        retryCount: retryCount + 1,
        delay,
      });
    }
  }

  // Move message to dead letter queue
  private async moveToDeadLetter(message: any, error: any): Promise<void> {
    const queueConfig = this.config.get(message.queue);
    const deadLetterQueue = queueConfig?.deadLetterQueue || `${message.queue}_dlq`;

    await this.convexClient.mutation(api.zeroTouch.moveToDeadLetter, {
      messageId: message._id,
      deadLetterQueue,
      error: typeof error === "string" ? { message: error } : {
        message: error.message,
        code: error.code || "PROCESSING_FAILED",
        stack: error.stack,
      },
    });

    this.emit("messageMovedToDeadLetter", {
      messageId: message._id,
      originalQueue: message.queue,
      deadLetterQueue,
    });
  }

  // Calculate backoff delay
  private calculateBackoffDelay(
    retryCount: number,
    backoffType: string,
    initialDelay: number
  ): number {
    switch (backoffType) {
      case "exponential":
        return initialDelay * Math.pow(2, retryCount - 1);
      case "linear":
        return initialDelay * retryCount;
      case "constant":
      default:
        return initialDelay;
    }
  }

  // Wait for queue to complete processing
  private async waitForQueueCompletion(queueName: string): Promise<void> {
    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        const processingSet = this.processingMessages.get(queueName);
        if (!processingSet || processingSet.size === 0) {
          clearInterval(checkInterval);
          resolve();
        }
      }, 100);
    });
  }

  // Get queue statistics
  async getQueueStats(queueName: string): Promise<any> {
    return await this.convexClient.query(api.zeroTouch.getQueueStats, {
      queue: queueName,
    });
  }

  // Purge queue
  async purgeQueue(queueName: string): Promise<number> {
    const count = await this.convexClient.mutation(api.zeroTouch.purgeQueue, {
      queue: queueName,
    });

    this.emit("queuePurged", { queue: queueName, count });
    return count;
  }

  // Schedule message
  async scheduleMessage(
    message: QueueMessage,
    scheduleTime: Date | number
  ): Promise<Id<"message_queue">> {
    const scheduledFor = typeof scheduleTime === "number" 
      ? scheduleTime 
      : scheduleTime.getTime();

    return await this.enqueue({
      ...message,
      scheduledFor,
    });
  }

  // Create delayed message helper
  delay(queue: string, payload: any, delayMs: number): Promise<Id<"message_queue">> {
    return this.scheduleMessage(
      { queue, payload },
      Date.now() + delayMs
    );
  }

  // Create priority message helper
  priority(
    queue: string,
    payload: any,
    priority: number
  ): Promise<Id<"message_queue">> {
    return this.enqueue({ queue, payload, priority });
  }

  // Create correlated message helper
  correlate(
    queue: string,
    payload: any,
    correlationId: string,
    causationId?: string
  ): Promise<Id<"message_queue">> {
    return this.enqueue({
      queue,
      payload,
      metadata: { correlationId, causationId },
    });
  }
}

// Queue builder for fluent API
export class QueueBuilder {
  private queue: MessageQueue;
  private config: Partial<QueueConfig>;

  constructor(queue: MessageQueue, name: string) {
    this.queue = queue;
    this.config = { name };
  }

  concurrency(value: number): this {
    this.config.concurrency = value;
    return this;
  }

  retries(maxRetries: number, backoffType: QueueConfig["retryPolicy"]["backoffType"] = "exponential", initialDelay = 1000): this {
    this.config.retryPolicy = { maxRetries, backoffType, initialDelay };
    return this;
  }

  visibility(timeout: number): this {
    this.config.visibilityTimeout = timeout;
    return this;
  }

  deadLetter(queueName: string, threshold = 3): this {
    this.config.deadLetterQueue = queueName;
    this.config.deadLetterThreshold = threshold;
    return this;
  }

  handler(handler: MessageHandler): this {
    this.queue.registerHandler(this.config.name!, handler);
    return this;
  }

  build(): void {
    const finalConfig: QueueConfig = {
      name: this.config.name!,
      concurrency: this.config.concurrency || 1,
      retryPolicy: this.config.retryPolicy || {
        maxRetries: 3,
        backoffType: "exponential",
        initialDelay: 1000,
      },
      visibilityTimeout: this.config.visibilityTimeout || 30000,
      deadLetterQueue: this.config.deadLetterQueue,
      deadLetterThreshold: this.config.deadLetterThreshold,
    };

    this.queue.registerQueue(finalConfig);
  }
}

// Helper to create queue builder
export function createQueue(messageQueue: MessageQueue, name: string): QueueBuilder {
  return new QueueBuilder(messageQueue, name);
}