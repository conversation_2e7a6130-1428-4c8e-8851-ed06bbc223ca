import { z } from "zod";

// Error codes enum
export enum ErrorCode {
  // System errors
  SYSTEM_ERROR = "SYSTEM_ERROR",
  INITIALIZATION_ERROR = "INITIALIZATION_ERROR",
  CONFIGURATION_ERROR = "CONFIGURATION_ERROR",
  
  // Workflow errors
  WORKFLOW_NOT_FOUND = "WORKFLOW_NOT_FOUND",
  WORKFLOW_EXECUTION_ERROR = "WORKFLOW_EXECUTION_ERROR",
  WORKFLOW_TIMEOUT = "WORKFLOW_TIMEOUT",
  WORKFLOW_CANCELLED = "WORKFLOW_CANCELLED",
  
  // Agent errors
  AGENT_NOT_FOUND = "AGENT_NOT_FOUND",
  AGENT_OVERLOADED = "AGENT_OVERLOADED",
  AGENT_TASK_FAILED = "AGENT_TASK_FAILED",
  AGENT_VALIDATION_ERROR = "AGENT_VALIDATION_ERROR",
  
  // Integration errors
  INTEGRATION_AUTH_ERROR = "INTEGRATION_AUTH_ERROR",
  INTEGRATION_API_ERROR = "INTEGRATION_API_ERROR",
  INTEGRATION_RATE_LIMIT = "INTEGRATION_RATE_LIMIT",
  INTEGRATION_TIMEOUT = "INTEGRATION_TIMEOUT",
  
  // Queue errors
  QUEUE_FULL = "QUEUE_FULL",
  MESSAGE_PROCESSING_ERROR = "MESSAGE_PROCESSING_ERROR",
  MESSAGE_EXPIRED = "MESSAGE_EXPIRED",
  
  // Validation errors
  VALIDATION_ERROR = "VALIDATION_ERROR",
  SCHEMA_MISMATCH = "SCHEMA_MISMATCH",
  REQUIRED_FIELD_MISSING = "REQUIRED_FIELD_MISSING",
}

// Error severity levels
export enum ErrorSeverity {
  LOW = "low",
  MEDIUM = "medium",
  HIGH = "high",
  CRITICAL = "critical",
}

// Base error class
export class ZeroTouchError extends Error {
  code: ErrorCode;
  severity: ErrorSeverity;
  details: Record<string, any>;
  timestamp: number;
  correlationId?: string;
  retryable: boolean;

  constructor(
    message: string,
    code: ErrorCode,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    details: Record<string, any> = {},
    retryable = true
  ) {
    super(message);
    this.name = "ZeroTouchError";
    this.code = code;
    this.severity = severity;
    this.details = details;
    this.timestamp = Date.now();
    this.retryable = retryable;
    
    // Maintain proper stack trace
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      code: this.code,
      severity: this.severity,
      details: this.details,
      timestamp: this.timestamp,
      correlationId: this.correlationId,
      retryable: this.retryable,
      stack: this.stack,
    };
  }
}

// Specific error classes
export class WorkflowError extends ZeroTouchError {
  workflowId?: string;
  instanceId?: string;
  stepId?: string;

  constructor(
    message: string,
    code: ErrorCode,
    details: {
      workflowId?: string;
      instanceId?: string;
      stepId?: string;
      [key: string]: any;
    } = {}
  ) {
    super(message, code, ErrorSeverity.HIGH, details);
    this.name = "WorkflowError";
    this.workflowId = details.workflowId;
    this.instanceId = details.instanceId;
    this.stepId = details.stepId;
  }
}

export class AgentError extends ZeroTouchError {
  agentId?: string;
  taskId?: string;

  constructor(
    message: string,
    code: ErrorCode,
    details: {
      agentId?: string;
      taskId?: string;
      [key: string]: any;
    } = {}
  ) {
    super(message, code, ErrorSeverity.MEDIUM, details);
    this.name = "AgentError";
    this.agentId = details.agentId;
    this.taskId = details.taskId;
  }
}

export class IntegrationError extends ZeroTouchError {
  integration: string;
  statusCode?: number;
  responseBody?: any;

  constructor(
    message: string,
    code: ErrorCode,
    integration: string,
    details: {
      statusCode?: number;
      responseBody?: any;
      [key: string]: any;
    } = {}
  ) {
    super(message, code, ErrorSeverity.HIGH, details);
    this.name = "IntegrationError";
    this.integration = integration;
    this.statusCode = details.statusCode;
    this.responseBody = details.responseBody;

    // Determine if retryable based on status code
    if (this.statusCode) {
      this.retryable = this.statusCode >= 500 || this.statusCode === 429;
    }
  }
}

export class ValidationError extends ZeroTouchError {
  validationErrors: Array<{
    field: string;
    message: string;
    value?: any;
  }>;

  constructor(
    message: string,
    validationErrors: Array<{
      field: string;
      message: string;
      value?: any;
    }>
  ) {
    super(
      message,
      ErrorCode.VALIDATION_ERROR,
      ErrorSeverity.LOW,
      { validationErrors },
      false // Validation errors are not retryable
    );
    this.name = "ValidationError";
    this.validationErrors = validationErrors;
  }

  static fromZodError(error: z.ZodError): ValidationError {
    const validationErrors = error.errors.map(err => ({
      field: err.path.join("."),
      message: err.message,
      value: err.code,
    }));

    return new ValidationError(
      "Validation failed",
      validationErrors
    );
  }
}

// Retry configuration
export interface RetryConfig {
  maxRetries: number;
  backoffType: "exponential" | "linear" | "constant";
  initialDelay: number;
  maxDelay?: number;
  factor?: number;
  jitter?: boolean;
  retryableErrors?: ErrorCode[];
  onRetry?: (error: Error, attempt: number) => void;
}

// Default retry configurations
export const DefaultRetryConfigs: Record<string, RetryConfig> = {
  workflow: {
    maxRetries: 3,
    backoffType: "exponential",
    initialDelay: 1000,
    maxDelay: 30000,
    factor: 2,
    jitter: true,
    retryableErrors: [
      ErrorCode.WORKFLOW_TIMEOUT,
      ErrorCode.SYSTEM_ERROR,
    ],
  },
  agent: {
    maxRetries: 2,
    backoffType: "exponential",
    initialDelay: 500,
    maxDelay: 10000,
    factor: 2,
    retryableErrors: [
      ErrorCode.AGENT_TASK_FAILED,
      ErrorCode.SYSTEM_ERROR,
    ],
  },
  integration: {
    maxRetries: 3,
    backoffType: "exponential",
    initialDelay: 1000,
    maxDelay: 60000,
    factor: 2,
    jitter: true,
    retryableErrors: [
      ErrorCode.INTEGRATION_API_ERROR,
      ErrorCode.INTEGRATION_TIMEOUT,
      ErrorCode.INTEGRATION_RATE_LIMIT,
    ],
  },
  queue: {
    maxRetries: 5,
    backoffType: "exponential",
    initialDelay: 2000,
    maxDelay: 120000,
    factor: 2,
    retryableErrors: [
      ErrorCode.MESSAGE_PROCESSING_ERROR,
      ErrorCode.QUEUE_FULL,
    ],
  },
};

// Retry mechanism
export class RetryHandler {
  private config: RetryConfig;

  constructor(config: RetryConfig) {
    this.config = config;
  }

  async execute<T>(
    fn: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T> {
    let lastError: Error | undefined;

    for (let attempt = 0; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;

        // Check if error is retryable
        if (!this.isRetryable(error)) {
          throw error;
        }

        // Check if we've exhausted retries
        if (attempt === this.config.maxRetries) {
          throw new ZeroTouchError(
            `Max retries (${this.config.maxRetries}) exceeded: ${lastError.message}`,
            ErrorCode.SYSTEM_ERROR,
            ErrorSeverity.HIGH,
            {
              originalError: lastError,
              attempts: attempt + 1,
              context,
            },
            false
          );
        }

        // Calculate delay
        const delay = this.calculateDelay(attempt);

        // Call onRetry callback if provided
        if (this.config.onRetry) {
          this.config.onRetry(lastError, attempt + 1);
        }

        // Wait before retrying
        await this.sleep(delay);
      }
    }

    throw lastError;
  }

  private isRetryable(error: any): boolean {
    if (error instanceof ZeroTouchError) {
      if (!error.retryable) return false;

      if (this.config.retryableErrors && this.config.retryableErrors.length > 0) {
        return this.config.retryableErrors.includes(error.code);
      }

      return true;
    }

    // Retry on network errors
    if (error.code === "ECONNREFUSED" || error.code === "ETIMEDOUT") {
      return true;
    }

    return false;
  }

  private calculateDelay(attempt: number): number {
    let delay: number;

    switch (this.config.backoffType) {
      case "exponential":
        const factor = this.config.factor || 2;
        delay = this.config.initialDelay * Math.pow(factor, attempt);
        break;

      case "linear":
        delay = this.config.initialDelay * (attempt + 1);
        break;

      case "constant":
      default:
        delay = this.config.initialDelay;
    }

    // Apply max delay cap
    if (this.config.maxDelay) {
      delay = Math.min(delay, this.config.maxDelay);
    }

    // Apply jitter if enabled
    if (this.config.jitter) {
      const jitterRange = delay * 0.1; // 10% jitter
      const jitter = Math.random() * jitterRange * 2 - jitterRange;
      delay += jitter;
    }

    return Math.max(0, delay);
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Error boundary wrapper
export function errorBoundary<T extends (...args: any[]) => any>(
  fn: T,
  errorHandler?: (error: Error) => void
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      if (errorHandler) {
        errorHandler(error as Error);
      }

      // Re-throw ZeroTouchError as is
      if (error instanceof ZeroTouchError) {
        throw error;
      }

      // Wrap other errors
      throw new ZeroTouchError(
        error.message || "An unexpected error occurred",
        ErrorCode.SYSTEM_ERROR,
        ErrorSeverity.HIGH,
        {
          originalError: error,
          stack: error.stack,
        }
      );
    }
  }) as T;
}

// Circuit breaker implementation
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: "closed" | "open" | "half-open" = "closed";

  constructor(
    private readonly threshold: number = 5,
    private readonly timeout: number = 60000, // 1 minute
    private readonly resetTimeout: number = 30000 // 30 seconds
  ) {}

  async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === "open") {
      if (Date.now() - this.lastFailureTime > this.timeout) {
        this.state = "half-open";
      } else {
        throw new ZeroTouchError(
          "Circuit breaker is open",
          ErrorCode.SYSTEM_ERROR,
          ErrorSeverity.HIGH,
          {
            failures: this.failures,
            lastFailureTime: this.lastFailureTime,
          },
          false
        );
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    if (this.state === "half-open") {
      this.state = "closed";
    }
    this.failures = 0;
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();

    if (this.failures >= this.threshold) {
      this.state = "open";
    }
  }

  getState(): string {
    return this.state;
  }

  reset(): void {
    this.failures = 0;
    this.lastFailureTime = 0;
    this.state = "closed";
  }
}

// Global error handler
export class GlobalErrorHandler {
  private static handlers: Map<ErrorCode, (error: ZeroTouchError) => void> = new Map();

  static register(code: ErrorCode, handler: (error: ZeroTouchError) => void): void {
    this.handlers.set(code, handler);
  }

  static handle(error: Error): void {
    if (error instanceof ZeroTouchError) {
      const handler = this.handlers.get(error.code);
      if (handler) {
        handler(error);
      } else {
        console.error(`Unhandled error code: ${error.code}`, error);
      }
    } else {
      console.error("Unhandled error:", error);
    }
  }
}