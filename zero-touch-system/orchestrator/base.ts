import { ConvexClient } from "convex/browser";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { EventEmitter } from "events";
import { StateGraph, END } from "@langchain/langgraph";
import { BaseMessage, HumanMessage, SystemMessage } from "@langchain/core/messages";
import { z } from "zod";

// Workflow State Schema
export const WorkflowStateSchema = z.object({
  workflowId: z.string(),
  instanceId: z.string().optional(),
  status: z.enum(["pending", "running", "completed", "failed", "cancelled"]),
  currentStep: z.string().optional(),
  progress: z.number().min(0).max(100),
  context: z.object({
    variables: z.record(z.any()),
    artifacts: z.array(z.object({
      name: z.string(),
      type: z.string(),
      url: z.string().optional(),
      data: z.any().optional(),
    })),
  }),
  steps: z.array(z.object({
    stepId: z.string(),
    status: z.enum(["pending", "running", "completed", "failed", "skipped"]),
    startedAt: z.number().optional(),
    completedAt: z.number().optional(),
    agentId: z.string().optional(),
    input: z.record(z.any()),
    output: z.record(z.any()).optional(),
    error: z.string().optional(),
    retryCount: z.number().default(0),
  })),
  error: z.object({
    message: z.string(),
    step: z.string(),
    code: z.string(),
    details: z.record(z.any()),
  }).optional(),
  metadata: z.object({
    priority: z.enum(["low", "medium", "high", "critical"]),
    dueDate: z.number().optional(),
    tags: z.array(z.string()),
    customFields: z.record(z.any()),
  }),
});

export type WorkflowState = z.infer<typeof WorkflowStateSchema>;

// Base Orchestrator Class
export abstract class BaseOrchestrator extends EventEmitter {
  protected convexClient: ConvexClient;
  protected graph: StateGraph<WorkflowState>;
  protected state: WorkflowState;
  protected abortController: AbortController;

  constructor(convexClient: ConvexClient) {
    super();
    this.convexClient = convexClient;
    this.abortController = new AbortController();
    this.graph = new StateGraph<WorkflowState>({
      channels: WorkflowStateSchema.shape,
    });
  }

  // Abstract methods to be implemented by specific orchestrators
  abstract buildGraph(): void;
  abstract validateInput(input: any): Promise<boolean>;
  abstract getWorkflowType(): string;

  // Initialize workflow instance
  async initialize(workflowId: Id<"workflows">, triggerData: any): Promise<string> {
    try {
      // Create workflow instance in database
      const instanceId = await this.convexClient.mutation(api.zeroTouch.createWorkflowInstance, {
        workflowId,
        triggeredBy: "system",
        triggerData,
      });

      // Initialize state
      this.state = {
        workflowId,
        instanceId,
        status: "pending",
        progress: 0,
        context: {
          variables: {},
          artifacts: [],
        },
        steps: [],
        metadata: {
          priority: "medium",
          tags: [],
          customFields: {},
        },
      };

      // Build the workflow graph
      this.buildGraph();

      this.emit("initialized", { workflowId, instanceId });
      return instanceId;
    } catch (error) {
      this.emit("error", { error, phase: "initialization" });
      throw error;
    }
  }

  // Execute the workflow
  async execute(): Promise<WorkflowState> {
    try {
      this.state.status = "running";
      this.emit("started", { state: this.state });

      // Update instance status
      await this.updateInstanceStatus("running");

      // Compile and run the graph
      const app = this.graph.compile();
      const result = await app.invoke(this.state, {
        signal: this.abortController.signal,
      });

      this.state = result;
      this.state.status = "completed";
      
      await this.updateInstanceStatus("completed");
      this.emit("completed", { state: this.state });

      return this.state;
    } catch (error) {
      this.state.status = "failed";
      this.state.error = {
        message: error.message,
        step: this.state.currentStep || "unknown",
        code: "EXECUTION_ERROR",
        details: { error: error.toString() },
      };

      await this.updateInstanceStatus("failed", error);
      this.emit("failed", { state: this.state, error });
      throw error;
    }
  }

  // Cancel workflow execution
  async cancel(): Promise<void> {
    this.abortController.abort();
    this.state.status = "cancelled";
    await this.updateInstanceStatus("cancelled");
    this.emit("cancelled", { state: this.state });
  }

  // Update workflow instance in database
  protected async updateInstanceStatus(status: string, error?: any): Promise<void> {
    if (!this.state.instanceId) return;

    await this.convexClient.mutation(api.zeroTouch.updateWorkflowInstance, {
      instanceId: this.state.instanceId as Id<"workflow_instances">,
      status,
      currentStep: this.state.currentStep,
      progress: this.state.progress,
      context: this.state.context,
      steps: this.state.steps,
      error: error ? {
        message: error.message,
        step: this.state.currentStep || "unknown",
        code: error.code || "UNKNOWN_ERROR",
        details: error.details || {},
      } : undefined,
    });
  }

  // Add step to workflow
  protected addStep(stepConfig: {
    id: string;
    name: string;
    handler: (state: WorkflowState) => Promise<Partial<WorkflowState>>;
    dependencies?: string[];
    timeout?: number;
    retryPolicy?: {
      maxRetries: number;
      backoffType: string;
      initialDelay: number;
    };
  }): void {
    const { id, handler, timeout = 300000 } = stepConfig; // Default 5 min timeout

    this.graph.addNode(id, async (state: WorkflowState) => {
      const stepIndex = state.steps.findIndex(s => s.stepId === id);
      const step = stepIndex >= 0 ? state.steps[stepIndex] : {
        stepId: id,
        status: "pending" as const,
        input: {},
        retryCount: 0,
      };

      try {
        // Update step status to running
        step.status = "running";
        step.startedAt = Date.now();
        state.currentStep = id;
        
        if (stepIndex >= 0) {
          state.steps[stepIndex] = step;
        } else {
          state.steps.push(step);
        }

        this.emit("stepStarted", { stepId: id, state });

        // Execute step with timeout
        const timeoutPromise = new Promise<never>((_, reject) => {
          setTimeout(() => reject(new Error(`Step ${id} timed out`)), timeout);
        });

        const result = await Promise.race([
          handler(state),
          timeoutPromise,
        ]);

        // Update step status to completed
        step.status = "completed";
        step.completedAt = Date.now();
        step.output = result as any;

        // Update progress
        const completedSteps = state.steps.filter(s => s.status === "completed").length;
        state.progress = Math.round((completedSteps / state.steps.length) * 100);

        this.emit("stepCompleted", { stepId: id, state, result });

        return { ...state, ...result };
      } catch (error) {
        step.status = "failed";
        step.error = error.message;
        step.retryCount++;

        this.emit("stepFailed", { stepId: id, state, error });

        // Check retry policy
        if (stepConfig.retryPolicy && step.retryCount < stepConfig.retryPolicy.maxRetries) {
          // Calculate backoff delay
          const delay = this.calculateBackoffDelay(
            step.retryCount,
            stepConfig.retryPolicy.backoffType,
            stepConfig.retryPolicy.initialDelay
          );

          await new Promise(resolve => setTimeout(resolve, delay));
          
          // Retry the step
          return this.graph.nodes[id](state);
        }

        throw error;
      }
    });
  }

  // Calculate backoff delay for retries
  private calculateBackoffDelay(retryCount: number, backoffType: string, initialDelay: number): number {
    switch (backoffType) {
      case "exponential":
        return initialDelay * Math.pow(2, retryCount - 1);
      case "linear":
        return initialDelay * retryCount;
      case "constant":
      default:
        return initialDelay;
    }
  }

  // Add conditional edge
  protected addConditionalEdge(
    fromNode: string,
    condition: (state: WorkflowState) => string | typeof END,
    edgeMap: Record<string, string>
  ): void {
    this.graph.addConditionalEdges(fromNode, condition, edgeMap);
  }

  // Add simple edge
  protected addEdge(from: string, to: string): void {
    this.graph.addEdge(from, to);
  }

  // Set entry point
  protected setEntryPoint(node: string): void {
    this.graph.setEntryPoint(node);
  }

  // Get current state
  getState(): WorkflowState {
    return { ...this.state };
  }

  // Update context variables
  updateContext(updates: Partial<WorkflowState["context"]>): void {
    this.state.context = {
      ...this.state.context,
      ...updates,
      variables: {
        ...this.state.context.variables,
        ...(updates.variables || {}),
      },
      artifacts: [
        ...this.state.context.artifacts,
        ...(updates.artifacts || []),
      ],
    };
  }

  // Add artifact
  addArtifact(artifact: {
    name: string;
    type: string;
    url?: string;
    data?: any;
  }): void {
    this.state.context.artifacts.push(artifact);
  }

  // Log audit event
  protected async logAudit(action: string, details: any): Promise<void> {
    await this.convexClient.mutation(api.zeroTouch.createAuditLog, {
      action,
      entityType: "workflow_instance",
      entityId: this.state.instanceId || "",
      metadata: {
        workflowInstanceId: this.state.instanceId as Id<"workflow_instances">,
        context: details,
        tags: this.state.metadata.tags,
      },
    });
  }

  // Send notification
  protected async sendNotification(notification: {
    type: string;
    channel: string;
    recipient: any;
    subject: string;
    content: any;
    priority?: string;
  }): Promise<void> {
    await this.convexClient.mutation(api.zeroTouch.createNotification, {
      ...notification,
      metadata: {
        workflowInstanceId: this.state.instanceId as Id<"workflow_instances">,
        tags: this.state.metadata.tags,
      },
    });
  }

  // Queue message
  protected async queueMessage(message: {
    queue: string;
    payload: any;
    priority?: number;
    scheduledFor?: number;
  }): Promise<void> {
    await this.convexClient.mutation(api.zeroTouch.enqueueMessage, {
      ...message,
      metadata: {
        workflowInstanceId: this.state.instanceId as Id<"workflow_instances">,
        tags: this.state.metadata.tags,
      },
    });
  }
}