# 🌐 Tailscale GPU Monitoring Setup

## Overview

Your RTX 4090 GPU monitoring system is now available securely via Tailscale, allowing you to monitor and control your GPU from anywhere in the world.

## 🔗 Access URLs

### Tailscale Network Access
- **GPU Backend API**: `http://*************:9092`
- **Health Check**: `http://*************:9092/health`
- **GPU Status**: `http://*************:9092/api/gpu/status`
- **WebSocket**: `ws://*************:9092/ws/gpu-monitor`
- **API Docs**: `http://*************:9092/docs`

### Local Network Access (Fallback)
- **GPU Backend API**: `http://*************:9092`
- **Health Check**: `http://*************:9092/health`

## 🎮 Frontend Integration

The Zero-Touch Tender System frontend automatically detects and connects to your GPU via Tailscale:

1. **Primary Connection**: Tailscale IP (`*************`)
2. **Fallback Connection**: Local IP (`*************`)
3. **Real-time Updates**: WebSocket connection for live monitoring

## 🚀 Quick Test Commands

```bash
# Test GPU backend health via Tailscale
curl http://*************:9092/health

# Get real-time GPU status
curl http://*************:9092/api/gpu/status | jq

# Execute nvidia-smi command remotely
curl -X POST http://*************:9092/api/gpu/execute \
  -H "Content-Type: application/json" \
  -d '{"command": "nvidia-smi --query-gpu=temperature.gpu,utilization.gpu --format=csv,noheader"}'
```

## 📊 Available Endpoints

### Health Check
```bash
GET /health
# Returns: {"status":"healthy","gpu_available":true,"gpu_name":"NVIDIA GeForce RTX 4090"}
```

### GPU Status
```bash
GET /api/gpu/status
# Returns: Complete GPU metrics including temperature, memory, utilization, processes
```

### Command Execution
```bash
POST /api/gpu/execute
Content-Type: application/json
{
  "command": "nvidia-smi --query-gpu=name --format=csv,noheader"
}
```

### WebSocket Real-time Monitoring
```javascript
const ws = new WebSocket('ws://*************:9092/ws/gpu-monitor');
ws.onmessage = (event) => {
  const data = JSON.parse(event.data);
  console.log('GPU Update:', data);
};
```

## 🔧 Management Commands

### Start/Stop GPU Backend
```bash
# Start GPU backend
ssh root@************* "cd /root/gpu-backend && source venv/bin/activate && nohup python gpu-backend-server.py > gpu-backend.log 2>&1 &"

# Stop GPU backend
ssh root@************* "pkill -f gpu-backend-server"

# Check status
ssh root@************* "ps aux | grep gpu-backend-server"
```

### View Logs
```bash
# Real-time logs
ssh root@************* "cd /root/gpu-backend && tail -f gpu-backend.log"

# System logs
ssh root@************* "journalctl -u gpu-backend.service -f"
```

### Restart Services
```bash
# Restart GPU backend
ssh root@************* "cd /root/gpu-backend && pkill -f gpu-backend-server && source venv/bin/activate && nohup python gpu-backend-server.py > gpu-backend.log 2>&1 &"
```

## 🌍 Remote Access Benefits

### Secure Access
- **Encrypted Connection**: All traffic encrypted via Tailscale VPN
- **Zero-Config**: No port forwarding or firewall changes needed
- **Device Authentication**: Only your Tailscale devices can access

### Global Availability
- Monitor your RTX 4090 from anywhere in the world
- Mobile-friendly web interface
- Real-time notifications and alerts

### Network Resilience
- Automatic failover between connection methods
- Works behind NAT and firewalls
- Persistent connection management

## 📱 Mobile Access

Access your GPU monitor from any device on your Tailscale network:

1. **iPhone/Android**: Open Safari/Chrome and navigate to `http://*************:9092`
2. **iPad/Tablet**: Full responsive dashboard interface
3. **Laptop**: Complete monitoring and control interface

## 🔒 Security Features

### Network Security
- **Private Network**: Only accessible via your Tailscale tailnet
- **Device Authentication**: Each device must be authorized
- **Traffic Encryption**: End-to-end encryption for all communications

### API Security
- **Command Filtering**: Only allowed GPU commands can be executed
- **Input Validation**: All API inputs are validated and sanitized
- **Rate Limiting**: Prevents abuse and overload

## 🎯 Use Cases

### Remote Monitoring
- Check GPU temperature and utilization from anywhere
- Monitor long-running document processing jobs
- Receive alerts for thermal or performance issues

### Development
- Debug ML models remotely
- Monitor training progress
- Optimize GPU utilization

### IT Management
- Remote diagnostics and troubleshooting
- Performance monitoring and capacity planning
- Automated health checks and reporting

## 🛠️ Troubleshooting

### Connection Issues
```bash
# Test Tailscale connectivity
tailscale ping *************

# Check Tailscale status
tailscale status

# Test GPU backend directly
ssh root@************* "curl localhost:9092/health"
```

### Service Issues
```bash
# Check if service is running
ssh root@************* "ps aux | grep gpu-backend"

# Check port binding
ssh root@************* "netstat -tlnp | grep 9092"

# Restart with debugging
ssh root@************* "cd /root/gpu-backend && source venv/bin/activate && python gpu-backend-server.py"
```

### Frontend Issues
```bash
# Check browser console for WebSocket errors
# Update Tailscale IP in gpu-config.ts if changed
# Verify CORS settings for cross-origin requests
```

## 🔄 Maintenance

### Regular Tasks
- **Weekly**: Check GPU backend logs for errors
- **Monthly**: Update Tailscale client on GPU machine
- **Quarterly**: Review and rotate API keys if implemented

### Updates
```bash
# Update GPU backend
ssh root@************* "cd /root/gpu-backend && git pull && source venv/bin/activate && pip install -r requirements.txt --upgrade"

# Update Tailscale
ssh root@************* "tailscale update"
```

## 📈 Monitoring Dashboard

Access the complete GPU monitoring dashboard at:
**http://localhost:3000/gpu-monitor** (when running the Zero-Touch system locally)

Features:
- **Real-time GPU metrics** (temperature, utilization, memory)
- **Live document processing pipeline** visualization
- **Interactive charts** and performance graphs
- **Console output** streaming
- **GPU process** monitoring
- **Historical data** and trends

## 🎉 Success Confirmation

Your RTX 4090 GPU monitoring system is now:
- ✅ **Accessible via Tailscale** (`*************:9092`)
- ✅ **Securely connected** to your private network
- ✅ **Real-time monitoring** with WebSocket updates
- ✅ **Remote command execution** capability
- ✅ **Mobile-friendly** responsive interface
- ✅ **Production-ready** with error handling and failover

**Next Steps:**
1. Bookmark `http://*************:9092` for quick access
2. Test the monitoring dashboard from different devices
3. Set up automated alerts for critical GPU metrics
4. Integrate with your document processing workflows

Your RTX 4090 is now globally accessible and monitored! 🚀