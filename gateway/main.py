from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import uuid
import os
import asyncio
import httpx
from datetime import datetime
import redis.asyncio as redis
import json
from contextlib import asynccontextmanager
from prometheus_client import Counter, Histogram, generate_latest
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import structlog

# Configure structured logging
logger = structlog.get_logger()

# Metrics
document_uploads = Counter('document_uploads_total', 'Total document uploads')
processing_duration = Histogram('processing_duration_seconds', 'Processing duration by model', ['model'])
processing_errors = Counter('processing_errors_total', 'Total processing errors', ['model'])

# Rate limiting
limiter = Limiter(key_func=get_remote_address)

# Redis client
redis_client: Optional[redis.Redis] = None

# Model service endpoints
MODEL_SERVICES = {
    "surya": "http://surya:8000",
    "qwen": "http://qwen:8000",
    "layoutlm": "http://layoutlm:8000",
    "minimax": "http://minimax:8000",
    "phi4": "http://phi4:8000"
}

# Async context manager for startup/shutdown
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    global redis_client
    redis_client = await redis.from_url(
        os.getenv("REDIS_URL", "redis://redis:6379"),
        encoding="utf-8",
        decode_responses=True
    )
    logger.info("Connected to Redis")
    
    yield
    
    # Shutdown
    await redis_client.close()
    logger.info("Disconnected from Redis")

# Initialize FastAPI app
app = FastAPI(
    title="Document Processing Gateway",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add rate limit error handler
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Models
class ProcessRequest(BaseModel):
    document_id: str
    models: List[str] = Field(default=["surya", "layoutlm", "qwen"])
    pipeline: str = Field(default="tender", description="Processing pipeline to use")
    options: Dict[str, Any] = Field(default_factory=dict)

class ProcessResponse(BaseModel):
    job_id: str
    status: str
    message: str

class JobStatus(BaseModel):
    job_id: str
    status: str
    progress: float
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime

# Health check endpoint
@app.get("/health")
async def health_check():
    try:
        # Check Redis connection
        await redis_client.ping()
        
        # Check model services
        service_status = {}
        async with httpx.AsyncClient(timeout=5.0) as client:
            for service, url in MODEL_SERVICES.items():
                try:
                    response = await client.get(f"{url}/health")
                    service_status[service] = response.status_code == 200
                except:
                    service_status[service] = False
        
        all_healthy = all(service_status.values())
        
        return {
            "status": "healthy" if all_healthy else "degraded",
            "services": service_status,
            "timestamp": datetime.utcnow()
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        raise HTTPException(status_code=503, detail="Service unavailable")

# Upload endpoint
@app.post("/api/upload")
@limiter.limit("10/minute")
async def upload_document(
    request: Any,  # Required for rate limiter
    file: UploadFile = File(...),
    background_tasks: BackgroundTasks = BackgroundTasks()
):
    try:
        document_uploads.inc()
        
        # Validate file
        if file.size > 100 * 1024 * 1024:  # 100MB limit
            raise HTTPException(status_code=413, detail="File too large")
        
        # Generate document ID
        doc_id = str(uuid.uuid4())
        
        # Save file
        file_path = f"/app/uploads/{doc_id}_{file.filename}"
        content = await file.read()
        
        with open(file_path, "wb") as f:
            f.write(content)
        
        # Store metadata in Redis
        metadata = {
            "id": doc_id,
            "filename": file.filename,
            "size": file.size,
            "content_type": file.content_type,
            "path": file_path,
            "uploaded_at": datetime.utcnow().isoformat(),
            "status": "uploaded"
        }
        
        await redis_client.hset(f"document:{doc_id}", mapping=metadata)
        await redis_client.expire(f"document:{doc_id}", 86400)  # 24 hour TTL
        
        logger.info("Document uploaded", document_id=doc_id, filename=file.filename)
        
        return {
            "document_id": doc_id,
            "filename": file.filename,
            "size": file.size,
            "status": "uploaded"
        }
        
    except Exception as e:
        logger.error("Upload failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# Process endpoint
@app.post("/api/process", response_model=ProcessResponse)
@limiter.limit("30/minute")
async def process_document(
    request: Any,  # Required for rate limiter
    process_request: ProcessRequest,
    background_tasks: BackgroundTasks
):
    try:
        # Validate document exists
        doc_exists = await redis_client.exists(f"document:{process_request.document_id}")
        if not doc_exists:
            raise HTTPException(status_code=404, detail="Document not found")
        
        # Create job
        job_id = str(uuid.uuid4())
        job_data = {
            "job_id": job_id,
            "document_id": process_request.document_id,
            "models": json.dumps(process_request.models),
            "pipeline": process_request.pipeline,
            "options": json.dumps(process_request.options),
            "status": "queued",
            "progress": 0,
            "created_at": datetime.utcnow().isoformat(),
            "updated_at": datetime.utcnow().isoformat()
        }
        
        await redis_client.hset(f"job:{job_id}", mapping=job_data)
        await redis_client.expire(f"job:{job_id}", 86400)  # 24 hour TTL
        
        # Queue for processing
        await redis_client.lpush("processing_queue", job_id)
        
        # Start processing in background
        background_tasks.add_task(process_job, job_id)
        
        logger.info("Processing job created", job_id=job_id, document_id=process_request.document_id)
        
        return ProcessResponse(
            job_id=job_id,
            status="queued",
            message="Document queued for processing"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Process request failed", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))

# Job status endpoint
@app.get("/api/job/{job_id}", response_model=JobStatus)
async def get_job_status(job_id: str):
    try:
        job_data = await redis_client.hgetall(f"job:{job_id}")
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Parse results if available
        results = None
        if job_data.get("results"):
            results = json.loads(job_data["results"])
        
        return JobStatus(
            job_id=job_id,
            status=job_data["status"],
            progress=float(job_data["progress"]),
            results=results,
            error=job_data.get("error"),
            created_at=datetime.fromisoformat(job_data["created_at"]),
            updated_at=datetime.fromisoformat(job_data["updated_at"])
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Get job status failed", error=str(e), job_id=job_id)
        raise HTTPException(status_code=500, detail=str(e))

# Background job processor
async def process_job(job_id: str):
    try:
        # Get job data
        job_data = await redis_client.hgetall(f"job:{job_id}")
        if not job_data:
            return
        
        # Update status
        await update_job_status(job_id, "processing", 0)
        
        # Get document data
        doc_data = await redis_client.hgetall(f"document:{job_data['document_id']}")
        if not doc_data:
            await update_job_status(job_id, "failed", 0, error="Document not found")
            return
        
        # Parse models
        models = json.loads(job_data["models"])
        total_models = len(models)
        results = {}
        
        # Process through each model
        async with httpx.AsyncClient(timeout=300.0) as client:
            for i, model in enumerate(models):
                try:
                    progress = (i / total_models) * 100
                    await update_job_status(job_id, "processing", progress)
                    
                    # Call model service
                    with processing_duration.labels(model=model).time():
                        if model == "surya":
                            response = await process_with_surya(client, doc_data["path"])
                        else:
                            response = await process_with_model(client, model, doc_data["path"])
                    
                    results[model] = response
                    
                except Exception as e:
                    processing_errors.labels(model=model).inc()
                    logger.error(f"Model {model} failed", error=str(e), job_id=job_id)
                    results[model] = {"error": str(e)}
        
        # Combine results based on pipeline
        if job_data["pipeline"] == "tender":
            final_results = await combine_tender_results(results)
        else:
            final_results = results
        
        # Update job with results
        await update_job_status(job_id, "completed", 100, results=final_results)
        
        logger.info("Job completed", job_id=job_id)
        
    except Exception as e:
        logger.error("Job processing failed", error=str(e), job_id=job_id)
        await update_job_status(job_id, "failed", 0, error=str(e))

# Model-specific processing functions
async def process_with_surya(client: httpx.AsyncClient, file_path: str) -> Dict[str, Any]:
    """Process document with Surya OCR"""
    with open(file_path, "rb") as f:
        files = {"file": f}
        response = await client.post(
            f"{MODEL_SERVICES['surya']}/process",
            files=files
        )
    return response.json()

async def process_with_model(client: httpx.AsyncClient, model: str, file_path: str) -> Dict[str, Any]:
    """Process document with specified model"""
    with open(file_path, "rb") as f:
        files = {"file": f}
        response = await client.post(
            f"{MODEL_SERVICES[model]}/process",
            files=files
        )
    return response.json()

# Combine results for tender pipeline
async def combine_tender_results(results: Dict[str, Any]) -> Dict[str, Any]:
    """Combine results from multiple models for tender processing"""
    combined = {
        "document_structure": {},
        "extracted_text": "",
        "tables": [],
        "key_information": {},
        "confidence_scores": {}
    }
    
    # Combine OCR results
    if "surya" in results and not results["surya"].get("error"):
        combined["extracted_text"] = results["surya"].get("text", "")
        combined["document_structure"] = results["surya"].get("layout", {})
    
    # Add layout understanding
    if "layoutlm" in results and not results["layoutlm"].get("error"):
        combined["document_structure"].update(results["layoutlm"].get("structure", {}))
    
    # Add semantic understanding
    if "qwen" in results and not results["qwen"].get("error"):
        combined["key_information"] = results["qwen"].get("entities", {})
    
    # Add long-context analysis
    if "minimax" in results and not results["minimax"].get("error"):
        combined["key_information"].update(results["minimax"].get("analysis", {}))
    
    # Add financial analysis
    if "phi4" in results and not results["phi4"].get("error"):
        combined["key_information"]["financial_analysis"] = results["phi4"].get("financial", {})
    
    return combined

# Update job status
async def update_job_status(
    job_id: str, 
    status: str, 
    progress: float, 
    results: Optional[Dict[str, Any]] = None,
    error: Optional[str] = None
):
    updates = {
        "status": status,
        "progress": progress,
        "updated_at": datetime.utcnow().isoformat()
    }
    
    if results:
        updates["results"] = json.dumps(results)
    
    if error:
        updates["error"] = error
    
    await redis_client.hset(f"job:{job_id}", mapping=updates)

# Metrics endpoint
@app.get("/metrics")
async def get_metrics():
    return generate_latest()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)