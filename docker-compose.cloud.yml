# Cloud-specific overrides for docker-compose.yml
# Use with: docker-compose -f docker-compose.yml -f docker-compose.cloud.yml up
version: '3.9'

services:
  # Add Nginx load balancer for cloud deployment
  nginx:
    image: nginx:alpine
    container_name: doc-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - gateway
    networks:
      - doc-network
    restart: unless-stopped

  # Gateway with cloud storage support
  gateway:
    environment:
      - STORAGE_BACKEND=${STORAGE_BACKEND:-s3}  # s3, gcs, azure
      - ENABLE_DISTRIBUTED_TRACING=true
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
    deploy:
      replicas: 2
      update_config:
        parallelism: 1
        delay: 10s
      restart_policy:
        condition: on-failure

  # Surya with multi-GPU support
  surya:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all  # Use all available GPUs
              capabilities: [gpu]
      replicas: ${SURYA_REPLICAS:-1}
    environment:
      - CUDA_VISIBLE_DEVICES=${SURYA_GPU_IDS:-0}

  # Qwen with auto-scaling
  qwen:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ['1']  # Use specific GPU
              capabilities: [gpu]
      replicas: ${QWEN_REPLICAS:-1}
      endpoint_mode: vip
    environment:
      - TENSOR_PARALLEL_SIZE=${QWEN_TP_SIZE:-1}
      - PIPELINE_PARALLEL_SIZE=${QWEN_PP_SIZE:-1}

  # MiniMax with high memory configuration
  minimax:
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: ${MINIMAX_GPU_COUNT:-1}
              capabilities: [gpu]
        limits:
          memory: 48G  # For cloud instances with more RAM
    environment:
      - TENSOR_PARALLEL_SIZE=${MINIMAX_TP_SIZE:-2}  # Split across 2 GPUs if available
      - MAX_MODEL_LEN=4000000  # 4M tokens for cloud

  # Add distributed tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: doc-jaeger
    ports:
      - "16686:16686"  # Jaeger UI
      - "14268:14268"  # HTTP collector
    environment:
      - COLLECTOR_ZIPKIN_HOST_PORT=:9411
    networks:
      - doc-network
    restart: unless-stopped

  # Add cloud-native storage
  minio:
    image: minio/minio:latest
    container_name: doc-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=${MINIO_ROOT_USER:-minioadmin}
      - MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD:-minioadmin}
    command: server /data --console-address ":9001"
    volumes:
      - minio-data:/data
    networks:
      - doc-network
    restart: unless-stopped
    profiles: ["storage"]

  # Add Celery for distributed task processing
  celery-worker:
    build: ./gateway
    container_name: doc-celery-worker
    command: celery -A app.celery worker --loglevel=info --concurrency=4
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
      - postgres
    networks:
      - doc-network
    deploy:
      replicas: ${CELERY_WORKERS:-2}
    restart: unless-stopped

  # Add Flower for Celery monitoring
  flower:
    build: ./gateway
    container_name: doc-flower
    command: celery -A app.celery flower
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
    ports:
      - "5555:5555"
    depends_on:
      - redis
    networks:
      - doc-network
    restart: unless-stopped
    profiles: ["monitoring"]

volumes:
  minio-data:

# Cloud-specific network configuration
networks:
  doc-network:
    driver: overlay  # For Docker Swarm mode
    attachable: true
    ipam:
      config:
        - subnet: ********/24