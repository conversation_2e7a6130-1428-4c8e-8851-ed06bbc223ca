/**
 * Scheduler External API Integrations
 * 
 * This file contains integration examples for:
 * - Google Calendar API
 * - Microsoft Teams/Zoom meeting platforms
 * - Twilio SMS notifications
 * - Email services (SendGrid/AWS SES)
 */

// Google Calendar Integration
export class GoogleCalendarIntegration {
  private accessToken: string;
  private calendarId: string;

  constructor(accessToken: string, calendarId: string = 'primary') {
    this.accessToken = accessToken;
    this.calendarId = calendarId;
  }

  async createEvent(meeting: any) {
    const event = {
      summary: meeting.title,
      description: meeting.description,
      start: {
        dateTime: new Date(meeting.startTime).toISOString(),
        timeZone: meeting.timezone,
      },
      end: {
        dateTime: new Date(meeting.endTime).toISOString(),
        timeZone: meeting.timezone,
      },
      attendees: meeting.participants.map((p: any) => ({
        email: p.email,
        responseStatus: p.responseStatus,
        optional: !p.isRequired,
      })),
      reminders: {
        useDefault: false,
        overrides: meeting.reminders.map((r: any) => ({
          method: r.channel === 'email' ? 'email' : 'popup',
          minutes: r.minutes,
        })),
      },
      conferenceData: meeting.location.type === 'online' ? {
        createRequest: {
          requestId: meeting.id,
          conferenceSolutionKey: {
            type: 'hangoutsMeet',
          },
        },
      } : undefined,
    };

    const response = await fetch(
      `https://www.googleapis.com/calendar/v3/calendars/${this.calendarId}/events?conferenceDataVersion=1`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(event),
      }
    );

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async updateEvent(eventId: string, updates: any) {
    const response = await fetch(
      `https://www.googleapis.com/calendar/v3/calendars/${this.calendarId}/events/${eventId}`,
      {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      }
    );

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async checkAvailability(emails: string[], timeMin: Date, timeMax: Date) {
    const response = await fetch(
      'https://www.googleapis.com/calendar/v3/freeBusy',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          timeMin: timeMin.toISOString(),
          timeMax: timeMax.toISOString(),
          items: emails.map(email => ({ id: email })),
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Google Calendar API error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.calendars;
  }
}

// Microsoft Teams Integration
export class TeamsIntegration {
  private clientId: string;
  private clientSecret: string;
  private tenantId: string;

  constructor(clientId: string, clientSecret: string, tenantId: string) {
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.tenantId = tenantId;
  }

  async getAccessToken() {
    const response = await fetch(
      `https://login.microsoftonline.com/${this.tenantId}/oauth2/v2.0/token`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          client_id: this.clientId,
          client_secret: this.clientSecret,
          grant_type: 'client_credentials',
          scope: 'https://graph.microsoft.com/.default',
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Teams auth error: ${response.statusText}`);
    }

    const data = await response.json();
    return data.access_token;
  }

  async createMeeting(meeting: any) {
    const accessToken = await this.getAccessToken();

    const teamsEvent = {
      subject: meeting.title,
      body: {
        contentType: 'HTML',
        content: meeting.description,
      },
      startDateTime: new Date(meeting.startTime).toISOString(),
      endDateTime: new Date(meeting.endTime).toISOString(),
      location: {
        displayName: 'Microsoft Teams Meeting',
      },
      attendees: meeting.participants.map((p: any) => ({
        emailAddress: {
          address: p.email,
          name: p.name,
        },
        type: p.isRequired ? 'required' : 'optional',
      })),
      allowNewTimeProposals: true,
      isOnlineMeeting: true,
      onlineMeetingProvider: 'teamsForBusiness',
    };

    const response = await fetch(
      'https://graph.microsoft.com/v1.0/me/events',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(teamsEvent),
      }
    );

    if (!response.ok) {
      throw new Error(`Teams API error: ${response.statusText}`);
    }

    return await response.json();
  }
}

// Zoom Integration
export class ZoomIntegration {
  private apiKey: string;
  private apiSecret: string;

  constructor(apiKey: string, apiSecret: string) {
    this.apiKey = apiKey;
    this.apiSecret = apiSecret;
  }

  generateJWT() {
    // In production, use proper JWT library
    const header = {
      alg: 'HS256',
      typ: 'JWT',
    };

    const payload = {
      iss: this.apiKey,
      exp: Math.floor(Date.now() / 1000) + 60 * 60,
    };

    // This is a simplified example - use a proper JWT library in production
    return 'mock-jwt-token';
  }

  async createMeeting(meeting: any) {
    const jwt = this.generateJWT();

    const zoomMeeting = {
      topic: meeting.title,
      type: 2, // Scheduled meeting
      start_time: new Date(meeting.startTime).toISOString(),
      duration: Math.floor((meeting.endTime - meeting.startTime) / (1000 * 60)),
      timezone: meeting.timezone,
      password: this.generatePassword(),
      agenda: meeting.description,
      settings: {
        host_video: true,
        participant_video: true,
        join_before_host: false,
        mute_upon_entry: true,
        watermark: false,
        use_pmi: false,
        approval_type: 0,
        audio: 'both',
        auto_recording: 'cloud',
        enforce_login: false,
        waiting_room: true,
        registrants_email_notification: true,
        meeting_authentication: false,
        encryption_type: 'enhanced_encryption',
      },
    };

    const response = await fetch(
      'https://api.zoom.us/v2/users/me/meetings',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${jwt}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(zoomMeeting),
      }
    );

    if (!response.ok) {
      throw new Error(`Zoom API error: ${response.statusText}`);
    }

    return await response.json();
  }

  private generatePassword(): string {
    return Math.random().toString(36).substring(2, 10);
  }
}

// Twilio SMS Integration
export class TwilioIntegration {
  private accountSid: string;
  private authToken: string;
  private fromNumber: string;

  constructor(accountSid: string, authToken: string, fromNumber: string) {
    this.accountSid = accountSid;
    this.authToken = authToken;
    this.fromNumber = fromNumber;
  }

  async sendSMS(to: string, message: string) {
    const auth = Buffer.from(`${this.accountSid}:${this.authToken}`).toString('base64');

    const response = await fetch(
      `https://api.twilio.com/2010-04-01/Accounts/${this.accountSid}/Messages.json`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${auth}`,
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          To: to,
          From: this.fromNumber,
          Body: message,
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`Twilio API error: ${response.statusText}`);
    }

    return await response.json();
  }

  async sendMeetingInvite(contact: any, meeting: any) {
    const message = `
${contact.name}, you're invited to: ${meeting.title}
Date: ${new Date(meeting.startTime).toLocaleDateString()}
Time: ${new Date(meeting.startTime).toLocaleTimeString()}
RSVP: ${meeting.rsvpUrl}
`;

    return this.sendSMS(contact.phone, message.trim());
  }

  async sendReminder(contact: any, meeting: any, urgency: string) {
    const messages = {
      gentle: `Reminder: ${meeting.title} is scheduled for ${new Date(meeting.startTime).toLocaleDateString()}. Please RSVP at ${meeting.rsvpUrl}`,
      urgent: `URGENT: ${meeting.title} is tomorrow! Please confirm your attendance: ${meeting.rsvpUrl}`,
      final: `FINAL REMINDER: ${meeting.title} starts in 24 hours. Join at: ${meeting.joinLink}`,
    };

    const message = messages[urgency as keyof typeof messages] || messages.gentle;
    return this.sendSMS(contact.phone, message);
  }
}

// SendGrid Email Integration
export class SendGridIntegration {
  private apiKey: string;
  private fromEmail: string;
  private fromName: string;

  constructor(apiKey: string, fromEmail: string, fromName: string) {
    this.apiKey = apiKey;
    this.fromEmail = fromEmail;
    this.fromName = fromName;
  }

  async sendEmail(to: string, subject: string, htmlContent: string, textContent?: string) {
    const response = await fetch(
      'https://api.sendgrid.com/v3/mail/send',
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: to }],
          }],
          from: {
            email: this.fromEmail,
            name: this.fromName,
          },
          subject: subject,
          content: [
            {
              type: 'text/plain',
              value: textContent || 'Please view this email in HTML',
            },
            {
              type: 'text/html',
              value: htmlContent,
            },
          ],
        }),
      }
    );

    if (!response.ok) {
      throw new Error(`SendGrid API error: ${response.statusText}`);
    }

    return response.status === 202;
  }

  async sendMeetingInvite(contact: any, meeting: any) {
    const subject = `Meeting Invitation: ${meeting.title}`;
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #0066cc; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
    .button { display: inline-block; padding: 12px 24px; background: #0066cc; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px; }
    .details { background: white; padding: 15px; margin: 15px 0; border-radius: 4px; }
    .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Meeting Invitation</h1>
    </div>
    <div class="content">
      <p>Dear ${contact.name},</p>
      <p>You're invited to attend the following meeting:</p>
      
      <div class="details">
        <h2>${meeting.title}</h2>
        <p><strong>Date:</strong> ${new Date(meeting.startTime).toLocaleDateString()}</p>
        <p><strong>Time:</strong> ${new Date(meeting.startTime).toLocaleTimeString()} - ${new Date(meeting.endTime).toLocaleTimeString()}</p>
        <p><strong>Location:</strong> ${meeting.location.value}</p>
        <p><strong>Description:</strong> ${meeting.description}</p>
      </div>
      
      <p>Please respond to this invitation:</p>
      <a href="${meeting.rsvpUrl}?response=accepted" class="button" style="background: #28a745;">Accept</a>
      <a href="${meeting.rsvpUrl}?response=declined" class="button" style="background: #dc3545;">Decline</a>
      <a href="${meeting.rsvpUrl}?response=tentative" class="button" style="background: #ffc107;">Maybe</a>
      
      ${meeting.location.type === 'online' ? `
      <div class="details">
        <h3>Join Online</h3>
        <p><a href="${meeting.joinLink}">Click here to join the meeting</a></p>
      </div>
      ` : ''}
      
      ${meeting.agenda && meeting.agenda.length > 0 ? `
      <div class="details">
        <h3>Agenda</h3>
        <ol>
          ${meeting.agenda.map((item: any) => `<li>${item.item} (${item.duration} min)</li>`).join('')}
        </ol>
      </div>
      ` : ''}
    </div>
    <div class="footer">
      <p>This invitation was sent by the ARAPS Bid Writing Studio Scheduler</p>
      <p><a href="${meeting.calendarLinks?.google}">Add to Google Calendar</a> | <a href="${meeting.calendarLinks?.outlook}">Add to Outlook</a></p>
    </div>
  </div>
</body>
</html>
    `;

    const textContent = `
Meeting Invitation: ${meeting.title}

Dear ${contact.name},

You're invited to attend the following meeting:

Title: ${meeting.title}
Date: ${new Date(meeting.startTime).toLocaleDateString()}
Time: ${new Date(meeting.startTime).toLocaleTimeString()} - ${new Date(meeting.endTime).toLocaleTimeString()}
Location: ${meeting.location.value}

Description: ${meeting.description}

Please RSVP at: ${meeting.rsvpUrl}

${meeting.location.type === 'online' ? `Join online at: ${meeting.joinLink}` : ''}
    `.trim();

    return this.sendEmail(contact.email, subject, htmlContent, textContent);
  }

  async sendReminder(contact: any, meeting: any, hoursUntil: number) {
    const subject = `Reminder: ${meeting.title} - ${hoursUntil < 24 ? 'Tomorrow' : `in ${Math.floor(hoursUntil / 24)} days`}`;
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
  <style>
    body { font-family: Arial, sans-serif; color: #333; }
    .container { max-width: 600px; margin: 0 auto; padding: 20px; }
    .header { background: #ff6b35; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
    .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
    .button { display: inline-block; padding: 12px 24px; background: #0066cc; color: white; text-decoration: none; border-radius: 4px; }
    .details { background: white; padding: 15px; margin: 15px 0; border-radius: 4px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Meeting Reminder</h1>
    </div>
    <div class="content">
      <p>Hi ${contact.name},</p>
      <p>This is a reminder about your upcoming meeting:</p>
      
      <div class="details">
        <h2>${meeting.title}</h2>
        <p><strong>Date:</strong> ${new Date(meeting.startTime).toLocaleDateString()}</p>
        <p><strong>Time:</strong> ${new Date(meeting.startTime).toLocaleTimeString()}</p>
        <p><strong>Location:</strong> ${meeting.location.value}</p>
        ${meeting.responseStatus === 'pending' ? '<p style="color: #dc3545;"><strong>⚠ You haven\'t responded to this invitation yet!</strong></p>' : ''}
      </div>
      
      ${meeting.location.type === 'online' ? `
      <p style="text-align: center; margin: 20px 0;">
        <a href="${meeting.joinLink}" class="button">Join Meeting</a>
      </p>
      ` : ''}
      
      ${meeting.responseStatus === 'pending' ? `
      <p>Please confirm your attendance:</p>
      <p style="text-align: center;">
        <a href="${meeting.rsvpUrl}" class="button">Update RSVP</a>
      </p>
      ` : ''}
    </div>
  </div>
</body>
</html>
    `;

    return this.sendEmail(contact.email, subject, htmlContent);
  }
}

// Calendar ICS File Generator
export class CalendarFileGenerator {
  static generateICS(meeting: any): string {
    const formatDate = (date: number) => {
      return new Date(date).toISOString().replace(/[-:]/g, '').replace(/\.\d{3}/, '');
    };

    const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//ARAPS//Bid Writing Studio//EN
CALSCALE:GREGORIAN
METHOD:REQUEST
BEGIN:VEVENT
UID:${meeting.id}@bidwritingstudio.com
DTSTAMP:${formatDate(Date.now())}
DTSTART:${formatDate(meeting.startTime)}
DTEND:${formatDate(meeting.endTime)}
SUMMARY:${meeting.title}
DESCRIPTION:${meeting.description.replace(/\n/g, '\\n')}
LOCATION:${meeting.location.value}
ORGANIZER:CN=${meeting.organizerName}:mailto:${meeting.organizerEmail}
${meeting.participants.map((p: any) => 
  `ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=${p.isRequired ? 'REQ-PARTICIPANT' : 'OPT-PARTICIPANT'};PARTSTAT=${p.responseStatus.toUpperCase()};CN=${p.name}:mailto:${p.email}`
).join('\n')}
${meeting.reminders.map((r: any) => `
BEGIN:VALARM
TRIGGER:-PT${r.minutes}M
ACTION:DISPLAY
DESCRIPTION:Reminder: ${meeting.title}
END:VALARM`).join('')}
STATUS:CONFIRMED
TRANSP:OPAQUE
END:VEVENT
END:VCALENDAR`;

    return icsContent;
  }

  static downloadICS(meeting: any, filename: string = 'meeting.ics') {
    const icsContent = this.generateICS(meeting);
    const blob = new Blob([icsContent], { type: 'text/calendar' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}

// Notification Orchestrator
export class NotificationOrchestrator {
  private emailService: SendGridIntegration;
  private smsService: TwilioIntegration;

  constructor(
    emailService: SendGridIntegration,
    smsService: TwilioIntegration
  ) {
    this.emailService = emailService;
    this.smsService = smsService;
  }

  async sendMultiChannelNotification(
    contact: any,
    meeting: any,
    type: 'invitation' | 'reminder' | 'update' | 'cancellation'
  ) {
    const results = {
      email: { sent: false, error: null as any },
      sms: { sent: false, error: null as any },
      push: { sent: false, error: null as any },
    };

    // Send via preferred channels
    if (contact.notificationPreferences.email) {
      try {
        if (type === 'invitation') {
          await this.emailService.sendMeetingInvite(contact, meeting);
        } else if (type === 'reminder') {
          const hoursUntil = (meeting.startTime - Date.now()) / (1000 * 60 * 60);
          await this.emailService.sendReminder(contact, meeting, hoursUntil);
        }
        results.email.sent = true;
      } catch (error) {
        results.email.error = error;
      }
    }

    if (contact.notificationPreferences.sms && contact.phone) {
      try {
        if (type === 'invitation') {
          await this.smsService.sendMeetingInvite(contact, meeting);
        } else if (type === 'reminder') {
          const urgency = this.calculateUrgency(meeting.startTime);
          await this.smsService.sendReminder(contact, meeting, urgency);
        }
        results.sms.sent = true;
      } catch (error) {
        results.sms.error = error;
      }
    }

    // Push notifications would be implemented here
    if (contact.notificationPreferences.push) {
      // Implementation would depend on push notification service
      results.push.sent = false;
    }

    return results;
  }

  private calculateUrgency(meetingTime: number): string {
    const hoursUntil = (meetingTime - Date.now()) / (1000 * 60 * 60);
    if (hoursUntil < 24) return 'final';
    if (hoursUntil < 48) return 'urgent';
    return 'gentle';
  }
}

// Example usage and configuration
export const schedulerConfig = {
  google: {
    clientId: process.env.GOOGLE_CLIENT_ID,
    clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    redirectUri: process.env.GOOGLE_REDIRECT_URI,
  },
  teams: {
    clientId: process.env.TEAMS_CLIENT_ID,
    clientSecret: process.env.TEAMS_CLIENT_SECRET,
    tenantId: process.env.TEAMS_TENANT_ID,
  },
  zoom: {
    apiKey: process.env.ZOOM_API_KEY,
    apiSecret: process.env.ZOOM_API_SECRET,
  },
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    fromNumber: process.env.TWILIO_FROM_NUMBER,
  },
  sendgrid: {
    apiKey: process.env.SENDGRID_API_KEY,
    fromEmail: process.env.SENDGRID_FROM_EMAIL,
    fromName: 'ARAPS Bid Writing Studio',
  },
};