// Content Builder Templates Library
// Pre-configured templates for different meeting types and content formats

export interface ContentTemplate {
  id: string;
  name: string;
  description: string;
  category: "presentation" | "document" | "agenda" | "script";
  meetingType: string[];
  duration: number;
  contentType: string;
  theme: string;
  parameters: any;
  structure: any;
  tags: string[];
}

export const contentTemplates: ContentTemplate[] = [
  // Presentation Templates
  {
    id: "executive-pitch-45",
    name: "Executive Pitch (45 min)",
    description: "Comprehensive executive presentation for senior decision makers",
    category: "presentation",
    meetingType: ["pitch", "executive_briefing"],
    duration: 45,
    contentType: "presentation_deck",
    theme: "professional",
    parameters: {
      includeVoiceScript: true,
      includeBranding: true,
      slides: [
        { type: "title", duration: 2 },
        { type: "agenda", duration: 2 },
        { type: "company_overview", duration: 8 },
        { type: "understanding_needs", duration: 5 },
        { type: "solution_overview", duration: 10 },
        { type: "case_studies", duration: 8 },
        { type: "pricing_value", duration: 5 },
        { type: "implementation", duration: 3 },
        { type: "qa_closing", duration: 2 },
      ],
    },
    structure: {
      keyMessages: [
        "Proven track record and industry leadership",
        "Deep understanding of client requirements",
        "Comprehensive solution with measurable benefits",
        "Strong ROI and value proposition",
      ],
      callToAction: "Partnership for long-term success",
    },
    tags: ["executive", "pitch", "comprehensive", "c-suite"],
  },
  
  {
    id: "technical-deep-dive-90",
    name: "Technical Deep Dive (90 min)",
    description: "Detailed technical presentation for operational teams",
    category: "presentation",
    meetingType: ["technical_review", "workshop"],
    duration: 90,
    contentType: "presentation_deck",
    theme: "modern",
    parameters: {
      includeVoiceScript: false,
      includeBranding: true,
      slides: [
        { type: "title", duration: 3 },
        { type: "agenda", duration: 2 },
        { type: "technical_overview", duration: 15 },
        { type: "methodology", duration: 20 },
        { type: "service_delivery", duration: 15 },
        { type: "quality_assurance", duration: 10 },
        { type: "technology_systems", duration: 10 },
        { type: "compliance_safety", duration: 10 },
        { type: "qa_discussion", duration: 5 },
      ],
    },
    structure: {
      keyMessages: [
        "Advanced methodologies and best practices",
        "Technology-enabled service delivery",
        "Comprehensive quality management",
        "Full compliance and safety protocols",
      ],
      focus: ["technical_specifications", "implementation_details", "quality_metrics"],
    },
    tags: ["technical", "detailed", "operational", "methodology"],
  },

  {
    id: "quick-overview-20",
    name: "Quick Overview (20 min)",
    description: "Concise overview for busy executives",
    category: "presentation",
    meetingType: ["executive_briefing", "introduction"],
    duration: 20,
    contentType: "presentation_deck",
    theme: "minimal",
    parameters: {
      includeVoiceScript: true,
      includeBranding: true,
      slides: [
        { type: "title", duration: 1 },
        { type: "company_snapshot", duration: 5 },
        { type: "value_proposition", duration: 8 },
        { type: "next_steps", duration: 3 },
        { type: "contact", duration: 3 },
      ],
    },
    structure: {
      keyMessages: [
        "Who we are and our credentials",
        "Unique value we bring",
        "Clear next steps",
      ],
      callToAction: "Schedule detailed discussion",
    },
    tags: ["executive", "brief", "overview", "introduction"],
  },

  // Meeting Agenda Templates
  {
    id: "tender-kickoff-agenda",
    name: "Tender Kickoff Meeting",
    description: "Structured agenda for tender project initiation",
    category: "agenda",
    meetingType: ["kickoff", "planning"],
    duration: 60,
    contentType: "meeting_agenda",
    theme: "corporate",
    parameters: {
      sections: [
        { name: "Welcome & Introductions", duration: 10, presenter: "Project Manager" },
        { name: "Project Overview", duration: 15, presenter: "Account Director" },
        { name: "Tender Requirements Review", duration: 20, presenter: "Bid Manager" },
        { name: "Timeline & Milestones", duration: 10, presenter: "Project Manager" },
        { name: "Roles & Responsibilities", duration: 3, presenter: "Team Lead" },
        { name: "Next Steps", duration: 2, presenter: "Account Director" },
      ],
      materials: ["Tender documents", "Project charter", "Timeline", "Contact list"],
    },
    structure: {
      objectives: [
        "Align team on project scope and requirements",
        "Establish clear timeline and deliverables",
        "Define roles and communication protocols",
      ],
      outcomes: ["Project plan approved", "Team responsibilities clear", "Next meeting scheduled"],
    },
    tags: ["kickoff", "planning", "tender", "team"],
  },

  {
    id: "client-presentation-agenda",
    name: "Client Presentation Meeting",
    description: "Professional agenda for client pitch presentations",
    category: "agenda",
    meetingType: ["pitch", "presentation"],
    duration: 90,
    contentType: "meeting_agenda",
    theme: "professional",
    parameters: {
      sections: [
        { name: "Welcome & Agenda Review", duration: 5, presenter: "Account Director" },
        { name: "Company Introduction", duration: 15, presenter: "Managing Director" },
        { name: "Solution Presentation", duration: 30, presenter: "Solutions Director" },
        { name: "Case Studies", duration: 15, presenter: "Client Success Manager" },
        { name: "Implementation Plan", duration: 10, presenter: "Operations Manager" },
        { name: "Commercial Discussion", duration: 10, presenter: "Commercial Director" },
        { name: "Q&A Session", duration: 3, presenter: "All" },
        { name: "Next Steps", duration: 2, presenter: "Account Director" },
      ],
    },
    structure: {
      preparation: [
        "Rehearse presentation flow",
        "Prepare Q&A responses",
        "Review client research",
      ],
    },
    tags: ["client", "presentation", "pitch", "formal"],
  },

  // Voice Script Templates
  {
    id: "narrator-executive-summary",
    name: "Executive Summary Narration",
    description: "Professional voice-over script for executive presentations",
    category: "script",
    meetingType: ["executive_briefing", "pitch"],
    duration: 15,
    contentType: "voice_script",
    theme: "professional",
    parameters: {
      tone: "authoritative",
      pace: "measured",
      emphasis: ["credentials", "results", "value"],
    },
    structure: {
      introduction: "Welcome executives to our presentation",
      body: "Highlight key credentials and value propositions",
      closing: "Call to action for partnership",
    },
    tags: ["narration", "executive", "voice-over", "professional"],
  },

  // Site-Specific Templates
  {
    id: "site-coverage-heatmap",
    name: "Service Coverage Heat Map",
    description: "Visual representation of service locations and intensity",
    category: "document",
    meetingType: ["technical_review", "operational"],
    duration: 30,
    contentType: "site_heatmap",
    theme: "modern",
    parameters: {
      mapStyle: "satellite",
      includeMetrics: true,
      showRoutes: true,
      legendPosition: "bottom-left",
    },
    structure: {
      overlays: ["service_zones", "frequency", "response_times"],
      metrics: ["coverage_percentage", "average_response", "peak_hours"],
    },
    tags: ["visualization", "operations", "coverage", "analytics"],
  },

  // Compliance Templates
  {
    id: "compliance-dashboard",
    name: "Compliance Status Dashboard",
    description: "Real-time compliance tracking and reporting",
    category: "document",
    meetingType: ["compliance_review", "audit"],
    duration: 45,
    contentType: "compliance_dashboard",
    theme: "corporate",
    parameters: {
      categories: ["safety", "environmental", "quality", "regulatory"],
      alertThresholds: { critical: 95, warning: 85 },
      updateFrequency: "daily",
    },
    structure: {
      sections: ["overview", "by_category", "trending", "actions_required"],
      metrics: ["compliance_score", "open_items", "overdue_items"],
    },
    tags: ["compliance", "dashboard", "reporting", "audit"],
  },

  // Q&A Preparation Templates
  {
    id: "comprehensive-qa-prep",
    name: "Comprehensive Q&A Preparation",
    description: "Thorough preparation for all potential questions",
    category: "document",
    meetingType: ["pitch", "review", "interview"],
    duration: 60,
    contentType: "qa_preparation",
    theme: "professional",
    parameters: {
      categories: [
        "company_credentials",
        "technical_capabilities",
        "pricing_commercial",
        "implementation",
        "risk_management",
        "references",
      ],
      difficultyLevels: ["easy", "medium", "hard"],
      responseLength: "2-3 minutes",
    },
    structure: {
      sections: [
        "Anticipated questions by category",
        "Difficult question strategies",
        "Redirect techniques",
        "Closing statements",
      ],
    },
    tags: ["qa", "preparation", "interview", "defense"],
  },

  // Team Introduction Templates
  {
    id: "team-introduction-slides",
    name: "Team Introduction Presentation",
    description: "Professional team member introductions with credentials",
    category: "presentation",
    meetingType: ["pitch", "kickoff", "introduction"],
    duration: 30,
    contentType: "team_introduction",
    theme: "professional",
    parameters: {
      includePhotos: true,
      includeQualifications: true,
      includeExperience: true,
      format: "individual_slides",
    },
    structure: {
      sections: [
        "Team overview",
        "Individual profiles",
        "Collective experience",
        "Client commitment",
      ],
    },
    tags: ["team", "introduction", "credentials", "personal"],
  },

  // Timeline Templates
  {
    id: "implementation-timeline",
    name: "Implementation Timeline",
    description: "Detailed project implementation timeline with milestones",
    category: "document",
    meetingType: ["planning", "review", "update"],
    duration: 30,
    contentType: "timeline_visualization",
    theme: "modern",
    parameters: {
      phases: ["mobilization", "implementation", "optimization", "steady_state"],
      showDependencies: true,
      includeResources: true,
      granularity: "weekly",
    },
    structure: {
      milestones: ["contract_signature", "team_mobilization", "go_live", "review"],
      criticalPath: true,
    },
    tags: ["timeline", "implementation", "planning", "milestones"],
  },
];

// Template Categories
export const templateCategories = {
  presentation: {
    name: "Presentations",
    description: "Slide decks and visual presentations",
    icon: "Presentation",
  },
  document: {
    name: "Documents",
    description: "Reports, dashboards, and analysis documents",
    icon: "FileText",
  },
  agenda: {
    name: "Meeting Agendas",
    description: "Structured meeting agendas and schedules",
    icon: "List",
  },
  script: {
    name: "Voice Scripts",
    description: "Narration and voice-over scripts",
    icon: "Mic",
  },
};

// Meeting Type Configurations
export const meetingTypeConfigs = {
  pitch: {
    name: "Sales Pitch",
    duration: [30, 45, 60],
    focus: ["credentials", "value_proposition", "differentiation"],
    audience: "decision_makers",
  },
  executive_briefing: {
    name: "Executive Briefing",
    duration: [15, 20, 30],
    focus: ["high_level_overview", "strategic_value", "roi"],
    audience: "c_suite",
  },
  technical_review: {
    name: "Technical Review",
    duration: [60, 90, 120],
    focus: ["methodology", "implementation", "technical_details"],
    audience: "technical_teams",
  },
  kickoff: {
    name: "Project Kickoff",
    duration: [45, 60, 90],
    focus: ["alignment", "planning", "team_building"],
    audience: "project_team",
  },
  compliance_review: {
    name: "Compliance Review",
    duration: [30, 45, 60],
    focus: ["compliance_status", "risk_management", "corrective_actions"],
    audience: "compliance_officers",
  },
};

// Utility Functions
export function getTemplatesByCategory(category: string): ContentTemplate[] {
  return contentTemplates.filter(template => template.category === category);
}

export function getTemplatesByMeetingType(meetingType: string): ContentTemplate[] {
  return contentTemplates.filter(template => 
    template.meetingType.includes(meetingType)
  );
}

export function getTemplatesByDuration(minDuration: number, maxDuration: number): ContentTemplate[] {
  return contentTemplates.filter(template => 
    template.duration >= minDuration && template.duration <= maxDuration
  );
}

export function searchTemplates(query: string): ContentTemplate[] {
  const searchTerm = query.toLowerCase();
  return contentTemplates.filter(template =>
    template.name.toLowerCase().includes(searchTerm) ||
    template.description.toLowerCase().includes(searchTerm) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
  );
}

export function getRecommendedTemplates(
  meetingType: string,
  duration: number,
  audience: string
): ContentTemplate[] {
  let recommendations = contentTemplates.filter(template => {
    // Match meeting type
    const meetingTypeMatch = template.meetingType.includes(meetingType);
    
    // Duration should be within 25% of target
    const durationMatch = Math.abs(template.duration - duration) <= duration * 0.25;
    
    return meetingTypeMatch && durationMatch;
  });

  // Sort by relevance score
  recommendations.sort((a, b) => {
    const aDurationDiff = Math.abs(a.duration - duration);
    const bDurationDiff = Math.abs(b.duration - duration);
    return aDurationDiff - bDurationDiff;
  });

  return recommendations.slice(0, 5);
}

// Template Customization
export function customizeTemplate(
  templateId: string,
  customizations: Partial<ContentTemplate>
): ContentTemplate {
  const template = contentTemplates.find(t => t.id === templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  return {
    ...template,
    ...customizations,
    id: `${templateId}-custom-${Date.now()}`,
    parameters: {
      ...template.parameters,
      ...customizations.parameters,
    },
    structure: {
      ...template.structure,
      ...customizations.structure,
    },
  };
}

// Template Validation
export function validateTemplate(template: ContentTemplate): string[] {
  const errors: string[] = [];

  if (!template.name || template.name.trim().length === 0) {
    errors.push("Template name is required");
  }

  if (!template.contentType) {
    errors.push("Content type is required");
  }

  if (template.duration <= 0 || template.duration > 240) {
    errors.push("Duration must be between 1 and 240 minutes");
  }

  if (!template.meetingType || template.meetingType.length === 0) {
    errors.push("At least one meeting type must be specified");
  }

  return errors;
}

// Export template as JSON
export function exportTemplate(templateId: string): string {
  const template = contentTemplates.find(t => t.id === templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  return JSON.stringify(template, null, 2);
}

// Import template from JSON
export function importTemplate(jsonString: string): ContentTemplate {
  try {
    const template = JSON.parse(jsonString);
    const errors = validateTemplate(template);
    
    if (errors.length > 0) {
      throw new Error(`Template validation failed: ${errors.join(", ")}`);
    }

    return template;
  } catch (error) {
    throw new Error(`Failed to import template: ${error}`);
  }
}