global:
  scrape_interval: 15s
  evaluation_interval: 15s

scrape_configs:
  - job_name: 'gateway'
    static_configs:
      - targets: ['gateway:8000']
    metrics_path: '/metrics'

  - job_name: 'minimax'
    static_configs:
      - targets: ['minimax:8001']
    metrics_path: '/metrics'

  - job_name: 'qwen'
    static_configs:
      - targets: ['qwen:8002']
    metrics_path: '/metrics'

  - job_name: 'surya'
    static_configs:
      - targets: ['surya:8003']
    metrics_path: '/metrics'

  - job_name: 'layoutlm'
    static_configs:
      - targets: ['layoutlm:8004']
    metrics_path: '/metrics'

  - job_name: 'phi4'
    static_configs:
      - targets: ['phi4:8005']
    metrics_path: '/metrics'

  - job_name: 'nvidia-gpu'
    static_configs:
      - targets: ['nvidia-smi-exporter:9835']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']