#!/bin/bash

echo "🔍 GPU Diagnostic Script for RTX 4090"
echo "====================================="
echo ""

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo "⚠️  Please run as root (you're already root@*************)"
fi

# 1. Check if NVIDIA driver is installed
echo "1️⃣ Checking NVIDIA Driver..."
if ! command -v nvidia-smi &> /dev/null; then
    echo "❌ NVIDIA driver not found!"
    echo "   Installing NVIDIA driver..."
    
    # Detect OS
    if [ -f /etc/debian_version ]; then
        # Debian/Ubuntu
        echo "   Detected Debian/Ubuntu system"
        apt-get update
        apt-get install -y software-properties-common
        add-apt-repository -y ppa:graphics-drivers/ppa
        apt-get update
        
        # Install latest driver
        apt-get install -y nvidia-driver-550
        
    elif [ -f /etc/redhat-release ]; then
        # RHEL/CentOS
        echo "   Detected RHEL/CentOS system"
        dnf config-manager --add-repo https://developer.download.nvidia.com/compute/cuda/repos/rhel8/x86_64/cuda-rhel8.repo
        dnf clean all
        dnf -y module install nvidia-driver:latest-dkms
    fi
    
    echo "   Driver installed. Reboot required!"
    echo "   Run: reboot"
    exit 1
else
    echo "✅ NVIDIA driver found"
    nvidia-smi --query-gpu=driver_version --format=csv,noheader
fi

# 2. Check GPU visibility
echo ""
echo "2️⃣ Checking GPU Visibility..."
GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader | head -1)
if [ -z "$GPU_COUNT" ] || [ "$GPU_COUNT" -eq 0 ]; then
    echo "❌ No GPUs detected!"
    
    # Try to fix common issues
    echo "   Attempting fixes..."
    
    # Reset GPU
    echo "   - Resetting GPU..."
    nvidia-smi --gpu-reset
    
    # Reload driver
    echo "   - Reloading NVIDIA modules..."
    rmmod nvidia_uvm
    rmmod nvidia_drm
    rmmod nvidia_modeset
    rmmod nvidia
    modprobe nvidia
    modprobe nvidia_modeset
    modprobe nvidia_drm
    modprobe nvidia_uvm
    
    # Check again
    GPU_COUNT=$(nvidia-smi --query-gpu=count --format=csv,noheader | head -1)
    if [ -z "$GPU_COUNT" ] || [ "$GPU_COUNT" -eq 0 ]; then
        echo "   ❌ Still no GPUs detected. Hardware issue?"
        echo "   Check:"
        echo "   - GPU properly seated in PCIe slot"
        echo "   - Power cables connected (RTX 4090 needs 3x8-pin or 16-pin)"
        echo "   - BIOS settings (disable CSM, enable Above 4G Decoding)"
        exit 1
    fi
fi

echo "✅ GPU detected: $GPU_COUNT GPU(s)"
nvidia-smi --query-gpu=name,pci.bus_id --format=csv

# 3. Check GPU state
echo ""
echo "3️⃣ Checking GPU State..."
GPU_STATE=$(nvidia-smi --query-gpu=pstate --format=csv,noheader | head -1)
echo "   Performance state: $GPU_STATE"

# 4. Check for errors
echo ""
echo "4️⃣ Checking for GPU Errors..."
ERROR_COUNT=$(nvidia-smi --query-gpu=ecc.errors.corrected.total --format=csv,noheader | head -1)
if [ "$ERROR_COUNT" != "N/A" ] && [ "$ERROR_COUNT" -gt 0 ]; then
    echo "⚠️  ECC errors detected: $ERROR_COUNT"
else
    echo "✅ No ECC errors"
fi

# 5. Check temperature
echo ""
echo "5️⃣ Checking Temperature..."
TEMP=$(nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader | head -1)
echo "   GPU Temperature: ${TEMP}°C"
if [ "$TEMP" -gt 85 ]; then
    echo "⚠️  GPU running hot! Check cooling"
fi

# 6. Check power
echo ""
echo "6️⃣ Checking Power Status..."
POWER=$(nvidia-smi --query-gpu=power.draw --format=csv,noheader | head -1)
POWER_LIMIT=$(nvidia-smi --query-gpu=power.limit --format=csv,noheader | head -1)
echo "   Power Draw: $POWER / $POWER_LIMIT"

# 7. Check persistence mode
echo ""
echo "7️⃣ Checking Persistence Mode..."
PERSISTENCE=$(nvidia-smi --query-gpu=persistence_mode --format=csv,noheader | head -1)
if [ "$PERSISTENCE" = "Disabled" ]; then
    echo "⚠️  Persistence mode disabled. Enabling..."
    nvidia-smi -pm 1
else
    echo "✅ Persistence mode enabled"
fi

# 8. Check Docker GPU support
echo ""
echo "8️⃣ Checking Docker GPU Support..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not installed!"
    echo "   Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
fi

# Check NVIDIA Container Toolkit
if ! dpkg -l | grep -q nvidia-container-toolkit; then
    echo "❌ NVIDIA Container Toolkit not installed!"
    echo "   Installing..."
    
    distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
    curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | apt-key add -
    curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | tee /etc/apt/sources.list.d/nvidia-docker.list
    
    apt-get update
    apt-get install -y nvidia-container-toolkit
    systemctl restart docker
else
    echo "✅ NVIDIA Container Toolkit installed"
fi

# 9. Test Docker GPU access
echo ""
echo "9️⃣ Testing Docker GPU Access..."
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi > /dev/null 2>&1
if [ $? -eq 0 ]; then
    echo "✅ Docker can access GPU"
else
    echo "❌ Docker cannot access GPU!"
    echo "   Configuring Docker..."
    
    # Configure Docker daemon
    cat > /etc/docker/daemon.json <<EOF
{
    "default-runtime": "nvidia",
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    }
}
EOF
    
    systemctl restart docker
    
    # Test again
    docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Fixed! Docker can now access GPU"
    else
        echo "❌ Still failing. Check nvidia-container-runtime installation"
    fi
fi

# 10. Full nvidia-smi output
echo ""
echo "🔟 Full GPU Status:"
echo "=================="
nvidia-smi

echo ""
echo "✅ Diagnostic complete!"
echo ""
echo "Summary:"
echo "--------"
nvidia-smi --query-gpu=name,driver_version,pstate,temperature.gpu,power.draw,memory.used,memory.total --format=csv

echo ""
echo "🚀 Ready to test? Run:"
echo "   docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi"