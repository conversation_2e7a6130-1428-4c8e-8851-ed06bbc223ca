-- Initialize document processing pipeline database

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create tables
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id VARCHAR(255) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path TEXT,
    file_url TEXT,
    file_size BIGINT,
    content_type VARCHAR(100),
    status VARCHAR(50) DEFAULT 'uploaded',
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS processing_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id VARCHAR(255) UNIQUE NOT NULL,
    document_id VARCHAR(255) REFERENCES documents(document_id),
    status VARCHAR(50) DEFAULT 'queued',
    priority INTEGER DEFAULT 5,
    progress FLOAT DEFAULT 0.0,
    models TEXT[] DEFAULT ARRAY[]::TEXT[],
    processing_options JSONB DEFAULT '{}'::jsonb,
    results JSONB DEFAULT '{}'::jsonb,
    error TEXT,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS model_results (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id VARCHAR(255) REFERENCES processing_jobs(job_id),
    document_id VARCHAR(255) REFERENCES documents(document_id),
    model_name VARCHAR(100) NOT NULL,
    model_version VARCHAR(50),
    processing_time FLOAT,
    results JSONB NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_hash VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    permissions JSONB DEFAULT '{}'::jsonb,
    rate_limit INTEGER DEFAULT 100,
    is_active BOOLEAN DEFAULT true,
    last_used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP
);

-- Create indexes
CREATE INDEX idx_documents_status ON documents(status);
CREATE INDEX idx_documents_uploaded_at ON documents(uploaded_at DESC);
CREATE INDEX idx_processing_jobs_status ON processing_jobs(status);
CREATE INDEX idx_processing_jobs_priority ON processing_jobs(priority DESC);
CREATE INDEX idx_processing_jobs_created_at ON processing_jobs(created_at DESC);
CREATE INDEX idx_model_results_job_id ON model_results(job_id);
CREATE INDEX idx_model_results_document_id ON model_results(document_id);
CREATE INDEX idx_model_results_model_name ON model_results(model_name);
CREATE INDEX idx_api_keys_is_active ON api_keys(is_active);

-- Create update trigger for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_processing_jobs_updated_at BEFORE UPDATE ON processing_jobs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for monitoring
CREATE OR REPLACE VIEW job_statistics AS
SELECT 
    status,
    COUNT(*) as count,
    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_processing_time_seconds,
    MIN(created_at) as oldest_job,
    MAX(created_at) as newest_job
FROM processing_jobs
GROUP BY status;

CREATE OR REPLACE VIEW model_performance AS
SELECT 
    model_name,
    COUNT(*) as total_processes,
    AVG(processing_time) as avg_processing_time,
    MIN(processing_time) as min_processing_time,
    MAX(processing_time) as max_processing_time,
    STDDEV(processing_time) as stddev_processing_time
FROM model_results
GROUP BY model_name;

-- Insert default API key for development (remove in production)
INSERT INTO api_keys (key_hash, name, permissions, rate_limit)
VALUES (
    crypt('dev-api-key-123', gen_salt('bf')),
    'Development API Key',
    '{"read": true, "write": true, "admin": false}'::jsonb,
    1000
) ON CONFLICT DO NOTHING;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO docuser;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO docuser;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO docuser;