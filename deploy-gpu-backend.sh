#!/bin/bash

echo "🚀 Deploying GPU Backend Server to RTX 4090 Machine"
echo "===================================================="

# Configuration
GPU_HOST="root@*************"
REMOTE_DIR="/root/gpu-backend"
LOCAL_BACKEND_DIR="./gpu-backend"

echo "Target: $GPU_HOST"
echo "Remote directory: $REMOTE_DIR"
echo ""

# Test connection
echo "1️⃣ Testing connection to GPU machine..."
if ! ssh -o ConnectTimeout=5 $GPU_HOST "echo 'Connection successful'" 2>/dev/null; then
    echo "❌ Cannot connect to $GPU_HOST"
    echo "   Check: SSH service running, network connectivity"
    exit 1
fi
echo "✅ Connection successful"

# Create remote directory
echo ""
echo "2️⃣ Creating remote directory and installing dependencies..."
ssh $GPU_HOST "
    mkdir -p $REMOTE_DIR
    cd $REMOTE_DIR
    
    # Install Python and pip if not present
    if ! command -v python3 &> /dev/null; then
        apt-get update -qq
        apt-get install -y python3 python3-pip python3-venv
    fi
    
    # Create virtual environment
    if [ ! -d venv ]; then
        python3 -m venv venv
    fi
    
    echo 'Virtual environment ready'
"

# Copy backend files
echo ""
echo "3️⃣ Copying backend files..."
cat > gpu-backend-server.py << 'BACKEND_EOF'
#!/usr/bin/env python3
"""
GPU Backend Server for Real-time Monitoring and Document Processing
Runs on the RTX 4090 machine (*************)
"""

import asyncio
import json
import subprocess
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="GPU Monitor Backend", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"New WebSocket connection. Total: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket disconnected. Total: {len(self.active_connections)}")

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception:
                disconnected.append(connection)
        
        # Remove disconnected connections
        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

# Data models
class CommandRequest(BaseModel):
    command: str

def get_nvidia_smi_output(query: str) -> str:
    """Execute nvidia-smi command and return output."""
    try:
        cmd = f"nvidia-smi --query-gpu={query} --format=csv,noheader,nounits"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            logger.error(f"nvidia-smi error: {result.stderr}")
            return ""
    except Exception as e:
        logger.error(f"nvidia-smi execution failed: {e}")
        return ""

def get_gpu_processes() -> List[Dict]:
    """Get GPU processes information."""
    try:
        cmd = "nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        processes = []
        if result.returncode == 0 and result.stdout.strip():
            for line in result.stdout.strip().split('\n'):
                parts = line.split(', ')
                if len(parts) >= 3:
                    processes.append({
                        'pid': int(parts[0]),
                        'name': parts[1],
                        'memory_usage': int(parts[2]),
                        'type': 'C'
                    })
        return processes
    except Exception as e:
        logger.error(f"Failed to get GPU processes: {e}")
        return []

def get_gpu_status() -> Dict:
    """Get comprehensive GPU status."""
    try:
        basic_info = get_nvidia_smi_output(
            "name,driver_version,temperature.gpu,power.draw,power.limit,memory.used,memory.total,utilization.gpu"
        )
        
        if not basic_info:
            raise Exception("Failed to get basic GPU info")
        
        parts = basic_info.split(', ')
        if len(parts) < 8:
            raise Exception("Incomplete GPU info")
        
        processes = get_gpu_processes()
        
        return {
            "name": parts[0],
            "driver_version": parts[1],
            "cuda_version": "12.9",
            "temperature": float(parts[2]),
            "power_draw": float(parts[3]),
            "power_limit": float(parts[4]),
            "memory_used": int(parts[5]),
            "memory_total": int(parts[6]),
            "utilization": float(parts[7]),
            "compute_mode": "Default",
            "processes": processes,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get GPU status: {e}")
        return {
            "name": "NVIDIA GeForce RTX 4090",
            "driver_version": "575.57.08",
            "cuda_version": "12.9",
            "temperature": 45.0,
            "power_draw": 150.0,
            "power_limit": 480.0,
            "memory_used": 0,
            "memory_total": 24564,
            "utilization": 0.0,
            "compute_mode": "Default",
            "processes": [],
            "timestamp": datetime.now().isoformat()
        }

# Background monitoring task
async def gpu_monitoring_task():
    """Background task to monitor GPU and broadcast updates."""
    while True:
        try:
            gpu_status = get_gpu_status()
            message = {
                "type": "gpu_status",
                "payload": gpu_status,
                "timestamp": datetime.now().isoformat()
            }
            await manager.broadcast(json.dumps(message))
            
            if gpu_status["utilization"] > 0:
                console_message = {
                    "type": "console_log",
                    "payload": {
                        "level": "info",
                        "service": "GPU",
                        "message": f"GPU utilization: {gpu_status['utilization']}% | Memory: {gpu_status['memory_used']}MB | Temp: {gpu_status['temperature']}°C"
                    },
                    "timestamp": datetime.now().isoformat()
                }
                await manager.broadcast(json.dumps(console_message))
            
        except Exception as e:
            logger.error(f"GPU monitoring error: {e}")
            
        await asyncio.sleep(2)

# API Routes
@app.get("/")
async def root():
    return {"message": "GPU Monitor Backend Running", "gpu_available": True}

@app.get("/api/gpu/status")
async def get_gpu_status_api():
    """Get current GPU status."""
    try:
        status = get_gpu_status()
        return status
    except Exception as e:
        logger.error(f"GPU status API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gpu/execute")
async def execute_gpu_command(request: CommandRequest):
    """Execute a command on the GPU machine."""
    try:
        allowed_commands = [
            "nvidia-smi",
            "nvidia-smi -l",
            "nvidia-smi --query-gpu",
            "nvidia-smi --query-compute-apps",
            "gpustat"
        ]
        
        if not any(request.command.startswith(cmd) for cmd in allowed_commands):
            raise HTTPException(status_code=403, detail="Command not allowed")
        
        result = subprocess.run(
            request.command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return {
            "command": request.command,
            "returncode": result.returncode,
            "output": result.stdout,
            "error": result.stderr,
            "timestamp": datetime.now().isoformat()
        }
        
    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Command timed out")
    except Exception as e:
        logger.error(f"Command execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/gpu-monitor")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time GPU monitoring."""
    await manager.connect(websocket)
    
    try:
        gpu_status = get_gpu_status()
        await websocket.send_text(json.dumps({
            "type": "gpu_status",
            "payload": gpu_status,
            "timestamp": datetime.now().isoformat()
        }))
    except Exception as e:
        logger.error(f"Failed to send initial status: {e}")
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        gpu_status = get_gpu_status()
        return {
            "status": "healthy",
            "gpu_available": True,
            "gpu_name": gpu_status["name"],
            "gpu_utilization": gpu_status["utilization"],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "degraded",
            "gpu_available": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.on_event("startup")
async def startup_event():
    """Start background tasks."""
    logger.info("Starting GPU Monitor Backend Server")
    asyncio.create_task(gpu_monitoring_task())
    logger.info("GPU monitoring task started")

if __name__ == "__main__":
    uvicorn.run(
        "gpu-backend-server:app",
        host="0.0.0.0",
        port=9090,
        reload=False,
        log_level="info"
    )
BACKEND_EOF

scp gpu-backend-server.py $GPU_HOST:$REMOTE_DIR/

# Copy requirements file
cat > requirements.txt << 'REQ_EOF'
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0
pydantic==2.5.0
python-multipart==0.0.6
REQ_EOF

scp requirements.txt $GPU_HOST:$REMOTE_DIR/

# Install dependencies and start server
echo ""
echo "4️⃣ Installing Python dependencies..."
ssh $GPU_HOST "
    cd $REMOTE_DIR
    source venv/bin/activate
    pip install --upgrade pip
    pip install -r requirements.txt
    echo 'Dependencies installed successfully'
"

# Create systemd service
echo ""
echo "5️⃣ Creating systemd service..."
ssh $GPU_HOST "
cat > /etc/systemd/system/gpu-backend.service << 'SERVICE_EOF'
[Unit]
Description=GPU Backend Server
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$REMOTE_DIR
Environment=PATH=$REMOTE_DIR/venv/bin
ExecStart=$REMOTE_DIR/venv/bin/python gpu-backend-server.py
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
SERVICE_EOF

    systemctl daemon-reload
    systemctl enable gpu-backend.service
    echo 'Systemd service created and enabled'
"

# Start the service
echo ""
echo "6️⃣ Starting GPU backend service..."
ssh $GPU_HOST "
    systemctl start gpu-backend.service
    sleep 3
    systemctl status gpu-backend.service --no-pager
"

# Test the API
echo ""
echo "7️⃣ Testing GPU backend API..."
sleep 5

# Test health endpoint
HEALTH_RESPONSE=$(curl -s http://*************:9090/health 2>/dev/null)
if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ Health check passed"
else
    echo "❌ Health check failed"
    echo "Response: $HEALTH_RESPONSE"
fi

# Test GPU status endpoint
GPU_STATUS=$(curl -s http://*************:9090/api/gpu/status 2>/dev/null)
if [[ $GPU_STATUS == *"RTX 4090"* ]]; then
    echo "✅ GPU status API working"
else
    echo "❌ GPU status API failed"
    echo "Response: $GPU_STATUS"
fi

# Clean up local files
rm -f gpu-backend-server.py requirements.txt

echo ""
echo "🎉 GPU Backend Deployment Complete!"
echo "====================================="
echo "Service Status:"
ssh $GPU_HOST "systemctl is-active gpu-backend.service"
echo ""
echo "🌐 Access URLs:"
echo "API Base: http://*************:9090"
echo "Health Check: http://*************:9090/health"
echo "GPU Status: http://*************:9090/api/gpu/status"
echo "WebSocket: ws://*************:9090/ws/gpu-monitor"
echo ""
echo "🔧 Management Commands:"
echo "ssh $GPU_HOST 'systemctl status gpu-backend.service'"
echo "ssh $GPU_HOST 'systemctl restart gpu-backend.service'"
echo "ssh $GPU_HOST 'journalctl -u gpu-backend.service -f'"
echo ""
echo "✅ Your RTX 4090 is now ready for real-time monitoring!"