/* === FONT IMPORTS === */
@import url('https://fonts.googleapis.com/css2?family=Geist+Mono:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* === CSS CUSTOM PROPERTIES (Design Tokens) === */
:root {
  /* Color Palette - Dark Theme */
  --color-background: #0a0a0a;
  --color-background-secondary: #111111;
  --color-background-tertiary: #1a1a1a;
  --color-background-elevated: #222222;
  --color-background-overlay: #0000008a;
  
  /* Surface Colors */
  --color-surface: #1e1e1e;
  --color-surface-secondary: #2a2a2a;
  --color-surface-tertiary: #333333;
  --color-surface-hover: #3a3a3a;
  --color-surface-active: #444444;
  
  /* Text Colors */
  --color-text-primary: #ffffff;
  --color-text-secondary: #e5e5e5;
  --color-text-tertiary: #b3b3b3;
  --color-text-quaternary: #808080;
  --color-text-disabled: #525252;
  --color-text-inverse: #0a0a0a;
  
  /* Border Colors */
  --color-border: #333333;
  --color-border-secondary: #404040;
  --color-border-tertiary: #4d4d4d;
  --color-border-focus: #0ea5e9;
  --color-border-error: #dc2626;
  --color-border-success: #059669;
  --color-border-warning: #d97706;
  
  /* Primary Colors (Blue) */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;
  --color-primary-950: #172554;
  
  /* Accent Colors (Green) */
  --color-accent-50: #ecfdf5;
  --color-accent-100: #d1fae5;
  --color-accent-200: #a7f3d0;
  --color-accent-300: #6ee7b7;
  --color-accent-400: #34d399;
  --color-accent-500: #10b981;
  --color-accent-600: #059669;
  --color-accent-700: #047857;
  --color-accent-800: #065f46;
  --color-accent-900: #064e3b;
  --color-accent-950: #022c22;
  
  /* Secondary Colors (Teal) */
  --color-secondary-50: #f0fdfa;
  --color-secondary-100: #ccfbf1;
  --color-secondary-200: #99f6e4;
  --color-secondary-300: #5eead4;
  --color-secondary-400: #2dd4bf;
  --color-secondary-500: #14b8a6;
  --color-secondary-600: #0d9488;
  --color-secondary-700: #0f766e;
  --color-secondary-800: #115e59;
  --color-secondary-900: #134e4a;
  --color-secondary-950: #042f2e;
  
  /* Semantic Colors */
  --color-error: #dc2626;
  --color-error-bg: #fef2f2;
  --color-error-border: #fecaca;
  --color-success: #059669;
  --color-success-bg: #ecfdf5;
  --color-success-border: #a7f3d0;
  --color-warning: #d97706;
  --color-warning-bg: #fffbeb;
  --color-warning-border: #fed7aa;
  --color-info: #0ea5e9;
  --color-info-bg: #f0f9ff;
  --color-info-border: #bae6fd;
  
  /* Spacing Scale */
  --spacing-xs: 0.25rem;     /* 4px */
  --spacing-sm: 0.5rem;      /* 8px */
  --spacing-md: 0.75rem;     /* 12px */
  --spacing-lg: 1rem;        /* 16px */
  --spacing-xl: 1.5rem;      /* 24px */
  --spacing-2xl: 2rem;       /* 32px */
  --spacing-3xl: 3rem;       /* 48px */
  --spacing-4xl: 4rem;       /* 64px */
  --spacing-5xl: 6rem;       /* 96px */
  --spacing-6xl: 8rem;       /* 128px */
  
  /* Typography Scale */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  
  /* Line Heights */
  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-loose: 1.75;
  
  /* Border Radius */
  --radius-xs: 0.125rem;        /* 2px */
  --radius-sm: 0.25rem;         /* 4px */
  --radius-md: 0.375rem;        /* 6px */
  --radius-lg: 0.5rem;          /* 8px */
  --radius-xl: 0.75rem;         /* 12px */
  --radius-2xl: 1rem;           /* 16px */
  --radius-3xl: 1.5rem;         /* 24px */
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  
  /* Transitions */
  --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-Index Scale */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* === BASE STYLES === */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--color-background);
  color: var(--color-text-primary);
  overflow-x: hidden;
  min-height: 100vh;
}

/* === TYPOGRAPHY === */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: var(--line-height-tight);
  color: var(--color-text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  line-height: var(--line-height-normal);
  color: var(--color-text-secondary);
}

code, pre {
  font-family: 'Geist Mono', 'Fira Code', 'Consolas', monospace;
  font-feature-settings: 'liga' 1, 'calt' 1;
}

/* === CUSTOM SCROLLBAR === */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-background-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--color-surface-tertiary);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-surface-hover);
}

::-webkit-scrollbar-corner {
  background: var(--color-background-secondary);
}

/* === COMPONENT UTILITIES === */
@layer components {
  /* Form Controls */
  .input-field {
    @apply w-full px-4 py-3 rounded-lg bg-gray-800/50 border border-gray-700 text-white placeholder-gray-400 focus:border-primary-500 focus:ring-2 focus:ring-primary-500/20 outline-none transition-all duration-200 hover:border-gray-600;
  }
  
  .input-field-error {
    @apply border-red-500 focus:border-red-500 focus:ring-red-500/20;
  }
  
  .input-field-success {
    @apply border-green-500 focus:border-green-500 focus:ring-green-500/20;
  }
  
  /* Buttons */
  .btn-primary {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg bg-primary-600 text-white font-medium hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-secondary {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg bg-gray-700 text-white font-medium hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-accent {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg bg-accent-600 text-white font-medium hover:bg-accent-700 focus:outline-none focus:ring-2 focus:ring-accent-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-outline {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg border-2 border-gray-600 text-gray-300 font-medium hover:border-gray-500 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-ghost {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg text-gray-300 font-medium hover:bg-gray-800 hover:text-white focus:outline-none focus:ring-2 focus:ring-gray-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-destructive {
    @apply inline-flex items-center justify-center px-6 py-3 rounded-lg bg-red-600 text-white font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500/50 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  /* Cards */
  .card {
    @apply bg-gray-800/50 border border-gray-700 rounded-xl p-6 backdrop-blur-sm;
  }
  
  .card-elevated {
    @apply bg-gray-800/80 border border-gray-600 rounded-xl p-6 shadow-lg backdrop-blur-sm;
  }
  
  .card-interactive {
    @apply bg-gray-800/50 border border-gray-700 rounded-xl p-6 hover:bg-gray-800/70 hover:border-gray-600 transition-all duration-200 cursor-pointer backdrop-blur-sm;
  }
  
  /* Status Badges */
  .badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-600/20 text-primary-300 border border-primary-600/30;
  }
  
  .badge-secondary {
    @apply bg-gray-600/20 text-gray-300 border border-gray-600/30;
  }
  
  .badge-accent {
    @apply bg-accent-600/20 text-accent-300 border border-accent-600/30;
  }
  
  .badge-success {
    @apply bg-green-600/20 text-green-300 border border-green-600/30;
  }
  
  .badge-warning {
    @apply bg-yellow-600/20 text-yellow-300 border border-yellow-600/30;
  }
  
  .badge-error {
    @apply bg-red-600/20 text-red-300 border border-red-600/30;
  }
  
  /* Loading States */
  .loading-spinner {
    @apply animate-spin rounded-full h-6 w-6 border-2 border-gray-600 border-t-primary-500;
  }
  
  .loading-pulse {
    @apply animate-pulse bg-gray-700 rounded;
  }
  
  /* Focus States */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-primary-500/50 focus:ring-offset-2 focus:ring-offset-gray-900;
  }
  
  /* Dividers */
  .divider {
    @apply border-gray-700;
  }
  
  .divider-vertical {
    @apply border-l border-gray-700;
  }
}

/* === UTILITY CLASSES === */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-400 to-accent-400 bg-clip-text text-transparent;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }
  
  .bg-gradient-accent {
    @apply bg-gradient-to-r from-accent-600 to-accent-700;
  }
  
  .bg-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700;
  }
  
  .glass-effect {
    @apply bg-white/5 backdrop-blur-lg border border-white/10;
  }
  
  .glass-effect-dark {
    @apply bg-black/20 backdrop-blur-lg border border-white/5;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }
  
  .shadow-glow-accent {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  
  .transition-smooth {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .transition-fast {
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .transition-slow {
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

/* === ANIMATIONS === */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.3s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

.animate-shimmer {
  animation: shimmer 1.5s infinite;
}

/* === LEGACY COMPATIBILITY === */
.auth-input-field {
  @apply input-field;
}

.auth-button {
  @apply btn-primary;
}
