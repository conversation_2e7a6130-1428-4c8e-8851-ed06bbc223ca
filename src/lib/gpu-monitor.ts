import { WebSocket } from 'ws';

export interface GPUMetrics {
  name: string;
  driver_version: string;
  cuda_version: string;
  temperature: number;
  power_draw: number;
  power_limit: number;
  memory_used: number;
  memory_total: number;
  utilization: number;
  compute_mode: string;
  processes: GPUProcess[];
  timestamp: string;
}

export interface GPUProcess {
  pid: number;
  name: string;
  memory_usage: number;
  type: string;
}

export interface DocumentProcessingEvent {
  type: 'document_job' | 'process_update' | 'console_log' | 'gpu_status';
  payload: any;
  timestamp: string;
}

class GPUMonitorService {
  private wsConnections: Set<WebSocket> = new Set();
  private gpuMetricsInterval: NodeJS.Timeout | null = null;
  private isMonitoring = false;

  constructor() {
    this.startGPUMonitoring();
  }

  // Add WebSocket connection
  addConnection(ws: WebSocket) {
    this.wsConnections.add(ws);
    
    ws.on('close', () => {
      this.wsConnections.delete(ws);
    });

    // Send initial GPU status
    this.sendGPUStatus(ws);
  }

  // Start monitoring GPU metrics
  private startGPUMonitoring() {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    
    // Poll GPU metrics every 2 seconds
    this.gpuMetricsInterval = setInterval(async () => {
      try {
        const metrics = await this.fetchGPUMetrics();
        this.broadcast({
          type: 'gpu_status',
          payload: metrics,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Failed to fetch GPU metrics:', error);
      }
    }, 2000);
  }

  // Stop monitoring
  stopMonitoring() {
    if (this.gpuMetricsInterval) {
      clearInterval(this.gpuMetricsInterval);
      this.gpuMetricsInterval = null;
    }
    this.isMonitoring = false;
  }

  // Fetch GPU metrics from remote machine
  private async fetchGPUMetrics(): Promise<GPUMetrics> {
    try {
      // Execute nvidia-smi command on remote GPU machine
      const response = await fetch('http://*************:9090/api/gpu/status');
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      // Fallback to mock data if remote is unavailable
      return this.getMockGPUMetrics();
    }
  }

  // Mock GPU metrics for testing
  private getMockGPUMetrics(): GPUMetrics {
    const utilization = Math.floor(Math.random() * 40) + 30; // 30-70%
    const memoryUsed = Math.floor(Math.random() * 8000) + 4000; // 4-12GB
    const temperature = Math.floor(Math.random() * 15) + 45; // 45-60°C
    const power = Math.floor(Math.random() * 100) + 150; // 150-250W
    
    return {
      name: "NVIDIA GeForce RTX 4090",
      driver_version: "575.57.08",
      cuda_version: "12.9",
      temperature,
      power_draw: power,
      power_limit: 480,
      memory_used: memoryUsed,
      memory_total: 24564,
      utilization,
      compute_mode: "Default",
      processes: [
        {
          pid: 1234,
          name: "python",
          memory_usage: memoryUsed * 0.6,
          type: "C"
        },
        {
          pid: 5678,
          name: "surya-ocr",
          memory_usage: memoryUsed * 0.4,
          type: "C"
        }
      ],
      timestamp: new Date().toISOString()
    };
  }

  // Send GPU status to specific client
  private sendGPUStatus(ws: WebSocket) {
    this.fetchGPUMetrics().then(metrics => {
      this.send(ws, {
        type: 'gpu_status',
        payload: metrics,
        timestamp: new Date().toISOString()
      });
    });
  }

  // Broadcast event to all connected clients
  broadcast(event: DocumentProcessingEvent) {
    const message = JSON.stringify(event);
    
    this.wsConnections.forEach(ws => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      }
    });
  }

  // Send event to specific client
  send(ws: WebSocket, event: DocumentProcessingEvent) {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(event));
    }
  }

  // Document processing events
  onDocumentJobUpdate(job: any) {
    this.broadcast({
      type: 'document_job',
      payload: job,
      timestamp: new Date().toISOString()
    });
  }

  onConsoleLog(level: string, service: string, message: string) {
    this.broadcast({
      type: 'console_log',
      payload: { level, service, message },
      timestamp: new Date().toISOString()
    });
  }

  onProcessUpdate(processes: any[]) {
    this.broadcast({
      type: 'process_update',
      payload: processes,
      timestamp: new Date().toISOString()
    });
  }

  // Get connection count
  getConnectionCount(): number {
    return this.wsConnections.size;
  }
}

// Singleton instance
export const gpuMonitorService = new GPUMonitorService();

// GPU monitoring utilities
export class GPUCommandExecutor {
  private static GPU_HOST = '*************';
  private static GPU_USER = 'root';

  // Execute command on GPU machine
  static async executeCommand(command: string): Promise<string> {
    try {
      const response = await fetch(`http://${this.GPU_HOST}:9090/api/gpu/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ command }),
      });

      if (!response.ok) {
        throw new Error(`Command execution failed: ${response.statusText}`);
      }

      const result = await response.json();
      return result.output;
    } catch (error) {
      console.error('GPU command execution error:', error);
      throw error;
    }
  }

  // Get detailed GPU status
  static async getDetailedGPUStatus(): Promise<any> {
    const commands = [
      'nvidia-smi --query-gpu=name,driver_version,pstate,temperature.gpu,power.draw,power.limit,memory.used,memory.total,utilization.gpu --format=csv,noheader,nounits',
      'nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits',
      'nvidia-smi --query-gpu=clocks.current.graphics,clocks.current.memory,clocks.max.graphics,clocks.max.memory --format=csv,noheader,nounits'
    ];

    try {
      const results = await Promise.all(
        commands.map(cmd => this.executeCommand(cmd))
      );

      return {
        basic_info: this.parseCSV(results[0]),
        processes: this.parseCSV(results[1]),
        clocks: this.parseCSV(results[2]),
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get detailed GPU status:', error);
      return null;
    }
  }

  // Parse CSV output from nvidia-smi
  private static parseCSV(csvData: string): any[] {
    if (!csvData || csvData.trim() === '') return [];
    
    return csvData.trim().split('\n').map(line => {
      return line.split(', ').map(cell => cell.trim());
    });
  }

  // Monitor GPU temperature and throttling
  static async checkGPUHealth(): Promise<{
    temperature: number;
    isThrottling: boolean;
    powerLimit: number;
    memoryErrors: number;
  }> {
    try {
      const tempResult = await this.executeCommand('nvidia-smi --query-gpu=temperature.gpu --format=csv,noheader,nounits');
      const throttleResult = await this.executeCommand('nvidia-smi --query-gpu=pstate --format=csv,noheader');
      const powerResult = await this.executeCommand('nvidia-smi --query-gpu=power.draw,power.limit --format=csv,noheader,nounits');
      
      const temperature = parseInt(tempResult.trim());
      const pstate = throttleResult.trim();
      const [powerDraw, powerLimit] = powerResult.trim().split(', ').map(p => parseFloat(p));
      
      return {
        temperature,
        isThrottling: pstate !== 'P0', // P0 is maximum performance
        powerLimit: powerLimit,
        memoryErrors: 0 // RTX 4090 doesn't have ECC
      };
    } catch (error) {
      console.error('GPU health check failed:', error);
      return {
        temperature: 0,
        isThrottling: false,
        powerLimit: 480,
        memoryErrors: 0
      };
    }
  }
}

// Performance monitoring utilities
export class GPUPerformanceTracker {
  private static metrics: any[] = [];
  private static maxHistorySize = 1000;

  static addMetric(metric: any) {
    this.metrics.push({
      ...metric,
      timestamp: Date.now()
    });

    // Keep only recent metrics
    if (this.metrics.length > this.maxHistorySize) {
      this.metrics = this.metrics.slice(-this.maxHistorySize);
    }
  }

  static getMetrics(timeRange: number = 300000): any[] { // Default 5 minutes
    const cutoff = Date.now() - timeRange;
    return this.metrics.filter(m => m.timestamp > cutoff);
  }

  static getAverageUtilization(timeRange: number = 60000): number { // Default 1 minute
    const recentMetrics = this.getMetrics(timeRange);
    if (recentMetrics.length === 0) return 0;
    
    const sum = recentMetrics.reduce((acc, m) => acc + (m.utilization || 0), 0);
    return sum / recentMetrics.length;
  }

  static getPeakMemoryUsage(timeRange: number = 300000): number {
    const recentMetrics = this.getMetrics(timeRange);
    if (recentMetrics.length === 0) return 0;
    
    return Math.max(...recentMetrics.map(m => m.memory_used || 0));
  }

  static getProcessingThroughput(): { documentsPerHour: number; avgProcessingTime: number } {
    // This would be calculated from actual processing jobs
    // For now, return mock data
    return {
      documentsPerHour: 12,
      avgProcessingTime: 145
    };
  }
}