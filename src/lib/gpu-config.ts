// GPU Monitor Configuration for Tailscale Access

export const GPU_CONFIG = {
  // Tailscale IP of the RTX 4090 machine
  TAILSCALE_IP: '*************',
  GPU_HOST: '*************', // Local network fallback
  GPU_PORT: 9092,
  
  // API Endpoints
  get BASE_URL() {
    // Try Tailscale first, fallback to local IP
    return `http://${this.TAILSCALE_IP}:${this.GPU_PORT}`;
  },
  
  get WEBSOCKET_URL() {
    return `ws://${this.TAILSCALE_IP}:${this.GPU_PORT}/ws/gpu-monitor`;
  },
  
  get API_ENDPOINTS() {
    return {
      health: `${this.BASE_URL}/health`,
      status: `${this.BASE_URL}/api/gpu/status`,
      execute: `${this.BASE_URL}/api/gpu/execute`,
      websocket: this.WEBSOCKET_URL
    };
  },
  
  // Connection settings
  CONNECTION: {
    timeout: 5000, // 5 seconds
    retryInterval: 3000, // 3 seconds
    maxRetries: 5
  },
  
  // GPU monitoring settings
  MONITORING: {
    updateInterval: 2000, // 2 seconds
    historySize: 300, // 5 minutes at 2s intervals
    alertThresholds: {
      temperature: 85, // °C
      memoryUsage: 90, // %
      powerUsage: 95   // %
    }
  }
};

// Connection testing utility
export async function testGPUConnection(): Promise<{
  connected: boolean;
  endpoint: string;
  response?: any;
  error?: string;
}> {
  const endpoints = [
    `http://${GPU_CONFIG.TAILSCALE_IP}:${GPU_CONFIG.GPU_PORT}`,
    `http://${GPU_CONFIG.GPU_HOST}:${GPU_CONFIG.GPU_PORT}`
  ];
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(`${endpoint}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(GPU_CONFIG.CONNECTION.timeout)
      });
      
      if (response.ok) {
        const data = await response.json();
        return {
          connected: true,
          endpoint,
          response: data
        };
      }
    } catch (error) {
      console.warn(`Failed to connect to ${endpoint}:`, error);
    }
  }
  
  return {
    connected: false,
    endpoint: 'none',
    error: 'All connection attempts failed'
  };
}

// Tailscale-specific utilities
export const TAILSCALE_CONFIG = {
  MACHINE_NAME: 'pve', // Proxmox machine name
  TAILSCALE_IP: '*************',
  
  // Generate machine-specific URLs
  getMachineUrl(port: number = 9092) {
    return `http://${this.TAILSCALE_IP}:${port}`;
  },
  
  // Magic DNS (if enabled)
  getMagicDnsUrl(port: number = 9092) {
    return `http://${this.MACHINE_NAME}:${port}`;
  }
};

export default GPU_CONFIG;