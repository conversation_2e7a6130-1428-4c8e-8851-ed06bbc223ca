"use client";

import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  Cpu, 
  HardDrive, 
  Thermometer, 
  Zap, 
  Monitor,
  FileText,
  Clock,
  BarChart3,
  Terminal,
  Play,
  Pause,
  RefreshCw
} from 'lucide-react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';

interface GPUStatus {
  name: string;
  driver_version: string;
  cuda_version: string;
  temperature: number;
  power_draw: number;
  power_limit: number;
  memory_used: number;
  memory_total: number;
  utilization: number;
  compute_mode: string;
  processes: GPUProcess[];
  timestamp: string;
}

interface GPUProcess {
  pid: number;
  name: string;
  memory_usage: number;
  type: string;
}

interface DocumentJob {
  id: string;
  filename: string;
  status: 'queued' | 'processing' | 'completed' | 'failed';
  model: string;
  progress: number;
  start_time: string;
  end_time?: string;
  gpu_memory_peak: number;
  processing_time: number;
  input_size: number;
  output_size: number;
}

interface ConsoleLog {
  timestamp: string;
  level: 'info' | 'warn' | 'error' | 'debug';
  service: string;
  message: string;
}

export default function GPUMonitorDashboard() {
  const [gpuStatus, setGpuStatus] = useState<GPUStatus | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [gpuHistory, setGpuHistory] = useState<any[]>([]);
  const [documentJobs, setDocumentJobs] = useState<DocumentJob[]>([]);
  const [consoleLogs, setConsoleLogs] = useState<ConsoleLog[]>([]);
  const [activeJobs, setActiveJobs] = useState<DocumentJob[]>([]);
  
  const wsRef = useRef<WebSocket | null>(null);
  const consoleRef = useRef<HTMLDivElement>(null);

  // WebSocket connection for real-time updates
  useEffect(() => {
    const connectWebSocket = () => {
      try {
        // Use Tailscale IP for secure access
        wsRef.current = new WebSocket('ws://*************:9092/ws/gpu-monitor');
        
        wsRef.current.onopen = () => {
          setIsConnected(true);
          addConsoleLog('info', 'WebSocket', 'Connected to GPU monitor');
        };
        
        wsRef.current.onmessage = (event) => {
          const data = JSON.parse(event.data);
          
          switch (data.type) {
            case 'gpu_status':
              setGpuStatus(data.payload);
              updateGpuHistory(data.payload);
              break;
            case 'document_job':
              updateDocumentJob(data.payload);
              break;
            case 'console_log':
              addConsoleLog(data.payload.level, data.payload.service, data.payload.message);
              break;
            case 'process_update':
              updateActiveJobs(data.payload);
              break;
          }
        };
        
        wsRef.current.onclose = () => {
          setIsConnected(false);
          addConsoleLog('warn', 'WebSocket', 'Disconnected from GPU monitor');
          
          // Reconnect after 3 seconds
          setTimeout(connectWebSocket, 3000);
        };
        
        wsRef.current.onerror = (error) => {
          addConsoleLog('error', 'WebSocket', 'Connection error: ' + error);
        };
        
      } catch (error) {
        addConsoleLog('error', 'WebSocket', 'Failed to connect: ' + error);
      }
    };

    connectWebSocket();

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, []);

  // Auto-scroll console
  useEffect(() => {
    if (consoleRef.current) {
      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;
    }
  }, [consoleLogs]);

  const updateGpuHistory = (status: GPUStatus) => {
    const historyPoint = {
      time: new Date().toLocaleTimeString(),
      utilization: status.utilization,
      memory: (status.memory_used / status.memory_total) * 100,
      temperature: status.temperature,
      power: (status.power_draw / status.power_limit) * 100
    };
    
    setGpuHistory(prev => {
      const newHistory = [...prev, historyPoint];
      return newHistory.slice(-30); // Keep last 30 points
    });
  };

  const updateDocumentJob = (job: DocumentJob) => {
    setDocumentJobs(prev => {
      const index = prev.findIndex(j => j.id === job.id);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = job;
        return updated;
      }
      return [job, ...prev].slice(0, 50); // Keep last 50 jobs
    });
  };

  const updateActiveJobs = (jobs: DocumentJob[]) => {
    setActiveJobs(jobs);
  };

  const addConsoleLog = (level: ConsoleLog['level'], service: string, message: string) => {
    const log: ConsoleLog = {
      timestamp: new Date().toLocaleTimeString(),
      level,
      service,
      message
    };
    
    setConsoleLogs(prev => [...prev, log].slice(-100)); // Keep last 100 logs
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processing': return 'bg-blue-500';
      case 'completed': return 'bg-green-500';
      case 'failed': return 'bg-red-500';
      case 'queued': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getLogColor = (level: string) => {
    switch (level) {
      case 'error': return 'text-red-400';
      case 'warn': return 'text-yellow-400';
      case 'info': return 'text-blue-400';
      case 'debug': return 'text-gray-400';
      default: return 'text-white';
    }
  };

  return (
    <div className="p-6 space-y-6 bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">GPU Monitor Dashboard</h1>
          <p className="text-gray-600">Real-time RTX 4090 monitoring and document processing</p>
        </div>
        <div className="flex items-center space-x-4">
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            {autoRefresh ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            {autoRefresh ? "Pause" : "Resume"}
          </Button>
        </div>
      </div>

      {/* GPU Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Monitor className="w-5 h-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">GPU Name</p>
                <p className="font-semibold">{gpuStatus?.name || "RTX 4090"}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Activity className="w-5 h-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">GPU Utilization</p>
                <p className="font-semibold">{gpuStatus?.utilization || 0}%</p>
                <Progress value={gpuStatus?.utilization || 0} className="mt-1" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <HardDrive className="w-5 h-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">VRAM Usage</p>
                <p className="font-semibold">
                  {gpuStatus ? `${(gpuStatus.memory_used / 1024).toFixed(1)}GB / ${(gpuStatus.memory_total / 1024).toFixed(1)}GB` : "0GB / 24GB"}
                </p>
                <Progress 
                  value={gpuStatus ? (gpuStatus.memory_used / gpuStatus.memory_total) * 100 : 0} 
                  className="mt-1" 
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Thermometer className="w-5 h-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Temperature</p>
                <p className="font-semibold">{gpuStatus?.temperature || 0}°C</p>
                <div className="flex items-center space-x-2 mt-1">
                  <Zap className="w-3 h-3 text-orange-500" />
                  <span className="text-xs text-gray-600">
                    {gpuStatus ? `${gpuStatus.power_draw}W / ${gpuStatus.power_limit}W` : "0W / 480W"}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="processing" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="processing">Document Processing</TabsTrigger>
          <TabsTrigger value="metrics">GPU Metrics</TabsTrigger>
          <TabsTrigger value="console">Live Console</TabsTrigger>
          <TabsTrigger value="processes">GPU Processes</TabsTrigger>
        </TabsList>

        {/* Document Processing Tab */}
        <TabsContent value="processing" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Active Jobs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="w-5 h-5" />
                  <span>Active Processing Jobs</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  {activeJobs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">No active jobs</p>
                  ) : (
                    <div className="space-y-3">
                      {activeJobs.map((job) => (
                        <div key={job.id} className="border rounded-lg p-3">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <p className="font-medium truncate">{job.filename}</p>
                              <p className="text-sm text-gray-600">{job.model}</p>
                            </div>
                            <Badge className={getStatusColor(job.status)}>
                              {job.status}
                            </Badge>
                          </div>
                          <Progress value={job.progress} className="mb-2" />
                          <div className="flex justify-between text-xs text-gray-500">
                            <span>GPU: {job.gpu_memory_peak}MB</span>
                            <span>{job.processing_time}s</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </ScrollArea>
              </CardContent>
            </Card>

            {/* Recent Jobs History */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Clock className="w-5 h-5" />
                  <span>Recent Jobs</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-64">
                  <div className="space-y-2">
                    {documentJobs.slice(0, 10).map((job) => (
                      <div key={job.id} className="flex justify-between items-center p-2 border-b">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium truncate">{job.filename}</p>
                          <p className="text-xs text-gray-500">{job.model}</p>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge size="sm" className={getStatusColor(job.status)}>
                            {job.status}
                          </Badge>
                          <span className="text-xs text-gray-500">
                            {job.processing_time}s
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* GPU Metrics Tab */}
        <TabsContent value="metrics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Utilization Chart */}
            <Card>
              <CardHeader>
                <CardTitle>GPU Utilization Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={gpuHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="utilization" stroke="#3B82F6" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Memory Usage Chart */}
            <Card>
              <CardHeader>
                <CardTitle>VRAM Usage Over Time</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <AreaChart data={gpuHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Area type="monotone" dataKey="memory" stroke="#8B5CF6" fill="#8B5CF6" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Temperature Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Temperature Monitoring</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <LineChart data={gpuHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[30, 90]} />
                    <Tooltip />
                    <Line type="monotone" dataKey="temperature" stroke="#EF4444" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Power Usage Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Power Consumption</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={200}>
                  <AreaChart data={gpuHistory}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip />
                    <Area type="monotone" dataKey="power" stroke="#F59E0B" fill="#F59E0B" fillOpacity={0.3} />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Live Console Tab */}
        <TabsContent value="console">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Terminal className="w-5 h-5" />
                <span>Live Console Output</span>
                <Button size="sm" variant="outline" onClick={() => setConsoleLogs([])}>
                  Clear
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div 
                ref={consoleRef}
                className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm h-96 overflow-y-auto"
              >
                {consoleLogs.length === 0 ? (
                  <p className="text-gray-500">Waiting for console output...</p>
                ) : (
                  consoleLogs.map((log, index) => (
                    <div key={index} className="mb-1">
                      <span className="text-gray-500">[{log.timestamp}]</span>
                      <span className={`ml-2 ${getLogColor(log.level)}`}>{log.level.toUpperCase()}</span>
                      <span className="ml-2 text-blue-400">{log.service}:</span>
                      <span className="ml-2">{log.message}</span>
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* GPU Processes Tab */}
        <TabsContent value="processes">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cpu className="w-5 h-5" />
                <span>GPU Processes</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {gpuStatus?.processes && gpuStatus.processes.length > 0 ? (
                  gpuStatus.processes.map((process, index) => (
                    <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{process.name}</p>
                        <p className="text-sm text-gray-600">PID: {process.pid} | Type: {process.type}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{process.memory_usage} MB</p>
                        <p className="text-sm text-gray-600">GPU Memory</p>
                      </div>
                    </div>
                  ))
                ) : (
                  <p className="text-gray-500 text-center py-8">No GPU processes running</p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}