"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  FileText, 
  Eye, 
  Brain, 
  Layers, 
  Zap, 
  CheckCircle, 
  Clock, 
  AlertCircle,
  Download,
  Upload,
  ArrowRight,
  Cpu,
  HardDrive
} from 'lucide-react';

interface PipelineStage {
  id: string;
  name: string;
  model: string;
  status: 'waiting' | 'processing' | 'completed' | 'failed';
  progress: number;
  gpu_memory: number;
  processing_time: number;
  estimated_time?: number;
  icon: React.ReactNode;
  color: string;
}

interface DocumentProcessingJob {
  id: string;
  filename: string;
  fileSize: number;
  uploadTime: string;
  status: 'uploading' | 'queued' | 'processing' | 'completed' | 'failed';
  currentStage: number;
  stages: PipelineStage[];
  totalProgress: number;
  startTime: string;
  endTime?: string;
  inputPreview?: string;
  outputData?: any;
  errorMessage?: string;
}

const PIPELINE_STAGES: Omit<PipelineStage, 'status' | 'progress' | 'gpu_memory' | 'processing_time'>[] = [
  {
    id: 'surya',
    name: 'OCR & Layout',
    model: 'Surya-OCR',
    icon: <Eye className="w-4 h-4" />,
    color: 'bg-blue-500'
  },
  {
    id: 'layoutlm',
    name: 'Structure Analysis',
    model: 'LayoutLMv3',
    icon: <Layers className="w-4 h-4" />,
    color: 'bg-purple-500'
  },
  {
    id: 'qwen',
    name: 'Content Understanding',
    model: 'Qwen2.5-VL',
    icon: <Brain className="w-4 h-4" />,
    color: 'bg-green-500'
  },
  {
    id: 'minimax',
    name: 'Long Context Analysis',
    model: 'MiniMax-M1',
    icon: <Zap className="w-4 h-4" />,
    color: 'bg-orange-500'
  },
  {
    id: 'phi4',
    name: 'Financial Analysis',
    model: 'Phi-4',
    icon: <FileText className="w-4 h-4" />,
    color: 'bg-red-500'
  }
];

export default function DocumentPipelineVisualizer() {
  const [jobs, setJobs] = useState<DocumentProcessingJob[]>([]);
  const [selectedJob, setSelectedJob] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  // Mock data for demonstration
  useEffect(() => {
    const mockJob: DocumentProcessingJob = {
      id: 'job-1',
      filename: 'BKI001194_Tender_Information_Pack.pdf',
      fileSize: 2.4 * 1024 * 1024, // 2.4MB
      uploadTime: new Date().toISOString(),
      status: 'processing',
      currentStage: 2,
      totalProgress: 45,
      startTime: new Date(Date.now() - 120000).toISOString(), // 2 minutes ago
      stages: PIPELINE_STAGES.map((stage, index) => ({
        ...stage,
        status: index < 2 ? 'completed' : index === 2 ? 'processing' : 'waiting',
        progress: index < 2 ? 100 : index === 2 ? 45 : 0,
        gpu_memory: index < 2 ? (index === 0 ? 3400 : 2800) : index === 2 ? 4200 : 0,
        processing_time: index < 2 ? (index === 0 ? 35 : 28) : index === 2 ? 12 : 0,
        estimated_time: index === 2 ? 25 : index > 2 ? 30 : undefined
      })),
      inputPreview: "ICN Tender Information Pack\n\nProject: Office Cleaning Services\nLocation: 500 Bourke Street, Melbourne\nTender ID: BKI001194\n\nScope of Work:\n- Daily office cleaning\n- Window cleaning (monthly)\n- Carpet cleaning (quarterly)..."
    };

    setJobs([mockJob]);
    setSelectedJob(mockJob.id);
  }, []);

  const getStageIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'processing': return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'failed': return <AlertCircle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-50 border-green-200';
      case 'processing': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'failed': return 'text-red-600 bg-red-50 border-red-200';
      case 'waiting': return 'text-gray-600 bg-gray-50 border-gray-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const selectedJobData = jobs.find(job => job.id === selectedJob);

  return (
    <div className="space-y-6">
      {/* Job Queue */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Document Processing Queue</span>
            <Badge variant="outline">
              {jobs.filter(job => job.status === 'processing').length} Processing
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {jobs.map((job) => (
              <div 
                key={job.id}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedJob === job.id ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedJob(job.id)}
              >
                <div className="flex justify-between items-start mb-2">
                  <div className="flex-1 min-w-0">
                    <p className="font-medium truncate">{job.filename}</p>
                    <p className="text-sm text-gray-600">
                      {(job.fileSize / (1024 * 1024)).toFixed(1)}MB • Uploaded {new Date(job.uploadTime).toLocaleTimeString()}
                    </p>
                  </div>
                  <Badge className={getStatusColor(job.status)}>
                    {job.status}
                  </Badge>
                </div>
                <Progress value={job.totalProgress} className="mb-2" />
                <div className="flex justify-between text-xs text-gray-500">
                  <span>Stage {job.currentStage + 1}/5 • {job.stages[job.currentStage]?.name}</span>
                  <span>{job.totalProgress}% Complete</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Pipeline View */}
      {selectedJobData && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Pipeline Stages */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Processing Pipeline</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {selectedJobData.stages.map((stage, index) => (
                    <div key={stage.id} className="relative">
                      {index < selectedJobData.stages.length - 1 && (
                        <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-200" />
                      )}
                      
                      <div className={`flex items-start space-x-4 p-4 rounded-lg border ${getStatusColor(stage.status)}`}>
                        <div className={`flex-shrink-0 w-12 h-12 rounded-full ${stage.color} flex items-center justify-center text-white`}>
                          {stage.icon}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex justify-between items-start mb-2">
                            <div>
                              <h3 className="font-medium">{stage.name}</h3>
                              <p className="text-sm text-gray-600">{stage.model}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              {getStageIcon(stage.status)}
                              {stage.status === 'processing' && stage.estimated_time && (
                                <span className="text-xs text-gray-500">
                                  ~{stage.estimated_time}s remaining
                                </span>
                              )}
                            </div>
                          </div>
                          
                          {stage.status !== 'waiting' && (
                            <>
                              <Progress value={stage.progress} className="mb-2" />
                              <div className="flex justify-between text-xs text-gray-500">
                                <span className="flex items-center space-x-1">
                                  <HardDrive className="w-3 h-3" />
                                  <span>{stage.gpu_memory}MB GPU</span>
                                </span>
                                <span className="flex items-center space-x-1">
                                  <Clock className="w-3 h-3" />
                                  <span>{stage.processing_time}s</span>
                                </span>
                              </div>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Input/Output Preview */}
          <div className="space-y-6">
            {/* Input Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Upload className="w-4 h-4" />
                  <span>Input Document</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="border rounded-lg p-3 bg-gray-50">
                    <p className="text-sm font-medium mb-2">Document Preview</p>
                    <ScrollArea className="h-32">
                      <pre className="text-xs text-gray-600 whitespace-pre-wrap">
                        {selectedJobData.inputPreview}
                      </pre>
                    </ScrollArea>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Size: {(selectedJobData.fileSize / (1024 * 1024)).toFixed(1)}MB</span>
                    <span>Format: PDF</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Current Stage Output */}
            {selectedJobData.status === 'processing' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Cpu className="w-4 h-4" />
                    <span>Live Processing</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2 text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                      <span>Processing with {selectedJobData.stages[selectedJobData.currentStage].model}</span>
                    </div>
                    
                    <div className="border rounded-lg p-3 bg-gray-50">
                      <p className="text-xs text-gray-600 font-mono">
                        {`> Loading model checkpoint... ✓`}<br/>
                        {`> Initializing CUDA context... ✓`}<br/>
                        {`> Processing document chunks... [3/7]`}<br/>
                        {`> Extracting layout information...`}<br/>
                        {`> Current GPU memory: ${selectedJobData.stages[selectedJobData.currentStage].gpu_memory}MB`}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Output Results */}
            {selectedJobData.status === 'completed' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Download className="w-4 h-4" />
                    <span>Processing Results</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="border rounded-lg p-3 bg-green-50 border-green-200">
                      <p className="text-sm text-green-800 font-medium mb-2">
                        ✓ Processing Complete
                      </p>
                      <div className="text-xs text-green-700 space-y-1">
                        <p>• OCR extracted 2,347 words</p>
                        <p>• Identified 15 sections</p>
                        <p>• Found 8 key requirements</p>
                        <p>• Generated compliance analysis</p>
                      </div>
                    </div>
                    
                    <Button className="w-full" size="sm">
                      Download Results
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Performance Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between text-sm">
                    <span>Total Processing Time</span>
                    <span className="font-medium">
                      {selectedJobData.endTime 
                        ? `${Math.round((new Date(selectedJobData.endTime).getTime() - new Date(selectedJobData.startTime).getTime()) / 1000)}s`
                        : `${Math.round((Date.now() - new Date(selectedJobData.startTime).getTime()) / 1000)}s`
                      }
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>Peak GPU Memory</span>
                    <span className="font-medium">
                      {Math.max(...selectedJobData.stages.map(s => s.gpu_memory))}MB
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>Stages Completed</span>
                    <span className="font-medium">
                      {selectedJobData.stages.filter(s => s.status === 'completed').length}/5
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span>Throughput</span>
                    <span className="font-medium">
                      {selectedJobData.fileSize && selectedJobData.endTime 
                        ? `${((selectedJobData.fileSize / (1024 * 1024)) / ((new Date(selectedJobData.endTime).getTime() - new Date(selectedJobData.startTime).getTime()) / 1000)).toFixed(1)} MB/s`
                        : 'Calculating...'
                      }
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  );
}