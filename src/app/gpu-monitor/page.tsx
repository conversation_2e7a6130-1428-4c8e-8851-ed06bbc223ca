"use client";

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Monitor, 
  Activity, 
  FileText, 
  Settings,
  ArrowLeft,
  ExternalLink
} from 'lucide-react';
import Link from 'next/link';
import GPUMonitorDashboard from '@/components/gpu/GPUMonitorDashboard';
import DocumentPipelineVisualizer from '@/components/gpu/DocumentPipelineVisualizer';

export default function GPUMonitorPage() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Link href="/dashboard">
                <Button variant="ghost" size="sm">
                  <ArrowLeft className="w-4 h-4 mr-2" />
                  Back to Dashboard
                </Button>
              </Link>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">GPU Monitor & Control Center</h1>
                <p className="text-gray-600">RTX 4090 • Real-time monitoring and document processing</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                GPU Online
              </Badge>
              <Button variant="outline" size="sm">
                <ExternalLink className="w-4 h-4 mr-2" />
                Remote Access
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4 lg:w-96">
            <TabsTrigger value="overview" className="flex items-center space-x-2">
              <Monitor className="w-4 h-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="processing" className="flex items-center space-x-2">
              <FileText className="w-4 h-4" />
              <span>Processing</span>
            </TabsTrigger>
            <TabsTrigger value="metrics" className="flex items-center space-x-2">
              <Activity className="w-4 h-4" />
              <span>Metrics</span>
            </TabsTrigger>
            <TabsTrigger value="settings" className="flex items-center space-x-2">
              <Settings className="w-4 h-4" />
              <span>Settings</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab - Full GPU Dashboard */}
          <TabsContent value="overview">
            <GPUMonitorDashboard />
          </TabsContent>

          {/* Document Processing Tab */}
          <TabsContent value="processing">
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle>Document Processing Pipeline</CardTitle>
                  <p className="text-gray-600">
                    Real-time visualization of document processing through the AI pipeline
                  </p>
                </CardHeader>
                <CardContent>
                  <DocumentPipelineVisualizer />
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Detailed Metrics Tab */}
          <TabsContent value="metrics">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Performance Analytics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-8 border rounded-lg bg-gray-50">
                      <Activity className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Advanced performance analytics coming soon</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Track processing efficiency, model performance, and resource optimization
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Historical Data</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="text-center p-8 border rounded-lg bg-gray-50">
                      <Monitor className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">Historical data analysis coming soon</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Long-term trends, performance baselines, and capacity planning
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>GPU Configuration</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Performance Mode</p>
                        <p className="text-sm text-gray-600">Maximum performance for processing</p>
                      </div>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Enabled
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Power Limit</p>
                        <p className="text-sm text-gray-600">480W maximum power consumption</p>
                      </div>
                      <Badge variant="outline">
                        480W
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Memory Clock</p>
                        <p className="text-sm text-gray-600">Memory frequency optimization</p>
                      </div>
                      <Badge variant="outline">
                        Auto
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monitoring Settings</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Refresh Rate</p>
                        <p className="text-sm text-gray-600">Data update frequency</p>
                      </div>
                      <Badge variant="outline">
                        2 seconds
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Alert Threshold</p>
                        <p className="text-sm text-gray-600">Temperature warning limit</p>
                      </div>
                      <Badge variant="outline">
                        85°C
                      </Badge>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium">Data Retention</p>
                        <p className="text-sm text-gray-600">Historical data storage</p>
                      </div>
                      <Badge variant="outline">
                        30 days
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}