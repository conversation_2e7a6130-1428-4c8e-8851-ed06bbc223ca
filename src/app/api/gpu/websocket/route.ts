import { NextRequest } from 'next/server';
import { WebSocketServer } from 'ws';
import { gpuMonitorService } from '@/lib/gpu-monitor';

// Global WebSocket server instance
let wss: WebSocketServer | null = null;

export async function GET(request: NextRequest) {
  // Extract the socket from the upgrade request
  const upgrade = request.headers.get('upgrade');
  
  if (upgrade !== 'websocket') {
    return new Response('Expected websocket upgrade', { status: 400 });
  }

  try {
    // Initialize WebSocket server if not already done
    if (!wss) {
      wss = new WebSocketServer({ port: 9091 });
      
      wss.on('connection', (ws) => {
        console.log('New GPU monitor WebSocket connection');
        
        // Add connection to GPU monitor service
        gpuMonitorService.addConnection(ws);
        
        ws.on('close', () => {
          console.log('GPU monitor WebSocket connection closed');
        });
        
        ws.on('error', (error) => {
          console.error('GPU monitor WebSocket error:', error);
        });
        
        // Send welcome message
        ws.send(JSON.stringify({
          type: 'connected',
          payload: { message: 'Connected to GPU monitor' },
          timestamp: new Date().toISOString()
        }));
      });
      
      console.log('GPU monitor WebSocket server started on port 9091');
    }

    return new Response('WebSocket server running', { status: 200 });
  } catch (error) {
    console.error('Failed to initialize WebSocket server:', error);
    return new Response('Failed to initialize WebSocket server', { status: 500 });
  }
}

// Handle WebSocket upgrade in a separate function for Next.js
export const dynamic = 'force-dynamic';