import { NextRequest, NextResponse } from 'next/server';
import { <PERSON>UCommandExecutor, GPUPerformanceTracker } from '@/lib/gpu-monitor';

export async function GET(request: NextRequest) {
  try {
    // Get detailed GPU status
    const gpuStatus = await GPUCommandExecutor.getDetailedGPUStatus();
    const healthInfo = await GPUCommandExecutor.checkGPUHealth();
    
    if (!gpuStatus) {
      // Return mock data if GPU is not accessible
      const mockStatus = {
        name: "NVIDIA GeForce RTX 4090",
        driver_version: "575.57.08",
        cuda_version: "12.9",
        temperature: 48,
        power_draw: 187,
        power_limit: 480,
        memory_used: 4200,
        memory_total: 24564,
        utilization: 35,
        compute_mode: "Default",
        processes: [
          {
            pid: 1234,
            name: "python",
            memory_usage: 2500,
            type: "C"
          }
        ],
        health: {
          temperature: 48,
          isThrottling: false,
          powerLimit: 480,
          memoryErrors: 0
        },
        performance: {
          averageUtilization: GPUPerformanceTracker.getAverageUtilization(),
          peakMemoryUsage: GPUPerformanceTracker.getPeakMemoryUsage(),
          throughput: GPUPerformanceTracker.getProcessingThroughput()
        },
        timestamp: new Date().toISOString()
      };
      
      return NextResponse.json(mockStatus);
    }

    // Parse the CSV data from nvidia-smi
    const basicInfo = gpuStatus.basic_info[0];
    const processes = gpuStatus.processes.map((proc: string[]) => ({
      pid: parseInt(proc[0]),
      name: proc[1],
      memory_usage: parseInt(proc[2]),
      type: "C"
    }));

    const response = {
      name: basicInfo[0],
      driver_version: basicInfo[1],
      cuda_version: "12.9", // Static for now
      pstate: basicInfo[2],
      temperature: parseInt(basicInfo[3]),
      power_draw: parseFloat(basicInfo[4]),
      power_limit: parseFloat(basicInfo[5]),
      memory_used: parseInt(basicInfo[6]),
      memory_total: parseInt(basicInfo[7]),
      utilization: parseInt(basicInfo[8]),
      compute_mode: "Default",
      processes,
      health: healthInfo,
      performance: {
        averageUtilization: GPUPerformanceTracker.getAverageUtilization(),
        peakMemoryUsage: GPUPerformanceTracker.getPeakMemoryUsage(),
        throughput: GPUPerformanceTracker.getProcessingThroughput()
      },
      clocks: gpuStatus.clocks[0] ? {
        graphics_current: parseInt(gpuStatus.clocks[0][0]),
        memory_current: parseInt(gpuStatus.clocks[0][1]),
        graphics_max: parseInt(gpuStatus.clocks[0][2]),
        memory_max: parseInt(gpuStatus.clocks[0][3])
      } : null,
      timestamp: new Date().toISOString()
    };

    // Add to performance tracker
    GPUPerformanceTracker.addMetric({
      utilization: response.utilization,
      memory_used: response.memory_used,
      temperature: response.temperature,
      power_draw: response.power_draw
    });

    return NextResponse.json(response);
    
  } catch (error) {
    console.error('GPU status API error:', error);
    
    // Return error response with mock data
    return NextResponse.json({
      error: 'Failed to fetch GPU status',
      message: error instanceof Error ? error.message : 'Unknown error',
      mockData: {
        name: "NVIDIA GeForce RTX 4090",
        driver_version: "575.57.08",
        cuda_version: "12.9",
        temperature: 45,
        power_draw: 150,
        power_limit: 480,
        memory_used: 0,
        memory_total: 24564,
        utilization: 0,
        compute_mode: "Default",
        processes: [],
        timestamp: new Date().toISOString()
      }
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { command } = await request.json();
    
    if (!command) {
      return NextResponse.json({ error: 'Command is required' }, { status: 400 });
    }

    // Execute command on GPU machine
    const output = await GPUCommandExecutor.executeCommand(command);
    
    return NextResponse.json({
      command,
      output,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('GPU command execution error:', error);
    
    return NextResponse.json({
      error: 'Command execution failed',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}