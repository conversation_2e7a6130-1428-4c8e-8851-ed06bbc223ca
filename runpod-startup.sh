#!/bin/bash
# RunPod Startup Script - Automatically runs when pod starts

echo "🚀 Starting Document Processing Pipeline on RunPod"
echo "================================================"

# Set environment variables
export WORKSPACE=/workspace
export HF_HOME=$WORKSPACE/models
export TORCH_HOME=$WORKSPACE/models/torch
export TRANSFORMERS_CACHE=$WORKSPACE/models/transformers

# System info
echo "📊 System Information:"
echo "  Hostname: $(hostname)"
echo "  CPU: $(nproc) cores"
echo "  Memory: $(free -h | grep Mem | awk '{print $2}')"
echo "  GPU: $(nvidia-smi --query-gpu=name --format=csv,noheader | head -1)"
echo "  CUDA: $(nvidia-smi --query-gpu=driver_version --format=csv,noheader | head -1)"
echo ""

# Install system dependencies
echo "📦 Installing system dependencies..."
apt-get update -qq
apt-get install -y --no-install-recommends \
    git \
    wget \
    curl \
    docker.io \
    docker-compose \
    htop \
    nvtop \
    jq \
    > /dev/null 2>&1

# Clone repository if not exists
if [ ! -d "$WORKSPACE/doc-processor" ]; then
    echo "📥 Cloning repository..."
    cd $WORKSPACE
    git clone https://github.com/yourusername/doc-processor.git
fi

cd $WORKSPACE/doc-processor

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p $WORKSPACE/{models,uploads,outputs,redis-data,postgres-data}

# Download models if not already downloaded
if [ ! -f "$WORKSPACE/models/.models_downloaded" ]; then
    echo "📥 Downloading AI models (this may take 10-15 minutes)..."
    python3 scripts/download-models.py
else
    echo "✅ Models already downloaded"
fi

# Start Docker services
echo "🐳 Starting Docker services..."
systemctl start docker || service docker start

# Wait for Docker to be ready
sleep 5

# Pull latest images
echo "📥 Pulling Docker images..."
docker-compose -f runpod-docker-compose.yml pull

# Start the services
echo "🚀 Starting document processing services..."
docker-compose -f runpod-docker-compose.yml up -d

# Wait for services to start
echo "⏳ Waiting for services to be ready..."
sleep 30

# Health check
echo "🏥 Checking service health..."
for i in {1..10}; do
    if curl -s http://localhost:8000/health > /dev/null; then
        echo "✅ Gateway is healthy!"
        break
    else
        echo "  Attempt $i/10 - Gateway not ready yet..."
        sleep 10
    fi
done

# Show service status
echo ""
echo "📊 Service Status:"
docker-compose -f runpod-docker-compose.yml ps

# Get RunPod proxy URLs
if [ ! -z "$RUNPOD_POD_ID" ]; then
    echo ""
    echo "🌐 Access URLs:"
    echo "  Gateway API: https://${RUNPOD_POD_ID}-8000.proxy.runpod.net"
    echo "  Nginx Proxy: https://${RUNPOD_POD_ID}-80.proxy.runpod.net"
    echo "  Direct SSH: ssh root@${RUNPOD_POD_ID}.runpod.io -p 22"
fi

# Show resource usage
echo ""
echo "💻 Resource Usage:"
nvidia-smi
echo ""
docker stats --no-stream

# Create helpful aliases
cat >> ~/.bashrc << 'EOF'

# Document Processing Aliases
alias dp-status='docker-compose -f /workspace/doc-processor/runpod-docker-compose.yml ps'
alias dp-logs='docker-compose -f /workspace/doc-processor/runpod-docker-compose.yml logs -f'
alias dp-restart='docker-compose -f /workspace/doc-processor/runpod-docker-compose.yml restart'
alias dp-gpu='watch -n 1 nvidia-smi'
alias dp-test='curl -s http://localhost:8000/health | jq .'

EOF

echo ""
echo "✅ Document Processing Pipeline is ready!"
echo ""
echo "📝 Useful commands:"
echo "  dp-status  - Check service status"
echo "  dp-logs    - View logs"
echo "  dp-restart - Restart services"
echo "  dp-gpu     - Monitor GPU usage"
echo "  dp-test    - Test health endpoint"
echo ""
echo "🎉 Happy document processing!"