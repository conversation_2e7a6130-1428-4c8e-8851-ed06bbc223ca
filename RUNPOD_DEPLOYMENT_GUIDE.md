# 🚀 RunPod Deployment Guide - Document Processing Pipeline

## 📋 Prerequisites

1. **RunPod Account**: Create at [runpod.io](https://runpod.io)
2. **RunPod Credits**: Add credits for GPU usage
3. **HuggingFace Token** (optional): For gated models like Qwen2.5-VL

## 🎯 Deployment Options

### **Option 1: RunPod Templates (Recommended)**

#### Step 1: Create Docker Images

```bash
# Build and push images to Docker Hub
docker build -t yourusername/doc-gateway:latest ./gateway
docker build -t yourusername/doc-surya:latest ./services/surya

docker push yourusername/doc-gateway:latest
docker push yourusername/doc-surya:latest
```

#### Step 2: Create RunPod Template

1. Go to [RunPod Templates](https://www.runpod.io/console/templates)
2. Click "New Template"
3. Configure:

```yaml
Template Name: Document Processing Pipeline
Container Image: yourusername/doc-gateway:latest
Container Disk: 50 GB
Volume Disk: 200 GB
Volume Mount Path: /workspace
Expose HTTP Ports: 8000,8001,8002,8003,80
Expose TCP Ports: 5432,6379
Environment Variables:
  - RUNPOD_GPU_COUNT=1
  - HF_TOKEN=your_huggingface_token
  - ENABLE_FP16=true
Docker Command: |
  cd /workspace && \
  git clone https://github.com/yourusername/doc-processor.git && \
  cd doc-processor && \
  docker-compose -f runpod-docker-compose.yml up -d
```

#### Step 3: Deploy Pod

1. Go to [RunPod Pods](https://www.runpod.io/console/pods)
2. Click "Deploy"
3. Select your template
4. Choose GPU:
   - **RTX 4090**: Best value for this workload
   - **RTX A5000**: More VRAM (24GB)
   - **A100 40GB**: For production scale
   - **A100 80GB**: For maximum context length
5. Deploy!

### **Option 2: RunPod CLI Deployment**

```bash
# Install RunPod CLI
pip install runpodctl

# Login
runpodctl login

# Create pod with custom configuration
runpodctl create pod \
  --name "doc-processor" \
  --gpu-type "RTX 4090" \
  --gpu-count 1 \
  --container-disk-size 50 \
  --volume-size 200 \
  --ports "8000:8000,8001:8001,8002:8002,8003:8003,80:80" \
  --env "HF_TOKEN=your_token" \
  --bid-price 0.5
```

### **Option 3: RunPod Serverless (For Scaling)**

Create serverless endpoint for auto-scaling:

```python
# runpod_serverless.py
import runpod

def handler(job):
    """Process document with our pipeline"""
    document = job["input"]["document"]
    models = job["input"].get("models", ["surya", "qwen"])
    
    # Process document
    results = process_document(document, models)
    
    return {"output": results}

# Configure handler
runpod.serverless.start({
    "handler": handler,
    "gpu_type": "RTX 4090",
    "max_workers": 10,
    "idle_timeout": 60
})
```

## 🔧 Post-Deployment Setup

### 1. **SSH into Your Pod**

```bash
# Get pod ID from RunPod dashboard
ssh root@[pod-id].runpod.io -p 22
```

### 2. **Clone Repository**

```bash
cd /workspace
git clone https://github.com/yourusername/your-repo.git doc-processor
cd doc-processor
```

### 3. **Configure Environment**

```bash
# Copy and edit environment file
cp .env.docker.example .env.docker
nano .env.docker

# Add your keys
HF_TOKEN=your_huggingface_token
RUNPOD_API_KEY=your_runpod_api_key
```

### 4. **Start Services**

```bash
# Use RunPod-optimized compose file
docker-compose -f runpod-docker-compose.yml up -d

# Check status
docker-compose ps

# View logs
docker-compose logs -f
```

### 5. **Download Models (First Run)**

```bash
# This will download all models to persistent storage
docker-compose --profile setup up model-downloader

# Models are cached in /workspace/models
```

## 🌐 Accessing Your Services

### **RunPod Proxy URLs**

RunPod provides proxy URLs for your services:

```
https://[pod-id]-8000.proxy.runpod.net  → Gateway API
https://[pod-id]-8001.proxy.runpod.net  → Qwen Model
https://[pod-id]-8002.proxy.runpod.net  → MiniMax Model
https://[pod-id]-8003.proxy.runpod.net  → Phi-4 Model
https://[pod-id]-80.proxy.runpod.net    → Nginx (Main Entry)
```

### **Update Your Next.js App**

```typescript
// .env.production
DOCKER_GATEWAY_URL=https://[pod-id]-80.proxy.runpod.net

// In your code
const processor = new DocumentProcessor({
  gatewayUrl: process.env.DOCKER_GATEWAY_URL,
  apiKey: process.env.DOCKER_API_KEY
});
```

## 📊 RunPod GPU Recommendations

| GPU Type | VRAM | Price/hr | Best For |
|----------|------|----------|----------|
| RTX 4090 | 24GB | $0.44 | Development & Testing |
| RTX A5000 | 24GB | $0.38 | Production (Stable) |
| RTX 3090 | 24GB | $0.22 | Budget Option |
| A100 40GB | 40GB | $1.09 | High Volume |
| A100 80GB | 80GB | $1.89 | Maximum Context |
| H100 80GB | 80GB | $4.89 | Enterprise Scale |

**Recommendation**: Start with **RTX 4090** - best price/performance ratio

## 🚀 Performance Optimization for RunPod

### **1. Model Caching**
```yaml
volumes:
  - /workspace/models:/models  # Persistent model storage
```

### **2. GPU Memory Allocation**
```bash
# For RTX 4090 (24GB)
SURYA_GPU_UTIL=0.15      # ~3.6GB
QWEN_GPU_UTIL=0.40       # ~9.6GB
LAYOUTLM_GPU_UTIL=0.15   # ~3.6GB
MINIMAX_GPU_UTIL=0.85    # ~20.4GB (AWQ quantized)
PHI4_GPU_UTIL=0.25       # ~6GB
```

### **3. Batch Processing**
```yaml
SURYA_BATCH_SIZE=8       # Increased from 4
LAYOUTLM_BATCH=16        # Increased from 8
```

## 🔍 Monitoring & Logs

### **RunPod Dashboard**
- GPU utilization
- Memory usage
- Network traffic
- Container logs

### **Custom Monitoring**
```bash
# SSH into pod
ssh root@[pod-id].runpod.io

# GPU monitoring
watch -n 1 nvidia-smi

# Service logs
docker-compose logs -f gateway

# All logs
docker-compose logs -f
```

## 💰 Cost Optimization

### **1. Spot Instances**
```bash
# Use bid pricing for 50-70% savings
runpodctl create pod --bid-price 0.3
```

### **2. Auto-Stop**
```python
# Add to your gateway
@app.on_event("startup")
async def setup_auto_stop():
    # Stop pod after 30 min of inactivity
    scheduler.add_job(
        check_and_stop_if_idle,
        'interval',
        minutes=5,
        id='idle_checker'
    )
```

### **3. Serverless for Sporadic Use**
- Only pay for processing time
- Auto-scales to demand
- No idle costs

## 🛠️ Troubleshooting

### **GPU Not Detected**
```bash
# Check CUDA installation
nvidia-smi

# Verify Docker GPU support
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
```

### **Model Download Issues**
```bash
# Manual download with HF token
export HF_TOKEN=your_token
huggingface-cli download Qwen/Qwen2.5-VL-7B --local-dir /workspace/models/qwen
```

### **Out of Memory**
```bash
# Reduce batch sizes
export SURYA_BATCH_SIZE=2
export LAYOUTLM_BATCH=4

# Use more aggressive quantization
export QUANTIZATION=int8
```

## 📱 RunPod Mobile App

Monitor your deployment on the go:
- iOS/Android apps available
- Real-time GPU stats
- Start/stop pods
- View logs

## 🎯 Expected Performance on RunPod

With RTX 4090:
- **50-page tender**: 30-45 seconds
- **Throughput**: 80-100 pages/minute
- **Cost**: ~$0.10-0.15 per document

With A100 40GB:
- **50-page tender**: 20-30 seconds
- **Throughput**: 150-200 pages/minute
- **Cost**: ~$0.20-0.30 per document

## 🔒 Security Notes

1. **Use RunPod Private Network** for internal communication
2. **Enable API authentication** on gateway
3. **Rotate credentials** regularly
4. **Use secure endpoints** (HTTPS)
5. **Monitor access logs**

---

Ready to deploy? RunPod makes it easy to get enterprise-grade document processing running in minutes! 🚀