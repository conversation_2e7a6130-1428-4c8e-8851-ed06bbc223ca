version: '3.9'

services:
  # FastAPI Gateway - Routes requests to appropriate model services
  gateway:
    build: ./gateway
    container_name: doc-gateway
    ports:
      - "8000:8000"
    environment:
      - REDIS_URL=redis://redis:6379
      - DATABASE_URL=******************************************/docdb
      - ENABLE_METRICS=true
    depends_on:
      - redis
      - postgres
    volumes:
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
    networks:
      - doc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Surya - OCR and Layout Detection
  surya:
    build: 
      context: ./services/surya
      dockerfile: Dockerfile
    container_name: surya-ocr
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 8G
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - SURYA_BATCH_SIZE=${SURYA_BATCH_SIZE:-4}
      - MODEL_CACHE=/models
      - ENABLE_FP16=true
    volumes:
      - ./models/surya:/models
      - ./shared:/shared
    networks:
      - doc-network
    restart: unless-stopped

  # Qwen2.5-VL - Multimodal Understanding
  qwen:
    build: 
      context: ./services/qwen
      dockerfile: Dockerfile
    container_name: qwen-vl
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 12G
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=Qwen/Qwen2.5-VL-7B
      - VLLM_GPU_MEMORY_UTILIZATION=${QWEN_GPU_UTIL:-0.4}
      - TENSOR_PARALLEL_SIZE=1
      - MODEL_CACHE=/models
    volumes:
      - ./models/qwen:/models
      - ./shared:/shared
    ports:
      - "8001:8000"
    networks:
      - doc-network
    restart: unless-stopped

  # LayoutLMv3 - Document Structure Analysis
  layoutlm:
    build: 
      context: ./services/layoutlm
      dockerfile: Dockerfile
    container_name: layoutlm-v3
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 6G
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=microsoft/layoutlmv3-base
      - BATCH_SIZE=${LAYOUTLM_BATCH:-8}
      - MODEL_CACHE=/models
    volumes:
      - ./models/layoutlm:/models
      - ./shared:/shared
    networks:
      - doc-network
    restart: unless-stopped

  # MiniMax-M1 - Long Context Processing
  minimax:
    build: 
      context: ./services/minimax
      dockerfile: Dockerfile
    container_name: minimax-m1
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 24G
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=MiniMax-01-M1
      - VLLM_GPU_MEMORY_UTILIZATION=${MINIMAX_GPU_UTIL:-0.9}
      - QUANTIZATION=awq
      - MAX_MODEL_LEN=1000000
      - MODEL_CACHE=/models
    volumes:
      - ./models/minimax:/models
      - ./shared:/shared
    ports:
      - "8002:8000"
    networks:
      - doc-network
    restart: unless-stopped
    command: >
      vllm serve MiniMax-01-M1 
      --max-model-len 1000000 
      --gpu-memory-utilization 0.9
      --dtype float16
      --quantization awq

  # Phi-4 - Financial Analysis
  phi4:
    build: 
      context: ./services/phi4
      dockerfile: Dockerfile
    container_name: phi-4
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
        limits:
          memory: 10G
    environment:
      - CUDA_VISIBLE_DEVICES=0
      - MODEL_NAME=microsoft/phi-4
      - VLLM_GPU_MEMORY_UTILIZATION=${PHI4_GPU_UTIL:-0.3}
      - MODEL_CACHE=/models
    volumes:
      - ./models/phi4:/models
      - ./shared:/shared
    ports:
      - "8003:8000"
    networks:
      - doc-network
    restart: unless-stopped

  # Redis for caching and job queue
  redis:
    image: redis:7-alpine
    container_name: doc-redis
    command: redis-server --appendonly yes --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
    networks:
      - doc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # PostgreSQL for metadata and results
  postgres:
    image: postgres:16-alpine
    container_name: doc-postgres
    environment:
      - POSTGRES_USER=docuser
      - POSTGRES_PASSWORD=docpass
      - POSTGRES_DB=docdb
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    networks:
      - doc-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U docuser"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Optional: Monitoring with Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: doc-prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
    ports:
      - "9090:9090"
    networks:
      - doc-network
    restart: unless-stopped
    profiles: ["monitoring"]

  # Optional: Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    container_name: doc-grafana
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_SECURITY_ADMIN_USER=admin
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    ports:
      - "3001:3000"
    networks:
      - doc-network
    depends_on:
      - prometheus
    restart: unless-stopped
    profiles: ["monitoring"]

networks:
  doc-network:
    driver: bridge

volumes:
  redis-data:
  postgres-data:
  prometheus-data:
  grafana-data: