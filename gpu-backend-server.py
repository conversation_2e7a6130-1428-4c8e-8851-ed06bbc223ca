#!/usr/bin/env python3
"""
GPU Backend Server for Real-time Monitoring and Document Processing
Runs on the RTX 4090 machine (192.168.1.100)
"""

import asyncio
import json
import subprocess
import time
import logging
from datetime import datetime
from typing import Dict, List, Optional
from pathlib import Path

from fastapi import Fast<PERSON><PERSON>, WebSocket, WebSocketDisconnect, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import J<PERSON><PERSON>esponse
from pydantic import BaseModel
import uvicorn

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="GPU Monitor Backend", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# WebSocket connections
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"New WebSocket connection. Total: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
            logger.info(f"WebSocket disconnected. Total: {len(self.active_connections)}")

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception:
                disconnected.append(connection)
        
        # Remove disconnected connections
        for conn in disconnected:
            self.disconnect(conn)

manager = ConnectionManager()

# Data models
class CommandRequest(BaseModel):
    command: str

def get_nvidia_smi_output(query: str) -> str:
    """Execute nvidia-smi command and return output."""
    try:
        cmd = f"nvidia-smi --query-gpu={query} --format=csv,noheader,nounits"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            return result.stdout.strip()
        else:
            logger.error(f"nvidia-smi error: {result.stderr}")
            return ""
    except Exception as e:
        logger.error(f"nvidia-smi execution failed: {e}")
        return ""

def get_gpu_processes() -> List[Dict]:
    """Get GPU processes information."""
    try:
        cmd = "nvidia-smi --query-compute-apps=pid,process_name,used_memory --format=csv,noheader,nounits"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        processes = []
        if result.returncode == 0 and result.stdout.strip():
            for line in result.stdout.strip().split('\n'):
                parts = line.split(', ')
                if len(parts) >= 3:
                    processes.append({
                        'pid': int(parts[0]),
                        'name': parts[1],
                        'memory_usage': int(parts[2]),
                        'type': 'C'
                    })
        return processes
    except Exception as e:
        logger.error(f"Failed to get GPU processes: {e}")
        return []

def get_gpu_status() -> Dict:
    """Get comprehensive GPU status."""
    try:
        basic_info = get_nvidia_smi_output(
            "name,driver_version,temperature.gpu,power.draw,power.limit,memory.used,memory.total,utilization.gpu"
        )
        
        if not basic_info:
            raise Exception("Failed to get basic GPU info")
        
        parts = basic_info.split(', ')
        if len(parts) < 8:
            raise Exception("Incomplete GPU info")
        
        processes = get_gpu_processes()
        
        return {
            "name": parts[0],
            "driver_version": parts[1],
            "cuda_version": "12.9",
            "temperature": float(parts[2]),
            "power_draw": float(parts[3]),
            "power_limit": float(parts[4]),
            "memory_used": int(parts[5]),
            "memory_total": int(parts[6]),
            "utilization": float(parts[7]),
            "compute_mode": "Default",
            "processes": processes,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"Failed to get GPU status: {e}")
        return {
            "name": "NVIDIA GeForce RTX 4090",
            "driver_version": "575.57.08",
            "cuda_version": "12.9",
            "temperature": 45.0,
            "power_draw": 150.0,
            "power_limit": 480.0,
            "memory_used": 0,
            "memory_total": 24564,
            "utilization": 0.0,
            "compute_mode": "Default",
            "processes": [],
            "timestamp": datetime.now().isoformat()
        }

# Background monitoring task
async def gpu_monitoring_task():
    """Background task to monitor GPU and broadcast updates."""
    while True:
        try:
            gpu_status = get_gpu_status()
            message = {
                "type": "gpu_status",
                "payload": gpu_status,
                "timestamp": datetime.now().isoformat()
            }
            await manager.broadcast(json.dumps(message))
            
            if gpu_status["utilization"] > 0:
                console_message = {
                    "type": "console_log",
                    "payload": {
                        "level": "info",
                        "service": "GPU",
                        "message": f"GPU utilization: {gpu_status['utilization']}% | Memory: {gpu_status['memory_used']}MB | Temp: {gpu_status['temperature']}°C"
                    },
                    "timestamp": datetime.now().isoformat()
                }
                await manager.broadcast(json.dumps(console_message))
            
        except Exception as e:
            logger.error(f"GPU monitoring error: {e}")
            
        await asyncio.sleep(2)

# API Routes
@app.get("/")
async def root():
    return {"message": "GPU Monitor Backend Running", "gpu_available": True}

@app.get("/api/gpu/status")
async def get_gpu_status_api():
    """Get current GPU status."""
    try:
        status = get_gpu_status()
        return status
    except Exception as e:
        logger.error(f"GPU status API error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/gpu/execute")
async def execute_gpu_command(request: CommandRequest):
    """Execute a command on the GPU machine."""
    try:
        allowed_commands = [
            "nvidia-smi",
            "nvidia-smi -l",
            "nvidia-smi --query-gpu",
            "nvidia-smi --query-compute-apps",
            "gpustat"
        ]
        
        if not any(request.command.startswith(cmd) for cmd in allowed_commands):
            raise HTTPException(status_code=403, detail="Command not allowed")
        
        result = subprocess.run(
            request.command,
            shell=True,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return {
            "command": request.command,
            "returncode": result.returncode,
            "output": result.stdout,
            "error": result.stderr,
            "timestamp": datetime.now().isoformat()
        }
        
    except subprocess.TimeoutExpired:
        raise HTTPException(status_code=408, detail="Command timed out")
    except Exception as e:
        logger.error(f"Command execution error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.websocket("/ws/gpu-monitor")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time GPU monitoring."""
    await manager.connect(websocket)
    
    try:
        gpu_status = get_gpu_status()
        await websocket.send_text(json.dumps({
            "type": "gpu_status",
            "payload": gpu_status,
            "timestamp": datetime.now().isoformat()
        }))
    except Exception as e:
        logger.error(f"Failed to send initial status: {e}")
    
    try:
        while True:
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        manager.disconnect(websocket)

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    try:
        gpu_status = get_gpu_status()
        return {
            "status": "healthy",
            "gpu_available": True,
            "gpu_name": gpu_status["name"],
            "gpu_utilization": gpu_status["utilization"],
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        return {
            "status": "degraded",
            "gpu_available": False,
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }

@app.on_event("startup")
async def startup_event():
    """Start background tasks."""
    logger.info("Starting GPU Monitor Backend Server")
    asyncio.create_task(gpu_monitoring_task())
    logger.info("GPU monitoring task started")

if __name__ == "__main__":
    uvicorn.run(
        "gpu-backend-server:app",
        host="0.0.0.0",
        port=9090,
        reload=False,
        log_level="info"
    )
