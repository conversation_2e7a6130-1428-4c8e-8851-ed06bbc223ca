# Document Processing Pipeline Makefile

.PHONY: help build up down logs ps clean test health

# Default target
help:
	@echo "Document Processing Pipeline - Available Commands:"
	@echo ""
	@echo "  make build          - Build all Docker images"
	@echo "  make up             - Start all services (local mode)"
	@echo "  make up-cloud       - Start all services (cloud mode)"
	@echo "  make down           - Stop all services"
	@echo "  make logs           - View logs from all services"
	@echo "  make ps             - Show running services"
	@echo "  make clean          - Clean up volumes and images"
	@echo "  make test-upload    - Test document upload"
	@echo "  make test-process   - Test document processing"
	@echo "  make health         - Check health of all services"
	@echo "  make monitoring     - Start with monitoring enabled"
	@echo ""

# Build all images
build:
	docker-compose build

# Start services (local mode)
up:
	docker-compose up -d

# Start services (cloud mode)
up-cloud:
	docker-compose -f docker-compose.yml -f docker-compose.cloud.yml up -d

# Stop all services
down:
	docker-compose down

# View logs
logs:
	docker-compose logs -f

# Show running services
ps:
	docker-compose ps

# Clean up
clean:
	docker-compose down -v
	docker system prune -f

# Health check
health:
	@echo "Checking service health..."
	@curl -s http://localhost:8000/health | jq . || echo "Gateway not responding"

# Start with monitoring
monitoring:
	docker-compose --profile monitoring up -d

# Test upload
test-upload:
	@echo "Testing document upload..."
	@curl -X POST -F "file=@test-document.pdf" http://localhost:8000/api/upload | jq .

# Test processing
test-process:
	@echo "Testing document processing..."
	@curl -X POST \
		-H "Content-Type: application/json" \
		-d '{"document_id": "test-id", "models": ["surya", "layoutlm"]}' \
		http://localhost:8000/api/process | jq .

# GPU monitoring
gpu-monitor:
	@watch -n 1 nvidia-smi

# Service-specific logs
logs-gateway:
	docker-compose logs -f gateway

logs-surya:
	docker-compose logs -f surya

logs-qwen:
	docker-compose logs -f qwen

logs-minimax:
	docker-compose logs -f minimax

# Development helpers
dev-gateway:
	cd gateway && uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Database operations
db-shell:
	docker-compose exec postgres psql -U docuser -d docdb

redis-cli:
	docker-compose exec redis redis-cli

# Monitoring URLs
urls:
	@echo "Service URLs:"
	@echo "  Gateway:    http://localhost:8000"
	@echo "  Prometheus: http://localhost:9090"
	@echo "  Grafana:    http://localhost:3001"
	@echo "  Flower:     http://localhost:5555"
	@echo "  Jaeger:     http://localhost:16686"