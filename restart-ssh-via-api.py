#!/usr/bin/env python3

"""
SSH Recovery via GPU Backend API
Attempts to restart SSH service through the working GPU backend
"""

import requests
import json
import time

GPU_BASE_URL = "http://*************:9092"

def test_ssh_restart():
    """Try to restart SSH via the GPU backend if it has system commands"""
    
    print("🔧 Attempting SSH Recovery via GPU Backend API")
    print("=" * 50)
    
    # Test 1: Check if GPU backend has system command capability
    print("1️⃣ Testing GPU backend health...")
    try:
        response = requests.get(f"{GPU_BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            print("✅ GPU backend is healthy")
            health_data = response.json()
            print(f"   GPU: {health_data.get('gpu_name', 'Unknown')}")
        else:
            print("❌ GPU backend health check failed")
            return False
    except Exception as e:
        print(f"❌ Failed to connect to GPU backend: {e}")
        return False
    
    print()
    
    # Test 2: Try different command endpoints
    print("2️⃣ Testing command execution capabilities...")
    
    # Try the execute endpoint we've seen before
    test_commands = [
        "echo 'API test successful'",
        "whoami",
        "systemctl status ssh",
        "systemctl restart ssh"
    ]
    
    for cmd in test_commands:
        print(f"   Testing: {cmd}")
        try:
            payload = {"command": cmd}
            response = requests.post(
                f"{GPU_BASE_URL}/api/gpu/execute",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ Success: {result}")
                if cmd == "systemctl restart ssh":
                    print("   🎉 SSH restart command executed!")
                    return True
            else:
                print(f"   ❌ Failed: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print()
    
    # Test 3: Alternative endpoints
    print("3️⃣ Checking for alternative endpoints...")
    
    endpoints_to_test = [
        "/api/system/restart-ssh",
        "/api/admin/command",
        "/api/system/service",
        "/admin/ssh-restart",
        "/docs"  # Check API documentation
    ]
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.get(f"{GPU_BASE_URL}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ Found endpoint: {endpoint}")
                if endpoint == "/docs":
                    print("   📚 API documentation available - check for system commands")
            elif response.status_code != 404:
                print(f"   ⚠️  Endpoint {endpoint} returned {response.status_code}")
        except:
            pass
    
    return False

def alternative_approaches():
    """Suggest alternative approaches if API method fails"""
    print()
    print("🛠️ Alternative Recovery Methods")
    print("=" * 35)
    print()
    
    print("Method A: Physical/Console Access")
    print("  Since port 22 is open but SSH hangs:")
    print("  1. Access machine physically or via console")
    print("  2. Run: sudo systemctl restart ssh")
    print("  3. Run: sudo systemctl restart sshd")
    print("  4. Check: sudo journalctl -u ssh -f")
    print()
    
    print("Method B: Network Reset")
    print("  1. Restart network: sudo systemctl restart networking")
    print("  2. Restart Tailscale: sudo systemctl restart tailscaled")
    print("  3. Check firewall: sudo ufw status")
    print()
    
    print("Method C: SSH Configuration Reset")
    print("  1. Check SSH config: sudo nano /etc/ssh/sshd_config")
    print("  2. Verify: Port 22, PermitRootLogin yes")
    print("  3. Restart: sudo systemctl restart ssh")
    print()
    
    print("Method D: System Reboot")
    print("  If all else fails:")
    print("  1. Power cycle the machine")
    print("  2. Wait 3-5 minutes for full boot")
    print("  3. Test: ssh root@*************")
    print()

def wait_and_test_ssh():
    """Wait and test SSH connectivity"""
    print("4️⃣ Testing SSH connectivity...")
    
    for i in range(3):
        print(f"   Attempt {i+1}/3...")
        try:
            import subprocess
            result = subprocess.run([
                "ssh", "-o", "ConnectTimeout=10", "-o", "BatchMode=yes",
                "root@*************", "echo 'SSH WORKING'"
            ], capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print("   ✅ SSH is working!")
                print(f"   Output: {result.stdout.strip()}")
                return True
            else:
                print(f"   ❌ SSH failed: {result.stderr.strip()}")
        except subprocess.TimeoutExpired:
            print("   ❌ SSH connection timed out")
        except Exception as e:
            print(f"   ❌ SSH test error: {e}")
        
        if i < 2:
            print("   Waiting 10 seconds...")
            time.sleep(10)
    
    return False

if __name__ == "__main__":
    # Try API-based recovery
    api_success = test_ssh_restart()
    
    if api_success:
        print("\n⏳ Waiting 15 seconds for SSH service to restart...")
        time.sleep(15)
        
        if wait_and_test_ssh():
            print("\n🎉 SSH Recovery Successful!")
            print("\nYou can now run:")
            print("ssh root@*************")
            print("cd /root/refact-setup && source venv/bin/activate")
            print("python3 -c 'import torch; print(f\"CUDA: {torch.cuda.is_available()}\")'")
        else:
            print("\n❌ SSH still not responding after restart attempt")
            alternative_approaches()
    else:
        print("\n❌ Could not restart SSH via API")
        alternative_approaches()
    
    print("\n📊 Current Status:")
    print(f"GPU Backend: ✅ Working ({GPU_BASE_URL})")
    print("SSH Service: ❌ Needs manual intervention")
    print("\n🔗 Monitor GPU while fixing SSH:")
    print(f"curl {GPU_BASE_URL}/api/gpu/status")