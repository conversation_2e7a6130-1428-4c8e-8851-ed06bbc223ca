#!/bin/bash

echo "🎭 Starting ProHeadshots on RTX 4090 Server"
echo "==========================================="
echo ""

# Configuration
PROJECT_DIR="/root/ProHeadshots"
PORT="3004"
HOST="0.0.0.0"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if project exists
if [ ! -d "$PROJECT_DIR" ]; then
    log_error "ProHeadshots directory not found: $PROJECT_DIR"
    exit 1
fi

cd "$PROJECT_DIR"

# Stop any existing Next.js processes
log_info "Stopping existing Next.js processes..."
pkill -f "next dev" || true
pkill -f "next-server" || true
sleep 2

# Check environment file
if [ ! -f ".env.local" ]; then
    log_warning "No .env.local found, creating from template..."
    cp .env.example .env.local
    
    # Set basic development values
    sed -i 's|http://localhost:3000|http://*************:3004|g' .env.local
    sed -i 's|ENVIRONMENT=DEVELOPMENT|ENVIRONMENT=DEVELOPMENT|g' .env.local
    
    log_info "Created .env.local with development settings"
fi

# Check Node.js dependencies
if [ ! -d "node_modules" ]; then
    log_info "Installing Node.js dependencies..."
    npm install
fi

# Create startup script
cat > start.sh << EOF
#!/bin/bash
cd "$PROJECT_DIR"
export HOST="$HOST"
export PORT="$PORT"
export NODE_ENV="development"

echo "🚀 Starting ProHeadshots..."
echo "Local access: http://localhost:$PORT"
echo "Network access: http://*************:$PORT"
echo "Tailscale access: http://*************:$PORT"
echo ""

# Start with proper network binding
npm run dev -- --hostname $HOST --port $PORT
EOF

chmod +x start.sh

# Create systemd service for permanent running
cat > /etc/systemd/system/proheadshots.service << EOF
[Unit]
Description=ProHeadshots AI Service
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$PROJECT_DIR
Environment=NODE_ENV=development
Environment=PORT=$PORT
Environment=HOST=$HOST
ExecStart=/usr/bin/npm run dev -- --hostname $HOST --port $PORT
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF

# Reload systemd and enable service
systemctl daemon-reload
systemctl enable proheadshots.service

log_success "ProHeadshots service configured"

# Start the application
log_info "Starting ProHeadshots application..."

# Method 1: Try systemd service
if systemctl start proheadshots.service; then
    log_success "ProHeadshots started via systemd"
    sleep 5
    
    # Check if it's running
    if systemctl is-active --quiet proheadshots.service; then
        log_success "Service is active and running"
    else
        log_warning "Service started but may not be active, checking logs..."
        journalctl -u proheadshots.service --no-pager -n 10
    fi
else
    log_warning "Systemd service failed, trying manual start..."
    
    # Method 2: Manual start
    export HOST="$HOST"
    export PORT="$PORT"
    export NODE_ENV="development"
    
    nohup npm run dev -- --hostname "$HOST" --port "$PORT" > proheadshots.log 2>&1 &
    NEXT_PID=$!
    
    log_info "Started manually with PID: $NEXT_PID"
    sleep 5
    
    if ps -p $NEXT_PID > /dev/null; then
        log_success "Manual start successful"
    else
        log_error "Manual start failed"
        cat proheadshots.log
        exit 1
    fi
fi

# Wait for startup
log_info "Waiting for application to start..."
for i in {1..30}; do
    if curl -s http://localhost:$PORT >/dev/null 2>&1; then
        log_success "Application is responding on port $PORT"
        break
    fi
    
    if [ $i -eq 30 ]; then
        log_error "Application failed to start after 30 seconds"
        log_info "Checking logs..."
        if [ -f "proheadshots.log" ]; then
            tail -20 proheadshots.log
        fi
        journalctl -u proheadshots.service --no-pager -n 20
        exit 1
    fi
    
    echo -n "."
    sleep 1
done

echo ""

# Test network connectivity
log_info "Testing network connectivity..."

# Test localhost
if curl -s -I http://localhost:$PORT >/dev/null; then
    log_success "✅ Localhost access working"
else
    log_warning "❌ Localhost access failed"
fi

# Test local network
if curl -s -I http://*************:$PORT >/dev/null; then
    log_success "✅ Local network access working"
else
    log_warning "❌ Local network access failed"
fi

# Test Tailscale
if curl -s -I http://*************:$PORT >/dev/null; then
    log_success "✅ Tailscale access working"
else
    log_warning "❌ Tailscale access failed - may need firewall rules"
fi

# Configure firewall if needed
log_info "Configuring firewall for port $PORT..."
ufw allow $PORT/tcp >/dev/null 2>&1 || true
iptables -A INPUT -p tcp --dport $PORT -j ACCEPT >/dev/null 2>&1 || true

echo ""
echo "🎉 ProHeadshots Installation Summary"
echo "==================================="
log_success "✅ Application is running"
log_success "✅ Systemd service configured"
log_success "✅ Firewall rules added"

echo ""
echo "🌐 Access URLs:"
echo "==============="
echo "Local: http://localhost:$PORT"
echo "Network: http://*************:$PORT"
echo "Tailscale: http://*************:$PORT"

echo ""
echo "🔧 Management Commands:"
echo "======================"
echo "Status: systemctl status proheadshots"
echo "Logs: journalctl -u proheadshots -f"
echo "Restart: systemctl restart proheadshots"
echo "Stop: systemctl stop proheadshots"

echo ""
echo "📁 Project Location: $PROJECT_DIR"
echo "🚀 Manual start: cd $PROJECT_DIR && ./start.sh"

echo ""
log_info "🎭 ProHeadshots is ready for AI headshot generation!"
log_info "💡 Note: You'll need to configure API keys in .env.local for full functionality"