#!/bin/bash

echo "🎭 ProHeadshots Installation Script for RTX 4090"
echo "================================================="
echo ""

# Configuration
PROJECT_DIR="/root/proheadshots"
VENV_DIR="/root/proheadshots-env"
GPU_CHECK=true

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    log_error "Please run as root or with sudo"
    exit 1
fi

echo "🔍 System Information:"
echo "======================"
log_info "OS: $(lsb_release -d 2>/dev/null | cut -f2 || echo 'Unknown')"
log_info "Kernel: $(uname -r)"
log_info "Architecture: $(uname -m)"
echo ""

# Step 1: Check GPU availability
echo "1️⃣ Checking GPU Setup..."
echo "========================"

if command -v nvidia-smi &> /dev/null; then
    log_success "NVIDIA drivers installed"
    nvidia-smi --query-gpu=name,driver_version,memory.total --format=csv,noheader
    
    # Check CUDA
    if [ -d "/usr/local/cuda" ] || command -v nvcc &> /dev/null; then
        log_success "CUDA toolkit available"
        nvcc --version 2>/dev/null | head -1 || echo "CUDA version check failed"
    else
        log_warning "CUDA toolkit not found in standard locations"
    fi
else
    log_error "NVIDIA drivers not found"
    log_info "Please install NVIDIA drivers first"
    exit 1
fi

echo ""

# Step 2: Install system dependencies
echo "2️⃣ Installing System Dependencies..."
echo "===================================="

log_info "Updating package list..."
apt update

log_info "Installing Git and development tools..."
apt install -y git curl wget build-essential

log_info "Installing Python development packages..."
apt install -y python3 python3-pip python3-venv python3-dev

log_info "Installing image processing libraries..."
apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1

log_info "Installing additional dependencies..."
apt install -y ffmpeg libmagic1 libmagic-dev

log_success "System dependencies installed"
echo ""

# Step 3: Clone the repository
echo "3️⃣ Cloning ProHeadshots Repository..."
echo "====================================="

if [ -d "$PROJECT_DIR" ]; then
    log_warning "Project directory already exists, removing..."
    rm -rf "$PROJECT_DIR"
fi

log_info "Cloning repository..."
git clone https://github.com/thaohienhomes/ProHeadshots.git "$PROJECT_DIR"

if [ $? -eq 0 ]; then
    log_success "Repository cloned successfully"
else
    log_error "Failed to clone repository"
    exit 1
fi

cd "$PROJECT_DIR"
log_info "Project structure:"
ls -la

echo ""

# Step 4: Create and setup Python virtual environment
echo "4️⃣ Setting Up Python Environment..."
echo "==================================="

log_info "Creating virtual environment..."
python3 -m venv "$VENV_DIR"

log_info "Activating virtual environment..."
source "$VENV_DIR/bin/activate"

log_info "Upgrading pip..."
pip install --upgrade pip

# Check if requirements.txt exists
if [ -f "requirements.txt" ]; then
    log_info "Installing Python dependencies from requirements.txt..."
    pip install -r requirements.txt
elif [ -f "pyproject.toml" ]; then
    log_info "Installing project with pyproject.toml..."
    pip install -e .
else
    log_warning "No requirements.txt or pyproject.toml found"
    log_info "Installing common ML dependencies..."
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    pip install transformers diffusers accelerate
    pip install pillow opencv-python-headless numpy
    pip install fastapi uvicorn
    pip install gradio streamlit
fi

echo ""

# Step 5: Test PyTorch GPU access
echo "5️⃣ Testing PyTorch GPU Access..."
echo "================================"

log_info "Testing PyTorch installation..."
python3 -c "
import torch
print(f'PyTorch version: {torch.__version__}')
print(f'CUDA available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'CUDA version: {torch.version.cuda}')
    print(f'GPU count: {torch.cuda.device_count()}')
    print(f'GPU name: {torch.cuda.get_device_name(0)}')
    print(f'GPU memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB')
    
    # Test GPU tensor operations
    try:
        x = torch.randn(1000, 1000).cuda()
        y = torch.matmul(x, x)
        print(f'GPU tensor test: SUCCESS')
        print(f'Memory allocated: {torch.cuda.memory_allocated() / 1024**2:.1f}MB')
    except Exception as e:
        print(f'GPU tensor test: FAILED - {e}')
else:
    print('WARNING: CUDA not available - will run on CPU only')
"

echo ""

# Step 6: Analyze project structure and setup
echo "6️⃣ Analyzing Project Structure..."
echo "================================="

log_info "Looking for main application files..."

# Common files to check for
declare -a files=("app.py" "main.py" "run.py" "server.py" "gradio_app.py" "streamlit_app.py")

for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        log_success "Found: $file"
    fi
done

# Check for configuration files
log_info "Configuration files:"
for config in "config.py" "config.yaml" "config.json" ".env.example" "docker-compose.yml"; do
    if [ -f "$config" ]; then
        log_success "Found: $config"
    fi
done

# Look for models directory
if [ -d "models" ]; then
    log_success "Models directory found"
elif [ -d "checkpoints" ]; then
    log_success "Checkpoints directory found"
else
    log_info "Creating models directory..."
    mkdir -p models
fi

echo ""

# Step 7: Check README for specific setup instructions
echo "7️⃣ Checking Project Documentation..."
echo "===================================="

if [ -f "README.md" ]; then
    log_success "README.md found"
    echo ""
    echo "📚 README Content (first 30 lines):"
    echo "-----------------------------------"
    head -30 README.md
    echo ""
else
    log_warning "No README.md found"
fi

# Step 8: Create startup scripts
echo "8️⃣ Creating Startup Scripts..."
echo "=============================="

# Create activation script
cat > activate.sh << 'EOF'
#!/bin/bash
echo "🎭 Activating ProHeadshots Environment"
cd /root/proheadshots
source /root/proheadshots-env/bin/activate
echo "✅ Environment activated"
echo "📁 Current directory: $(pwd)"
echo "🐍 Python: $(which python)"
echo "📦 Pip packages:"
pip list | grep -E "(torch|transformers|diffusers|gradio|streamlit|fastapi)"
echo ""
echo "🚀 Ready to run ProHeadshots!"
echo "💡 Try: python app.py (if it exists)"
EOF

chmod +x activate.sh

# Create GPU test script
cat > test_gpu.py << 'EOF'
#!/usr/bin/env python3
import torch
import sys

def test_gpu():
    print("🔬 GPU Test for ProHeadshots")
    print("=" * 30)
    
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA version: {torch.version.cuda}")
        print(f"GPU count: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"GPU {i}: {props.name}")
            print(f"  Memory: {props.total_memory / 1024**3:.1f}GB")
            print(f"  Compute capability: {props.major}.{props.minor}")
        
        # Test tensor operations
        try:
            print("\n🧪 Testing GPU operations...")
            device = torch.device('cuda:0')
            x = torch.randn(1000, 1000, device=device)
            y = torch.matmul(x, x)
            memory_used = torch.cuda.memory_allocated() / 1024**2
            print(f"✅ GPU tensor operations successful")
            print(f"📊 Memory used: {memory_used:.1f}MB")
            
            # Clear GPU memory
            del x, y
            torch.cuda.empty_cache()
            print("🧹 GPU memory cleared")
            
        except Exception as e:
            print(f"❌ GPU operations failed: {e}")
            return False
            
        return True
    else:
        print("❌ CUDA not available")
        return False

if __name__ == "__main__":
    success = test_gpu()
    sys.exit(0 if success else 1)
EOF

chmod +x test_gpu.py

log_success "Startup scripts created:"
log_info "  ./activate.sh - Activate environment"
log_info "  ./test_gpu.py - Test GPU functionality"

echo ""

# Step 9: Final setup and recommendations
echo "9️⃣ Final Setup and Recommendations..."
echo "====================================="

log_success "ProHeadshots installation completed!"
echo ""

log_info "📁 Installation paths:"
echo "  Project: $PROJECT_DIR"
echo "  Virtual env: $VENV_DIR"
echo ""

log_info "🚀 To get started:"
echo "  1. cd $PROJECT_DIR"
echo "  2. source activate.sh"
echo "  3. python test_gpu.py"
echo "  4. Look for main application file (app.py, main.py, etc.)"
echo ""

log_info "🔧 Common startup commands to try:"
if [ -f "app.py" ]; then
    echo "  python app.py"
fi
if [ -f "main.py" ]; then
    echo "  python main.py"
fi
if [ -f "gradio_app.py" ]; then
    echo "  python gradio_app.py"
fi
if [ -f "streamlit_app.py" ]; then
    echo "  streamlit run streamlit_app.py"
fi

echo ""

log_info "🌐 Network access:"
echo "  Local: http://*************:PORT"
echo "  Tailscale: http://*************:PORT"
echo "  Replace PORT with the actual port (usually 7860, 8080, 8000, or 3000)"

echo ""

# Create a status check script
cat > status.sh << 'EOF'
#!/bin/bash
echo "📊 ProHeadshots Status Check"
echo "============================"
echo ""

echo "🖥️  System:"
nvidia-smi --query-gpu=name,temperature.gpu,utilization.gpu,memory.used,memory.total --format=csv,noheader

echo ""
echo "🐍 Python Environment:"
if [ -d "/root/proheadshots-env" ]; then
    source /root/proheadshots-env/bin/activate
    echo "Python: $(python --version)"
    echo "PyTorch: $(python -c 'import torch; print(torch.__version__)' 2>/dev/null || echo 'Not installed')"
    echo "CUDA: $(python -c 'import torch; print(torch.cuda.is_available())' 2>/dev/null || echo 'Not available')"
else
    echo "Virtual environment not found"
fi

echo ""
echo "📂 Project:"
if [ -d "/root/proheadshots" ]; then
    echo "Directory: ✅ Exists"
    cd /root/proheadshots
    echo "Files: $(ls -1 | wc -l) items"
    echo "Size: $(du -sh . | cut -f1)"
else
    echo "Directory: ❌ Not found"
fi

echo ""
echo "🌐 Network:"
echo "SSH: $(systemctl is-active ssh)"
echo "GPU Backend: $(curl -s http://localhost:9092/health >/dev/null && echo 'Running' || echo 'Stopped')"
EOF

chmod +x status.sh

log_success "Status script created: ./status.sh"

echo ""
echo "🎉 Installation Summary"
echo "======================"
log_success "✅ ProHeadshots cloned from GitHub"
log_success "✅ Python virtual environment created"
log_success "✅ Dependencies installed"
log_success "✅ GPU support configured"
log_success "✅ Startup scripts created"

echo ""
log_info "🔧 Next steps:"
echo "1. Fix SSH access (via Proxmox console: systemctl restart ssh)"
echo "2. SSH to server: ssh root@*************"
echo "3. Run: cd /root/proheadshots && ./activate.sh"
echo "4. Test GPU: python test_gpu.py"
echo "5. Start application: python app.py (or check README)"

echo ""
log_info "📱 Monitor progress via GPU dashboard:"
echo "http://*************:9092"
echo ""

deactivate 2>/dev/null || true
log_success "🚀 ProHeadshots installation script completed!"