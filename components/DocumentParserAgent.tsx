"use client";

import React, { useState, useCallback } from "react";
import { useQuery, useMutation, useAction } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { toast } from "sonner";
import {
  FileText,
  Upload,
  Loader2,
  CheckCircle,
  AlertCircle,
  Eye,
  Download,
  RefreshCw,
  Mail,
  Settings,
  BarChart,
  FileSearch,
  Brain,
  Zap,
} from "lucide-react";

interface DocumentParserAgentProps {
  tenderId?: Id<"tenders">;
  onDocumentProcessed?: (result: any) => void;
}

export function DocumentParserAgent({ tenderId, onDocumentProcessed }: DocumentParserAgentProps) {
  const [selectedFiles, setSelectedFiles] = useState<Id<"files">[]>([]);
  const [processingOptions, setProcessingOptions] = useState({
    enableOCR: true,
    extractStructure: true,
    extractRequirements: true,
    extractEntities: true,
    confidenceThreshold: 0.7,
  });
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Queries
  const files = useQuery(api.files.listFiles, {
    category: "tender_document",
    tenderId,
  });

  const parsingResults = useQuery(api.agentDocumentParser.getDocumentParsingResults, {
    tenderId,
    limit: 10,
  });

  const agentTasks = useQuery(api.agents.getAgentTasks, {
    tenderId,
    type: "document_parsing",
    limit: 10,
  });

  // Mutations
  const createParsingTask = useMutation(api.agentDocumentParser.createDocumentParsingTask);
  const initializeAgent = useMutation(api.agentDocumentParser.initializeDocumentParserAgent);

  // Actions
  const batchProcess = useAction(api.agentDocumentParser.batchProcessDocuments);

  // Initialize agent on mount
  React.useEffect(() => {
    initializeAgent({}).catch(console.error);
  }, [initializeAgent]);

  const handleProcessFiles = useCallback(async () => {
    if (selectedFiles.length === 0) {
      toast.error("Please select files to process");
      return;
    }

    setIsProcessing(true);
    try {
      const result = await batchProcess({
        fileIds: selectedFiles,
        tenderId,
        processingOptions,
      });

      toast.success(`Created ${result.tasksCreated} parsing tasks`);
      setSelectedFiles([]);
      
      if (onDocumentProcessed) {
        onDocumentProcessed(result);
      }
    } catch (error) {
      toast.error("Failed to process documents");
      console.error(error);
    } finally {
      setIsProcessing(false);
    }
  }, [selectedFiles, tenderId, processingOptions, batchProcess, onDocumentProcessed]);

  const handleProcessSingleFile = useCallback(async (fileId: Id<"files">) => {
    try {
      const taskId = await createParsingTask({
        fileId,
        tenderId,
        priority: "high",
        processingOptions,
      });

      toast.success("Document parsing started");
    } catch (error) {
      toast.error("Failed to start parsing");
      console.error(error);
    }
  }, [createParsingTask, tenderId, processingOptions]);

  const toggleFileSelection = (fileId: Id<"files">) => {
    setSelectedFiles(prev => 
      prev.includes(fileId) 
        ? prev.filter(id => id !== fileId)
        : [...prev, fileId]
    );
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "processing":
      case "in_progress":
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case "failed":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-400" />;
    }
  };

  const formatConfidence = (confidence: number) => {
    const percentage = (confidence * 100).toFixed(1);
    const color = confidence >= 0.8 ? "text-green-600" : confidence >= 0.6 ? "text-yellow-600" : "text-red-600";
    return <span className={`font-medium ${color}`}>{percentage}%</span>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Brain className="w-6 h-6 text-blue-600" />
          <div>
            <h2 className="text-xl font-semibold">Document Parser Agent</h2>
            <p className="text-sm text-gray-600">
              Intelligent OCR and document analysis for tender documents
            </p>
          </div>
        </div>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <Settings className="w-5 h-5" />
        </button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
          <h3 className="font-medium mb-2">Processing Options</h3>
          <div className="grid grid-cols-2 gap-3">
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={processingOptions.enableOCR}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, enableOCR: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Enable OCR</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={processingOptions.extractStructure}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, extractStructure: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Extract Structure</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={processingOptions.extractRequirements}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, extractRequirements: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Extract Requirements</span>
            </label>
            <label className="flex items-center gap-2">
              <input
                type="checkbox"
                checked={processingOptions.extractEntities}
                onChange={(e) => setProcessingOptions(prev => ({ ...prev, extractEntities: e.target.checked }))}
                className="rounded border-gray-300"
              />
              <span className="text-sm">Extract Entities</span>
            </label>
          </div>
          <div className="mt-3">
            <label className="text-sm text-gray-700">
              Confidence Threshold: {(processingOptions.confidenceThreshold * 100).toFixed(0)}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={processingOptions.confidenceThreshold * 100}
              onChange={(e) => setProcessingOptions(prev => ({ 
                ...prev, 
                confidenceThreshold: parseInt(e.target.value) / 100 
              }))}
              className="w-full mt-1"
            />
          </div>
        </div>
      )}

      {/* File Selection */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-medium">Available Documents</h3>
          <button
            onClick={handleProcessFiles}
            disabled={selectedFiles.length === 0 || isProcessing}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isProcessing ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Zap className="w-4 h-4" />
                Process Selected ({selectedFiles.length})
              </>
            )}
          </button>
        </div>

        <div className="space-y-2 max-h-64 overflow-y-auto">
          {files?.map((file) => (
            <div
              key={file._id}
              className={`flex items-center justify-between p-3 rounded-lg border transition-colors cursor-pointer ${
                selectedFiles.includes(file._id)
                  ? "border-blue-500 bg-blue-50"
                  : "border-gray-200 hover:bg-gray-50"
              }`}
              onClick={() => toggleFileSelection(file._id)}
            >
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={selectedFiles.includes(file._id)}
                  onChange={() => {}}
                  className="rounded border-gray-300"
                />
                <FileText className="w-5 h-5 text-gray-400" />
                <div>
                  <p className="font-medium text-sm">{file.name}</p>
                  <p className="text-xs text-gray-500">
                    {file.mimeType} • {(file.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleProcessSingleFile(file._id);
                }}
                className="p-2 hover:bg-white rounded-lg transition-colors"
              >
                <FileSearch className="w-4 h-4 text-blue-600" />
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Active Tasks */}
      {agentTasks && agentTasks.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium mb-4">Active Parsing Tasks</h3>
          <div className="space-y-2">
            {agentTasks.map((task) => (
              <div key={task._id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(task.status)}
                  <div>
                    <p className="font-medium text-sm">{task.input.content}</p>
                    <p className="text-xs text-gray-500">
                      {task.status} • Progress: {task.progress}%
                    </p>
                  </div>
                </div>
                {task.output && (
                  <div className="text-sm text-gray-600">
                    Confidence: {formatConfidence(task.output.confidence)}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Results */}
      {parsingResults && parsingResults.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium mb-4">Recent Parsing Results</h3>
          <div className="space-y-3">
            {parsingResults.map((result) => (
              <div key={result._id} className="border-b border-gray-100 pb-3 last:border-0">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(result.status)}
                      <p className="font-medium text-sm">{result.fileName}</p>
                    </div>
                    <div className="mt-1 text-xs text-gray-500">
                      {result.status === "completed" && result.wordCount && (
                        <span>{result.wordCount.toLocaleString()} words • </span>
                      )}
                      {result.pageCount && <span>{result.pageCount} pages • </span>}
                      {result.confidence && (
                        <span>Confidence: {formatConfidence(result.confidence)}</span>
                      )}
                    </div>
                    {result.keyPhrases && result.keyPhrases.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {result.keyPhrases.slice(0, 5).map((phrase, idx) => (
                          <span
                            key={idx}
                            className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                          >
                            {phrase}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                    <button className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Email Integration Status */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-3">
          <Mail className="w-5 h-5 text-blue-600" />
          <div className="flex-1">
            <p className="font-medium text-sm">Email Integration</p>
            <p className="text-xs text-gray-600">
              Automatically process tender documents from Gmail attachments
            </p>
          </div>
          <button className="px-3 py-1.5 bg-white border border-blue-300 text-blue-700 rounded-lg text-sm hover:bg-blue-50 transition-colors">
            Configure
          </button>
        </div>
      </div>

      {/* Agent Stats */}
      <div className="grid grid-cols-3 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-2">
            <BarChart className="w-4 h-4" />
            <span className="text-sm">Documents Processed</span>
          </div>
          <p className="text-2xl font-semibold">{parsingResults?.length || 0}</p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-2">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm">Success Rate</span>
          </div>
          <p className="text-2xl font-semibold">
            {parsingResults && parsingResults.length > 0
              ? Math.round(
                  (parsingResults.filter(r => r.status === "completed").length / 
                   parsingResults.length) * 100
                )
              : 0}%
          </p>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="flex items-center gap-2 text-gray-600 mb-2">
            <Zap className="w-4 h-4" />
            <span className="text-sm">Avg. Confidence</span>
          </div>
          <p className="text-2xl font-semibold">
            {parsingResults && parsingResults.length > 0
              ? Math.round(
                  parsingResults
                    .filter(r => r.confidence)
                    .reduce((sum, r) => sum + (r.confidence || 0), 0) /
                  parsingResults.filter(r => r.confidence).length * 100
                )
              : 0}%
          </p>
        </div>
      </div>
    </div>
  );
}