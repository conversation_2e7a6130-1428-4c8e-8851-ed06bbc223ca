"use client";

import React, { useState, useEffect } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "@/convex/_generated/api";
import { Id } from "@/convex/_generated/dataModel";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Clock, Users, FileText, CheckCircle2, AlertTriangle, Target, TrendingUp, Calendar, Bell, MessageSquare, Zap } from "lucide-react";
import { format } from "date-fns";

interface MeetingProcessorProps {
  meetingId?: Id<"scheduler_meetings">;
}

export default function MeetingProcessor({ meetingId }: MeetingProcessorProps) {
  const [selectedTab, setSelectedTab] = useState("summary");
  const [transcriptText, setTranscriptText] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Queries
  const meetingSummaries = useQuery(api.meetingSummarizer.getMeetingSummaries, {
    meetingId,
    limit: 10,
  });

  const tasks = useQuery(api.meetingTaskManager.getTasks, {
    meetingId,
    limit: 20,
  });

  const taskAnalytics = useQuery(api.meetingTaskManager.getTaskAnalytics, {
    meetingId,
    timeframe: "month",
  });

  // Mutations
  const processTranscript = useMutation(api.meetingSummarizer.processMeetingTranscript);
  const updateTaskProgress = useMutation(api.meetingTaskManager.updateTaskProgress);
  const createTaskFromActionItem = useMutation(api.meetingTaskManager.createTaskFromActionItem);

  const handleTranscriptUpload = async () => {
    if (!meetingId || !transcriptText) return;

    setIsProcessing(true);
    try {
      // Parse transcript (simplified for demo)
      const segments = transcriptText.split('\n').filter(line => line.trim()).map((line, index) => {
        const [speaker, ...textParts] = line.split(':');
        return {
          speakerId: speaker.trim(),
          text: textParts.join(':').trim(),
          startTime: index * 10000, // 10 seconds per segment
          endTime: (index + 1) * 10000,
        };
      });

      const speakers = [...new Set(segments.map(s => s.speakerId))].map(id => ({
        id,
        name: id,
        role: "participant",
      }));

      await processTranscript({
        meetingId,
        transcript: {
          rawText: transcriptText,
          speakers,
          segments,
          duration: segments.length * 10000,
          language: "en",
        },
      });

      setTranscriptText("");
    } catch (error) {
      console.error("Failed to process transcript:", error);
    } finally {
      setIsProcessing(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-500";
      case "in_progress": return "bg-blue-500";
      case "not_started": return "bg-gray-500";
      case "overdue": return "bg-red-500";
      default: return "bg-gray-500";
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "urgent": return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case "high": return <TrendingUp className="w-4 h-4 text-orange-500" />;
      case "normal": return <Target className="w-4 h-4 text-blue-500" />;
      default: return <Target className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Meeting Processing & Follow-up</h2>
        <div className="flex gap-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <FileText className="w-3 h-3" />
            {meetingSummaries?.length || 0} Summaries
          </Badge>
          <Badge variant="outline" className="flex items-center gap-1">
            <CheckCircle2 className="w-3 h-3" />
            {tasks?.filter(t => t.status === "completed").length || 0} / {tasks?.length || 0} Tasks
          </Badge>
        </div>
      </div>

      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="summary">Meeting Summary</TabsTrigger>
          <TabsTrigger value="tasks">Action Items</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="upload">Upload Transcript</TabsTrigger>
        </TabsList>

        <TabsContent value="summary" className="space-y-4">
          {meetingSummaries?.map((summary) => (
            <Card key={summary._id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle>{summary.meetingTitle || "Meeting Summary"}</CardTitle>
                    <CardDescription>
                      {summary.meetingDate && format(new Date(summary.meetingDate), "PPP")}
                    </CardDescription>
                  </div>
                  <Badge variant={summary.status === "analyzed" ? "default" : "secondary"}>
                    {summary.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Duration:</span> {Math.round(summary.duration / 60000)} minutes
                  </div>
                  <div>
                    <span className="text-gray-500">Speakers:</span> {summary.speakerCount}
                  </div>
                  <div>
                    <span className="text-gray-500">Word Count:</span> {summary.wordCount.toLocaleString()}
                  </div>
                  <div>
                    <span className="text-gray-500">Language:</span> {summary.language.toUpperCase()}
                  </div>
                </div>

                {summary.analysisResults && (
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Zap className="w-4 h-4" />
                        Key Decisions ({summary.analysisResults.decisions.length})
                      </h4>
                      <ul className="list-disc pl-5 space-y-1">
                        {summary.analysisResults.decisions.slice(0, 3).map((decision: any, index: number) => (
                          <li key={index} className="text-sm">
                            {decision.text.substring(0, 100)}...
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <Target className="w-4 h-4" />
                        Action Items ({summary.analysisResults.actionItems.length})
                      </h4>
                      <ul className="list-disc pl-5 space-y-1">
                        {summary.analysisResults.actionItems.slice(0, 3).map((item: any, index: number) => (
                          <li key={index} className="text-sm">
                            <span className="font-medium">{item.assignee}:</span> {item.description}
                            {item.deadline && <span className="text-gray-500"> (Due: {item.deadline})</span>}
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div>
                      <h4 className="font-semibold mb-2 flex items-center gap-2">
                        <AlertTriangle className="w-4 h-4" />
                        Risks & Concerns ({summary.analysisResults.risks.length})
                      </h4>
                      <ul className="list-disc pl-5 space-y-1">
                        {summary.analysisResults.risks.slice(0, 2).map((risk: any, index: number) => (
                          <li key={index} className="text-sm">
                            <Badge variant="outline" className="text-xs mr-2">
                              {risk.severity}
                            </Badge>
                            {risk.text.substring(0, 100)}...
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="pt-3 border-t">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">Engagement Score</span>
                        <div className="flex items-center gap-2">
                          <Progress value={summary.analysisResults.engagementScore} className="w-32" />
                          <span className="text-sm font-medium">
                            {Math.round(summary.analysisResults.engagementScore)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {summary.summary && (
                  <div className="pt-3 border-t">
                    <h4 className="font-semibold mb-2">AI-Generated Summary</h4>
                    <p className="text-sm text-gray-600">{summary.summary.content}</p>
                    <div className="flex gap-4 mt-2 text-xs text-gray-500">
                      <span>Confidence: {Math.round(summary.summary.confidence * 100)}%</span>
                      <span>Processing Time: {Math.round(summary.summary.processingTime / 1000)}s</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="tasks" className="space-y-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex gap-2">
              <Select defaultValue="all">
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Tasks</SelectItem>
                  <SelectItem value="not_started">Not Started</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
              <Select defaultValue="all">
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="Filter priority" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Priorities</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="low">Low</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {tasks?.map((task) => (
            <Card key={task._id} className={task.isOverdue ? "border-red-200" : ""}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div className="flex items-start gap-2">
                    {getPriorityIcon(task.priority)}
                    <div>
                      <CardTitle className="text-lg">{task.title}</CardTitle>
                      <CardDescription>{task.description}</CardDescription>
                    </div>
                  </div>
                  <Badge className={getStatusColor(task.status)}>
                    {task.status.replace('_', ' ')}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Assignee:</span> {task.assignee}
                  </div>
                  <div>
                    <span className="text-gray-500">Category:</span> {task.category}
                  </div>
                  <div>
                    <span className="text-gray-500">Due Date:</span>{" "}
                    {task.dueDate ? format(new Date(task.dueDate), "PP") : "Not set"}
                  </div>
                  <div>
                    <span className="text-gray-500">Progress:</span> {task.progress}%
                  </div>
                </div>

                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Progress</span>
                    <span className="text-sm text-gray-500">{task.progress}%</span>
                  </div>
                  <Progress value={task.progress} className="h-2" />
                </div>

                {task.metrics.blockers.length > 0 && (
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Blockers:</strong>
                      <ul className="list-disc pl-5 mt-1">
                        {task.metrics.blockers.map((blocker: any, index: number) => (
                          <li key={index} className="text-sm">
                            {blocker.description} ({blocker.severity})
                          </li>
                        ))}
                      </ul>
                    </AlertDescription>
                  </Alert>
                )}

                {task.progressNotes && task.progressNotes.length > 0 && (
                  <div className="border-t pt-3">
                    <h5 className="text-sm font-medium mb-2">Recent Updates</h5>
                    {task.progressNotes.slice(0, 2).map((note: any) => (
                      <div key={note._id} className="text-sm text-gray-600 mb-1">
                        <span className="font-medium">{note.createdBy}:</span> {note.note}
                        <span className="text-xs text-gray-400 ml-2">
                          ({format(new Date(note.createdAt), "PP")})
                        </span>
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex gap-2 pt-3 border-t">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      const newProgress = Math.min(100, task.progress + 25);
                      updateTaskProgress({
                        taskId: task._id,
                        progress: newProgress,
                        status: newProgress === 100 ? "completed" : "in_progress",
                      });
                    }}
                  >
                    Update Progress
                  </Button>
                  <Button size="sm" variant="outline">
                    <MessageSquare className="w-4 h-4 mr-1" />
                    Add Note
                  </Button>
                  <Button size="sm" variant="outline">
                    <Bell className="w-4 h-4 mr-1" />
                    Set Reminder
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {taskAnalytics && (
            <>
              <div className="grid grid-cols-4 gap-4">
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Total Tasks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{taskAnalytics.totalTasks}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {Math.round(taskAnalytics.completionRate)}%
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Overdue Tasks</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-600">
                      {taskAnalytics.overdueTasks}
                    </div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-medium">Avg. Completion Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {Math.round(taskAnalytics.averageCompletionTime)} days
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Task Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="text-sm font-medium mb-2">By Priority</h4>
                      <div className="grid grid-cols-4 gap-2">
                        {Object.entries(taskAnalytics.byPriority).map(([priority, count]) => (
                          <div key={priority} className="text-center">
                            <div className="text-2xl font-bold">{count}</div>
                            <div className="text-sm text-gray-500 capitalize">{priority}</div>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">By Category</h4>
                      <div className="grid grid-cols-3 gap-2">
                        {Object.entries(taskAnalytics.byCategory).map(([category, count]) => (
                          <div key={category} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                            <span className="text-sm capitalize">{category}</span>
                            <Badge variant="secondary">{count}</Badge>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">Top Assignees</h4>
                      <div className="space-y-2">
                        {taskAnalytics.topAssignees.map((assignee: any, index: number) => (
                          <div key={index} className="flex justify-between items-center">
                            <span className="text-sm">{assignee.assignee}</span>
                            <span className="text-sm text-gray-500">{assignee.count} tasks</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Performance Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-gray-500">Escalation Rate</span>
                      <div className="text-xl font-bold">
                        {Math.round(taskAnalytics.escalationRate)}%
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">In Progress</span>
                      <div className="text-xl font-bold">{taskAnalytics.inProgressTasks}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Upload Meeting Transcript</CardTitle>
              <CardDescription>
                Upload a transcript to automatically generate summaries and extract action items
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="transcript">Transcript Text</Label>
                <Textarea
                  id="transcript"
                  placeholder="Paste your meeting transcript here. Format: Speaker: Text"
                  value={transcriptText}
                  onChange={(e) => setTranscriptText(e.target.value)}
                  rows={10}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Format each line as "Speaker Name: What they said"
                </p>
              </div>

              <Button
                onClick={handleTranscriptUpload}
                disabled={!transcriptText || !meetingId || isProcessing}
                className="w-full"
              >
                {isProcessing ? "Processing..." : "Process Transcript"}
              </Button>

              {isProcessing && (
                <Alert>
                  <AlertDescription>
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                      Processing transcript and extracting insights...
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}