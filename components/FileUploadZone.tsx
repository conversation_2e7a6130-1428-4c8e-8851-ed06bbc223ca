"use client";

import React, { useState, useCallback, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';
import { 
  Upload, 
  File, 
  FileText, 
  FileImage, 
  FileVideo, 
  FileAudio,
  FileSpreadsheet,
  FileCode,
  Archive,
  X,
  AlertCircle,
  CheckCircle,
  Loader2,
  Plus,
  Image as ImageIcon,
  Play,
  Download,
  Eye,
  Share2,
  Trash2,
  Tag,
  Folder,
  Clock,
  User,
  HardDrive,
  Zap,
  Filter,
  Search,
  Grid,
  List,
  ChevronDown,
  MoreVertical,
  Edit3,
  Copy,
  Move,
  Lock,
  Unlock,
  Star,
  StarOff,
  RefreshCw,
  FileCheck,
  AlertTriangle,
  Info
} from 'lucide-react';
import { Id } from '../convex/_generated/dataModel';
import { FileUpload, FileType, FileCategory, FileStatus, FileProcessingJob } from '../types/file';

interface FileUploadZoneProps {
  tenderId?: Id<'tenders'>;
  folderId?: Id<'file_folders'>;
  category?: FileCategory;
  maxFiles?: number;
  maxFileSize?: number;
  acceptedTypes?: string[];
  onUploadComplete?: (files: FileUpload[]) => void;
  onUploadError?: (error: string) => void;
  className?: string;
  mode?: 'compact' | 'full';
  showPreview?: boolean;
  enableBulkActions?: boolean;
}

interface UploadingFile {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'error';
  error?: string;
  id?: string;
  preview?: string;
  processingJobs?: FileProcessingJob[];
}

const ACCEPTED_TYPES = {
  'application/pdf': { type: 'pdf', icon: FileText, color: 'text-red-500' },
  'application/msword': { type: 'doc', icon: FileText, color: 'text-blue-500' },
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { type: 'docx', icon: FileText, color: 'text-blue-500' },
  'application/vnd.ms-excel': { type: 'xls', icon: FileSpreadsheet, color: 'text-green-500' },
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { type: 'xlsx', icon: FileSpreadsheet, color: 'text-green-500' },
  'application/vnd.ms-powerpoint': { type: 'ppt', icon: FileText, color: 'text-orange-500' },
  'application/vnd.openxmlformats-officedocument.presentationml.presentation': { type: 'pptx', icon: FileText, color: 'text-orange-500' },
  'text/plain': { type: 'txt', icon: FileText, color: 'text-gray-500' },
  'text/csv': { type: 'csv', icon: FileSpreadsheet, color: 'text-green-500' },
  'image/jpeg': { type: 'image', icon: FileImage, color: 'text-purple-500' },
  'image/png': { type: 'image', icon: FileImage, color: 'text-purple-500' },
  'image/gif': { type: 'image', icon: FileImage, color: 'text-purple-500' },
  'image/webp': { type: 'image', icon: FileImage, color: 'text-purple-500' },
  'video/mp4': { type: 'video', icon: FileVideo, color: 'text-indigo-500' },
  'video/avi': { type: 'video', icon: FileVideo, color: 'text-indigo-500' },
  'video/mov': { type: 'video', icon: FileVideo, color: 'text-indigo-500' },
  'audio/mp3': { type: 'audio', icon: FileAudio, color: 'text-pink-500' },
  'audio/wav': { type: 'audio', icon: FileAudio, color: 'text-pink-500' },
  'audio/ogg': { type: 'audio', icon: FileAudio, color: 'text-pink-500' },
  'application/zip': { type: 'archive', icon: Archive, color: 'text-yellow-500' },
  'application/x-rar-compressed': { type: 'archive', icon: Archive, color: 'text-yellow-500' },
  'application/x-7z-compressed': { type: 'archive', icon: Archive, color: 'text-yellow-500' },
};

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
const MAX_FILES = 10;

export default function FileUploadZone({
  tenderId,
  folderId,
  category = 'tender_document',
  maxFiles = MAX_FILES,
  maxFileSize = MAX_FILE_SIZE,
  acceptedTypes,
  onUploadComplete,
  onUploadError,
  className = '',
  mode = 'full',
  showPreview = true,
  enableBulkActions = true,
}: FileUploadZoneProps) {
  const [uploadingFiles, setUploadingFiles] = useState<UploadingFile[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<FileUpload[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [searchQuery, setSearchQuery] = useState('');
  const [showAdvanced, setShowAdvanced] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  const createFile = useMutation(api.files.createFile);
  const processFile = useAction(api.files.processFile);

  const getFileTypeInfo = (mimeType: string) => {
    return ACCEPTED_TYPES[mimeType as keyof typeof ACCEPTED_TYPES] || { 
      type: 'other', 
      icon: File, 
      color: 'text-gray-500' 
    };
  };

  const validateFile = (file: File): string | null => {
    if (file.size > maxFileSize) {
      return `File size exceeds ${formatFileSize(maxFileSize)} limit`;
    }
    
    if (acceptedTypes && !acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported`;
    }
    
    return null;
  };

  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const generateFilePreview = useCallback((file: File): Promise<string> => {
    return new Promise((resolve) => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target?.result as string);
        reader.readAsDataURL(file);
      } else {
        resolve('');
      }
    });
  }, []);

  const calculateChecksum = useCallback((file: File): Promise<string> => {
    return new Promise((resolve) => {
      const reader = new FileReader();
      reader.onload = async (e) => {
        const buffer = e.target?.result as ArrayBuffer;
        const hashBuffer = await crypto.subtle.digest('SHA-256', buffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        resolve(hashHex);
      };
      reader.readAsArrayBuffer(file);
    });
  }, []);

  const uploadFile = useCallback(async (file: File): Promise<void> => {
    const validationError = validateFile(file);
    if (validationError) {
      toast.error(validationError);
      return;
    }

    const uploadingFile: UploadingFile = {
      file,
      progress: 0,
      status: 'pending',
      preview: showPreview ? await generateFilePreview(file) : '',
    };

    setUploadingFiles(prev => [...prev, uploadingFile]);

    try {
      // Generate upload URL
      const uploadUrl = await generateUploadUrl();
      
      // Update status to uploading
      setUploadingFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { ...f, status: 'uploading', progress: 0 }
            : f
        )
      );

      // Upload file with progress tracking
      const xhr = new XMLHttpRequest();
      
      xhr.upload.onprogress = (e) => {
        if (e.lengthComputable) {
          const progress = (e.loaded / e.total) * 100;
          setUploadingFiles(prev => 
            prev.map(f => 
              f.file === file 
                ? { ...f, progress }
                : f
            )
          );
        }
      };

      xhr.onload = async () => {
        if (xhr.status === 200) {
          try {
            const { storageId } = JSON.parse(xhr.responseText);
            
            // Update status to processing
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, status: 'processing', progress: 100 }
                  : f
              )
            );

            // Calculate checksum
            const checksum = await calculateChecksum(file);
            
            // Get file type info
            const typeInfo = getFileTypeInfo(file.type);
            
            // Create file record
            const fileId = await createFile({
              name: file.name,
              originalName: file.name,
              type: typeInfo.type,
              mimeType: file.type,
              size: file.size,
              storageId,
              storageKey: `${Date.now()}-${file.name}`,
              checksum,
              category,
              tenderId,
              folderId,
              tags: [],
              isVersioned: false,
            });

            // Update with file ID
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, id: fileId, status: 'completed' }
                  : f
              )
            );

            toast.success(`${file.name} uploaded successfully`);
            
            // Start background processing
            await processFile({ fileId, type: 'analysis' });
            
          } catch (error) {
            console.error('Error creating file record:', error);
            setUploadingFiles(prev => 
              prev.map(f => 
                f.file === file 
                  ? { ...f, status: 'error', error: 'Failed to process file' }
                  : f
              )
            );
            toast.error(`Failed to process ${file.name}`);
          }
        } else {
          setUploadingFiles(prev => 
            prev.map(f => 
              f.file === file 
                ? { ...f, status: 'error', error: 'Upload failed' }
                : f
            )
          );
          toast.error(`Failed to upload ${file.name}`);
        }
      };

      xhr.onerror = () => {
        setUploadingFiles(prev => 
          prev.map(f => 
            f.file === file 
              ? { ...f, status: 'error', error: 'Upload failed' }
              : f
          )
        );
        toast.error(`Failed to upload ${file.name}`);
      };

      xhr.open('POST', uploadUrl);
      xhr.setRequestHeader('Content-Type', file.type);
      xhr.send(file);
      
    } catch (error) {
      console.error('Upload error:', error);
      setUploadingFiles(prev => 
        prev.map(f => 
          f.file === file 
            ? { ...f, status: 'error', error: 'Upload failed' }
            : f
        )
      );
      toast.error(`Failed to upload ${file.name}`);
    }
  }, [generateUploadUrl, createFile, processFile, category, tenderId, folderId, validateFile, generateFilePreview, calculateChecksum, showPreview]);

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const filesToUpload = acceptedFiles.slice(0, maxFiles - uploadingFiles.length);
    filesToUpload.forEach(uploadFile);
  }, [uploadFile, maxFiles, uploadingFiles.length]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedTypes ? acceptedTypes.reduce((acc, type) => ({ ...acc, [type]: [] }), {}) : undefined,
    maxSize: maxFileSize,
    maxFiles: maxFiles - uploadingFiles.length,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
    onDropAccepted: () => setDragActive(false),
    onDropRejected: () => setDragActive(false),
  });

  const removeUploadingFile = (file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file));
  };

  const retryUpload = (file: File) => {
    setUploadingFiles(prev => prev.filter(f => f.file !== file));
    uploadFile(file);
  };

  const clearCompleted = () => {
    setUploadingFiles(prev => prev.filter(f => f.status !== 'completed'));
  };

  const selectAll = () => {
    const allFileIds = uploadingFiles.map(f => f.id).filter(Boolean) as string[];
    setSelectedFiles(allFileIds);
  };

  const deselectAll = () => {
    setSelectedFiles([]);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-500" />;
      case 'uploading':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />;
      case 'processing':
        return <Zap className="w-4 h-4 text-yellow-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <File className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-gray-200';
      case 'uploading':
        return 'bg-blue-200';
      case 'processing':
        return 'bg-yellow-200';
      case 'completed':
        return 'bg-green-200';
      case 'error':
        return 'bg-red-200';
      default:
        return 'bg-gray-200';
    }
  };

  if (mode === 'compact') {
    return (
      <div className={`relative ${className}`}>
        <div
          {...getRootProps()}
          className={`
            border-2 border-dashed rounded-lg p-4 text-center cursor-pointer transition-all
            ${dragActive || isDragActive 
              ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
              : 'border-gray-300 hover:border-gray-400 dark:border-gray-600'
            }
          `}
        >
          <input {...getInputProps()} />
          <Upload className="mx-auto h-8 w-8 text-gray-400 mb-2" />
          <p className="text-sm text-gray-600 dark:text-gray-300">
            Drop files here or click to browse
          </p>
          <p className="text-xs text-gray-500 mt-1">
            Max {maxFiles} files, {formatFileSize(maxFileSize)} each
          </p>
        </div>
        
        {uploadingFiles.length > 0 && (
          <div className="mt-4 space-y-2">
            {uploadingFiles.map((uploadingFile, index) => (
              <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded">
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{uploadingFile.file.name}</span>
                    {getStatusIcon(uploadingFile.status)}
                  </div>
                  {uploadingFile.status === 'uploading' && (
                    <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                      <div 
                        className="bg-blue-500 h-1 rounded-full transition-all"
                        style={{ width: `${uploadingFile.progress}%` }}
                      />
                    </div>
                  )}
                </div>
                <button
                  onClick={() => removeUploadingFile(uploadingFile.file)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Upload Zone */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all
          ${dragActive || isDragActive 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 scale-105' 
            : 'border-gray-300 hover:border-gray-400 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800/50'
          }
        `}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center space-y-4">
          <div className="relative">
            <Upload className={`
              mx-auto h-16 w-16 transition-all
              ${dragActive || isDragActive ? 'text-blue-500' : 'text-gray-400'}
            `} />
            {dragActive || isDragActive ? (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-20 h-20 border-2 border-blue-500 rounded-full animate-pulse" />
              </div>
            ) : null}
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {dragActive || isDragActive ? 'Drop files here' : 'Upload files'}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
              Drag and drop files here or click to browse
            </p>
            <p className="text-xs text-gray-500 mt-1">
              Max {maxFiles} files, {formatFileSize(maxFileSize)} each
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2 justify-center">
            {Object.entries(ACCEPTED_TYPES).slice(0, 8).map(([mimeType, info]) => {
              const IconComponent = info.icon;
              return (
                <div key={mimeType} className="flex items-center space-x-1 px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-xs">
                  <IconComponent className={`w-3 h-3 ${info.color}`} />
                  <span className="text-gray-600 dark:text-gray-300">{info.type.toUpperCase()}</span>
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* File Management Controls */}
      {uploadingFiles.length > 0 && (
        <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              {uploadingFiles.length} file{uploadingFiles.length > 1 ? 's' : ''}
            </span>
            {enableBulkActions && (
              <div className="flex items-center space-x-2">
                <button
                  onClick={selectAll}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  Select All
                </button>
                <button
                  onClick={deselectAll}
                  className="text-sm text-gray-600 hover:text-gray-700"
                >
                  Deselect All
                </button>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={clearCompleted}
              className="text-sm text-gray-600 hover:text-gray-700"
            >
              Clear Completed
            </button>
            <button
              onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              {viewMode === 'grid' ? <List className="w-4 h-4" /> : <Grid className="w-4 h-4" />}
            </button>
          </div>
        </div>
      )}

      {/* Uploading Files */}
      {uploadingFiles.length > 0 && (
        <div className={`
          ${viewMode === 'grid' 
            ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' 
            : 'space-y-2'
          }
        `}>
          {uploadingFiles.map((uploadingFile, index) => {
            const typeInfo = getFileTypeInfo(uploadingFile.file.type);
            const IconComponent = typeInfo.icon;

            if (viewMode === 'grid') {
              return (
                <div key={index} className="relative p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      {uploadingFile.preview ? (
                        <img 
                          src={uploadingFile.preview} 
                          alt={uploadingFile.file.name}
                          className="w-12 h-12 object-cover rounded"
                        />
                      ) : (
                        <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded flex items-center justify-center">
                          <IconComponent className={`w-6 h-6 ${typeInfo.color}`} />
                        </div>
                      )}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {uploadingFile.file.name}
                      </h4>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatFileSize(uploadingFile.file.size)}
                      </p>
                      
                      {uploadingFile.status === 'uploading' && (
                        <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                          <div 
                            className="bg-blue-500 h-1 rounded-full transition-all"
                            style={{ width: `${uploadingFile.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="absolute top-2 right-2 flex items-center space-x-1">
                    {getStatusIcon(uploadingFile.status)}
                    <button
                      onClick={() => removeUploadingFile(uploadingFile.file)}
                      className="text-gray-400 hover:text-red-500"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                  
                  {uploadingFile.status === 'error' && (
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs">
                      <p className="text-red-700">{uploadingFile.error}</p>
                      <button
                        onClick={() => retryUpload(uploadingFile.file)}
                        className="text-red-600 hover:text-red-700 mt-1"
                      >
                        Retry
                      </button>
                    </div>
                  )}
                </div>
              );
            } else {
              return (
                <div key={index} className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-800 border rounded-lg">
                  <div className="flex-shrink-0">
                    {uploadingFile.preview ? (
                      <img 
                        src={uploadingFile.preview} 
                        alt={uploadingFile.file.name}
                        className="w-8 h-8 object-cover rounded"
                      />
                    ) : (
                      <IconComponent className={`w-6 h-6 ${typeInfo.color}`} />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                        {uploadingFile.file.name}
                      </h4>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs text-gray-500">
                          {formatFileSize(uploadingFile.file.size)}
                        </span>
                        {getStatusIcon(uploadingFile.status)}
                      </div>
                    </div>
                    
                    {uploadingFile.status === 'uploading' && (
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div 
                          className="bg-blue-500 h-1 rounded-full transition-all"
                          style={{ width: `${uploadingFile.progress}%` }}
                        />
                      </div>
                    )}
                    
                    {uploadingFile.status === 'error' && (
                      <div className="mt-1">
                        <p className="text-xs text-red-600">{uploadingFile.error}</p>
                        <button
                          onClick={() => retryUpload(uploadingFile.file)}
                          className="text-xs text-red-600 hover:text-red-700 mt-1"
                        >
                          Retry
                        </button>
                      </div>
                    )}
                  </div>
                  
                  <button
                    onClick={() => removeUploadingFile(uploadingFile.file)}
                    className="text-gray-400 hover:text-red-500"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              );
            }
          })}
        </div>
      )}
    </div>
  );
}