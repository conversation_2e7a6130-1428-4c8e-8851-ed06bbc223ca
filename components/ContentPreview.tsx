"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Eye,
  Download,
  Share2,
  Maximize2,
  Edit,
  Check,
  Star,
  ChevronLeft,
  ChevronRight,
  FileText,
  Map,
  BarChart3,
  Calendar,
  Users,
  HelpCircle,
  List,
  Mic,
  Presentation,
} from "lucide-react";

interface ContentPreviewProps {
  content: any;
  onExport?: (format: string) => void;
  onEdit?: () => void;
  onShare?: () => void;
}

export function ContentPreview({ content, onExport, onEdit, onShare }: ContentPreviewProps) {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [view, setView] = useState<"slides" | "notes" | "script">("slides");

  if (!content) return null;

  const renderSlideContent = (slide: any) => {
    switch (content.type) {
      case "executive_summary":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-3xl font-bold mb-6">{slide.title}</h2>
            {slide.subtitle && (
              <h3 className="text-xl text-gray-600 mb-8">{slide.subtitle}</h3>
            )}
            <div className="space-y-6">
              {slide.content.keyPoints.map((point: any, index: number) => (
                <div key={index} className="flex items-start gap-4">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Check className="w-5 h-5 text-blue-600" />
                  </div>
                  <div>
                    <div className="font-semibold text-lg">{point.title}</div>
                    <div className="text-gray-600">{point.description}</div>
                  </div>
                </div>
              ))}
            </div>
            {slide.content.callToAction && (
              <div className="mt-8 p-4 bg-blue-50 rounded-lg text-center">
                <p className="text-lg font-medium text-blue-900">
                  {slide.content.callToAction}
                </p>
              </div>
            )}
            {slide.content.metrics && (
              <div className="mt-8 grid grid-cols-3 gap-4">
                {Object.entries(slide.content.metrics).map(([key, value]) => (
                  <div key={key} className="text-center">
                    <div className="text-2xl font-bold text-blue-600">{value as string}</div>
                    <div className="text-sm text-gray-500 capitalize">
                      {key.replace(/([A-Z])/g, " $1").trim()}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case "site_heatmap":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold mb-4">{slide.title}</h2>
            {slide.subtitle && (
              <h3 className="text-lg text-gray-600 mb-6">{slide.subtitle}</h3>
            )}
            <div className="bg-gray-100 rounded-lg p-4 h-96 flex items-center justify-center">
              <div className="text-gray-500">
                <Map className="w-16 h-16 mx-auto mb-4" />
                <p>Interactive heat map visualization</p>
                <p className="text-sm mt-2">
                  {slide.content.locations.length} locations mapped
                </p>
              </div>
            </div>
            {slide.content.legend && (
              <div className="mt-6">
                <h4 className="font-semibold mb-2">{slide.content.legend.title}</h4>
                <div className="flex gap-4">
                  {slide.content.legend.ranges.map((range: any, index: number) => (
                    <div key={index} className="flex items-center gap-2">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: range.color }}
                      />
                      <span className="text-sm">{range.label}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        );

      case "compliance_dashboard":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold mb-4">{slide.title}</h2>
            {slide.subtitle && (
              <h3 className="text-lg text-gray-600 mb-6">{slide.subtitle}</h3>
            )}
            <div className="grid grid-cols-4 gap-4 mb-8">
              <div className="text-center p-4 bg-gray-50 rounded-lg">
                <div className="text-3xl font-bold">
                  {slide.content.overview.totalRequirements}
                </div>
                <div className="text-sm text-gray-500">Total Requirements</div>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <div className="text-3xl font-bold text-green-600">
                  {slide.content.overview.compliant}
                </div>
                <div className="text-sm text-gray-500">Compliant</div>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <div className="text-3xl font-bold text-yellow-600">
                  {slide.content.overview.inProgress}
                </div>
                <div className="text-sm text-gray-500">In Progress</div>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <div className="text-3xl font-bold text-red-600">
                  {slide.content.overview.pending}
                </div>
                <div className="text-sm text-gray-500">Pending</div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-6">
              <div className="bg-gray-100 rounded-lg p-4 h-48 flex items-center justify-center">
                <div className="text-gray-500">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                  <p className="text-sm">Status Distribution Chart</p>
                </div>
              </div>
              <div className="bg-gray-100 rounded-lg p-4 h-48 flex items-center justify-center">
                <div className="text-gray-500">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                  <p className="text-sm">Category Breakdown</p>
                </div>
              </div>
            </div>
          </div>
        );

      case "timeline_visualization":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold mb-4">{slide.title}</h2>
            {slide.subtitle && (
              <h3 className="text-lg text-gray-600 mb-6">{slide.subtitle}</h3>
            )}
            <div className="relative">
              <div className="absolute left-1/2 transform -translate-x-1/2 h-full w-1 bg-gray-300" />
              {slide.content.timeline.events.map((event: any, index: number) => (
                <div
                  key={index}
                  className={`relative flex ${
                    index % 2 === 0 ? "justify-start" : "justify-end"
                  } mb-8`}
                >
                  <div
                    className={`w-5/12 ${
                      index % 2 === 0 ? "text-right pr-8" : "text-left pl-8"
                    }`}
                  >
                    <div className="bg-white border rounded-lg p-4 shadow-sm">
                      <div className="flex items-center gap-2 mb-2">
                        {event.milestone && <Star className="w-4 h-4 text-yellow-500" />}
                        <h4 className="font-semibold">{event.title}</h4>
                      </div>
                      <p className="text-sm text-gray-600">{event.description}</p>
                      <p className="text-xs text-gray-400 mt-2">
                        {new Date(event.date).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div
                    className="absolute left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full"
                    style={{ backgroundColor: event.color }}
                  />
                </div>
              ))}
            </div>
            {slide.content.phases && (
              <div className="mt-8 grid grid-cols-4 gap-4">
                {slide.content.phases.map((phase: any, index: number) => (
                  <div key={index} className="text-center">
                    <div
                      className={`p-3 rounded-lg ${
                        phase.status === "completed"
                          ? "bg-green-100"
                          : phase.status === "in_progress"
                          ? "bg-blue-100"
                          : "bg-gray-100"
                      }`}
                    >
                      <div className="font-semibold">{phase.name}</div>
                      <div className="text-sm text-gray-600">{phase.duration}</div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        );

      case "team_introduction":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold mb-4">{slide.title}</h2>
            {slide.subtitle && (
              <h3 className="text-lg text-gray-600 mb-6">{slide.subtitle}</h3>
            )}
            {slide.layout === "team-overview" ? (
              <div className="grid grid-cols-3 gap-6">
                {/* Team overview layout */}
                <div className="col-span-3 text-center mb-6">
                  <p className="text-lg">{slide.content.teamStructure?.hierarchy}</p>
                </div>
              </div>
            ) : (
              <div className="flex gap-8">
                <div className="w-1/3">
                  <div className="w-48 h-48 bg-gray-200 rounded-full mx-auto mb-4" />
                </div>
                <div className="w-2/3">
                  <h3 className="text-2xl font-bold mb-2">{slide.content.profile.name}</h3>
                  <p className="text-lg text-gray-600 mb-4">{slide.content.profile.role}</p>
                  <p className="mb-6">{slide.content.profile.bio}</p>
                  <div className="space-y-2">
                    {slide.content.profile.qualifications.map((qual: string, index: number) => (
                      <div key={index} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-500" />
                        <span>{qual}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        );

      case "presentation_deck":
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            {slide.layout === "title" && (
              <div className="text-center py-16">
                <h1 className="text-4xl font-bold mb-4">{slide.title}</h1>
                <p className="text-xl text-gray-600">{slide.subtitle}</p>
              </div>
            )}
            {slide.layout === "agenda" && (
              <>
                <h2 className="text-3xl font-bold mb-8">{slide.title}</h2>
                <div className="grid grid-cols-2 gap-4">
                  {slide.content.items.map((item: string, index: number) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-semibold text-blue-600">
                        {index + 1}
                      </div>
                      <span>{item}</span>
                    </div>
                  ))}
                </div>
              </>
            )}
            {slide.layout === "two-column" && (
              <>
                <h2 className="text-2xl font-bold mb-6">{slide.title}</h2>
                <div className="grid grid-cols-2 gap-8">
                  <div>
                    {slide.content.left.type === "bullets" && (
                      <ul className="space-y-3">
                        {slide.content.left.items.map((item: string, index: number) => (
                          <li key={index} className="flex items-start gap-2">
                            <span className="text-blue-600 mt-1">•</span>
                            <span>{item}</span>
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                  <div>
                    {slide.content.right.type === "image" && (
                      <div>
                        <div className="bg-gray-200 rounded-lg h-64 flex items-center justify-center">
                          <FileText className="w-16 h-16 text-gray-400" />
                        </div>
                        {slide.content.right.caption && (
                          <p className="text-sm text-gray-600 mt-2 text-center">
                            {slide.content.right.caption}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
            {slide.layout === "stats" && (
              <>
                <h2 className="text-2xl font-bold mb-8">{slide.title}</h2>
                <div className="grid grid-cols-4 gap-6">
                  {slide.content.stats.map((stat: any, index: number) => (
                    <div key={index} className="text-center">
                      <div className="text-4xl font-bold text-blue-600 mb-2">
                        {stat.value}
                      </div>
                      <div className="text-sm text-gray-600">{stat.label}</div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        );

      default:
        return (
          <div className="p-8 bg-white rounded-lg shadow-sm">
            <h2 className="text-2xl font-bold mb-4">Content Preview</h2>
            <p className="text-gray-600">
              Preview for {content.type} content type is not yet implemented.
            </p>
          </div>
        );
    }
  };

  const renderNotesContent = () => {
    if (!content.slides?.[currentSlide]?.notes) {
      return <p className="text-gray-500">No speaker notes for this slide.</p>;
    }
    return (
      <div className="prose prose-sm max-w-none">
        <p>{content.slides[currentSlide].notes}</p>
      </div>
    );
  };

  const renderScriptContent = () => {
    if (content.type !== "voice_script") {
      return <p className="text-gray-500">Voice script not available for this content type.</p>;
    }

    return (
      <div className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2">Introduction ({content.content.introduction.duration})</h3>
          <p className="mb-2">{content.content.introduction.script}</p>
          <p className="text-sm text-gray-600 italic">{content.content.introduction.notes}</p>
        </div>

        {content.content.sections.map((section: any, index: number) => (
          <div key={index}>
            <h3 className="font-semibold mb-2">
              {section.title} ({section.duration})
            </h3>
            <p className="mb-2">{section.script}</p>
            {section.emphasis && (
              <p className="text-sm text-gray-600">
                <strong>Emphasize:</strong> {section.emphasis.join(", ")}
              </p>
            )}
            {section.visualCues && (
              <p className="text-sm text-gray-600">
                <strong>Visual cues:</strong> {section.visualCues.join(", ")}
              </p>
            )}
            {section.pacing && (
              <p className="text-sm text-gray-600 italic">{section.pacing}</p>
            )}
          </div>
        ))}

        <div>
          <h3 className="font-semibold mb-2">Closing ({content.content.closing.duration})</h3>
          <p className="mb-2">{content.content.closing.script}</p>
          <p className="text-sm text-gray-600 mb-2">
            <strong>Call to action:</strong> {content.content.closing.callToAction}
          </p>
          <p className="text-sm text-gray-600 italic">{content.content.closing.finalNote}</p>
        </div>
      </div>
    );
  };

  const slides = content.slides || [];
  const totalSlides = slides.length;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{content.title || "Content Preview"}</CardTitle>
          <div className="flex items-center gap-2 mt-2">
            <Badge variant="secondary">{content.type}</Badge>
            {content.metadata?.confidence && (
              <Badge variant="outline">
                {(content.metadata.confidence * 100).toFixed(0)}% confidence
              </Badge>
            )}
          </div>
        </div>
        <div className="flex gap-2">
          {onEdit && (
            <Button onClick={onEdit} variant="outline" size="sm">
              <Edit className="w-4 h-4 mr-1" />
              Edit
            </Button>
          )}
          {onShare && (
            <Button onClick={onShare} variant="outline" size="sm">
              <Share2 className="w-4 h-4 mr-1" />
              Share
            </Button>
          )}
          {onExport && (
            <div className="flex gap-1">
              <Button onClick={() => onExport("pptx")} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                PPTX
              </Button>
              <Button onClick={() => onExport("pdf")} variant="outline" size="sm">
                <Download className="w-4 h-4 mr-1" />
                PDF
              </Button>
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={view} onValueChange={(v) => setView(v as any)} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="slides">Slides</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="script">Script</TabsTrigger>
          </TabsList>

          <TabsContent value="slides" className="mt-4">
            {totalSlides > 0 ? (
              <>
                <div className="mb-4 flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={() => setCurrentSlide(Math.max(0, currentSlide - 1))}
                      disabled={currentSlide === 0}
                      variant="outline"
                      size="sm"
                    >
                      <ChevronLeft className="w-4 h-4" />
                    </Button>
                    <span className="text-sm text-gray-600">
                      Slide {currentSlide + 1} of {totalSlides}
                    </span>
                    <Button
                      onClick={() => setCurrentSlide(Math.min(totalSlides - 1, currentSlide + 1))}
                      disabled={currentSlide === totalSlides - 1}
                      variant="outline"
                      size="sm"
                    >
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                  <Button variant="outline" size="sm">
                    <Maximize2 className="w-4 h-4 mr-1" />
                    Fullscreen
                  </Button>
                </div>

                <div className="border rounded-lg bg-gray-50 min-h-[500px] flex items-center justify-center">
                  {renderSlideContent(slides[currentSlide])}
                </div>

                {/* Slide thumbnails */}
                <div className="mt-4 flex gap-2 overflow-x-auto pb-2">
                  {slides.map((slide: any, index: number) => (
                    <button
                      key={index}
                      onClick={() => setCurrentSlide(index)}
                      className={`flex-shrink-0 w-24 h-16 rounded border-2 transition-colors ${
                        currentSlide === index
                          ? "border-blue-500"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                    >
                      <div className="w-full h-full bg-white rounded flex items-center justify-center text-xs text-gray-500">
                        Slide {index + 1}
                      </div>
                    </button>
                  ))}
                </div>
              </>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <Presentation className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                <p>No slides available for this content type.</p>
              </div>
            )}
          </TabsContent>

          <TabsContent value="notes" className="mt-4">
            <div className="min-h-[400px] p-6 bg-gray-50 rounded-lg">
              {renderNotesContent()}
            </div>
          </TabsContent>

          <TabsContent value="script" className="mt-4">
            <div className="min-h-[400px] p-6 bg-gray-50 rounded-lg">
              {renderScriptContent()}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}