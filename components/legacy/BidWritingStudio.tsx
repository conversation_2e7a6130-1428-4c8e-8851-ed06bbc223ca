'use client';

import React, { useState, useEffect, useMemo } from 'react';
import {
  FileText,
  Lightbulb,
  RefreshCw,
} from 'lucide-react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
// import { Id } from '../convex/_generated/dataModel';
import { toast } from 'sonner';
import TenderUpload from './TenderUpload';

export default function BidWritingStudio() {
  const tenders = useQuery(api.tenders.list);
  const [activeTenderId, setActiveTenderId] = useState<string | null>(
    null
  );

  useEffect(() => {
    if (tenders && tenders.length > 0 && !activeTenderId) {
      setActiveTenderId(tenders[0]._id);
    }
  }, [tenders, activeTenderId]);

  const tenderData = useQuery(
    api.tenders.get,
    activeTenderId ? { id: activeTenderId as any } : 'skip'
  );

  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);

  useEffect(() => {
    if (tenderData && tenderData.bidSections.length > 0) {
      // If there's no active section or the active section doesn't belong to the current tender, reset it
      const currentSectionIds = new Set(tenderData.bidSections.map(s => s._id));
      if (!activeSectionId || !currentSectionIds.has(activeSectionId as any)) {
        setActiveSectionId(tenderData.bidSections[0]._id);
      }
    } else if (!tenderData) {
      setActiveSectionId(null);
    }
  }, [tenderData, activeSectionId]);


  const bidSections = useMemo(() => tenderData?.bidSections ?? [], [tenderData]);

  const activeSection_data = useMemo(() =>
    bidSections.find((s) => s._id === activeSectionId), [bidSections, activeSectionId]
  );

  const [isGenerating, setIsGenerating] = useState<string | null>(null);
  const generateContent = useAction(api.ai.generateContent);
  const updateSectionContent = useMutation(api.bidSections.update);

  const handleGenerate = async (sectionId: string) => {
    if (!tenderData || !activeSection_data) return;
    setIsGenerating(sectionId);
    try {
      await generateContent({
        sectionId: sectionId as any,
        tenderName: (tenderData as any).name,
        clientName: (tenderData as any).clientName,
        sectionTitle: activeSection_data.title,
      });
      toast.success(`Content for "${activeSection_data.title}" generated!`);
    } catch (error) {
      toast.error('Failed to generate content.');
      console.error(error);
    } finally {
      setIsGenerating(null);
    }
  };

  const handleContentChange = (content: string) => {
    if (!activeSectionId) return;
    void updateSectionContent({ id: activeSectionId as any, content });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400 bg-green-900/20 border-green-400';
      case 'review':
        return 'text-blue-400 bg-blue-900/20 border-blue-400';
      case 'needs_revision':
        return 'text-red-400 bg-red-900/20 border-red-400';
      default:
        return 'text-gray-400 bg-gray-900/20 border-gray-600';
    }
  };

  if (tenders === undefined) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
      </div>
    );
  }

  if (tenders.length === 0) {
    return <TenderUpload />;
  }

  if (!tenderData) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
      </div>
    );
  }

  return (
    <div className="h-[800px] flex border border-gray-800 rounded-lg">
      {/* Sidebar - Section Navigation */}
      <div className="w-80 bg-gray-900 border-r border-gray-700 flex flex-col rounded-l-lg">
        <div className="p-4 border-b border-gray-700">
          <h2 className="text-lg font-bold mb-2">BID WRITING STUDIO</h2>
          <select
            value={activeTenderId ?? ''}
            onChange={(e) => setActiveTenderId(e.target.value)}
            className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-sm"
          >
            {tenders.map((tender) => (
              <option key={tender._id} value={tender._id}>
                {tender.name}
              </option>
            ))}
          </select>
        </div>

        {/* Section List */}
        <div className="flex-1 overflow-y-auto">
          {bidSections.map((section) => (
            <div
              key={section._id}
              onClick={() => setActiveSectionId(section._id)}
              className={`p-4 border-b border-gray-700 cursor-pointer transition-all ${activeSectionId === section._id
                  ? 'bg-gray-800 border-l-4 border-l-blue-400'
                  : 'hover:bg-gray-800/50'
                }`}
            >
              <div className="flex items-center justify-between mb-2">
                <h3 className="font-bold text-sm">{section.title}</h3>
                <div
                  className={`px-2 py-1 rounded text-xs font-bold border ${getStatusColor(
                    section.status
                  )}`}
                >
                  {section.status.toUpperCase()}
                </div>
              </div>

              <div className="flex items-center justify-between text-xs text-gray-400 mb-3">
                <span>Words: {section.wordCount}/{section.wordLimit || '∞'}</span>
                <span>Weight: {section.scoreWeight}%</span>
              </div>

              <div className="w-full bg-gray-700 rounded-full h-1 mb-2">
                <div
                  className={`h-1 rounded-full transition-all ${section.wordCount > 0 ? 'bg-green-400' : 'bg-gray-600'
                    }`}
                  style={{
                    width: section.wordLimit
                      ? `${Math.min(
                        (section.wordCount / section.wordLimit) * 100,
                        100
                      )}%`
                      : section.wordCount > 0
                        ? '100%'
                        : '0%',
                  }}
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {activeSection_data ? (
          <>
            {/* Section Header */}
            <div className="p-4 border-b border-gray-700 bg-gray-900">
              <div className="flex items-center justify-between mb-3">
                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-1">
                    {activeSection_data.title}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <span>Weight: {activeSection_data.scoreWeight}%</span>
                    <span>
                      Words: {activeSection_data.wordCount}/
                      {activeSection_data.wordLimit || '∞'}
                    </span>
                    <span>Status: {activeSection_data.status.toUpperCase()}</span>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <button
                    onClick={() => void handleGenerate(activeSection_data._id)}
                    disabled={isGenerating === activeSection_data._id}
                    className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
                  >
                    {isGenerating === activeSection_data._id ? (
                      <RefreshCw size={14} className="animate-spin" />
                    ) : (
                      <Lightbulb size={14} />
                    )}
                    <span>AI GENERATE</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Content Editor */}
            <div className="flex-1 p-4 overflow-hidden">
              <textarea
                value={activeSection_data.content}
                onChange={(e) => handleContentChange(e.target.value)}
                placeholder={`Start writing the ${activeSection_data.title} section...`}
                className="w-full h-full bg-black text-gray-100 font-mono text-sm resize-none outline-none border border-gray-700 rounded p-4 leading-relaxed"
              />
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FileText size={48} className="mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-bold mb-2">SELECT A BID SECTION</h3>
              <p className="text-gray-400">
                Choose a section from the sidebar to start writing
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
