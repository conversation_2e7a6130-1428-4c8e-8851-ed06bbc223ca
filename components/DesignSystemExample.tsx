import React from 'react';

/**
 * Design System Example Component
 * 
 * This component demonstrates the usage of the comprehensive dark theme design system
 * created for the Bid Writing Studio platform.
 */
export default function DesignSystemExample() {
  return (
    <div className="min-h-screen bg-background p-6 space-y-8">
      {/* Header Section */}
      <header className="text-center space-y-4">
        <h1 className="text-4xl font-bold text-gradient">
          Bid Writing Studio Design System
        </h1>
        <p className="text-text-secondary max-w-2xl mx-auto">
          A comprehensive dark theme design system built with Tailwind CSS, featuring 
          professional colors, accessible components, and consistent spacing.
        </p>
      </header>

      {/* Color Palette Section */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Color Palette</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Primary Colors */}
          <div className="card space-y-3">
            <h3 className="text-lg font-medium text-text-primary">Primary (Blue)</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-primary-600"></div>
                <span className="text-sm text-text-secondary">primary-600 (Default)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-primary-700"></div>
                <span className="text-sm text-text-secondary">primary-700 (Hover)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-primary-500"></div>
                <span className="text-sm text-text-secondary">primary-500 (Light)</span>
              </div>
            </div>
          </div>

          {/* Accent Colors */}
          <div className="card space-y-3">
            <h3 className="text-lg font-medium text-text-primary">Accent (Green)</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-accent-600"></div>
                <span className="text-sm text-text-secondary">accent-600 (Default)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-accent-700"></div>
                <span className="text-sm text-text-secondary">accent-700 (Hover)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-accent-500"></div>
                <span className="text-sm text-text-secondary">accent-500 (Light)</span>
              </div>
            </div>
          </div>

          {/* Secondary Colors */}
          <div className="card space-y-3">
            <h3 className="text-lg font-medium text-text-primary">Secondary (Teal)</h3>
            <div className="space-y-2">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-secondary-600"></div>
                <span className="text-sm text-text-secondary">secondary-600 (Default)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-secondary-700"></div>
                <span className="text-sm text-text-secondary">secondary-700 (Hover)</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 rounded bg-secondary-500"></div>
                <span className="text-sm text-text-secondary">secondary-500 (Light)</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Button Examples */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Button Components</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Primary Actions</h3>
            <div className="space-y-3">
              <button className="btn-primary w-full">Submit Bid</button>
              <button className="btn-accent w-full">Generate Content</button>
              <button className="btn-secondary w-full">Save Draft</button>
            </div>
          </div>

          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Secondary Actions</h3>
            <div className="space-y-3">
              <button className="btn-outline w-full">Cancel</button>
              <button className="btn-ghost w-full">More Options</button>
              <button className="btn-destructive w-full">Delete</button>
            </div>
          </div>

          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Disabled States</h3>
            <div className="space-y-3">
              <button className="btn-primary w-full" disabled>Disabled Primary</button>
              <button className="btn-outline w-full" disabled>Disabled Outline</button>
              <button className="btn-ghost w-full" disabled>Disabled Ghost</button>
            </div>
          </div>
        </div>
      </section>

      {/* Form Components */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Form Components</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Input Fields</h3>
            <div className="space-y-3">
              <input 
                type="text" 
                className="input-field" 
                placeholder="Standard input field"
              />
              <input 
                type="email" 
                className="input-field input-field-error" 
                placeholder="Error state input"
              />
              <input 
                type="password" 
                className="input-field input-field-success" 
                placeholder="Success state input"
              />
              <textarea 
                className="input-field" 
                rows={3}
                placeholder="Textarea input field"
              />
            </div>
          </div>

          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Form Layout</h3>
            <form className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Project Name
                </label>
                <input 
                  type="text" 
                  className="input-field" 
                  placeholder="Enter project name"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">
                  Description
                </label>
                <textarea 
                  className="input-field" 
                  rows={3}
                  placeholder="Enter project description"
                />
              </div>
              <div className="flex space-x-3">
                <button type="submit" className="btn-primary flex-1">
                  Create Project
                </button>
                <button type="button" className="btn-outline flex-1">
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      </section>

      {/* Card Components */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Card Components</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <h3 className="text-lg font-medium text-text-primary mb-3">Basic Card</h3>
            <p className="text-text-secondary mb-4">
              This is a standard card component with basic styling and padding.
            </p>
            <div className="flex space-x-2">
              <span className="badge badge-primary">In Progress</span>
              <span className="badge badge-secondary">Draft</span>
            </div>
          </div>

          <div className="card-elevated">
            <h3 className="text-lg font-medium text-text-primary mb-3">Elevated Card</h3>
            <p className="text-text-secondary mb-4">
              This is an elevated card with enhanced shadow and background opacity.
            </p>
            <div className="flex space-x-2">
              <span className="badge badge-success">Completed</span>
              <span className="badge badge-accent">Featured</span>
            </div>
          </div>

          <div className="card-interactive">
            <h3 className="text-lg font-medium text-text-primary mb-3">Interactive Card</h3>
            <p className="text-text-secondary mb-4">
              This card has hover effects and can be clicked for interaction.
            </p>
            <div className="flex space-x-2">
              <span className="badge badge-warning">Review</span>
              <span className="badge badge-error">Urgent</span>
            </div>
          </div>
        </div>
      </section>

      {/* Status Badges */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Status Badges</h2>
        
        <div className="card">
          <h3 className="text-lg font-medium text-text-primary mb-4">Badge Variations</h3>
          <div className="flex flex-wrap gap-3">
            <span className="badge badge-primary">Primary</span>
            <span className="badge badge-secondary">Secondary</span>
            <span className="badge badge-accent">Accent</span>
            <span className="badge badge-success">Success</span>
            <span className="badge badge-warning">Warning</span>
            <span className="badge badge-error">Error</span>
          </div>
        </div>
      </section>

      {/* Loading States */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Loading States</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Loading Spinner</h3>
            <div className="flex items-center space-x-4">
              <div className="loading-spinner" aria-label="Loading..."></div>
              <span className="text-text-secondary">Processing request...</span>
            </div>
          </div>

          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Skeleton Loading</h3>
            <div className="space-y-3">
              <div className="loading-pulse h-4 w-full"></div>
              <div className="loading-pulse h-4 w-3/4"></div>
              <div className="loading-pulse h-4 w-1/2"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Animation Examples */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Animation Examples</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="card animate-fade-in">
            <h3 className="text-lg font-medium text-text-primary mb-3">Fade In</h3>
            <p className="text-text-secondary">Gentle entrance animation</p>
          </div>

          <div className="card animate-slide-in">
            <h3 className="text-lg font-medium text-text-primary mb-3">Slide In</h3>
            <p className="text-text-secondary">Horizontal entrance animation</p>
          </div>

          <div className="card animate-slide-up">
            <h3 className="text-lg font-medium text-text-primary mb-3">Slide Up</h3>
            <p className="text-text-secondary">Vertical entrance animation</p>
          </div>

          <div className="card animate-scale-in">
            <h3 className="text-lg font-medium text-text-primary mb-3">Scale In</h3>
            <p className="text-text-secondary">Scaling entrance animation</p>
          </div>
        </div>
      </section>

      {/* Typography */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Typography</h2>
        
        <div className="card space-y-4">
          <h1 className="text-4xl font-bold text-text-primary">Heading 1 (4xl)</h1>
          <h2 className="text-3xl font-semibold text-text-primary">Heading 2 (3xl)</h2>
          <h3 className="text-2xl font-medium text-text-primary">Heading 3 (2xl)</h3>
          <h4 className="text-xl font-medium text-text-primary">Heading 4 (xl)</h4>
          <h5 className="text-lg font-medium text-text-primary">Heading 5 (lg)</h5>
          <h6 className="text-base font-medium text-text-primary">Heading 6 (base)</h6>
          
          <div className="mt-6 space-y-2">
            <p className="text-text-primary">Primary text - Main content and headings</p>
            <p className="text-text-secondary">Secondary text - Body text and descriptions</p>
            <p className="text-text-tertiary">Tertiary text - Placeholder text and labels</p>
            <p className="text-text-quaternary">Quaternary text - Muted text and timestamps</p>
            <p className="text-text-disabled">Disabled text - Inactive content</p>
          </div>

          <div className="mt-6">
            <code className="font-mono text-sm bg-surface px-2 py-1 rounded text-accent-400">
              Code example with Geist Mono font
            </code>
          </div>
        </div>
      </section>

      {/* Utility Classes */}
      <section className="space-y-6">
        <h2 className="text-2xl font-semibold text-text-primary">Utility Classes</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Glass Effects</h3>
            <div className="glass-effect p-4 rounded-lg">
              <p className="text-text-secondary">Glass effect with backdrop blur</p>
            </div>
            <div className="glass-effect-dark p-4 rounded-lg">
              <p className="text-text-secondary">Dark glass effect</p>
            </div>
          </div>

          <div className="card space-y-4">
            <h3 className="text-lg font-medium text-text-primary">Glow Effects</h3>
            <div className="bg-surface p-4 rounded-lg shadow-glow">
              <p className="text-text-secondary">Primary glow effect</p>
            </div>
            <div className="bg-surface p-4 rounded-lg shadow-glow-accent">
              <p className="text-text-secondary">Accent glow effect</p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="text-center pt-8 border-t border-border">
        <p className="text-text-tertiary">
          Design System v1.0 - Built with Tailwind CSS for Bid Writing Studio
        </p>
      </footer>
    </div>
  );
}