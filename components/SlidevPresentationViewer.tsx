"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs';

interface SlidevPresentation {
  _id: string;
  tenderId: string;
  slidesPath: string;
  exports: Record<string, string>;
  voiceScript?: string;
  presentationUrl?: string;
  status: string;
  createdAt: number;
  error?: string;
}

interface SlidevPresentationViewerProps {
  presentation: SlidevPresentation;
  onExport?: (format: string) => void;
  onStartServer?: () => void;
}

export function SlidevPresentationViewer({ 
  presentation, 
  onExport, 
  onStartServer 
}: SlidevPresentationViewerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [selectedExport, setSelectedExport] = useState<string>('');

  const exportFormats = [
    { key: 'pdf', label: 'PDF Document', icon: '📄', description: 'Print-ready PDF version' },
    { key: 'pptx', label: 'PowerPoint', icon: '📊', description: 'Editable PowerPoint presentation' },
    { key: 'png', label: 'Image Slides', icon: '🖼️', description: 'Individual slide images' },
    { key: 'html', label: 'HTML Export', icon: '🌐', description: 'Self-contained web page' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ready': return 'bg-green-100 text-green-800';
      case 'generating': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleExport = async (format: string) => {
    setIsLoading(true);
    setSelectedExport(format);
    try {
      await onExport?.(format);
    } finally {
      setIsLoading(false);
      setSelectedExport('');
    }
  };

  const formatCreatedDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (presentation.status === 'failed') {
    return (
      <Card className="border-red-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            ❌ Presentation Generation Failed
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{presentation.error}</p>
          <Button onClick={onStartServer} variant="outline">
            Retry Generation
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              📊 Slidev Presentation
              <Badge className={getStatusColor(presentation.status)}>
                {presentation.status}
              </Badge>
            </CardTitle>
            <div className="text-sm text-gray-500">
              Generated {formatCreatedDate(presentation.createdAt)}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Live Presentation */}
            {presentation.presentationUrl && (
              <div className="space-y-2">
                <h4 className="font-semibold text-green-700">🎬 Live Presentation</h4>
                <div className="space-y-1">
                  <Button 
                    onClick={() => window.open(presentation.presentationUrl, '_blank')}
                    className="w-full"
                    variant="default"
                  >
                    Open Presentation
                  </Button>
                  <Button 
                    onClick={() => window.open(`${presentation.presentationUrl}/presenter`, '_blank')}
                    className="w-full"
                    variant="outline"
                  >
                    Presenter Mode
                  </Button>
                  <Button 
                    onClick={() => window.open(`${presentation.presentationUrl}/overview`, '_blank')}
                    className="w-full"
                    variant="ghost"
                  >
                    Slide Overview
                  </Button>
                </div>
              </div>
            )}

            {/* Quick Stats */}
            <div className="space-y-2">
              <h4 className="font-semibold text-blue-700">📈 Presentation Stats</h4>
              <div className="space-y-1 text-sm">
                <div>Slides: ~11 slides</div>
                <div>Duration: ~15 minutes</div>
                <div>Exports: {Object.keys(presentation.exports).length} formats</div>
                <div>Voice Script: {presentation.voiceScript ? 'Included' : 'Not generated'}</div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="space-y-2">
              <h4 className="font-semibold text-purple-700">⚡ Quick Actions</h4>
              <div className="space-y-1">
                <Button 
                  onClick={() => handleExport('pdf')}
                  disabled={isLoading && selectedExport === 'pdf'}
                  variant="outline"
                  className="w-full"
                >
                  {isLoading && selectedExport === 'pdf' ? '⏳ Generating...' : '📄 Export PDF'}
                </Button>
                <Button 
                  onClick={() => handleExport('pptx')}
                  disabled={isLoading && selectedExport === 'pptx'}
                  variant="outline"
                  className="w-full"
                >
                  {isLoading && selectedExport === 'pptx' ? '⏳ Generating...' : '📊 Export PowerPoint'}
                </Button>
                {!presentation.presentationUrl && (
                  <Button 
                    onClick={onStartServer}
                    disabled={isLoading}
                    variant="default"
                    className="w-full"
                  >
                    🚀 Start Live Server
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Tabs */}
      <Tabs defaultValue="exports" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="exports">📤 Exports</TabsTrigger>
          <TabsTrigger value="voice">🎤 Voice Script</TabsTrigger>
          <TabsTrigger value="preview">👁️ Preview</TabsTrigger>
          <TabsTrigger value="settings">⚙️ Settings</TabsTrigger>
        </TabsList>

        {/* Export Formats Tab */}
        <TabsContent value="exports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Export to Multiple Formats</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {exportFormats.map((format) => (
                  <div key={format.key} className="border rounded-lg p-4 space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="text-2xl">{format.icon}</div>
                      <div>
                        <h4 className="font-semibold">{format.label}</h4>
                        <p className="text-sm text-gray-600">{format.description}</p>
                      </div>
                    </div>
                    
                    <div className="flex gap-2">
                      {presentation.exports[format.key] ? (
                        <Button
                          onClick={() => window.open(presentation.exports[format.key], '_blank')}
                          variant="default"
                          size="sm"
                          className="flex-1"
                        >
                          📥 Download
                        </Button>
                      ) : (
                        <Button
                          onClick={() => handleExport(format.key)}
                          disabled={isLoading && selectedExport === format.key}
                          variant="outline"
                          size="sm"
                          className="flex-1"
                        >
                          {isLoading && selectedExport === format.key ? '⏳ Generating...' : '🔄 Generate'}
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Voice Script Tab */}
        <TabsContent value="voice" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>🎤 Voice Script for Presentation</CardTitle>
            </CardHeader>
            <CardContent>
              {presentation.voiceScript ? (
                <div className="space-y-4">
                  <div className="bg-blue-50 rounded-lg p-4">
                    <h4 className="font-semibold text-blue-800 mb-2">📋 Script Overview</h4>
                    <ul className="text-sm text-blue-700 space-y-1">
                      <li>• Professional narration for each slide</li>
                      <li>• Estimated duration: 15-20 minutes</li>
                      <li>• Includes timing markers and emphasis points</li>
                      <li>• Ready for voice bot integration</li>
                    </ul>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4 max-h-96 overflow-y-auto">
                    <pre className="whitespace-pre-wrap text-sm">
                      {presentation.voiceScript}
                    </pre>
                  </div>
                  
                  <div className="flex gap-2">
                    <Button 
                      onClick={() => navigator.clipboard.writeText(presentation.voiceScript!)}
                      variant="outline"
                    >
                      📋 Copy Script
                    </Button>
                    <Button 
                      onClick={() => {
                        const blob = new Blob([presentation.voiceScript!], { type: 'text/plain' });
                        const url = URL.createObjectURL(blob);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = 'voice-script.txt';
                        a.click();
                      }}
                      variant="outline"
                    >
                      💾 Download Script
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-4">🎤</div>
                  <p>Voice script not generated for this presentation.</p>
                  <Button 
                    onClick={() => handleExport('voice')}
                    variant="outline"
                    className="mt-4"
                  >
                    Generate Voice Script
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preview Tab */}
        <TabsContent value="preview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>👁️ Presentation Preview</CardTitle>
            </CardHeader>
            <CardContent>
              {presentation.presentationUrl ? (
                <div className="space-y-4">
                  <div className="aspect-video bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                    <iframe
                      src={presentation.presentationUrl}
                      className="w-full h-full rounded-lg"
                      title="Slidev Presentation Preview"
                    />
                  </div>
                  <div className="flex justify-center gap-2">
                    <Button 
                      onClick={() => window.open(presentation.presentationUrl, '_blank')}
                      variant="default"
                    >
                      🔗 Open Full Screen
                    </Button>
                    <Button 
                      onClick={() => window.open(`${presentation.presentationUrl}/presenter`, '_blank')}
                      variant="outline"
                    >
                      🎤 Presenter View
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-4">📊</div>
                  <p>Live preview not available. Start the presentation server to enable preview.</p>
                  <Button 
                    onClick={onStartServer}
                    variant="default"
                    className="mt-4"
                  >
                    🚀 Start Presentation Server
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>⚙️ Presentation Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Theme</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="default">Default</option>
                      <option value="apple-basic">Apple Basic</option>
                      <option value="bricks">Bricks</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">Export Quality</label>
                    <select className="w-full p-2 border rounded-md">
                      <option value="high">High Quality</option>
                      <option value="medium">Medium Quality</option>
                      <option value="low">Low Quality (Fast)</option>
                    </select>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Include voice script in exports</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked />
                    <span className="text-sm">Include presenter notes</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" />
                    <span className="text-sm">Auto-start presentation server</span>
                  </label>
                </div>

                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    🔄 Regenerate Presentation
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default SlidevPresentationViewer;