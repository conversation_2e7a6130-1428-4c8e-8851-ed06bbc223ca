import React, { useState } from 'react';
import { useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';
import { UploadCloud, File, Loader2 } from 'lucide-react';

export default function TenderUpload() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const generateUploadUrl = useMutation(api.tenders.generateUploadUrl);
  const processDocument = useAction(api.tenderProcessing.processDocument);

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!selectedFile) {
      toast.error('Please select a file to upload.');
      return;
    }

    setIsProcessing(true);
    const toastId = toast.loading('Uploading document...');

    try {
      const postUrl = await generateUploadUrl();
      const result = await fetch(postUrl, {
        method: 'POST',
        headers: { 'Content-Type': selectedFile.type },
        body: selectedFile,
      });
      const { storageId } = await result.json();

      toast.loading('Processing document with AI...', { id: toastId });

      await processDocument({ storageId });

      toast.success('Tender created successfully!', { id: toastId });
      setSelectedFile(null);
    } catch (error) {
      console.error(error);
      toast.error('An error occurred during processing.', { id: toastId });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto text-center p-8 border-2 border-dashed border-gray-600 rounded-lg">
      <UploadCloud className="mx-auto h-12 w-12 text-gray-400" />
      <h2 className="mt-4 text-xl font-semibold text-white">
        Upload Tender Document
      </h2>
      <p className="mt-2 text-sm text-gray-400">
        Upload a PDF document to automatically create a new tender project.
      </p>
      <form onSubmit={(e) => void handleSubmit(e)} className="mt-6">
        <div className="flex items-center justify-center">
          <label
            htmlFor="file-upload"
            className="relative cursor-pointer rounded-md bg-gray-800 px-4 py-2 text-sm font-semibold text-white hover:bg-gray-700"
          >
            <span>Select a file</span>
            <input
              id="file-upload"
              name="file-upload"
              type="file"
              className="sr-only"
              accept=".pdf"
              onChange={handleFileChange}
              disabled={isProcessing}
            />
          </label>
        </div>

        {selectedFile && (
          <div className="mt-4 text-sm text-gray-300 flex items-center justify-center">
            <File className="h-4 w-4 mr-2" />
            <span>{selectedFile.name}</span>
          </div>
        )}

        <div className="mt-6">
          <button
            type="submit"
            disabled={!selectedFile || isProcessing}
            className="w-full max-w-xs mx-auto flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-black bg-white hover:bg-gray-200 disabled:opacity-50"
          >
            {isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Create Tender'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
