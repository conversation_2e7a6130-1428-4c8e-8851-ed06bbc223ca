'use client';

import React, { useState } from 'react';
import {
  BarChart3,
  Edit3,
  FileText,
  Search,
  Users,
  GitBranch,
  Settings,
  HelpCircle,
  ChevronRight,
  ChevronDown,
  Folder,
  Clock,
  Star,
  Archive,
  Plus,
  X,
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  isMobileMenuOpen: boolean;
  onMobileMenuClose: () => void;
}

interface SidebarItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
  children?: SidebarItem[];
}

export default function Sidebar({ 
  activeTab, 
  onTabChange, 
  isMobileMenuOpen, 
  onMobileMenuClose 
}: SidebarProps) {
  const [expandedSections, setExpandedSections] = useState<string[]>([
    'workspaces',
    'recent',
  ]);

  const toggleSection = (sectionId: string) => {
    setExpandedSections(prev =>
      prev.includes(sectionId)
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const mainNavItems: SidebarItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <BarChart3 size={20} />,
      badge: 3,
    },
    {
      id: 'bid-studio',
      label: 'Bid Studio',
      icon: <Edit3 size={20} />,
      badge: 2,
    },
    {
      id: 'files',
      label: 'Files',
      icon: <FileText size={20} />,
      badge: 5,
    },
    {
      id: 'search',
      label: 'Search',
      icon: <Search size={20} />,
    },
    {
      id: 'agents',
      label: 'AI Agents',
      icon: <Users size={20} />,
      badge: 1,
    },
    {
      id: 'workflow',
      label: 'Workflow',
      icon: <GitBranch size={20} />,
    },
  ];

  const workspaceItems: SidebarItem[] = [
    {
      id: 'office-cleaning',
      label: 'Office Cleaning Services',
      icon: <Folder size={16} />,
      children: [
        {
          id: 'office-cleaning-sections',
          label: 'Bid Sections',
          icon: <FileText size={16} />,
          badge: 12,
        },
        {
          id: 'office-cleaning-files',
          label: 'Documents',
          icon: <FileText size={16} />,
          badge: 8,
        },
      ],
    },
    {
      id: 'maintenance-services',
      label: 'Maintenance Services',
      icon: <Folder size={16} />,
      children: [
        {
          id: 'maintenance-sections',
          label: 'Bid Sections',
          icon: <FileText size={16} />,
          badge: 6,
        },
        {
          id: 'maintenance-files',
          label: 'Documents',
          icon: <FileText size={16} />,
          badge: 4,
        },
      ],
    },
    {
      id: 'security-services',
      label: 'Security Services',
      icon: <Folder size={16} />,
      children: [
        {
          id: 'security-sections',
          label: 'Bid Sections',
          icon: <FileText size={16} />,
          badge: 9,
        },
        {
          id: 'security-files',
          label: 'Documents',
          icon: <FileText size={16} />,
          badge: 15,
        },
      ],
    },
  ];

  const recentItems: SidebarItem[] = [
    {
      id: 'recent-executive-summary',
      label: 'Executive Summary',
      icon: <Clock size={16} />,
    },
    {
      id: 'recent-technical-specs',
      label: 'Technical Specifications',
      icon: <Clock size={16} />,
    },
    {
      id: 'recent-pricing-model',
      label: 'Pricing Model',
      icon: <Clock size={16} />,
    },
    {
      id: 'recent-compliance',
      label: 'Compliance Documentation',
      icon: <Clock size={16} />,
    },
  ];

  const quickActions = [
    {
      id: 'new-tender',
      label: 'New Tender',
      icon: <Plus size={16} />,
      color: 'bg-primary hover:bg-primary-700',
    },
    {
      id: 'upload-docs',
      label: 'Upload Documents',
      icon: <FileText size={16} />,
      color: 'bg-accent hover:bg-accent-700',
    },
  ];

  const handleNavItemClick = (itemId: string) => {
    onTabChange(itemId);
    if (isMobileMenuOpen) {
      onMobileMenuClose();
    }
  };

  const renderNavItem = (item: SidebarItem, level: number = 0) => {
    const isActive = activeTab === item.id;
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedSections.includes(item.id);
    const paddingLeft = level === 0 ? 'pl-3' : level === 1 ? 'pl-6' : 'pl-9';

    return (
      <div key={item.id}>
        <button
          onClick={() => {
            if (hasChildren) {
              toggleSection(item.id);
            } else {
              handleNavItemClick(item.id);
            }
          }}
          className={`w-full flex items-center justify-between ${paddingLeft} pr-3 py-2 text-left rounded-lg transition-colors group ${
            isActive
              ? 'bg-primary text-white'
              : 'text-text-secondary hover:bg-surface-hover hover:text-text-primary'
          }`}
        >
          <div className="flex items-center space-x-3">
            <div className={`${isActive ? 'text-white' : 'text-text-quaternary group-hover:text-text-secondary'}`}>
              {item.icon}
            </div>
            <span className="text-sm font-medium">{item.label}</span>
            {item.badge && (
              <span className={`px-2 py-1 text-xs font-bold rounded-full ${
                isActive 
                  ? 'bg-primary-800 text-white' 
                  : 'bg-surface-tertiary text-text-tertiary'
              }`}>
                {item.badge}
              </span>
            )}
          </div>
          {hasChildren && (
            <div className={`transition-transform ${isExpanded ? 'rotate-90' : ''}`}>
              <ChevronRight size={16} className={isActive ? 'text-white' : 'text-text-quaternary'} />
            </div>
          )}
        </button>
        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children!.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={onMobileMenuClose}
        />
      )}

      {/* Sidebar */}
      <aside
        className={`fixed lg:relative inset-y-0 left-0 z-50 w-80 bg-background-secondary border-r border-border transform transition-transform duration-300 ease-in-out lg:translate-x-0 ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="flex flex-col h-full">
          {/* Mobile Header */}
          <div className="lg:hidden flex items-center justify-between p-4 border-b border-border">
            <h2 className="text-lg font-bold text-text-primary">Navigation</h2>
            <button
              onClick={onMobileMenuClose}
              className="p-2 rounded-lg hover:bg-surface-hover transition-colors"
            >
              <X size={20} className="text-text-secondary" />
            </button>
          </div>

          {/* Quick Actions */}
          <div className="p-4 border-b border-border">
            <div className="space-y-2">
              {quickActions.map(action => (
                <button
                  key={action.id}
                  onClick={() => handleNavItemClick(action.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors ${action.color} text-white`}
                >
                  {action.icon}
                  <span className="text-sm font-medium">{action.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Main Navigation */}
          <div className="flex-1 overflow-y-auto">
            <nav className="p-4 space-y-2">
              {/* Main Navigation Items */}
              <div className="space-y-1">
                {mainNavItems.map(item => renderNavItem(item))}
              </div>

              {/* Workspaces Section */}
              <div className="pt-6">
                <button
                  onClick={() => toggleSection('workspaces')}
                  className="w-full flex items-center justify-between px-3 py-2 text-left"
                >
                  <h3 className="text-xs font-bold text-text-quaternary uppercase tracking-wide">
                    Workspaces
                  </h3>
                  <div className={`transition-transform ${expandedSections.includes('workspaces') ? 'rotate-90' : ''}`}>
                    <ChevronRight size={14} className="text-text-quaternary" />
                  </div>
                </button>
                {expandedSections.includes('workspaces') && (
                  <div className="mt-2 space-y-1">
                    {workspaceItems.map(item => renderNavItem(item))}
                  </div>
                )}
              </div>

              {/* Recent Section */}
              <div className="pt-6">
                <button
                  onClick={() => toggleSection('recent')}
                  className="w-full flex items-center justify-between px-3 py-2 text-left"
                >
                  <h3 className="text-xs font-bold text-text-quaternary uppercase tracking-wide">
                    Recent
                  </h3>
                  <div className={`transition-transform ${expandedSections.includes('recent') ? 'rotate-90' : ''}`}>
                    <ChevronRight size={14} className="text-text-quaternary" />
                  </div>
                </button>
                {expandedSections.includes('recent') && (
                  <div className="mt-2 space-y-1">
                    {recentItems.map(item => renderNavItem(item))}
                  </div>
                )}
              </div>

              {/* Favorites Section */}
              <div className="pt-6">
                <button
                  onClick={() => toggleSection('favorites')}
                  className="w-full flex items-center justify-between px-3 py-2 text-left"
                >
                  <h3 className="text-xs font-bold text-text-quaternary uppercase tracking-wide">
                    Favorites
                  </h3>
                  <div className={`transition-transform ${expandedSections.includes('favorites') ? 'rotate-90' : ''}`}>
                    <ChevronRight size={14} className="text-text-quaternary" />
                  </div>
                </button>
                {expandedSections.includes('favorites') && (
                  <div className="mt-2 space-y-1">
                    <div className="px-3 py-4 text-center">
                      <Star size={24} className="mx-auto text-text-quaternary mb-2" />
                      <p className="text-xs text-text-tertiary">
                        No favorites yet
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Archive Section */}
              <div className="pt-6">
                <button
                  onClick={() => toggleSection('archive')}
                  className="w-full flex items-center justify-between px-3 py-2 text-left"
                >
                  <h3 className="text-xs font-bold text-text-quaternary uppercase tracking-wide">
                    Archive
                  </h3>
                  <div className={`transition-transform ${expandedSections.includes('archive') ? 'rotate-90' : ''}`}>
                    <ChevronRight size={14} className="text-text-quaternary" />
                  </div>
                </button>
                {expandedSections.includes('archive') && (
                  <div className="mt-2 space-y-1">
                    <div className="px-3 py-4 text-center">
                      <Archive size={24} className="mx-auto text-text-quaternary mb-2" />
                      <p className="text-xs text-text-tertiary">
                        No archived items
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </nav>
          </div>

          {/* Bottom Section */}
          <div className="p-4 border-t border-border">
            <div className="space-y-1">
              <button
                onClick={() => handleNavItemClick('settings')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                  activeTab === 'settings'
                    ? 'bg-primary text-white'
                    : 'text-text-secondary hover:bg-surface-hover hover:text-text-primary'
                }`}
              >
                <Settings size={16} />
                <span className="text-sm font-medium">Settings</span>
              </button>
              <button
                onClick={() => handleNavItemClick('help')}
                className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg transition-colors ${
                  activeTab === 'help'
                    ? 'bg-primary text-white'
                    : 'text-text-secondary hover:bg-surface-hover hover:text-text-primary'
                }`}
              >
                <HelpCircle size={16} />
                <span className="text-sm font-medium">Help & Support</span>
              </button>
            </div>
          </div>
        </div>
      </aside>
    </>
  );
}