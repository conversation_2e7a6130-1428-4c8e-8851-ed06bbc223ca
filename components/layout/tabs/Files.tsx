'use client';

import React from 'react';
import { FileText, Upload, Folder, Search } from 'lucide-react';

interface FilesProps {
  className?: string;
}

export default function Files({ className = '' }: FilesProps) {
  return (
    <div className={`p-6 ${className}`}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-secondary/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <FileText size={32} className="text-secondary" />
          </div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Files</h1>
          <p className="text-text-tertiary">
            Document management and file storage system - Coming soon
          </p>
        </div>

        <div className="bg-surface border border-border rounded-lg p-8 text-center">
          <Upload size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-xl font-bold text-text-primary mb-2">File Management System</h3>
          <p className="text-text-tertiary mb-6">
            Advanced file management with intelligent document processing, version control, and collaboration features.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Folder size={24} className="mx-auto text-primary mb-2" />
              <h4 className="font-medium text-text-primary">Organization</h4>
              <p className="text-sm text-text-tertiary">Smart folder structure</p>
            </div>
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Search size={24} className="mx-auto text-accent mb-2" />
              <h4 className="font-medium text-text-primary">Search</h4>
              <p className="text-sm text-text-tertiary">Content-based search</p>
            </div>
            <div className="p-4 bg-surface-secondary rounded-lg">
              <FileText size={24} className="mx-auto text-secondary mb-2" />
              <h4 className="font-medium text-text-primary">Analysis</h4>
              <p className="text-sm text-text-tertiary">Document intelligence</p>
            </div>
          </div>

          <button className="px-6 py-3 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors">
            Coming Soon
          </button>
        </div>
      </div>
    </div>
  );
}