'use client';

import React from 'react';
import { Edit3, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Zap } from 'lucide-react';

interface BidStudioProps {
  className?: string;
}

export default function BidStudio({ className = '' }: BidStudioProps) {
  return (
    <div className={`p-6 ${className}`}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-accent/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Edit3 size={32} className="text-accent" />
          </div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Bid Studio</h1>
          <p className="text-text-tertiary">
            AI-powered bid writing workspace - Coming soon with enhanced features
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Brain size={24} className="text-primary" />
              <h3 className="text-lg font-bold text-text-primary">AI Writing Assistant</h3>
            </div>
            <p className="text-text-tertiary mb-4">
              Advanced AI agents will help you craft compelling bid content with industry-specific knowledge and best practices.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm text-text-secondary">Smart content generation</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm text-text-secondary">Compliance checking</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-primary rounded-full"></div>
                <span className="text-sm text-text-secondary">Quality scoring</span>
              </div>
            </div>
          </div>

          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <FileText size={24} className="text-accent" />
              <h3 className="text-lg font-bold text-text-primary">Document Intelligence</h3>
            </div>
            <p className="text-text-tertiary mb-4">
              Automatically analyze tender documents, extract requirements, and suggest optimal bid structures.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="text-sm text-text-secondary">Requirement extraction</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="text-sm text-text-secondary">Risk assessment</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-accent rounded-full"></div>
                <span className="text-sm text-text-secondary">Competitive analysis</span>
              </div>
            </div>
          </div>

          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Zap size={24} className="text-secondary" />
              <h3 className="text-lg font-bold text-text-primary">Workflow Automation</h3>
            </div>
            <p className="text-text-tertiary mb-4">
              Streamlined processes for collaboration, review cycles, and submission management.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-secondary rounded-full"></div>
                <span className="text-sm text-text-secondary">Automated workflows</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-secondary rounded-full"></div>
                <span className="text-sm text-text-secondary">Review management</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-secondary rounded-full"></div>
                <span className="text-sm text-text-secondary">Version control</span>
              </div>
            </div>
          </div>

          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center space-x-3 mb-4">
              <FileText size={24} className="text-warning" />
              <h3 className="text-lg font-bold text-text-primary">Legacy Integration</h3>
            </div>
            <p className="text-text-tertiary mb-4">
              The current bid writing functionality is being enhanced and will be integrated here.
            </p>
            <button className="w-full mt-4 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors">
              Access Current Studio
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}