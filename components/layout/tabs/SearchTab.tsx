'use client';

import React from 'react';
import { Search, Filter, Zap } from 'lucide-react';

interface SearchTabProps {
  className?: string;
}

export default function SearchTab({ className = '' }: SearchTabProps) {
  return (
    <div className={`p-6 ${className}`}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-info/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search size={32} className="text-info" />
          </div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">Search</h1>
          <p className="text-text-tertiary">
            Advanced search across all content - Coming soon
          </p>
        </div>

        <div className="bg-surface border border-border rounded-lg p-8 text-center">
          <Search size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-xl font-bold text-text-primary mb-2">Intelligent Search</h3>
          <p className="text-text-tertiary mb-6">
            Powerful search capabilities across tenders, documents, and content with AI-powered insights.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Filter size={24} className="mx-auto text-primary mb-2" />
              <h4 className="font-medium text-text-primary">Smart Filters</h4>
              <p className="text-sm text-text-tertiary">Advanced filtering options</p>
            </div>
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Zap size={24} className="mx-auto text-accent mb-2" />
              <h4 className="font-medium text-text-primary">AI Insights</h4>
              <p className="text-sm text-text-tertiary">Semantic search capabilities</p>
            </div>
          </div>

          <button className="px-6 py-3 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors">
            Coming Soon
          </button>
        </div>
      </div>
    </div>
  );
}