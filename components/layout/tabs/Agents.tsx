'use client';

import React from 'react';
import { Users, Brain, Cpu, Activity } from 'lucide-react';

interface AgentsProps {
  className?: string;
}

export default function Agents({ className = '' }: AgentsProps) {
  return (
    <div className={`p-6 ${className}`}>
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-warning/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Users size={32} className="text-warning" />
          </div>
          <h1 className="text-3xl font-bold text-text-primary mb-2">AI Agents</h1>
          <p className="text-text-tertiary">
            Intelligent AI agents for bid writing - Coming soon
          </p>
        </div>

        <div className="bg-surface border border-border rounded-lg p-8 text-center">
          <Brain size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-xl font-bold text-text-primary mb-2">AI Agent Management</h3>
          <p className="text-text-tertiary mb-6">
            Deploy and manage specialized AI agents for different aspects of bid writing and tender management.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Cpu size={24} className="mx-auto text-primary mb-2" />
              <h4 className="font-medium text-text-primary">Processing</h4>
              <p className="text-sm text-text-tertiary">Document analysis agents</p>
            </div>
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Brain size={24} className="mx-auto text-accent mb-2" />
              <h4 className="font-medium text-text-primary">Writing</h4>
              <p className="text-sm text-text-tertiary">Content generation agents</p>
            </div>
            <div className="p-4 bg-surface-secondary rounded-lg">
              <Activity size={24} className="mx-auto text-secondary mb-2" />
              <h4 className="font-medium text-text-primary">Monitoring</h4>
              <p className="text-sm text-text-tertiary">Quality assurance agents</p>
            </div>
          </div>

          <button className="px-6 py-3 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors">
            Coming Soon
          </button>
        </div>
      </div>
    </div>
  );
}