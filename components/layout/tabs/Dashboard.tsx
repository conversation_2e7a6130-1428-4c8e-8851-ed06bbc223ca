'use client';

import React, { useState } from 'react';
import {
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  FileText,
  Users,
  Calendar,
  DollarSign,
  Target,
  Activity,
  BarChart3,
  PieChart,
  Plus,
  Filter,
  Download,
  RefreshCw,
  Zap,
  Bot,
  PlayCircle,
  Settings,
} from 'lucide-react';
import { useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import LoadingSpinner, { SkeletonLoader, CircularProgress } from '../LoadingSpinner';

interface DashboardProps {
  className?: string;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  icon: React.ReactNode;
  color: 'primary' | 'accent' | 'secondary' | 'warning' | 'error';
  description?: string;
}

interface ActivityItem {
  id: string;
  type: 'tender_created' | 'section_completed' | 'ai_generated' | 'review_required' | 'submitted';
  title: string;
  description: string;
  timestamp: number;
  user: string;
  metadata?: Record<string, any>;
}

function MetricCard({ title, value, change, icon, color, description }: MetricCardProps) {
  const colorClasses = {
    primary: 'bg-primary/10 text-primary border-primary/20',
    accent: 'bg-accent/10 text-accent border-accent/20',
    secondary: 'bg-secondary/10 text-secondary border-secondary/20',
    warning: 'bg-warning/10 text-warning border-warning/20',
    error: 'bg-error/10 text-error border-error/20',
  };

  const changeColor = change && change > 0 ? 'text-accent' : change && change < 0 ? 'text-error' : 'text-text-tertiary';

  return (
    <div className="bg-surface border border-border rounded-lg p-6 hover:shadow-md transition-shadow">
      <div className="flex items-center justify-between mb-4">
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          {icon}
        </div>
        {change !== undefined && (
          <div className={`flex items-center space-x-1 ${changeColor}`}>
            {change > 0 ? (
              <TrendingUp size={16} />
            ) : change < 0 ? (
              <TrendingDown size={16} />
            ) : (
              <Activity size={16} />
            )}
            <span className="text-sm font-medium">
              {change > 0 ? '+' : ''}{change}%
            </span>
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <h3 className="text-2xl font-bold text-text-primary">{value}</h3>
        <p className="text-sm text-text-secondary">{title}</p>
        {description && (
          <p className="text-xs text-text-tertiary">{description}</p>
        )}
      </div>
    </div>
  );
}

function ActivityFeed({ activities }: { activities: ActivityItem[] }) {
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'tender_created':
        return <Plus size={16} className="text-primary" />;
      case 'section_completed':
        return <CheckCircle size={16} className="text-accent" />;
      case 'ai_generated':
        return <Users size={16} className="text-secondary" />;
      case 'review_required':
        return <AlertCircle size={16} className="text-warning" />;
      case 'submitted':
        return <FileText size={16} className="text-info" />;
      default:
        return <Activity size={16} className="text-text-tertiary" />;
    }
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  return (
    <div className="space-y-4">
      {activities.map((activity) => (
        <div key={activity.id} className="flex items-start space-x-3">
          <div className="flex-shrink-0 w-8 h-8 bg-surface-secondary rounded-full flex items-center justify-center">
            {getActivityIcon(activity.type)}
          </div>
          <div className="flex-1 min-w-0">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium text-text-primary truncate">
                {activity.title}
              </h4>
              <span className="text-xs text-text-quaternary">
                {formatTimeAgo(activity.timestamp)}
              </span>
            </div>
            <p className="text-xs text-text-tertiary mt-1">
              {activity.description}
            </p>
            <p className="text-xs text-text-quaternary mt-1">
              by {activity.user}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}

export default function Dashboard({ className = '' }: DashboardProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d'>('30d');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Mock data - replace with actual Convex queries
  const dashboardData = {
    metrics: {
      activeTenders: 8,
      completedSections: 67,
      pendingReviews: 12,
      winRate: 73.5,
      averageScore: 82.4,
      totalRevenue: 2400000,
    },
    recentActivity: [
      {
        id: '1',
        type: 'tender_created' as const,
        title: 'New Tender: Office Cleaning Services',
        description: 'Created tender for 500 Bourke Street Melbourne',
        timestamp: Date.now() - 1000 * 60 * 30,
        user: 'John Smith',
      },
      {
        id: '2',
        type: 'ai_generated' as const,
        title: 'AI Content Generated',
        description: 'Executive Summary completed for Maintenance Services',
        timestamp: Date.now() - 1000 * 60 * 60,
        user: 'AI Assistant',
      },
      {
        id: '3',
        type: 'section_completed' as const,
        title: 'Section Completed',
        description: 'Technical Specifications reviewed and approved',
        timestamp: Date.now() - 1000 * 60 * 60 * 2,
        user: 'Sarah Johnson',
      },
      {
        id: '4',
        type: 'review_required' as const,
        title: 'Review Required',
        description: 'Pricing Model needs final review before submission',
        timestamp: Date.now() - 1000 * 60 * 60 * 4,
        user: 'Mike Chen',
      },
      {
        id: '5',
        type: 'submitted' as const,
        title: 'Tender Submitted',
        description: 'Security Services tender submitted successfully',
        timestamp: Date.now() - 1000 * 60 * 60 * 6,
        user: 'Lisa Wang',
      },
    ],
    tenders: [
      {
        id: '1',
        name: 'Office Cleaning Services - 500 Bourke Street',
        progress: 85,
        deadline: Date.now() + 1000 * 60 * 60 * 48,
        status: 'in_progress',
        value: 450000,
        sections: { total: 12, completed: 10, pending: 2 },
      },
      {
        id: '2',
        name: 'Maintenance Services - Collins Square',
        progress: 60,
        deadline: Date.now() + 1000 * 60 * 60 * 24 * 7,
        status: 'in_progress',
        value: 320000,
        sections: { total: 8, completed: 5, pending: 3 },
      },
      {
        id: '3',
        name: 'Security Services - Crown Casino',
        progress: 100,
        deadline: Date.now() - 1000 * 60 * 60 * 24,
        status: 'submitted',
        value: 680000,
        sections: { total: 15, completed: 15, pending: 0 },
      },
    ],
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Simulate refresh delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsRefreshing(false);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-AU', {
      style: 'currency',
      currency: 'AUD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDeadline = (timestamp: number) => {
    const now = Date.now();
    const diff = timestamp - now;
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (days > 0) return `${days} days`;
    if (hours > 0) return `${hours} hours`;
    return 'Due now';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'text-warning bg-warning/10';
      case 'submitted':
        return 'text-accent bg-accent/10';
      case 'won':
        return 'text-accent bg-accent/10';
      case 'lost':
        return 'text-error bg-error/10';
      default:
        return 'text-text-tertiary bg-surface-secondary';
    }
  };

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-text-primary">Dashboard</h1>
          <p className="text-text-tertiary mt-1">
            Welcome back! Here's what's happening with your tenders.
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value as '7d' | '30d' | '90d')}
            className="px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
          >
            <option value="7d">Last 7 days</option>
            <option value="30d">Last 30 days</option>
            <option value="90d">Last 90 days</option>
          </select>
          <button
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-700 disabled:opacity-50 text-white rounded-lg transition-colors"
          >
            <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
            <span className="text-sm">Refresh</span>
          </button>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6">
        <MetricCard
          title="Active Tenders"
          value={dashboardData.metrics.activeTenders}
          change={12}
          icon={<FileText size={24} />}
          color="primary"
          description="Currently in progress"
        />
        <MetricCard
          title="Completed Sections"
          value={dashboardData.metrics.completedSections}
          change={8}
          icon={<CheckCircle size={24} />}
          color="accent"
          description="This month"
        />
        <MetricCard
          title="Pending Reviews"
          value={dashboardData.metrics.pendingReviews}
          change={-15}
          icon={<Clock size={24} />}
          color="warning"
          description="Awaiting approval"
        />
        <MetricCard
          title="Win Rate"
          value={`${dashboardData.metrics.winRate}%`}
          change={5.2}
          icon={<Target size={24} />}
          color="secondary"
          description="Last 12 months"
        />
        <MetricCard
          title="Average Score"
          value={`${dashboardData.metrics.averageScore}%`}
          change={3.1}
          icon={<BarChart3 size={24} />}
          color="accent"
          description="Tender evaluations"
        />
        <MetricCard
          title="Total Value"
          value={formatCurrency(dashboardData.metrics.totalRevenue)}
          change={22}
          icon={<DollarSign size={24} />}
          color="primary"
          description="Active tenders"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Active Tenders */}
        <div className="lg:col-span-2">
          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-text-primary">Active Tenders</h2>
              <button className="flex items-center space-x-2 text-primary hover:text-primary-700 transition-colors">
                <Plus size={16} />
                <span className="text-sm">New Tender</span>
              </button>
            </div>
            
            <div className="space-y-4">
              {dashboardData.tenders.map((tender) => (
                <div key={tender.id} className="p-4 bg-surface-secondary rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="font-medium text-text-primary">{tender.name}</h3>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${getStatusColor(tender.status)}`}>
                      {tender.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 mb-3">
                    <div>
                      <p className="text-xs text-text-tertiary">Progress</p>
                      <div className="flex items-center space-x-2 mt-1">
                        <div className="flex-1 bg-surface-tertiary rounded-full h-2">
                          <div
                            className="bg-primary rounded-full h-2 transition-all duration-300"
                            style={{ width: `${tender.progress}%` }}
                          />
                        </div>
                        <span className="text-xs text-text-secondary">{tender.progress}%</span>
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-text-tertiary">Deadline</p>
                      <p className="text-sm text-text-secondary mt-1">
                        {formatDeadline(tender.deadline)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-xs text-text-tertiary">Value</p>
                      <p className="text-sm font-medium text-text-primary">
                        {formatCurrency(tender.value)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-text-tertiary">Sections</p>
                      <p className="text-sm font-medium text-text-primary">
                        {tender.sections.completed}/{tender.sections.total}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 pt-4 border-t border-border">
              <button className="w-full text-center text-sm text-primary hover:text-primary-700 transition-colors">
                View All Tenders
              </button>
            </div>
          </div>
        </div>

        {/* Activity Feed */}
        <div className="lg:col-span-1">
          <div className="bg-surface border border-border rounded-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-text-primary">Recent Activity</h2>
              <button className="text-primary hover:text-primary-700 transition-colors">
                <Filter size={16} />
              </button>
            </div>
            
            <ActivityFeed activities={dashboardData.recentActivity} />
            
            <div className="mt-6 pt-4 border-t border-border">
              <button className="w-full text-center text-sm text-primary hover:text-primary-700 transition-colors">
                View All Activity
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <h2 className="text-xl font-bold text-text-primary mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center space-x-3 p-4 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors">
            <Plus size={20} />
            <span>New Tender</span>
          </button>
          <button className="flex items-center space-x-3 p-4 bg-accent hover:bg-accent-700 text-white rounded-lg transition-colors">
            <FileText size={20} />
            <span>Upload Documents</span>
          </button>
          <button className="flex items-center space-x-3 p-4 bg-secondary hover:bg-secondary-700 text-white rounded-lg transition-colors">
            <Users size={20} />
            <span>Manage Agents</span>
          </button>
          <button className="flex items-center space-x-3 p-4 bg-surface-secondary hover:bg-surface-hover text-text-primary border border-border rounded-lg transition-colors">
            <Download size={20} />
            <span>Export Data</span>
          </button>
        </div>
      </div>
    </div>
  );
}