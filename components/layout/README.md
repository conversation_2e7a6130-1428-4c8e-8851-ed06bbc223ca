# Layout System Documentation

## Overview

The layout system provides a comprehensive, responsive, and accessible foundation for the Bid Writing Studio platform. It implements a modern dark theme design system with sophisticated navigation, state management, and error handling capabilities.

## Architecture

### Component Hierarchy

```
TenderPlatform (Main Container)
├── Header (Top Navigation & User Controls)
├── Sidebar (Left Navigation & Workspace Management)
├── TabNavigation (Feature Tab Controls)
└── Content Area
    ├── Dashboard
    ├── BidStudio
    ├── Files
    ├── SearchTab
    ├── Agents
    └── Workflow
```

## Core Components

### 1. TenderPlatform

The main platform container that orchestrates the entire application layout.

**Features:**
- Responsive layout management
- State persistence (localStorage)
- Global error boundary
- Keyboard shortcuts
- Mobile-first responsive design

**State Management:**
- Active tab tracking
- Sidebar collapse state
- Mobile menu state
- Loading and error states

**Keyboard Shortcuts:**
- `Ctrl/Cmd + 1-6`: Switch between main tabs
- `Ctrl/Cmd + N`: New tender
- `Ctrl/Cmd + U`: Upload documents

### 2. Header

Top navigation bar with user controls and global actions.

**Features:**
- Logo and branding
- Global search bar
- Theme toggle (light/dark)
- Notifications dropdown
- User profile menu
- Mobile menu toggle

**Components:**
- Search functionality
- Notification system
- User avatar and dropdown
- Theme switching

### 3. Sidebar

Left navigation panel for workspace and feature navigation.

**Features:**
- Collapsible sections
- Workspace organization
- Recent items tracking
- Favorites system
- Archive management
- Quick actions

**Sections:**
- Main navigation
- Workspaces (tender projects)
- Recent items
- Favorites
- Archive
- Settings & help

### 4. TabNavigation

Horizontal tab navigation for main platform features.

**Features:**
- Visual tab indicators
- Badge notifications
- Responsive tab layout
- Status bar with quick stats
- Quick action buttons

**Tabs:**
- Dashboard (overview)
- Bid Studio (writing workspace)
- Files (document management)
- Search (content search)
- Agents (AI management)
- Workflow (process automation)

## Tab Components

### Dashboard
Provides comprehensive overview of tenders, metrics, and activity.

**Widgets:**
- Metrics cards (active tenders, completion rates, etc.)
- Active tenders list with progress tracking
- Recent activity feed
- Quick actions panel

### BidStudio
AI-powered writing workspace (placeholder with roadmap).

**Planned Features:**
- AI writing assistant
- Document intelligence
- Workflow automation
- Legacy integration

### Files
Document management system (placeholder).

**Planned Features:**
- Smart organization
- Content-based search
- Document analysis
- Version control

### SearchTab
Advanced search capabilities (placeholder).

**Planned Features:**
- Intelligent search
- Smart filters
- AI insights
- Semantic search

### Agents
AI agent management (placeholder).

**Planned Features:**
- Agent deployment
- Performance monitoring
- Configuration management
- Task assignment

### Workflow
Process automation (placeholder).

**Planned Features:**
- Workflow designer
- Automation rules
- Approval chains
- Process monitoring

## Utility Components

### LoadingSpinner
Comprehensive loading state management.

**Variants:**
- `LoadingSpinner`: Basic spinner with message
- `RefreshSpinner`: Icon-based refresh indicator
- `SkeletonLoader`: Content placeholder
- `ShimmerEffect`: Animated loading effect
- `DotsLoader`: Animated dots indicator
- `ProgressBar`: Linear progress indicator
- `CircularProgress`: Circular progress indicator

### ErrorBoundary
React error boundary with fallback UI.

**Features:**
- Error catching and reporting
- Graceful degradation
- Recovery mechanisms
- Error logging
- Custom fallback components

**Components:**
- `ErrorBoundary`: Main boundary component
- `SimpleErrorFallback`: Minimal error display
- `useErrorHandler`: Error handling hook
- `withErrorBoundary`: HOC wrapper

## Design System Integration

### Colors
Fully integrated with the dark theme design system:
- Background hierarchy (primary, secondary, tertiary, elevated)
- Surface colors with hover states
- Text color hierarchy (primary, secondary, tertiary, quaternary)
- Semantic colors (error, success, warning, info)
- Brand colors (primary, accent, secondary)

### Typography
- Inter font for UI text
- Geist Mono for technical content
- Consistent sizing scale
- Proper line heights

### Spacing
- Consistent spacing scale (xs, sm, md, lg, xl, 2xl, etc.)
- Form field spacing
- Section spacing
- Component padding

### Responsive Design
- Mobile-first approach
- Breakpoint-aware components
- Adaptive sidebar behavior
- Touch-friendly interactions

## State Management

### Local State
Each component manages its own UI state using React hooks.

### Persistent State
Key application state is persisted to localStorage:
- Active tab selection
- Sidebar collapse state
- User preferences
- Recently accessed items

### Global State
Platform-wide state managed through props and context:
- Authentication state
- User data
- Global notifications
- Theme preferences

## Accessibility Features

### Keyboard Navigation
- Full keyboard accessibility
- Focus management
- Skip links
- Keyboard shortcuts

### Screen Reader Support
- Proper ARIA labels
- Semantic HTML structure
- Screen reader announcements
- Role attributes

### Color Contrast
- WCAG 2.1 AA compliance
- High contrast ratios
- Color-blind friendly
- Theme support

### Motion Preferences
- Respects `prefers-reduced-motion`
- Smooth transitions
- Optional animations
- Performance optimization

## Performance Optimization

### Code Splitting
- Lazy-loaded tab components
- Suspense boundaries
- Dynamic imports
- Bundle optimization

### Rendering Optimization
- React.memo for pure components
- useMemo for expensive calculations
- useCallback for stable references
- Optimized re-renders

### Loading States
- Skeleton placeholders
- Progressive loading
- Error boundaries
- Graceful degradation

## Mobile Experience

### Responsive Layout
- Sidebar transforms to overlay on mobile
- Touch-friendly interactions
- Swipe gestures
- Mobile-optimized spacing

### Navigation
- Mobile menu with overlay
- Touch-friendly buttons
- Simplified navigation
- Gesture support

### Performance
- Optimized for mobile networks
- Reduced bundle size
- Efficient rendering
- Battery-conscious animations

## Integration Points

### Convex Integration
- Real-time data synchronization
- Optimistic updates
- Error handling
- Loading states

### Authentication
- Convex Auth integration
- User session management
- Permission handling
- Secure state management

### File Management
- File upload handling
- Preview generation
- Progress tracking
- Error recovery

## Development Guidelines

### Adding New Tabs
1. Create component in `tabs/` directory
2. Add to tab configuration in `TabNavigation`
3. Update routing in `TenderPlatform`
4. Add keyboard shortcut if needed
5. Update sidebar navigation

### Styling Guidelines
- Use design system tokens
- Follow component patterns
- Maintain accessibility
- Test responsive behavior

### State Management
- Keep state close to usage
- Use localStorage for persistence
- Implement error boundaries
- Handle loading states

### Testing Considerations
- Test keyboard navigation
- Verify responsive behavior
- Check error boundaries
- Validate accessibility

## Future Enhancements

### Planned Features
- Drag-and-drop interface
- Customizable dashboard widgets
- Advanced theme system
- Real-time collaboration
- Progressive Web App features

### Performance Improvements
- Virtual scrolling for large lists
- Service worker caching
- Background sync
- Offline support

### Accessibility Enhancements
- Voice navigation
- Enhanced screen reader support
- Customizable UI scaling
- Color customization

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Dependencies

### Core Dependencies
- React 18+
- Next.js 15+
- TypeScript 5+
- Tailwind CSS 4+

### UI Dependencies
- Lucide React (icons)
- Sonner (toast notifications)
- Convex (data layer)

### Development Dependencies
- ESLint
- Prettier
- TypeScript
- Tailwind CSS

---

This layout system provides a robust foundation for the Bid Writing Studio platform, with room for growth and enhancement as the platform evolves.