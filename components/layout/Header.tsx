'use client';

import React, { useState } from 'react';
import {
  Bell,
  Search,
  Settings,
  User,
  Menu,
  X,
  Briefcase,
  ChevronDown,
  LogOut,
  HelpCircle,
  Moon,
  Sun,
} from 'lucide-react';
import { useAuthActions } from '@convex-dev/auth/react';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface HeaderProps {
  onMobileMenuToggle: () => void;
  isMobileMenuOpen: boolean;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export default function Header({ 
  onMobileMenuToggle, 
  isMobileMenuOpen, 
  activeTab, 
  onTabChange 
}: HeaderProps) {
  const { signOut } = useAuthActions();
  const [isProfileMenuOpen, setIsProfileMenuOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(true);

  // Mock user data - replace with actual user query
  const user = {
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'Bid Manager',
    avatar: null,
  };

  // Mock notifications - replace with actual notifications query
  const notifications = [
    {
      id: '1',
      type: 'reminder',
      title: 'Tender Deadline Approaching',
      message: 'Office Cleaning Services tender due in 2 hours',
      timestamp: Date.now() - 1000 * 60 * 30, // 30 minutes ago
      isRead: false,
    },
    {
      id: '2',
      type: 'success',
      title: 'Content Generated',
      message: 'AI has completed the Executive Summary section',
      timestamp: Date.now() - 1000 * 60 * 60, // 1 hour ago
      isRead: false,
    },
    {
      id: '3',
      type: 'info',
      title: 'Review Required',
      message: 'Technical Specifications section needs review',
      timestamp: Date.now() - 1000 * 60 * 60 * 2, // 2 hours ago
      isRead: true,
    },
  ];

  const handleSignOut = async () => {
    await signOut();
    setIsProfileMenuOpen(false);
  };

  const handleThemeToggle = () => {
    setIsDarkMode(!isDarkMode);
    // Theme toggle logic would go here
  };

  const formatTimeAgo = (timestamp: number) => {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'reminder':
        return '⏰';
      case 'success':
        return '✅';
      case 'info':
        return 'ℹ️';
      case 'warning':
        return '⚠️';
      case 'error':
        return '❌';
      default:
        return '📄';
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <header className="h-16 bg-background-secondary border-b border-border flex items-center justify-between px-4 lg:px-6 relative z-50">
      {/* Left Section - Logo and Mobile Menu */}
      <div className="flex items-center space-x-4">
        {/* Mobile Menu Toggle */}
        <button
          onClick={onMobileMenuToggle}
          className="lg:hidden p-2 rounded-lg hover:bg-surface-hover transition-colors"
          aria-label="Toggle mobile menu"
        >
          {isMobileMenuOpen ? (
            <X size={20} className="text-text-secondary" />
          ) : (
            <Menu size={20} className="text-text-secondary" />
          )}
        </button>

        {/* Logo */}
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <Briefcase size={16} className="text-white" />
          </div>
          <div className="hidden sm:block">
            <h1 className="text-lg font-bold text-text-primary">
              Bid Writing Studio
            </h1>
            <p className="text-xs text-text-tertiary">
              ARA Property Services
            </p>
          </div>
        </div>
      </div>

      {/* Center Section - Search Bar */}
      <div className="hidden md:flex flex-1 max-w-md mx-8">
        <div className="relative w-full">
          <Search size={18} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-quaternary" />
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder="Search tenders, sections, files..."
            className="w-full pl-10 pr-4 py-2 bg-surface rounded-lg border border-border focus:border-primary focus:outline-none transition-colors text-text-secondary placeholder-text-quaternary"
          />
        </div>
      </div>

      {/* Right Section - Actions and User */}
      <div className="flex items-center space-x-2">
        {/* Theme Toggle */}
        <button
          onClick={handleThemeToggle}
          className="p-2 rounded-lg hover:bg-surface-hover transition-colors"
          aria-label="Toggle theme"
        >
          {isDarkMode ? (
            <Sun size={18} className="text-text-secondary" />
          ) : (
            <Moon size={18} className="text-text-secondary" />
          )}
        </button>

        {/* Notifications */}
        <div className="relative">
          <button
            onClick={() => setIsNotificationsOpen(!isNotificationsOpen)}
            className="relative p-2 rounded-lg hover:bg-surface-hover transition-colors"
            aria-label="Notifications"
          >
            <Bell size={18} className="text-text-secondary" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 w-5 h-5 bg-error rounded-full flex items-center justify-center text-xs text-white font-bold">
                {unreadCount > 9 ? '9+' : unreadCount}
              </span>
            )}
          </button>

          {/* Notifications Dropdown */}
          {isNotificationsOpen && (
            <div className="absolute right-0 top-full mt-2 w-80 bg-surface border border-border rounded-lg shadow-lg z-dropdown">
              <div className="p-4 border-b border-border">
                <h3 className="font-bold text-text-primary">Notifications</h3>
                <p className="text-sm text-text-tertiary">
                  {unreadCount} unread notifications
                </p>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`p-4 border-b border-border hover:bg-surface-hover transition-colors ${
                      !notification.isRead ? 'bg-surface-secondary' : ''
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <span className="text-lg">
                        {getNotificationIcon(notification.type)}
                      </span>
                      <div className="flex-1">
                        <h4 className="font-medium text-text-primary text-sm">
                          {notification.title}
                        </h4>
                        <p className="text-sm text-text-tertiary">
                          {notification.message}
                        </p>
                        <p className="text-xs text-text-quaternary mt-1">
                          {formatTimeAgo(notification.timestamp)}
                        </p>
                      </div>
                      {!notification.isRead && (
                        <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              <div className="p-4 border-t border-border">
                <button className="w-full text-center text-sm text-primary hover:text-primary-700 transition-colors">
                  View All Notifications
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Settings */}
        <button
          className="p-2 rounded-lg hover:bg-surface-hover transition-colors"
          aria-label="Settings"
        >
          <Settings size={18} className="text-text-secondary" />
        </button>

        {/* User Profile */}
        <div className="relative">
          <button
            onClick={() => setIsProfileMenuOpen(!isProfileMenuOpen)}
            className="flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors"
            aria-label="User menu"
          >
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
              {user.avatar ? (
                <img
                  src={user.avatar}
                  alt={user.name}
                  className="w-full h-full rounded-full object-cover"
                />
              ) : (
                <User size={16} className="text-white" />
              )}
            </div>
            <div className="hidden sm:block text-left">
              <p className="text-sm font-medium text-text-primary">
                {user.name}
              </p>
              <p className="text-xs text-text-tertiary">
                {user.role}
              </p>
            </div>
            <ChevronDown size={16} className="text-text-secondary" />
          </button>

          {/* Profile Dropdown */}
          {isProfileMenuOpen && (
            <div className="absolute right-0 top-full mt-2 w-64 bg-surface border border-border rounded-lg shadow-lg z-dropdown">
              <div className="p-4 border-b border-border">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary rounded-full flex items-center justify-center">
                    {user.avatar ? (
                      <img
                        src={user.avatar}
                        alt={user.name}
                        className="w-full h-full rounded-full object-cover"
                      />
                    ) : (
                      <User size={20} className="text-white" />
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-text-primary">
                      {user.name}
                    </p>
                    <p className="text-sm text-text-tertiary">
                      {user.email}
                    </p>
                    <p className="text-xs text-text-quaternary">
                      {user.role}
                    </p>
                  </div>
                </div>
              </div>
              <div className="p-2">
                <button className="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors text-left">
                  <User size={16} className="text-text-secondary" />
                  <span className="text-sm text-text-primary">Profile</span>
                </button>
                <button className="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors text-left">
                  <Settings size={16} className="text-text-secondary" />
                  <span className="text-sm text-text-primary">Settings</span>
                </button>
                <button className="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors text-left">
                  <HelpCircle size={16} className="text-text-secondary" />
                  <span className="text-sm text-text-primary">Help</span>
                </button>
                <hr className="my-2 border-border" />
                <button
                  onClick={handleSignOut}
                  className="w-full flex items-center space-x-2 p-2 rounded-lg hover:bg-surface-hover transition-colors text-left text-error"
                >
                  <LogOut size={16} />
                  <span className="text-sm">Sign Out</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Click outside handler for dropdowns */}
      {(isProfileMenuOpen || isNotificationsOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsProfileMenuOpen(false);
            setIsNotificationsOpen(false);
          }}
        />
      )}
    </header>
  );
}