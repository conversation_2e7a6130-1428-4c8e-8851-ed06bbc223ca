'use client';

import React from 'react';
import { Loader2, RefreshCw } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'accent' | 'minimal';
  message?: string;
  className?: string;
  showIcon?: boolean;
}

export default function LoadingSpinner({
  size = 'md',
  variant = 'default',
  message,
  className = '',
  showIcon = true,
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const variantClasses = {
    default: 'text-text-secondary',
    primary: 'text-primary',
    accent: 'text-accent',
    minimal: 'text-text-quaternary',
  };

  const messageClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  };

  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      {showIcon && (
        <div className={`animate-spin ${sizeClasses[size]} ${variantClasses[variant]}`}>
          <Loader2 />
        </div>
      )}
      {message && (
        <p className={`text-text-tertiary ${messageClasses[size]} text-center`}>
          {message}
        </p>
      )}
    </div>
  );
}

// Alternative spinning icon component
export function RefreshSpinner({
  size = 'md',
  className = '',
}: {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6',
  };

  return (
    <RefreshCw className={`animate-spin ${sizeClasses[size]} ${className}`} />
  );
}

// Skeleton loading component
export function SkeletonLoader({
  className = '',
  variant = 'default',
}: {
  className?: string;
  variant?: 'default' | 'text' | 'circular';
}) {
  const baseClasses = 'animate-pulse bg-surface-secondary';
  
  const variantClasses = {
    default: 'rounded-lg',
    text: 'rounded h-4',
    circular: 'rounded-full',
  };

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} />
  );
}

// Shimmer effect component
export function ShimmerEffect({
  className = '',
  children,
}: {
  className?: string;
  children?: React.ReactNode;
}) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      {children}
      <div className="absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/10 to-transparent" />
    </div>
  );
}

// Dots loading indicator
export function DotsLoader({
  size = 'md',
  color = 'primary',
  className = '',
}: {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'accent' | 'secondary';
  className?: string;
}) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4',
  };

  const colorClasses = {
    primary: 'bg-primary',
    accent: 'bg-accent',
    secondary: 'bg-secondary',
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <div
        className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-bounce`}
        style={{ animationDelay: '0ms' }}
      />
      <div
        className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-bounce`}
        style={{ animationDelay: '150ms' }}
      />
      <div
        className={`${sizeClasses[size]} ${colorClasses[color]} rounded-full animate-bounce`}
        style={{ animationDelay: '300ms' }}
      />
    </div>
  );
}

// Progress bar component
export function ProgressBar({
  progress,
  className = '',
  variant = 'primary',
  size = 'md',
  showPercentage = false,
}: {
  progress: number;
  className?: string;
  variant?: 'primary' | 'accent' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  showPercentage?: boolean;
}) {
  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3',
  };

  const variantClasses = {
    primary: 'bg-primary',
    accent: 'bg-accent',
    secondary: 'bg-secondary',
  };

  const clampedProgress = Math.min(Math.max(progress, 0), 100);

  return (
    <div className={`w-full ${className}`}>
      <div className={`w-full bg-surface-secondary rounded-full ${sizeClasses[size]}`}>
        <div
          className={`${sizeClasses[size]} ${variantClasses[variant]} rounded-full transition-all duration-300 ease-out`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
      {showPercentage && (
        <div className="mt-2 text-xs text-text-tertiary text-right">
          {Math.round(clampedProgress)}%
        </div>
      )}
    </div>
  );
}

// Circular progress component
export function CircularProgress({
  progress,
  size = 'md',
  variant = 'primary',
  className = '',
  showPercentage = false,
}: {
  progress: number;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'primary' | 'accent' | 'secondary';
  className?: string;
  showPercentage?: boolean;
}) {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
  };

  const strokeClasses = {
    sm: 2,
    md: 3,
    lg: 4,
    xl: 6,
  };

  const colorClasses = {
    primary: 'text-primary',
    accent: 'text-accent',
    secondary: 'text-secondary',
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
    xl: 'text-lg',
  };

  const clampedProgress = Math.min(Math.max(progress, 0), 100);
  const circumference = 2 * Math.PI * 45; // radius of 45
  const strokeDashoffset = circumference - (clampedProgress / 100) * circumference;

  return (
    <div className={`relative inline-flex items-center justify-center ${sizeClasses[size]} ${className}`}>
      <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke="currentColor"
          strokeWidth={strokeClasses[size]}
          fill="transparent"
          className="text-surface-secondary"
        />
        <circle
          cx="50"
          cy="50"
          r="45"
          stroke="currentColor"
          strokeWidth={strokeClasses[size]}
          fill="transparent"
          strokeDasharray={circumference}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className={`transition-all duration-300 ease-out ${colorClasses[variant]}`}
        />
      </svg>
      {showPercentage && (
        <span className={`absolute inset-0 flex items-center justify-center font-bold text-text-primary ${textSizeClasses[size]}`}>
          {Math.round(clampedProgress)}%
        </span>
      )}
    </div>
  );
}