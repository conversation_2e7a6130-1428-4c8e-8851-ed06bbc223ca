'use client';

import React from 'react';
import { Alert<PERSON>riangle, RefreshCw, Bug, Home, Mail } from 'lucide-react';

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: React.ErrorInfo | null;
}

interface ErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ComponentType<ErrorFallbackProps>;
  onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorFallbackProps {
  error: Error;
  errorInfo: React.ErrorInfo;
  resetError: () => void;
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log error to external service
    console.error('Error caught by boundary:', error, errorInfo);
    
    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  resetError = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback || DefaultErrorFallback;
      return (
        <FallbackComponent
          error={this.state.error!}
          errorInfo={this.state.errorInfo!}
          resetError={this.resetError}
        />
      );
    }

    return this.props.children;
  }
}

// Default error fallback component
function DefaultErrorFallback({ error, errorInfo, resetError }: ErrorFallbackProps) {
  const [showDetails, setShowDetails] = React.useState(false);
  const [isReporting, setIsReporting] = React.useState(false);

  const handleReportError = async () => {
    setIsReporting(true);
    try {
      // Simulate error reporting
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Error reported:', { error, errorInfo });
    } catch (reportError) {
      console.error('Failed to report error:', reportError);
    } finally {
      setIsReporting(false);
    }
  };

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-surface border border-border rounded-lg p-6 text-center">
        <div className="w-16 h-16 bg-error/10 rounded-full flex items-center justify-center mx-auto mb-4">
          <AlertTriangle size={32} className="text-error" />
        </div>
        
        <h1 className="text-xl font-bold text-text-primary mb-2">
          Something went wrong
        </h1>
        
        <p className="text-text-tertiary mb-6">
          We're sorry, but something unexpected happened. Please try refreshing the page or contact support if the problem persists.
        </p>

        <div className="space-y-3 mb-6">
          <button
            onClick={resetError}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            <RefreshCw size={16} />
            <span>Try Again</span>
          </button>
          
          <button
            onClick={handleReload}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-surface-secondary hover:bg-surface-hover text-text-primary border border-border rounded-lg transition-colors"
          >
            <RefreshCw size={16} />
            <span>Reload Page</span>
          </button>
          
          <button
            onClick={handleGoHome}
            className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-surface-secondary hover:bg-surface-hover text-text-primary border border-border rounded-lg transition-colors"
          >
            <Home size={16} />
            <span>Go to Dashboard</span>
          </button>
        </div>

        <div className="border-t border-border pt-4">
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="flex items-center space-x-2 text-text-tertiary hover:text-text-primary transition-colors text-sm mx-auto"
          >
            <Bug size={14} />
            <span>{showDetails ? 'Hide' : 'Show'} Error Details</span>
          </button>
          
          {showDetails && (
            <div className="mt-4 p-3 bg-surface-secondary rounded-lg text-left">
              <div className="mb-2">
                <h3 className="text-sm font-medium text-text-primary">Error:</h3>
                <p className="text-xs text-text-tertiary font-mono break-all">
                  {error.message}
                </p>
              </div>
              
              <div className="mb-2">
                <h3 className="text-sm font-medium text-text-primary">Stack Trace:</h3>
                <pre className="text-xs text-text-tertiary font-mono whitespace-pre-wrap break-all max-h-32 overflow-y-auto">
                  {error.stack}
                </pre>
              </div>
              
              {errorInfo && (
                <div>
                  <h3 className="text-sm font-medium text-text-primary">Component Stack:</h3>
                  <pre className="text-xs text-text-tertiary font-mono whitespace-pre-wrap break-all max-h-32 overflow-y-auto">
                    {errorInfo.componentStack}
                  </pre>
                </div>
              )}
            </div>
          )}
          
          <button
            onClick={handleReportError}
            disabled={isReporting}
            className="mt-4 flex items-center space-x-2 text-text-tertiary hover:text-text-primary disabled:opacity-50 transition-colors text-sm mx-auto"
          >
            <Mail size={14} />
            <span>{isReporting ? 'Reporting...' : 'Report This Error'}</span>
          </button>
        </div>
      </div>
    </div>
  );
}

// Simple error fallback for smaller components
export function SimpleErrorFallback({ error, resetError }: ErrorFallbackProps) {
  return (
    <div className="p-4 bg-error/10 border border-error/20 rounded-lg">
      <div className="flex items-center space-x-2 mb-2">
        <AlertTriangle size={16} className="text-error" />
        <h3 className="text-sm font-medium text-text-primary">Error occurred</h3>
      </div>
      <p className="text-xs text-text-tertiary mb-3">
        {error.message}
      </p>
      <button
        onClick={resetError}
        className="text-xs text-error hover:text-error/80 transition-colors"
      >
        Try again
      </button>
    </div>
  );
}

// Custom hook for error handling
export function useErrorHandler() {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const handleError = React.useCallback((error: Error) => {
    setError(error);
    console.error('Error handled:', error);
  }, []);

  return {
    error,
    resetError,
    handleError,
  };
}

// HOC for wrapping components with error boundary
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ComponentType<ErrorFallbackProps>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
}

export default ErrorBoundary;