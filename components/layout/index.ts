/**
 * Layout Components Index
 * 
 * Centralized exports for all layout-related components
 */

// Core layout components
export { default as Header } from './Header';
export { default as Sidebar } from './Sidebar';
export { default as TabNavigation } from './TabNavigation';

// Utility components
export { default as LoadingSpinner } from './LoadingSpinner';
export { 
  Refresh<PERSON>pinner, 
  SkeletonLoader, 
  ShimmerEffect, 
  DotsLoader, 
  ProgressBar, 
  CircularProgress 
} from './LoadingSpinner';

export { default as ErrorBoundary } from './ErrorBoundary';
export { 
  SimpleErrorFallback, 
  useErrorHandler, 
  withErrorBoundary 
} from './ErrorBoundary';

// Tab content components
export { default as Dashboard } from './tabs/Dashboard';
export { default as BidStudio } from './tabs/BidStudio';
export { default as Files } from './tabs/Files';
export { default as SearchTab } from './tabs/SearchTab';
export { default as Agents } from './tabs/Agents';
export { default as Workflow } from './tabs/Workflow';

// Type definitions for layout components
export interface LayoutProps {
  className?: string;
  children?: React.ReactNode;
}

export interface TabContentProps extends LayoutProps {
  isActive?: boolean;
}

export interface NavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export interface ResponsiveProps {
  isMobile?: boolean;
  isTablet?: boolean;
  isDesktop?: boolean;
}