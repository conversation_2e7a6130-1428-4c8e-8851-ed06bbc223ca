'use client';

import React from 'react';
import {
  BarChart3,
  Edit3,
  FileText,
  Search,
  Users,
  GitBranch,
  Settings,
  HelpCircle,
  Plus,
  Clock,
  Star,
  AlertCircle,
  Zap,
} from 'lucide-react';

interface TabNavigationProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
}

interface Tab {
  id: string;
  label: string;
  shortLabel?: string;
  icon: React.ReactNode;
  badge?: number;
  color?: string;
  description?: string;
  isDisabled?: boolean;
}

export default function TabNavigation({ 
  activeTab, 
  onTabChange, 
  className = '' 
}: TabNavigationProps) {
  const tabs: Tab[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      shortLabel: 'Dashboard',
      icon: <BarChart3 size={18} />,
      badge: 3,
      color: 'primary',
      description: 'Overview of all tenders and progress',
    },
    {
      id: 'bid-studio',
      label: 'Bid Studio',
      shortLabel: 'Studio',
      icon: <Edit3 size={18} />,
      badge: 2,
      color: 'accent',
      description: 'AI-powered bid writing workspace',
    },
    {
      id: 'files',
      label: 'Files',
      shortLabel: 'Files',
      icon: <FileText size={18} />,
      badge: 5,
      color: 'secondary',
      description: 'Document management and storage',
    },
    {
      id: 'search',
      label: 'Search',
      shortLabel: 'Search',
      icon: <Search size={18} />,
      description: 'Advanced search across all content',
    },
    {
      id: 'agents',
      label: 'AI Agents',
      shortLabel: 'Agents',
      icon: <Users size={18} />,
      badge: 1,
      color: 'info',
      description: 'Manage AI writing agents',
    },
    {
      id: 'workflow',
      label: 'Workflow',
      shortLabel: 'Workflow',
      icon: <GitBranch size={18} />,
      description: 'Process automation and management',
    },
    {
      id: 'zero-touch',
      label: 'Zero-Touch',
      shortLabel: 'Zero-Touch',
      icon: <Zap size={18} />,
      badge: 0,
      color: 'warning',
      description: 'Automated tender processing system',
    },
  ];

  const utilityTabs: Tab[] = [
    {
      id: 'settings',
      label: 'Settings',
      shortLabel: 'Settings',
      icon: <Settings size={18} />,
      description: 'System configuration and preferences',
    },
    {
      id: 'help',
      label: 'Help & Support',
      shortLabel: 'Help',
      icon: <HelpCircle size={18} />,
      description: 'Documentation and support',
    },
  ];

  const getTabColorClasses = (tab: Tab, isActive: boolean) => {
    if (isActive) {
      switch (tab.color) {
        case 'primary':
          return 'bg-primary text-white border-primary';
        case 'accent':
          return 'bg-accent text-white border-accent';
        case 'secondary':
          return 'bg-secondary text-white border-secondary';
        case 'info':
          return 'bg-info text-white border-info';
        case 'warning':
          return 'bg-warning text-white border-warning';
        case 'error':
          return 'bg-error text-white border-error';
        default:
          return 'bg-surface-active text-text-primary border-border-focus';
      }
    }
    return 'bg-surface border-border hover:bg-surface-hover hover:border-border-secondary text-text-secondary hover:text-text-primary';
  };

  const getBadgeColorClasses = (tab: Tab, isActive: boolean) => {
    if (isActive) {
      switch (tab.color) {
        case 'primary':
          return 'bg-primary-800 text-white';
        case 'accent':
          return 'bg-accent-800 text-white';
        case 'secondary':
          return 'bg-secondary-800 text-white';
        case 'info':
          return 'bg-info/20 text-white';
        default:
          return 'bg-surface-tertiary text-white';
      }
    }
    return 'bg-surface-tertiary text-text-tertiary';
  };

  return (
    <div className={`bg-background-secondary border-b border-border ${className}`}>
      <div className="px-4 lg:px-6">
        {/* Main Tabs */}
        <div className="flex space-x-2 overflow-x-auto scrollbar-hide">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                disabled={tab.isDisabled}
                className={`flex-shrink-0 flex items-center space-x-2 px-4 py-3 border-b-2 transition-all duration-200 group ${
                  getTabColorClasses(tab, isActive)
                } ${
                  tab.isDisabled 
                    ? 'opacity-50 cursor-not-allowed' 
                    : 'cursor-pointer'
                }`}
                title={tab.description}
              >
                <div className={`transition-colors ${
                  isActive 
                    ? 'text-current' 
                    : 'text-text-quaternary group-hover:text-text-secondary'
                }`}>
                  {tab.icon}
                </div>
                <span className="text-sm font-medium whitespace-nowrap hidden sm:block">
                  {tab.label}
                </span>
                <span className="text-sm font-medium whitespace-nowrap sm:hidden">
                  {tab.shortLabel || tab.label}
                </span>
                {tab.badge !== undefined && tab.badge > 0 && (
                  <span className={`px-2 py-1 text-xs font-bold rounded-full transition-colors ${
                    getBadgeColorClasses(tab, isActive)
                  }`}>
                    {tab.badge > 99 ? '99+' : tab.badge}
                  </span>
                )}
                {tab.isDisabled && (
                  <AlertCircle size={14} className="text-warning" />
                )}
              </button>
            );
          })}
        </div>

        {/* Utility Tabs - Only show on larger screens */}
        <div className="hidden lg:flex justify-end items-center space-x-2 pt-2 pb-2">
          {utilityTabs.map((tab) => {
            const isActive = activeTab === tab.id;
            return (
              <button
                key={tab.id}
                onClick={() => onTabChange(tab.id)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-surface-active text-text-primary'
                    : 'text-text-tertiary hover:bg-surface-hover hover:text-text-primary'
                }`}
                title={tab.description}
              >
                <div className="text-current">
                  {tab.icon}
                </div>
                <span className="text-xs font-medium">
                  {tab.shortLabel || tab.label}
                </span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Status Bar */}
      <div className="px-4 lg:px-6 py-2 bg-surface/50 border-t border-border">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            {/* Active Tab Indicator */}
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
              <span className="text-xs text-text-tertiary">
                {tabs.find(t => t.id === activeTab)?.label || 'Unknown Tab'}
              </span>
            </div>

            {/* Quick Stats */}
            <div className="hidden md:flex items-center space-x-4 text-xs text-text-quaternary">
              <div className="flex items-center space-x-1">
                <Clock size={12} />
                <span>Last updated: 2 mins ago</span>
              </div>
              <div className="flex items-center space-x-1">
                <Star size={12} />
                <span>3 favorites</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="flex items-center space-x-2">
            <button
              onClick={() => onTabChange('new-tender')}
              className="flex items-center space-x-1 px-3 py-1 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors text-xs"
            >
              <Plus size={12} />
              <span className="hidden sm:inline">New Tender</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}