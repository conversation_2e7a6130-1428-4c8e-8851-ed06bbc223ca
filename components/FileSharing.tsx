"use client";

import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';
import { 
  Share2,
  Copy,
  Link,
  QrCode,
  Mail,
  Users,
  UserPlus,
  Globe,
  Lock,
  Unlock,
  Eye,
  Edit3,
  Download,
  X,
  Check,
  Calendar,
  Hash,
  Shield,
  Key,
  Clock,
  AlertTriangle,
  Info,
  Settings,
  MoreVertical,
  ExternalLink,
  Plus,
  Minus,
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Trash2,
  RefreshCw,
  Star,
  StarOff,
  Bookmark,
  Flag,
  Bell,
  BellOff,
  MessageSquare,
  Activity,
  BarChart3,
  TrendingUp,
  Database,
  Cloud,
  CloudOff,
  Wifi,
  WifiOff,
  Smartphone,
  Tablet,
  Laptop,
  Monitor,
  Server,
  HardDrive,
  SdCard,
  Usb,
  Bluetooth,
  BluetoothOff,
  Battery,
  BatteryLow,
  Power,
  PowerOff,
  Cpu,
  Memory,
  Router,
  Modem,
  Antenna,
  Satellite,
  Radio,
  Television,
  Camera,
  CameraOff,
  Mic,
  Mic<PERSON>ff,
  Speaker,
  Headphones,
  Microphone,
  Webcam,
  Printer,
  Scanner,
  Fax,
  Phone,
  Building,
  MapPin,
  Briefcase,
  DollarSign,
  Percent,
  Type,
  AlignLeft,
  Image as ImageIcon,
  Video,
  Music,
  Archive,
  File,
  FileText,
  FileImage,
  FileVideo,
  FileAudio,
  FileSpreadsheet,
  FileCode,
  Folder,
  FolderOpen,
  FolderPlus,
  Tag,
  Layers,
  Grid,
  List,
  ChevronDown,
  ChevronRight,
  ChevronLeft,
  ChevronUp,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  CornerDownRight,
  CornerUpLeft,
  Move,
  RotateCw,
  RotateCcw,
  FlipHorizontal,
  FlipVertical,
  ZoomIn,
  ZoomOut,
  Maximize2,
  Minimize2,
  Fullscreen,
  Minimize,
  Square,
  Circle,
  Triangle,
  Hexagon,
  Octagon,
  Pentagon,
  Diamond,
  Heart,
  Star as StarIcon,
  Crown,
  Award,
  Trophy,
  Medal,
  Gift,
  Cake,
  Pizza,
  Coffee,
  Wine,
  Beer,
  Martini,
  IceCream,
  Candy,
  Apple,
  Banana,
  Orange,
  Grape,
  Cherry,
  Strawberry,
  Lemon,
  Lime,
  Coconut,
  Carrot,
  Corn
} from 'lucide-react';
import { Id } from '../convex/_generated/dataModel';
import { FileUpload, FileShare, FilePermission } from '../types/file';

interface FileSharingProps {
  file: FileUpload;
  onClose: () => void;
  className?: string;
}

interface ShareSettingsProps {
  share: FileShare;
  onUpdate: (settings: Partial<FileShare>) => void;
  onDelete: () => void;
}

interface UserPermissionsProps {
  fileId: Id<'files'>;
  permissions: FilePermission[];
  onUpdate: (permissions: FilePermission[]) => void;
}

const PERMISSION_LEVELS = [
  { value: 'read', label: 'View Only', description: 'Can view and download the file' },
  { value: 'write', label: 'Edit', description: 'Can view, download, and edit the file' },
  { value: 'delete', label: 'Full Access', description: 'Can view, edit, delete, and manage permissions' },
  { value: 'admin', label: 'Admin', description: 'Full access including sharing and administration' },
];

const EXPIRY_OPTIONS = [
  { value: '', label: 'Never expires' },
  { value: '1hour', label: '1 hour' },
  { value: '1day', label: '1 day' },
  { value: '1week', label: '1 week' },
  { value: '1month', label: '1 month' },
  { value: '3months', label: '3 months' },
  { value: '6months', label: '6 months' },
  { value: '1year', label: '1 year' },
  { value: 'custom', label: 'Custom date' },
];

export default function FileSharing({
  file,
  onClose,
  className = '',
}: FileSharingProps) {
  const [activeTab, setActiveTab] = useState<'share' | 'permissions' | 'analytics'>('share');
  const [shareSettings, setShareSettings] = useState({
    isPublic: false,
    requiresAuth: true,
    password: '',
    expiresAt: '',
    maxDownloads: '',
    permissions: ['read'] as string[],
    allowedDomains: [] as string[],
    ipWhitelist: [] as string[],
  });
  const [customExpiry, setCustomExpiry] = useState('');
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserPermission, setNewUserPermission] = useState('read');
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [generatedLink, setGeneratedLink] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');

  // Convex hooks
  const shares = useQuery(api.files.getFileShares, { fileId: file._id });
  const permissions = useQuery(api.files.getFilePermissions, { fileId: file._id });
  const analytics = useQuery(api.files.getFileAnalytics, { fileId: file._id });
  
  const createShare = useMutation(api.files.createFileShare);
  const updateShare = useMutation(api.files.updateFileShare);
  const deleteShare = useMutation(api.files.deleteFileShare);
  const addPermission = useMutation(api.files.addFilePermission);
  const updatePermission = useMutation(api.files.updateFilePermission);
  const removePermission = useMutation(api.files.removeFilePermission);

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const calculateExpiryTimestamp = (option: string): number | undefined => {
    if (!option || option === '') return undefined;
    
    const now = Date.now();
    const hour = 60 * 60 * 1000;
    const day = 24 * hour;
    const week = 7 * day;
    const month = 30 * day;
    const year = 365 * day;

    switch (option) {
      case '1hour':
        return now + hour;
      case '1day':
        return now + day;
      case '1week':
        return now + week;
      case '1month':
        return now + month;
      case '3months':
        return now + 3 * month;
      case '6months':
        return now + 6 * month;
      case '1year':
        return now + year;
      case 'custom':
        return customExpiry ? new Date(customExpiry).getTime() : undefined;
      default:
        return undefined;
    }
  };

  const generateShareLink = async () => {
    try {
      const expiresAt = calculateExpiryTimestamp(shareSettings.expiresAt);
      const maxDownloads = shareSettings.maxDownloads ? parseInt(shareSettings.maxDownloads) : undefined;

      const shareId = await createShare({
        fileId: file._id,
        isPublic: shareSettings.isPublic,
        requiresAuth: shareSettings.requiresAuth,
        password: shareSettings.password || undefined,
        permissions: shareSettings.permissions,
        expiresAt,
        maxDownloads,
        allowedDomains: shareSettings.allowedDomains.length > 0 ? shareSettings.allowedDomains : undefined,
        ipWhitelist: shareSettings.ipWhitelist.length > 0 ? shareSettings.ipWhitelist : undefined,
      });

      const baseUrl = window.location.origin;
      const link = `${baseUrl}/shared/${shareId}`;
      setGeneratedLink(link);

      // Generate QR code (you would integrate with a QR code service)
      const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(link)}`;
      setQrCodeUrl(qrUrl);

      toast.success('Share link generated successfully!');
    } catch (error) {
      console.error('Error creating share:', error);
      toast.error('Failed to generate share link');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Copied to clipboard!');
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      toast.error('Failed to copy to clipboard');
    }
  };

  const sendEmailInvite = async () => {
    if (!newUserEmail) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      await addPermission({
        fileId: file._id,
        userId: newUserEmail, // In a real app, this would be resolved to a user ID
        permission: newUserPermission,
        expiresAt: calculateExpiryTimestamp(shareSettings.expiresAt),
      });

      // Send email notification (integrate with email service)
      toast.success(`Invitation sent to ${newUserEmail}`);
      setNewUserEmail('');
    } catch (error) {
      console.error('Error sending invite:', error);
      toast.error('Failed to send invitation');
    }
  };

  const renderShareTab = () => (
    <div className="p-6 space-y-6">
      {/* Quick Share Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={() => {
            setShareSettings({ 
              ...shareSettings, 
              isPublic: true, 
              requiresAuth: false,
              permissions: ['read'] 
            });
            generateShareLink();
          }}
          className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
        >
          <Globe className="w-8 h-8 mx-auto text-gray-400 mb-2" />
          <h3 className="font-medium text-gray-900">Public Link</h3>
          <p className="text-sm text-gray-500">Anyone with the link can view</p>
        </button>

        <button
          onClick={() => {
            setShareSettings({ 
              ...shareSettings, 
              isPublic: false, 
              requiresAuth: true,
              permissions: ['read'] 
            });
            generateShareLink();
          }}
          className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors"
        >
          <Lock className="w-8 h-8 mx-auto text-gray-400 mb-2" />
          <h3 className="font-medium text-gray-900">Secure Link</h3>
          <p className="text-sm text-gray-500">Requires authentication</p>
        </button>

        <button
          onClick={() => {
            setShareSettings({ 
              ...shareSettings, 
              password: Math.random().toString(36).substring(2, 8),
              permissions: ['read'] 
            });
            generateShareLink();
          }}
          className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-yellow-500 hover:bg-yellow-50 transition-colors"
        >
          <Key className="w-8 h-8 mx-auto text-gray-400 mb-2" />
          <h3 className="font-medium text-gray-900">Password Protected</h3>
          <p className="text-sm text-gray-500">Requires password to access</p>
        </button>
      </div>

      {/* Custom Share Settings */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900">Custom Share Settings</h3>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-700"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Access Level
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="access"
                  value="public"
                  checked={shareSettings.isPublic}
                  onChange={(e) => setShareSettings({ ...shareSettings, isPublic: e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">Public (anyone with link)</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="access"
                  value="private"
                  checked={!shareSettings.isPublic}
                  onChange={(e) => setShareSettings({ ...shareSettings, isPublic: !e.target.checked })}
                  className="mr-2"
                />
                <span className="text-sm">Private (invited users only)</span>
              </label>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Permissions
            </label>
            <div className="space-y-2">
              {['read', 'write', 'download', 'comment'].map((permission) => (
                <label key={permission} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={shareSettings.permissions.includes(permission)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setShareSettings({
                          ...shareSettings,
                          permissions: [...shareSettings.permissions, permission]
                        });
                      } else {
                        setShareSettings({
                          ...shareSettings,
                          permissions: shareSettings.permissions.filter(p => p !== permission)
                        });
                      }
                    }}
                    className="mr-2"
                  />
                  <span className="text-sm capitalize">{permission}</span>
                </label>
              ))}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expiry
            </label>
            <select
              value={shareSettings.expiresAt}
              onChange={(e) => setShareSettings({ ...shareSettings, expiresAt: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {EXPIRY_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {shareSettings.expiresAt === 'custom' && (
              <input
                type="datetime-local"
                value={customExpiry}
                onChange={(e) => setCustomExpiry(e.target.value)}
                className="w-full mt-2 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Password (optional)
            </label>
            <input
              type="text"
              value={shareSettings.password}
              onChange={(e) => setShareSettings({ ...shareSettings, password: e.target.value })}
              placeholder="Enter password"
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {showAdvanced && (
          <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Max Downloads (optional)
              </label>
              <input
                type="number"
                value={shareSettings.maxDownloads}
                onChange={(e) => setShareSettings({ ...shareSettings, maxDownloads: e.target.value })}
                placeholder="Unlimited"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Allowed Domains (one per line)
              </label>
              <textarea
                value={shareSettings.allowedDomains.join('\n')}
                onChange={(e) => setShareSettings({ 
                  ...shareSettings, 
                  allowedDomains: e.target.value.split('\n').filter(d => d.trim()) 
                })}
                placeholder="example.com"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                IP Whitelist (one per line)
              </label>
              <textarea
                value={shareSettings.ipWhitelist.join('\n')}
                onChange={(e) => setShareSettings({ 
                  ...shareSettings, 
                  ipWhitelist: e.target.value.split('\n').filter(ip => ip.trim()) 
                })}
                placeholder="***********/24"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="requiresAuth"
                checked={shareSettings.requiresAuth}
                onChange={(e) => setShareSettings({ ...shareSettings, requiresAuth: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="requiresAuth" className="text-sm text-gray-700">
                Require authentication for access
              </label>
            </div>
          </div>
        )}

        <button
          onClick={generateShareLink}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          Generate Share Link
        </button>
      </div>

      {/* Generated Link */}
      {generatedLink && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center justify-between mb-3">
            <h4 className="font-medium text-green-800">Share Link Generated</h4>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => copyToClipboard(generatedLink)}
                className="p-2 text-green-600 hover:text-green-700"
              >
                <Copy className="w-4 h-4" />
              </button>
              <button
                onClick={() => window.open(`mailto:?subject=File%20Shared%3A%20${encodeURIComponent(file.name)}&body=${encodeURIComponent(generatedLink)}`, '_blank')}
                className="p-2 text-green-600 hover:text-green-700"
              >
                <Mail className="w-4 h-4" />
              </button>
            </div>
          </div>
          <div className="bg-white p-3 rounded border text-sm font-mono break-all">
            {generatedLink}
          </div>
          {qrCodeUrl && (
            <div className="mt-3 text-center">
              <img src={qrCodeUrl} alt="QR Code" className="mx-auto" />
              <p className="text-xs text-green-600 mt-2">Scan to access</p>
            </div>
          )}
        </div>
      )}

      {/* User Invitation */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium text-gray-900">Invite Users</h3>
        <div className="flex space-x-3">
          <input
            type="email"
            value={newUserEmail}
            onChange={(e) => setNewUserEmail(e.target.value)}
            placeholder="Enter email address"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          <select
            value={newUserPermission}
            onChange={(e) => setNewUserPermission(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            {PERMISSION_LEVELS.map((level) => (
              <option key={level.value} value={level.value}>
                {level.label}
              </option>
            ))}
          </select>
          <button
            onClick={sendEmailInvite}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <UserPlus className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Active Shares */}
      {shares && shares.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Active Shares</h3>
          <div className="space-y-3">
            {shares.map((share) => (
              <div key={share.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      {share.isPublic ? (
                        <Globe className="w-4 h-4 text-blue-500" />
                      ) : (
                        <Lock className="w-4 h-4 text-green-500" />
                      )}
                      <span className="text-sm font-medium">
                        {share.isPublic ? 'Public Link' : 'Private Share'}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded ${
                        share.isActive ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                      }`}>
                        {share.isActive ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    <div className="text-sm text-gray-600 space-y-1">
                      <p>Permissions: {share.permissions.join(', ')}</p>
                      <p>Downloads: {share.downloadCount}{share.maxDownloads ? ` / ${share.maxDownloads}` : ''}</p>
                      <p>Created: {formatDate(share.createdAt)}</p>
                      {share.expiresAt && (
                        <p>Expires: {formatDate(share.expiresAt)}</p>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => copyToClipboard(`${window.location.origin}/shared/${share.shareToken}`)}
                      className="p-2 text-gray-400 hover:text-blue-600"
                    >
                      <Copy className="w-4 h-4" />
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          await deleteShare({ shareId: share.id });
                          toast.success('Share deleted');
                        } catch (error) {
                          toast.error('Failed to delete share');
                        }
                      }}
                      className="p-2 text-gray-400 hover:text-red-600"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const renderPermissionsTab = () => (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">User Permissions</h3>
        <button
          onClick={() => setActiveTab('share')}
          className="text-sm text-blue-600 hover:text-blue-700"
        >
          Invite Users
        </button>
      </div>

      {permissions && permissions.length > 0 ? (
        <div className="space-y-3">
          {permissions.map((permission) => (
            <div key={permission.id} className="p-4 border rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                      <Users className="w-4 h-4 text-gray-400" />
                    </div>
                    <div>
                      <p className="text-sm font-medium">{permission.userId}</p>
                      <p className="text-xs text-gray-500">
                        {PERMISSION_LEVELS.find(p => p.value === permission.permission)?.label || permission.permission}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={permission.permission}
                    onChange={async (e) => {
                      try {
                        await updatePermission({
                          permissionId: permission.id,
                          permission: e.target.value,
                        });
                        toast.success('Permission updated');
                      } catch (error) {
                        toast.error('Failed to update permission');
                      }
                    }}
                    className="text-sm px-2 py-1 border border-gray-300 rounded"
                  >
                    {PERMISSION_LEVELS.map((level) => (
                      <option key={level.value} value={level.value}>
                        {level.label}
                      </option>
                    ))}
                  </select>
                  <button
                    onClick={async () => {
                      try {
                        await removePermission({ permissionId: permission.id });
                        toast.success('Permission removed');
                      } catch (error) {
                        toast.error('Failed to remove permission');
                      }
                    }}
                    className="p-2 text-gray-400 hover:text-red-600"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>
              <div className="mt-3 text-xs text-gray-500">
                <p>Granted: {formatDate(permission.grantedAt)}</p>
                <p>Granted by: {permission.grantedBy}</p>
                {permission.expiresAt && (
                  <p>Expires: {formatDate(permission.expiresAt)}</p>
                )}
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <Users className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No user permissions</h3>
          <p className="text-gray-500 mb-4">
            This file hasn't been shared with any specific users yet.
          </p>
          <button
            onClick={() => setActiveTab('share')}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Invite Users
          </button>
        </div>
      )}
    </div>
  );

  const renderAnalyticsTab = () => (
    <div className="p-6 space-y-6">
      <h3 className="text-lg font-medium text-gray-900">Sharing Analytics</h3>
      
      {analytics ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="p-4 bg-blue-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Eye className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-blue-700">Views</span>
            </div>
            <p className="text-2xl font-bold text-blue-900 mt-2">
              {analytics.totalViews || 0}
            </p>
          </div>

          <div className="p-4 bg-green-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Download className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-green-700">Downloads</span>
            </div>
            <p className="text-2xl font-bold text-green-900 mt-2">
              {analytics.totalDownloads || 0}
            </p>
          </div>

          <div className="p-4 bg-purple-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-purple-700">Unique Users</span>
            </div>
            <p className="text-2xl font-bold text-purple-900 mt-2">
              {analytics.uniqueUsers || 0}
            </p>
          </div>

          <div className="p-4 bg-yellow-50 rounded-lg">
            <div className="flex items-center space-x-2">
              <Share2 className="w-5 h-5 text-yellow-500" />
              <span className="text-sm font-medium text-yellow-700">Shares</span>
            </div>
            <p className="text-2xl font-bold text-yellow-900 mt-2">
              {shares?.length || 0}
            </p>
          </div>
        </div>
      ) : (
        <div className="text-center py-8">
          <BarChart3 className="w-12 h-12 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">Analytics data is being collected...</p>
        </div>
      )}

      {/* Recent Activity */}
      <div>
        <h4 className="text-sm font-medium text-gray-700 mb-4">Recent Activity</h4>
        <div className="space-y-3">
          {/* Placeholder activity items */}
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm text-gray-900">File viewed by anonymous user</p>
              <p className="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm text-gray-900">File <NAME_EMAIL></p>
              <p className="text-xs text-gray-500">1 day ago</p>
            </div>
          </div>
          <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
            <div className="flex-1">
              <p className="text-sm text-gray-900">Share link created</p>
              <p className="text-xs text-gray-500">2 days ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl h-5/6">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-3">
            <Share2 className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className="text-lg font-semibold">Share File</h2>
              <p className="text-sm text-gray-500">{file.name}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b">
          <button
            onClick={() => setActiveTab('share')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'share'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Share Settings
          </button>
          <button
            onClick={() => setActiveTab('permissions')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'permissions'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            User Permissions
            {permissions && permissions.length > 0 && (
              <span className="ml-2 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                {permissions.length}
              </span>
            )}
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`px-6 py-3 text-sm font-medium ${
              activeTab === 'analytics'
                ? 'border-b-2 border-blue-500 text-blue-600'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            Analytics
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-auto">
          {activeTab === 'share' && renderShareTab()}
          {activeTab === 'permissions' && renderPermissionsTab()}
          {activeTab === 'analytics' && renderAnalyticsTab()}
        </div>
      </div>
    </div>
  );
}