'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';
import { 
  MessageSquare, 
  Send, 
  Paperclip, 
  Smile, 
  MoreVertical, 
  Users, 
  Bot, 
  User,
  Search,
  Filter,
  Plus,
  Settings,
  Download,
  Share2,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Phone,
  PhoneOff,
  Star,
  Pin,
  Archive,
  Trash2,
  Edit3,
  Reply,
  Hash,
  At,
  Code,
  Image,
  FileText,
  Zap,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  BarChart3,
  Activity,
  Cpu,
  Brain,
  Target,
  MessageCircle,
  Sparkles,
  Lightbulb,
  BookOpen,
  FileCheck,
  PenTool,
  Layers,
  Rocket,
  Shield,
  Gauge,
  Workflow,
  Timer,
  Trophy,
  Award,
  Percent,
  TrendingDown,
  AlertTriangle,
  RefreshCw,
  Pause,
  Play,
  Square,
  RotateCcw,
  FastForward,
  Rewind,
  Volume2,
  VolumeX,
  Monitor,
  Smartphone,
  Tablet,
  Laptop,
  ChevronDown,
  ChevronUp,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  MoreHorizontal,
  Copy,
  ExternalLink,
  Maximize2,
  Minimize2,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Globe,
  Wifi,
  WifiOff,
  Signal,
  Battery,
  BatteryLow,
  Bluetooth,
  BluetoothOff,
  Headphones,
  Speaker,
  Calendar,
  MapPin,
  Link,
  Unlink,
  Palette,
  Brush,
  Eraser,
  Crop,
  Scissors,
  Combine,
  Split,
  Merge,
  Duplicate,
  Group,
  Ungroup,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Bold,
  Italic,
  Underline,
  Strikethrough,
  Subscript,
  Superscript,
  List,
  ListOrdered,
  Indent,
  Outdent,
  Quote,
  Minus,
  Equal,
  Divide,
  Multiply,
  Calculator,
  Ruler,
  Compass,
  Triangle,
  Circle,
  Square as SquareIcon,
  Hexagon,
  Octagon,
  Pentagon,
  Polygon,
  Star as StarIcon,
  Heart,
  Diamond,
  Spade,
  Club,
  Clover,
  Flower,
  Flower2,
  Leaf,
  TreePine,
  TreeDeciduous,
  Sun,
  Moon,
  Cloud,
  CloudRain,
  CloudSnow,
  CloudLightning,
  CloudDrizzle,
  CloudHail,
  Cloudy,
  PartlyCloudy,
  Wind,
  Tornado,
  Snowflake,
  Droplets,
  Waves,
  Flame,
  Zap as Lightning,
  Thermometer,
  Umbrella,
  Rainbow,
  Sunrise,
  Sunset,
  Mountain,
  MountainSnow,
  Volcano,
  Island,
  Beach,
  Desert,
  Forest,
  Cactus,
  Flower as FlowerIcon,
  Mushroom,
  Shell,
  Fish,
  Rabbit,
  Turtle,
  Bird,
  Butterfly,
  Bug,
  Ant,
  Bee,
  Ladybug,
  Spider,
  Snail,
  Worm,
  Cat,
  Dog,
  Bone,
  PawPrint,
  Footprints,
  Baby,
  Child,
  Adult,
  PersonStanding,
  Users2,
  UserPlus,
  UserMinus,
  UserCheck,
  UserX,
  UserSearch,
  UserCog,
  Crown,
  Gem,
  Coins,
  Banknote,
  CreditCard,
  Wallet,
  PiggyBank,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  ArrowUpLeft,
  ArrowUpRight,
  ArrowDownLeft,
  ArrowDownRight,
  CornerUpLeft,
  CornerUpRight,
  CornerDownLeft,
  CornerDownRight,
  Navigation,
  Navigation2,
  Compass as CompassIcon,
  Map,
  MapPin as MapPinIcon,
  Route,
  Car,
  Truck,
  Bus,
  Train,
  Plane,
  Ship,
  Bike,
  Scooter,
  Motorcycle,
  Taxi,
  Fuel,
  Wrench,
  Hammer,
  Screwdriver,
  Drill,
  Saw,
  Axe,
  Pickaxe,
  Shovel,
  Rake,
  Scissors as ScissorsIcon,
  Knife,
  Fork,
  Spoon,
  ChefHat,
  Utensils,
  Soup,
  Pizza,
  Sandwich,
  Salad,
  Cake,
  Cookie,
  Donut,
  IceCream,
  Coffee,
  Tea,
  Wine,
  Beer,
  Cocktail,
  Milk,
  Egg,
  Bread,
  Croissant,
  Bagel,
  Pretzel,
  Cheese,
  Meat,
  Poultry,
  Seafood,
  Vegetable,
  Fruit,
  Apple,
  Banana,
  Orange,
  Lemon,
  Lime,
  Grape,
  Strawberry,
  Cherry,
  Peach,
  Pear,
  Pineapple,
  Coconut,
  Avocado,
  Tomato,
  Cucumber,
  Carrot,
  Pepper,
  Onion,
  Garlic,
  Potato,
  Corn,
  Broccoli,
  Cabbage,
  Lettuce,
  Spinach,
  Kale,
  Celery,
  Radish,
  Beet,
  Turnip,
  Parsnip,
  Asparagus,
  Artichoke,
  Mushroom as MushroomIcon,
  Herb,
  Spice,
  Salt,
  Sugar,
  Honey,
  Oil,
  Vinegar,
  Sauce,
  Ketchup,
  Mustard,
  Mayo,
  Relish,
  Pickle,
  Olive,
  Nut,
  Seed,
  Grain,
  Rice,
  Pasta,
  Noodle,
  Cereal,
  Oatmeal,
  Pancake,
  Waffle,
  Toast,
  Muffin,
  Scone,
  Biscuit,
  Cracker,
  Chip,
  Pretzel as PretzelIcon,
  Popcorn,
  Candy,
  Chocolate,
  Gum,
  Lollipop,
  Jellybean,
  Marshmallow,
  Taffy,
  Caramel,
  Fudge,
  Brownie,
  Cupcake,
  Pie,
  Tart,
  Pudding,
  Custard,
  Jello,
  Yogurt,
  Smoothie,
  Juice,
  Soda,
  Water,
  Bottle,
  Glass,
  Mug,
  Cup,
  Plate,
  Bowl,
  Pot,
  Pan,
  Kettle,
  Toaster,
  Microwave,
  Oven,
  Stove,
  Grill,
  Fridge,
  Freezer,
  Blender,
  Mixer,
  Processor,
  Grinder,
  Juicer,
  Scale,
  Timer as TimerIcon,
  Alarm,
  Bell,
  Chime,
  Whistle,
  Horn,
  Siren,
  Megaphone,
  Microphone,
  Speaker as SpeakerIcon,
  Headphones as HeadphonesIcon,
  Earbuds,
  Radio,
  Podcast,
  Music,
  Note,
  Chord,
  Clef,
  Flat,
  Sharp,
  Natural,
  Rest,
  Metronome,
  Tuner,
  Piano,
  Keyboard,
  Guitar,
  Bass,
  Violin,
  Cello,
  Viola,
  Harp,
  Flute,
  Clarinet,
  Saxophone,
  Trumpet,
  Trombone,
  Drum,
  Cymbals,
  Xylophone,
  Tambourine,
  Maracas,
  Bongo,
  Conga,
  Djembe,
  Timpani,
  Snare,
  Kick,
  HiHat,
  Crash,
  Ride,
  Splash,
  China,
  Cowbell,
  Woodblock,
  Claves,
  Shaker,
  Guiro,
  Cabasa,
  Vibraslap,
  Windchimes,
  Rainstick,
  Kazoo,
  Harmonica,
  Accordion,
  Banjo,
  Mandolin,
  Ukulele,
  Sitar,
  Tabla,
  Bongos,
  Congas,
  Djembes,
  Timpani as TimpaniIcon,
  Glockenspiel,
  Vibraphone,
  Marimba,
  Hang,
  Steelpan,
  Theremin,
  Synthesizer,
  Sampler,
  Sequencer,
  Turntable,
  Mixer as MixerIcon,
  Amplifier,
  Effects,
  Distortion,
  Reverb,
  Delay,
  Chorus,
  Flanger,
  Phaser,
  Compressor,
  Limiter,
  Gate,
  EQ,
  Filter,
  Overdrive,
  Fuzz,
  Wah,
  Tremolo,
  Vibrato,
  Octave,
  Harmony,
  Pitch,
  Transpose,
  Quantize,
  Swing,
  Groove,
  Tempo,
  BPM,
  Sync,
  Loop,
  Record,
  Play as PlayIcon,
  Pause as PauseIcon,
  Stop,
  Rewind as RewindIcon,
  FastForward as FastForwardIcon,
  Previous,
  Next,
  Shuffle,
  Repeat,
  RepeatOne,
  Random,
  Crossfade,
  Fade,
  Gain,
  Volume,
  Mute,
  Solo,
  Cue,
  Monitor as MonitorIcon,
  Meter,
  Spectrum,
  Waveform,
  Oscilloscope,
  Frequency,
  Amplitude,
  Phase,
  Stereo,
  Mono,
  Left,
  Right,
  Center,
  Surround,
  Subwoofer,
  Tweeter,
  Woofer,
  Midrange,
  Crossover,
  Passive,
  Active,
  Powered,
  Unpowered,
  Bluetooth as BluetoothIcon,
  Wireless,
  Wired,
  Cable,
  Jack,
  Plug,
  Socket,
  Adapter,
  Converter,
  Splitter,
  Combiner,
  Attenuator,
  Booster,
  Preamp,
  Poweramp,
  Integrated,
  Receiver,
  Tuner as TunerIcon,
  Antenna,
  Signal as SignalIcon,
  Noise,
  Interference,
  Distortion as DistortionIcon,
  Clipping,
  Saturation,
  Overdrive as OverdriveIcon,
  Compression,
  Expansion,
  Gating,
  Limiting,
  Normalization,
  Equalization,
  Filtering,
  Shelving,
  Peaking,
  Notching,
  Highpass,
  Lowpass,
  Bandpass,
  Bandstop,
  Allpass,
  Comb,
  Flanger as FlangerIcon,
  Phaser as PhaserIcon,
  Chorus as ChorusIcon,
  Delay as DelayIcon,
  Echo,
  Reverb as ReverbIcon,
  Hall,
  Room,
  Chamber,
  Plate,
  Spring,
  Convolution,
  Algorithmic,
  Granular,
  Pitch as PitchIcon,
  Time,
  Stretch,
  Shift,
  Bend,
  Glide,
  Portamento,
  Legato,
  Staccato,
  Accent,
  Sforzando,
  Crescendo,
  Diminuendo,
  Forte,
  Piano as PianoIcon,
  Pianissimo,
  Fortissimo,
  Mezzo,
  Dolce,
  Espressivo,
  Cantabile,
  Marcato,
  Tenuto,
  Fermata,
  Ritardando,
  Accelerando,
  Rubato,
  Rallentando,
  Stringendo,
  Sostenuto,
  Pedal,
  Damper,
  Sustain,
  Soft,
  Sostenuto as SostenutoIcon,
  Una,
  Corda,
  Tre,
  Tutte,
  Mezza,
  Voce,
  Senza,
  Con,
  Sordino,
  Arco,
  Pizzicato,
  Tremolo as TremoloIcon,
  Trill,
  Mordent,
  Turn,
  Appoggiatura,
  Acciaccatura,
  Glissando,
  Slide,
  Hammer,
  Pull,
  Bend as BendIcon,
  Release,
  Vibrato as VibratoIcon,
  Shake,
  Tap,
  Slap,
  Pop,
  Thumb,
  Finger,
  Pick,
  Plectrum,
  Bow,
  Mallet,
  Stick,
  Brush,
  Rod,
  Beater,
  Hammer as HammerIcon,
  Felt,
  Leather,
  Rubber,
  Plastic,
  Metal,
  Wood,
  Ivory,
  Ebony,
  Rosewood,
  Maple,
  Mahogany,
  Cedar,
  Spruce,
  Pine,
  Oak,
  Walnut,
  Cherry,
  Birch,
  Ash,
  Poplar,
  Basswood,
  Alder,
  Limba,
  Koa,
  Zebra,
  Padauk,
  Purpleheart,
  Bloodwood,
  Cocobolo,
  Granadillo,
  Wenge,
  Bubinga,
  Ovangkol,
  Sapele,
  Utile,
  Sipo,
  Kosipo,
  Makore,
  Obeche,
  Okoume,
  Meranti,
  Lauan,
  Mahogany as MahoganyIcon,
  Teak,
  Iroko,
  Afzelia,
  Doussie,
  Merbau,
  Jarrah,
  Karri,
  Blackbutt,
  Spotted,
  Gum,
  Tasmanian,
  Oak as OakIcon,
  American,
  White,
  Red,
  Chestnut,
  Beech,
  Sycamore,
  Plane,
  Lime,
  Basswood as BasswoodIcon,
  Tulip,
  Poplar as PoplarIcon,
  Aspen,
  Cottonwood,
  Willow,
  Alder as AlderIcon,
  Birch as BirchIcon,
  Paper,
  Yellow,
  Sweet,
  River,
  Gray,
  Silver,
  European,
  Hornbeam,
  Ironwood,
  Dogwood,
  Boxwood,
  Holly,
  Yew,
  Juniper,
  Cypress,
  Redwood,
  Sequoia,
  Fir,
  Hemlock,
  Larch,
  Tamarack,
  Cedar as CedarIcon,
  Incense,
  Western,
  Eastern,
  Northern,
  Southern,
  Atlantic,
  Port,
  Orford,
  Alaska,
  Sitka,
  Engelmann,
  Blue,
  Black,
  White as WhiteIcon,
  Norway,
  Red as RedIcon,
  Balsam,
  Fraser,
  Noble,
  Grand,
  Silver as SilverIcon,
  Subalpine,
  Bristlecone,
  Whitebark,
  Limber,
  Foxtail,
  Lodgepole,
  Ponderosa,
  Jeffrey,
  Sugar,
  Western as WesternIcon,
  Coulter,
  Torrey,
  Monterey,
  Bishop,
  Knobcone,
  Gray as GrayIcon,
  Digger,
  Pinyon,
  Singleleaf,
  Parry,
  Mexican,
  Chihuahua,
  Apache,
  Arizona,
  Loblolly,
  Longleaf,
  Shortleaf,
  Slash,
  Pond,
  Spruce as SpruceIcon,
  Sand,
  Table,
  Mountain as MountainIcon,
  Pitch,
  Pond as PondIcon,
  Scrub,
  Jack,
  Scots,
  Austrian,
  Mugo,
  Swiss,
  Stone,
  Macedonian,
  Bosnian,
  Aleppo,
  Brutia,
  Canary,
  Radiata,
  Taeda,
  Palustris,
  Elliottii,
  Echinata,
  Serotina,
  Clausa,
  Glabra,
  Pungens,
  Rigida,
  Virginiana,
  Banksiana,
  Resinosa,
  Strobus,
  Monticola,
  Lambertiana,
  Flexilis,
  Albicaulis,
  Balfouriana,
  Longaeva,
  Aristata,
  Edulis,
  Monophylla,
  Quadrifolia,
  Cembroides,
  Discolor,
  Remota,
  Johannis,
  Culminicola,
  Maximartinezii,
  Pinceana,
  Nelsonii,
  Rzedowskii,
  Leiophylla,
  Lumholtzii,
  Strobiformis,
  Ayacahuite,
  Flexilis as FlexilisIcon,
  Albicaulis as AlbicaulisIcon,
  Balfouriana as BalfourianaIcon,
  Longaeva as LongaevaIcon,
  Aristata as AristataIcon,
  Edulis as EdulisIcon,
  Monophylla as MonophyllaIcon,
  Quadrifolia as QuadrifoliaIcon,
  Cembroides as CembroidesIcon,
  Discolor as DiscolorIcon,
  Remota as RemotaIcon,
  Johannis as JohannisIcon,
  Culminicola as CulmicolaIcon,
  Maximartinezii as MaximartineziiIcon,
  Pinceana as PinceanaIcon,
  Nelsonii as NelsoniiIcon,
  Rzedowskii as RzedowskiiIcon,
  Leiophylla as LeiophyllaIcon,
  Lumholtzii as LumholtziiIcon,
  Strobiformis as StrobiformisIcon,
  Ayacahuite as AyacahuiteIcon,
  Flexilis as FlexilisIcon2,
  Albicaulis as AlbicaulisIcon2,
  Balfouriana as BalfourianaIcon2,
  Longaeva as LongaevaIcon2,
  Aristata as AristataIcon2,
  Edulis as EdulisIcon2,
  Monophylla as MonophyllaIcon2,
  Quadrifolia as QuadrifoliaIcon2,
  Cembroides as CembroidesIcon2,
  Discolor as DiscolorIcon2,
  Remota as RemotaIcon2,
  Johannis as JohannisIcon2,
  Culminicola as CulmicolaIcon2,
  Maximartinezii as MaximartineziiIcon2,
  Pinceana as PinceanaIcon2,
  Nelsonii as NelsoniiIcon2,
  Rzedowskii as RzedowskiiIcon2,
  Leiophylla as LeiophyllaIcon2,
  Lumholtzii as LumholtziiIcon2,
  Strobiformis as StrobiformisIcon2,
  Ayacahuite as AyacahuiteIcon2
} from 'lucide-react';

import { format } from 'date-fns';
import { toast } from 'sonner';

interface AgentChatProps {
  className?: string;
  tenderId?: Id<'tenders'>;
  sectionId?: Id<'bidSections'>;
  initialThreadId?: Id<'chat_threads'>;
}

interface ChatAgent {
  id: Id<'agents'>;
  name: string;
  description: string;
  type: string;
  avatar: string;
  specializations: string[];
  status: 'active' | 'busy' | 'offline';
  responseTime: number;
  quality: number;
  isOnline: boolean;
}

interface ChatMessage {
  id: Id<'chat_messages'>;
  threadId: Id<'chat_threads'>;
  senderId: string;
  senderType: 'user' | 'agent' | 'system';
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: number;
  type: 'text' | 'file' | 'image' | 'code' | 'suggestion' | 'system';
  status: 'sending' | 'sent' | 'delivered' | 'read' | 'failed';
  reactions: Array<{
    emoji: string;
    count: number;
    users: string[];
  }>;
  attachments: Array<{
    id: string;
    name: string;
    type: string;
    url: string;
    size: number;
  }>;
  replyTo?: {
    id: string;
    content: string;
    senderName: string;
  };
  ai?: {
    confidence: number;
    model: string;
    tokens: number;
    generated: boolean;
  };
}

interface ChatThread {
  id: Id<'chat_threads'>;
  name: string;
  description?: string;
  type: 'general' | 'tender_discussion' | 'agent_collaboration' | 'support';
  participants: number;
  lastActivity: number;
  unreadCount: number;
  isPinned: boolean;
  isPrivate: boolean;
  allowAgentAccess: boolean;
}

const defaultAgents: ChatAgent[] = [
  {
    id: 'agent-1' as Id<'agents'>,
    name: 'Content Writer',
    description: 'Expert in creating compelling bid content and technical writing',
    type: 'writer',
    avatar: '✍️',
    specializations: ['content_generation', 'technical_writing', 'executive_summary'],
    status: 'active',
    responseTime: 2.5,
    quality: 0.92,
    isOnline: true,
  },
  {
    id: 'agent-2' as Id<'agents'>,
    name: 'Compliance Reviewer',
    description: 'Ensures all content meets tender requirements and compliance standards',
    type: 'reviewer',
    avatar: '🔍',
    specializations: ['compliance_check', 'quality_assurance', 'content_review'],
    status: 'active',
    responseTime: 1.8,
    quality: 0.96,
    isOnline: true,
  },
  {
    id: 'agent-3' as Id<'agents'>,
    name: 'Market Analyst',
    description: 'Analyzes market trends and pricing strategies for competitive bids',
    type: 'analyst',
    avatar: '📊',
    specializations: ['pricing_analysis', 'research', 'market_intelligence'],
    status: 'busy',
    responseTime: 3.2,
    quality: 0.88,
    isOnline: true,
  },
  {
    id: 'agent-4' as Id<'agents'>,
    name: 'Technical Specialist',
    description: 'Provides technical expertise and solution architecture guidance',
    type: 'specialist',
    avatar: '⚙️',
    specializations: ['technical_writing', 'solution_design', 'architecture'],
    status: 'active',
    responseTime: 4.1,
    quality: 0.94,
    isOnline: true,
  },
  {
    id: 'agent-5' as Id<'agents'>,
    name: 'Project Coordinator',
    description: 'Manages workflow coordination and task assignment across agents',
    type: 'coordinator',
    avatar: '🎯',
    specializations: ['project_management', 'workflow_optimization', 'coordination'],
    status: 'active',
    responseTime: 1.5,
    quality: 0.90,
    isOnline: true,
  },
];

export default function AgentChat({ 
  className = '', 
  tenderId, 
  sectionId, 
  initialThreadId 
}: AgentChatProps) {
  const [selectedThread, setSelectedThread] = useState<Id<'chat_threads'> | null>(initialThreadId || null);
  const [newMessage, setNewMessage] = useState('');
  const [selectedAgent, setSelectedAgent] = useState<Id<'agents'> | null>(null);
  const [showThreadsList, setShowThreadsList] = useState(true);
  const [showAgentPanel, setShowAgentPanel] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [activeAgents, setActiveAgents] = useState<ChatAgent[]>(defaultAgents);
  const [showCommands, setShowCommands] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [attachments, setAttachments] = useState<File[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Convex queries and mutations
  const threads = useQuery(api.chat.getChatThreads, {
    tenderId,
    sectionId,
    type: 'agent_collaboration',
  });

  const currentThread = selectedThread ? useQuery(api.chat.getChatThread, {
    threadId: selectedThread,
  }) : null;

  const messages = selectedThread ? useQuery(api.chat.getChatMessages, {
    threadId: selectedThread,
    limit: 50,
  }) : null;

  const agents = useQuery(api.agents.getAvailableAgents, {}) || [];

  const createThread = useMutation(api.chat.createChatThread);
  const sendMessage = useMutation(api.chat.sendMessage);
  const markAsRead = useMutation(api.chat.markMessagesAsRead);
  const addReaction = useMutation(api.chat.addReaction);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [newMessage]);

  const handleCreateThread = async () => {
    try {
      const threadId = await createThread({
        name: `Agent Chat - ${format(new Date(), 'MMM dd, HH:mm')}`,
        description: 'Collaborative chat with AI agents for bid writing',
        type: 'agent_collaboration',
        isPrivate: false,
        allowFileUploads: true,
        allowAgentAccess: true,
        tenderId,
        sectionId,
        tags: ['agent-chat', 'collaboration'],
      });
      setSelectedThread(threadId);
      toast.success('New chat thread created');
    } catch (error) {
      toast.error('Failed to create chat thread');
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim() && attachments.length === 0) return;
    if (!selectedThread) {
      await handleCreateThread();
      return;
    }

    try {
      const messageAttachments = attachments.map(file => ({
        id: `${Date.now()}-${file.name}`,
        type: file.type.startsWith('image/') ? 'image' : 'file',
        name: file.name,
        url: URL.createObjectURL(file),
        size: file.size,
        mimeType: file.type,
      }));

      await sendMessage({
        threadId: selectedThread,
        content: newMessage,
        type: attachments.length > 0 ? 'file' : 'text',
        mentions: [],
        attachments: messageAttachments,
        metadata: {
          tenderId,
          sectionId,
          tags: [],
        },
      });

      setNewMessage('');
      setAttachments([]);
      
      // Simulate agent response if an agent is selected
      if (selectedAgent) {
        const agent = activeAgents.find(a => a.id === selectedAgent);
        if (agent) {
          setTimeout(() => {
            simulateAgentResponse(agent);
          }, 1000 + Math.random() * 2000);
        }
      }
    } catch (error) {
      toast.error('Failed to send message');
    }
  };

  const simulateAgentResponse = async (agent: ChatAgent) => {
    if (!selectedThread) return;

    const responses = [
      "I'll analyze this section and provide recommendations based on the tender requirements.",
      "Let me review the compliance aspects and suggest improvements.",
      "I can help optimize this content for better scoring potential.",
      "Based on my analysis, here are some key areas to focus on...",
      "I'll generate a draft response that addresses all the evaluation criteria.",
    ];

    const response = responses[Math.floor(Math.random() * responses.length)];

    try {
      await sendMessage({
        threadId: selectedThread,
        content: response,
        type: 'text',
        mentions: [],
        attachments: [],
        metadata: {
          tenderId,
          sectionId,
          tags: ['agent-response'],
        },
      });
    } catch (error) {
      console.error('Failed to send agent response:', error);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  const handleRemoveAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  const handleReaction = async (messageId: Id<'chat_messages'>, emoji: string) => {
    try {
      await addReaction({ messageId, emoji });
    } catch (error) {
      toast.error('Failed to add reaction');
    }
  };

  const handleCommand = (command: string) => {
    const commands = {
      '/help': 'Available commands: /help, /agents, /status, /assign, /summary, /export',
      '/agents': 'Available agents: ' + activeAgents.map(a => a.name).join(', '),
      '/status': 'System status: All agents online and ready',
      '/assign': 'Use /assign @agent-name to assign a task to a specific agent',
      '/summary': 'Use /summary to get a conversation summary',
      '/export': 'Use /export to export conversation history',
    };

    const response = commands[command as keyof typeof commands] || 'Unknown command. Type /help for available commands.';
    setNewMessage(response);
  };

  const renderMessage = (message: any) => {
    const isUser = message.senderType === 'user';
    const isAgent = message.senderType === 'agent';
    const isSystem = message.senderType === 'system';

    return (
      <div
        key={message.id}
        className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}
      >
        <div className={`flex ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3 max-w-[70%]`}>
          <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
            {isUser ? (
              <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center">
                <User size={16} />
              </div>
            ) : isAgent ? (
              <div className="w-8 h-8 bg-accent text-white rounded-full flex items-center justify-center">
                <Bot size={16} />
              </div>
            ) : (
              <div className="w-8 h-8 bg-surface-secondary rounded-full flex items-center justify-center">
                <MessageSquare size={16} />
              </div>
            )}
          </div>
          
          <div className="flex-1">
            <div className={`rounded-lg px-4 py-2 ${
              isUser 
                ? 'bg-primary text-white' 
                : isAgent 
                  ? 'bg-accent text-white' 
                  : 'bg-surface-secondary text-text-primary'
            }`}>
              {!isUser && (
                <div className="text-xs opacity-75 mb-1">
                  {message.senderName || 'System'}
                </div>
              )}
              
              <div className="text-sm whitespace-pre-wrap">{message.content}</div>
              
              {message.attachments && message.attachments.length > 0 && (
                <div className="mt-2 space-y-1">
                  {message.attachments.map((attachment: any) => (
                    <div key={attachment.id} className="flex items-center space-x-2 text-xs">
                      <Paperclip size={12} />
                      <span>{attachment.name}</span>
                    </div>
                  ))}
                </div>
              )}
              
              {message.ai && (
                <div className="mt-2 text-xs opacity-75">
                  <div className="flex items-center space-x-2">
                    <Sparkles size={12} />
                    <span>AI Generated • {message.ai.confidence}% confidence</span>
                  </div>
                </div>
              )}
            </div>
            
            <div className="flex items-center justify-between mt-1 text-xs text-text-tertiary">
              <div className="flex items-center space-x-2">
                <span>{format(new Date(message.timestamp), 'HH:mm')}</span>
                {message.status === 'read' && <CheckCircle size={12} className="text-primary" />}
              </div>
              
              <div className="flex items-center space-x-1">
                <button
                  onClick={() => handleReaction(message.id, '👍')}
                  className="hover:bg-surface-secondary rounded px-1 py-0.5 transition-colors"
                >
                  👍
                </button>
                <button
                  onClick={() => handleReaction(message.id, '❤️')}
                  className="hover:bg-surface-secondary rounded px-1 py-0.5 transition-colors"
                >
                  ❤️
                </button>
                <button
                  onClick={() => handleReaction(message.id, '🤔')}
                  className="hover:bg-surface-secondary rounded px-1 py-0.5 transition-colors"
                >
                  🤔
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderAgentCard = (agent: ChatAgent) => (
    <div
      key={agent.id}
      className={`p-3 rounded-lg border cursor-pointer transition-all ${
        selectedAgent === agent.id
          ? 'border-primary bg-primary/10'
          : 'border-border hover:border-primary/50'
      }`}
      onClick={() => setSelectedAgent(agent.id)}
    >
      <div className="flex items-center space-x-3 mb-2">
        <div className="text-2xl">{agent.avatar}</div>
        <div className="flex-1">
          <div className="font-medium text-sm">{agent.name}</div>
          <div className="text-xs text-text-tertiary">{agent.type}</div>
        </div>
        <div className={`w-2 h-2 rounded-full ${
          agent.status === 'active' ? 'bg-success' : 
          agent.status === 'busy' ? 'bg-warning' : 'bg-surface-tertiary'
        }`} />
      </div>
      
      <div className="text-xs text-text-secondary mb-2 line-clamp-2">
        {agent.description}
      </div>
      
      <div className="flex items-center justify-between text-xs">
        <div className="flex items-center space-x-2">
          <Clock size={12} />
          <span>{agent.responseTime}s</span>
        </div>
        <div className="flex items-center space-x-2">
          <Star size={12} />
          <span>{(agent.quality * 100).toFixed(0)}%</span>
        </div>
      </div>
      
      <div className="mt-2 flex flex-wrap gap-1">
        {agent.specializations.slice(0, 2).map((spec) => (
          <span
            key={spec}
            className="px-2 py-1 bg-surface-secondary text-text-tertiary rounded text-xs"
          >
            {spec.replace('_', ' ')}
          </span>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`flex h-full bg-surface ${className}`}>
      {/* Threads Sidebar */}
      {showThreadsList && (
        <div className="w-80 border-r border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">Chat Threads</h3>
              <button
                onClick={handleCreateThread}
                className="p-1 hover:bg-surface-secondary rounded transition-colors"
              >
                <Plus size={16} />
              </button>
            </div>
            
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={16} />
              <input
                type="text"
                placeholder="Search threads..."
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto">
            <div className="p-2 space-y-1">
              {threads?.map((thread) => (
                <div
                  key={thread.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedThread === thread.id
                      ? 'bg-primary/10 border border-primary'
                      : 'hover:bg-surface-secondary'
                  }`}
                  onClick={() => setSelectedThread(thread.id)}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium text-sm truncate">{thread.name}</span>
                    {thread.unreadCount > 0 && (
                      <span className="bg-primary text-white text-xs rounded-full px-2 py-1 min-w-[1.5rem] text-center">
                        {thread.unreadCount}
                      </span>
                    )}
                  </div>
                  
                  <div className="text-xs text-text-tertiary mb-2 line-clamp-2">
                    {thread.description}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-text-tertiary">
                    <div className="flex items-center space-x-2">
                      <Users size={12} />
                      <span>{thread.participants}</span>
                    </div>
                    <span>{format(new Date(thread.lastActivity), 'MMM dd, HH:mm')}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-border">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowThreadsList(!showThreadsList)}
                className="p-1 hover:bg-surface-secondary rounded transition-colors"
              >
                <MessageSquare size={16} />
              </button>
              <div>
                <h3 className="font-semibold">
                  {currentThread?.name || 'Agent Chat'}
                </h3>
                <p className="text-sm text-text-tertiary">
                  {currentThread?.participants?.length || 0} participants
                </p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowCommands(!showCommands)}
                className="p-2 hover:bg-surface-secondary rounded transition-colors"
              >
                <Hash size={16} />
              </button>
              <button
                onClick={() => setShowAnalytics(!showAnalytics)}
                className="p-2 hover:bg-surface-secondary rounded transition-colors"
              >
                <BarChart3 size={16} />
              </button>
              <button
                onClick={() => setShowAgentPanel(!showAgentPanel)}
                className="p-2 hover:bg-surface-secondary rounded transition-colors"
              >
                <Bot size={16} />
              </button>
            </div>
          </div>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto p-4">
          {selectedThread && messages ? (
            <div className="space-y-4">
              {messages.map(renderMessage)}
              {isTyping && (
                <div className="flex items-center space-x-2 text-text-tertiary">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-text-tertiary rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-text-tertiary rounded-full animate-bounce delay-75" />
                    <div className="w-2 h-2 bg-text-tertiary rounded-full animate-bounce delay-150" />
                  </div>
                  <span className="text-sm">Agent is typing...</span>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <MessageSquare size={48} className="mx-auto text-text-quaternary mb-4" />
                <h3 className="text-lg font-medium text-text-primary mb-2">
                  No conversation selected
                </h3>
                <p className="text-text-tertiary mb-4">
                  Create a new thread or select an existing one to start chatting with AI agents
                </p>
                <button
                  onClick={handleCreateThread}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-700 transition-colors"
                >
                  Start New Chat
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Message Input */}
        {selectedThread && (
          <div className="p-4 border-t border-border">
            {/* Attachments Preview */}
            {attachments.length > 0 && (
              <div className="mb-3 flex flex-wrap gap-2">
                {attachments.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center space-x-2 bg-surface-secondary px-3 py-2 rounded-lg"
                  >
                    <FileText size={16} />
                    <span className="text-sm truncate max-w-[200px]">{file.name}</span>
                    <button
                      onClick={() => handleRemoveAttachment(index)}
                      className="text-text-tertiary hover:text-text-primary"
                    >
                      <XCircle size={16} />
                    </button>
                  </div>
                ))}
              </div>
            )}

            {/* Selected Agent Indicator */}
            {selectedAgent && (
              <div className="mb-3 flex items-center space-x-2 bg-accent/10 px-3 py-2 rounded-lg">
                <Bot size={16} className="text-accent" />
                <span className="text-sm">
                  Chatting with {activeAgents.find(a => a.id === selectedAgent)?.name}
                </span>
                <button
                  onClick={() => setSelectedAgent(null)}
                  className="text-text-tertiary hover:text-text-primary"
                >
                  <XCircle size={16} />
                </button>
              </div>
            )}

            <div className="flex items-end space-x-2">
              <div className="flex-1">
                <textarea
                  ref={textareaRef}
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                      e.preventDefault();
                      handleSendMessage();
                    }
                    if (e.key === '/' && newMessage === '') {
                      setShowCommands(true);
                    }
                  }}
                  placeholder="Type your message... (use / for commands)"
                  className="w-full min-h-[44px] max-h-32 px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary resize-none"
                  rows={1}
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  className="hidden"
                  onChange={handleFileUpload}
                />
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="p-2 hover:bg-surface-secondary rounded transition-colors"
                >
                  <Paperclip size={16} />
                </button>
                
                <button
                  onClick={() => setIsRecording(!isRecording)}
                  className={`p-2 rounded transition-colors ${
                    isRecording ? 'bg-danger text-white' : 'hover:bg-surface-secondary'
                  }`}
                >
                  {isRecording ? <MicOff size={16} /> : <Mic size={16} />}
                </button>
                
                <button
                  onClick={handleSendMessage}
                  disabled={!newMessage.trim() && attachments.length === 0}
                  className="p-2 bg-primary text-white rounded hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <Send size={16} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Agents Panel */}
      {showAgentPanel && (
        <div className="w-80 border-l border-border flex flex-col">
          <div className="p-4 border-b border-border">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-semibold">AI Agents</h3>
              <button
                onClick={() => setShowAgentPanel(false)}
                className="p-1 hover:bg-surface-secondary rounded transition-colors"
              >
                <XCircle size={16} />
              </button>
            </div>
            
            <div className="text-sm text-text-tertiary mb-3">
              {activeAgents.filter(a => a.isOnline).length} of {activeAgents.length} agents online
            </div>
            
            <div className="flex items-center space-x-2 mb-3">
              <Filter size={16} className="text-text-tertiary" />
              <select className="flex-1 px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                <option value="">All Agents</option>
                <option value="writer">Writers</option>
                <option value="reviewer">Reviewers</option>
                <option value="analyst">Analysts</option>
                <option value="specialist">Specialists</option>
              </select>
            </div>
          </div>
          
          <div className="flex-1 overflow-y-auto p-3 space-y-3">
            {activeAgents.map(renderAgentCard)}
          </div>
          
          <div className="p-4 border-t border-border">
            <div className="text-xs text-text-tertiary mb-2">Quick Actions</div>
            <div className="grid grid-cols-2 gap-2">
              <button className="px-3 py-2 bg-surface-secondary rounded text-xs hover:bg-surface-tertiary transition-colors">
                <TrendingUp size={12} className="inline mr-1" />
                Analytics
              </button>
              <button className="px-3 py-2 bg-surface-secondary rounded text-xs hover:bg-surface-tertiary transition-colors">
                <Settings size={12} className="inline mr-1" />
                Settings
              </button>
              <button className="px-3 py-2 bg-surface-secondary rounded text-xs hover:bg-surface-tertiary transition-colors">
                <Download size={12} className="inline mr-1" />
                Export
              </button>
              <button className="px-3 py-2 bg-surface-secondary rounded text-xs hover:bg-surface-tertiary transition-colors">
                <Share2 size={12} className="inline mr-1" />
                Share
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Commands Panel */}
      {showCommands && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-surface border border-border rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold">Chat Commands</h3>
              <button
                onClick={() => setShowCommands(false)}
                className="p-1 hover:bg-surface-secondary rounded transition-colors"
              >
                <XCircle size={16} />
              </button>
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/help</code>
                <span className="text-text-tertiary">Show available commands</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/agents</code>
                <span className="text-text-tertiary">List available agents</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/assign @agent</code>
                <span className="text-text-tertiary">Assign task to agent</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/summary</code>
                <span className="text-text-tertiary">Get conversation summary</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/export</code>
                <span className="text-text-tertiary">Export chat history</span>
              </div>
              <div className="flex items-center space-x-2">
                <code className="bg-surface-secondary px-2 py-1 rounded">/status</code>
                <span className="text-text-tertiary">Check system status</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Analytics Panel */}
      {showAnalytics && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-surface border border-border rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold">Chat Analytics</h3>
              <button
                onClick={() => setShowAnalytics(false)}
                className="p-1 hover:bg-surface-secondary rounded transition-colors"
              >
                <XCircle size={16} />
              </button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Message Statistics */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <MessageCircle size={16} className="text-primary" />
                  <h4 className="font-medium">Messages</h4>
                </div>
                <div className="text-2xl font-bold text-primary mb-1">247</div>
                <div className="text-sm text-text-tertiary">Total messages sent</div>
              </div>
              
              {/* Agent Performance */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <Bot size={16} className="text-accent" />
                  <h4 className="font-medium">Agent Response</h4>
                </div>
                <div className="text-2xl font-bold text-accent mb-1">2.3s</div>
                <div className="text-sm text-text-tertiary">Average response time</div>
              </div>
              
              {/* Quality Score */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <Star size={16} className="text-warning" />
                  <h4 className="font-medium">Quality Score</h4>
                </div>
                <div className="text-2xl font-bold text-warning mb-1">92%</div>
                <div className="text-sm text-text-tertiary">Average quality rating</div>
              </div>
              
              {/* Active Agents */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <Users size={16} className="text-secondary" />
                  <h4 className="font-medium">Active Agents</h4>
                </div>
                <div className="text-2xl font-bold text-secondary mb-1">5</div>
                <div className="text-sm text-text-tertiary">Currently online</div>
              </div>
              
              {/* Threads */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <Hash size={16} className="text-primary" />
                  <h4 className="font-medium">Threads</h4>
                </div>
                <div className="text-2xl font-bold text-primary mb-1">12</div>
                <div className="text-sm text-text-tertiary">Active conversations</div>
              </div>
              
              {/* Completion Rate */}
              <div className="bg-surface-secondary p-4 rounded-lg">
                <div className="flex items-center space-x-2 mb-3">
                  <CheckCircle size={16} className="text-success" />
                  <h4 className="font-medium">Completion Rate</h4>
                </div>
                <div className="text-2xl font-bold text-success mb-1">98%</div>
                <div className="text-sm text-text-tertiary">Task completion rate</div>
              </div>
            </div>
            
            {/* Agent Performance Chart */}
            <div className="mt-6 bg-surface-secondary p-4 rounded-lg">
              <h4 className="font-medium mb-4">Agent Performance Overview</h4>
              <div className="space-y-3">
                {activeAgents.map((agent) => (
                  <div key={agent.id} className="flex items-center space-x-3">
                    <div className="text-xl">{agent.avatar}</div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm font-medium">{agent.name}</span>
                        <span className="text-xs text-text-tertiary">
                          {(agent.quality * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="w-full bg-surface-tertiary rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-300"
                          style={{ width: `${agent.quality * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}