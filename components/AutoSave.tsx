'use client';

import React, { useState, useEffect } from 'react';
import {
  Save,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Clock,
  History,
  ToggleLeft,
  ToggleRight,
  Eye,
  RotateCcw as Restore,
  X,
  Settings,
} from 'lucide-react';
import { format } from 'date-fns';

interface Version {
  id: string;
  content: string;
  timestamp: Date;
  sectionId: string;
  autoSaved?: boolean;
  wordCount?: number;
}

interface AutoSaveProps {
  enabled: boolean;
  onToggle: (enabled: boolean) => void;
  lastSaved: Date | null;
  isAutoSaving: boolean;
  versions?: Version[];
  onRestoreVersion?: (version: Version) => void;
}

export default function AutoSave({
  enabled,
  onToggle,
  lastSaved,
  isAutoSaving,
  versions = [],
  onRestoreVersion,
}: AutoSaveProps) {
  const [showVersionHistory, setShowVersionHistory] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<Version | null>(null);
  const [autoSaveInterval, setAutoSaveInterval] = useState(2000); // 2 seconds

  const getStatusIcon = () => {
    if (isAutoSaving) {
      return <RefreshCw size={16} className="animate-spin text-blue-400" />;
    }
    if (lastSaved) {
      return <CheckCircle size={16} className="text-green-400" />;
    }
    return <AlertCircle size={16} className="text-gray-400" />;
  };

  const getStatusText = () => {
    if (isAutoSaving) {
      return 'Saving...';
    }
    if (lastSaved) {
      return `Saved ${format(lastSaved, 'HH:mm')}`;
    }
    return 'Not saved';
  };

  const handleVersionRestore = (version: Version) => {
    if (onRestoreVersion) {
      onRestoreVersion(version);
      setSelectedVersion(null);
      setShowVersionHistory(false);
    }
  };

  const recentVersions = versions
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, 10);

  return (
    <>
      {/* Auto-save status indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className="bg-gray-900 border border-gray-700 rounded-lg p-3 shadow-lg">
          <div className="flex items-center space-x-3">
            {/* Auto-save toggle */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => onToggle(!enabled)}
                className="flex items-center space-x-1 text-sm"
              >
                {enabled ? (
                  <ToggleRight size={20} className="text-green-400" />
                ) : (
                  <ToggleLeft size={20} className="text-gray-400" />
                )}
                <span className="text-gray-300">Auto-save</span>
              </button>
            </div>

            {/* Separator */}
            <div className="w-px h-6 bg-gray-700" />

            {/* Status */}
            <div className="flex items-center space-x-2">
              {getStatusIcon()}
              <span className="text-sm text-gray-300">{getStatusText()}</span>
            </div>

            {/* Version history button */}
            {versions.length > 0 && (
              <>
                <div className="w-px h-6 bg-gray-700" />
                <button
                  onClick={() => setShowVersionHistory(!showVersionHistory)}
                  className="p-1 text-gray-400 hover:text-white"
                  title="Version History"
                >
                  <History size={16} />
                </button>
              </>
            )}
          </div>

          {/* Auto-save settings */}
          {enabled && (
            <div className="mt-2 pt-2 border-t border-gray-700">
              <div className="flex items-center space-x-2 text-xs text-gray-400">
                <Settings size={12} />
                <span>Interval:</span>
                <select
                  value={autoSaveInterval}
                  onChange={(e) => setAutoSaveInterval(Number(e.target.value))}
                  className="bg-gray-800 border border-gray-600 rounded px-2 py-1 text-xs"
                >
                  <option value={1000}>1s</option>
                  <option value={2000}>2s</option>
                  <option value={5000}>5s</option>
                  <option value={10000}>10s</option>
                  <option value={30000}>30s</option>
                </select>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Version History Modal */}
      {showVersionHistory && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden">
            {/* Header */}
            <div className="p-4 border-b border-gray-700 flex items-center justify-between">
              <h3 className="text-lg font-bold flex items-center space-x-2">
                <History size={20} />
                <span>Version History</span>
              </h3>
              <button
                onClick={() => setShowVersionHistory(false)}
                className="text-gray-400 hover:text-white p-1"
              >
                <X size={16} />
              </button>
            </div>

            {/* Version List */}
            <div className="p-4 overflow-y-auto max-h-96">
              {recentVersions.length === 0 ? (
                <div className="text-center text-gray-400 py-8">
                  <History size={48} className="mx-auto mb-4 opacity-50" />
                  <p>No version history available</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {recentVersions.map((version) => (
                    <div
                      key={version.id}
                      className={`p-3 rounded-lg border transition-all cursor-pointer ${
                        selectedVersion?.id === version.id
                          ? 'bg-gray-800 border-blue-400'
                          : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800'
                      }`}
                      onClick={() => setSelectedVersion(version)}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <Clock size={16} className="text-gray-400" />
                          <span className="text-sm font-medium">
                            {format(version.timestamp, 'MMM dd, yyyy HH:mm:ss')}
                          </span>
                          {version.autoSaved && (
                            <span className="text-xs bg-blue-900/20 text-blue-400 px-2 py-1 rounded">
                              Auto-saved
                            </span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          {version.wordCount && (
                            <span className="text-xs text-gray-400">
                              {version.wordCount} words
                            </span>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleVersionRestore(version);
                            }}
                            className="p-1 text-gray-400 hover:text-white"
                            title="Restore this version"
                          >
                            <Restore size={14} />
                          </button>
                        </div>
                      </div>
                      
                      {selectedVersion?.id === version.id && (
                        <div className="mt-2 pt-2 border-t border-gray-700">
                          <div className="text-sm text-gray-300 bg-gray-800 rounded p-2 max-h-32 overflow-y-auto">
                            <div
                              dangerouslySetInnerHTML={{
                                __html: version.content.substring(0, 200) + '...',
                              }}
                            />
                          </div>
                          <div className="flex justify-end mt-2">
                            <button
                              onClick={() => handleVersionRestore(version)}
                              className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                            >
                              Restore Version
                            </button>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            <div className="p-4 border-t border-gray-700 bg-gray-800/50">
              <div className="flex items-center justify-between text-sm text-gray-400">
                <span>
                  {recentVersions.length} version{recentVersions.length !== 1 ? 's' : ''} shown
                </span>
                <div className="flex items-center space-x-2">
                  <span>Auto-save: {enabled ? 'ON' : 'OFF'}</span>
                  <button
                    onClick={() => onToggle(!enabled)}
                    className="text-blue-400 hover:text-blue-300"
                  >
                    {enabled ? 'Disable' : 'Enable'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}