"use client";

import React, { useState } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../convex/_generated/api";
import { Id } from "../convex/_generated/dataModel";
import { 
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { toast } from "@/components/ui/use-toast";
import {
  FileText,
  Map,
  BarChart3,
  Calendar,
  Users,
  HelpCircle,
  List,
  Mic,
  Presentation,
  Download,
  Eye,
  Sparkles,
  CheckCircle,
  Clock,
  AlertCircle,
  PlusCircle,
  Trash2,
  Edit,
  Copy,
} from "lucide-react";

interface ContentBuilderProps {
  tenderId: Id<"tenders">;
}

const contentTypeIcons = {
  executive_summary: FileText,
  site_heatmap: Map,
  compliance_dashboard: BarChart3,
  timeline_visualization: Calendar,
  team_introduction: Users,
  qa_preparation: HelpCircle,
  meeting_agenda: List,
  voice_script: Mic,
  presentation_deck: Presentation,
  data_visualization: BarChart3,
};

const contentTypeDescriptions = {
  executive_summary: "High-level overview with key value propositions",
  site_heatmap: "Visual representation of service locations and coverage",
  compliance_dashboard: "Real-time compliance tracking and status",
  timeline_visualization: "Project milestones and implementation phases",
  team_introduction: "Professional profiles of your dedicated team",
  qa_preparation: "Anticipated questions with suggested responses",
  meeting_agenda: "Structured meeting plan with time allocations",
  voice_script: "Natural language script for presentations",
  presentation_deck: "Complete slide deck with visuals and notes",
  data_visualization: "Charts and graphs for data presentation",
};

export function ContentBuilder({ tenderId }: ContentBuilderProps) {
  const [selectedType, setSelectedType] = useState<string>("presentation_deck");
  const [formData, setFormData] = useState({
    title: "",
    description: "",
    targetAudience: "",
    meetingType: "pitch",
    duration: 60,
    theme: "professional",
    includeVoiceScript: true,
    includeBranding: true,
  });

  const [parameters, setParameters] = useState<any>({
    siteLocations: [],
    complianceItems: [],
    timelineEvents: [],
    teamMembers: [],
    qaTopics: [],
    dataPoints: [],
  });

  const createContentRequest = useMutation(api.agents.contentBuilder.createContentRequest);
  const assessQuality = useMutation(api.agents.contentBuilder.assessContentQuality);
  const exportContent = useMutation(api.agents.contentBuilder.exportContent);

  const handleSubmit = async () => {
    try {
      await createContentRequest({
        tenderId,
        contentType: selectedType as any,
        theme: formData.theme as any,
        title: formData.title,
        description: formData.description,
        targetAudience: formData.targetAudience,
        meetingType: formData.meetingType,
        duration: formData.duration,
        parameters: {
          includeVoiceScript: formData.includeVoiceScript,
          includeBranding: formData.includeBranding,
          ...parameters,
        },
      });

      toast({
        title: "Content generation started",
        description: "Your content is being generated. This may take a few moments.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start content generation",
        variant: "destructive",
      });
    }
  };

  const addParameter = (type: string) => {
    const newItem = {
      id: Date.now().toString(),
      // Default values based on type
      ...(type === "siteLocations" && {
        name: "",
        address: "",
        latitude: -37.8136,
        longitude: 144.9631,
        status: "active",
      }),
      ...(type === "complianceItems" && {
        category: "",
        requirement: "",
        status: "pending",
        priority: "medium",
      }),
      ...(type === "timelineEvents" && {
        date: Date.now(),
        title: "",
        description: "",
        milestone: false,
      }),
      ...(type === "teamMembers" && {
        name: "",
        role: "",
        bio: "",
      }),
      ...(type === "qaTopics" && {
        question: "",
        answer: "",
        category: "general",
        difficulty: "medium",
      }),
      ...(type === "dataPoints" && {
        label: "",
        value: "",
        type: "number",
      }),
    };

    setParameters({
      ...parameters,
      [type]: [...parameters[type], newItem],
    });
  };

  const updateParameter = (type: string, id: string, updates: any) => {
    setParameters({
      ...parameters,
      [type]: parameters[type].map((item: any) =>
        item.id === id ? { ...item, ...updates } : item
      ),
    });
  };

  const removeParameter = (type: string, id: string) => {
    setParameters({
      ...parameters,
      [type]: parameters[type].filter((item: any) => item.id !== id),
    });
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Content Type Selection */}
      <div className="lg:col-span-1 space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Content Type</CardTitle>
            <CardDescription>
              Select the type of content you want to generate
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            {Object.entries(contentTypeIcons).map(([type, Icon]) => (
              <button
                key={type}
                onClick={() => setSelectedType(type)}
                className={`w-full flex items-start gap-3 p-3 rounded-lg border transition-colors ${
                  selectedType === type
                    ? "border-primary bg-primary/5"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <Icon className="w-5 h-5 mt-0.5 text-gray-600" />
                <div className="text-left flex-1">
                  <div className="font-medium text-sm">
                    {type.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {contentTypeDescriptions[type as keyof typeof contentTypeDescriptions]}
                  </div>
                </div>
              </button>
            ))}
          </CardContent>
        </Card>

        {/* Templates */}
        <Card>
          <CardHeader>
            <CardTitle>Templates</CardTitle>
            <CardDescription>
              Quick-start with pre-configured templates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                setFormData({
                  ...formData,
                  title: "Cleaning Services Proposal",
                  description: "Comprehensive cleaning services for 500 Bourke Street",
                  targetAudience: "Property Management Team",
                  meetingType: "pitch",
                  duration: 45,
                });
              }}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Standard Pitch (45 min)
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                setFormData({
                  ...formData,
                  title: "Executive Briefing",
                  description: "High-level overview for C-suite executives",
                  targetAudience: "Executive Leadership",
                  meetingType: "executive_briefing",
                  duration: 20,
                });
              }}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Executive Brief (20 min)
            </Button>
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => {
                setFormData({
                  ...formData,
                  title: "Technical Deep Dive",
                  description: "Detailed service capabilities and methodology",
                  targetAudience: "Technical Evaluation Team",
                  meetingType: "technical_review",
                  duration: 90,
                });
              }}
            >
              <Sparkles className="w-4 h-4 mr-2" />
              Technical Review (90 min)
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Content Configuration */}
      <div className="lg:col-span-2">
        <Card>
          <CardHeader>
            <CardTitle>Configure Content</CardTitle>
            <CardDescription>
              Customize your {selectedType.replace(/_/g, " ")} generation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="basic" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">Basic Info</TabsTrigger>
                <TabsTrigger value="parameters">Parameters</TabsTrigger>
                <TabsTrigger value="preview">Preview</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                      id="title"
                      value={formData.title}
                      onChange={(e) =>
                        setFormData({ ...formData, title: e.target.value })
                      }
                      placeholder="Enter content title"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="audience">Target Audience</Label>
                    <Input
                      id="audience"
                      value={formData.targetAudience}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          targetAudience: e.target.value,
                        })
                      }
                      placeholder="e.g., Property Manager"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) =>
                      setFormData({ ...formData, description: e.target.value })
                    }
                    placeholder="Describe the content purpose and key messages"
                    rows={3}
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="meetingType">Meeting Type</Label>
                    <Select
                      value={formData.meetingType}
                      onValueChange={(value) =>
                        setFormData({ ...formData, meetingType: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pitch">Sales Pitch</SelectItem>
                        <SelectItem value="executive_briefing">
                          Executive Briefing
                        </SelectItem>
                        <SelectItem value="technical_review">
                          Technical Review
                        </SelectItem>
                        <SelectItem value="q&a_session">Q&A Session</SelectItem>
                        <SelectItem value="workshop">Workshop</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="duration">Duration (minutes)</Label>
                    <Input
                      id="duration"
                      type="number"
                      value={formData.duration}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          duration: parseInt(e.target.value),
                        })
                      }
                      min={15}
                      max={180}
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="theme">Theme</Label>
                    <Select
                      value={formData.theme}
                      onValueChange={(value) =>
                        setFormData({ ...formData, theme: value })
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="professional">Professional</SelectItem>
                        <SelectItem value="modern">Modern</SelectItem>
                        <SelectItem value="minimal">Minimal</SelectItem>
                        <SelectItem value="corporate">Corporate</SelectItem>
                        <SelectItem value="creative">Creative</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.includeVoiceScript}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          includeVoiceScript: e.target.checked,
                        })
                      }
                      className="rounded"
                    />
                    <span className="text-sm">Include voice-over script</span>
                  </label>

                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={formData.includeBranding}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          includeBranding: e.target.checked,
                        })
                      }
                      className="rounded"
                    />
                    <span className="text-sm">Include company branding</span>
                  </label>
                </div>
              </TabsContent>

              <TabsContent value="parameters" className="space-y-4">
                {selectedType === "site_heatmap" && (
                  <ParameterSection
                    title="Site Locations"
                    type="siteLocations"
                    items={parameters.siteLocations}
                    onAdd={() => addParameter("siteLocations")}
                    onUpdate={(id, updates) =>
                      updateParameter("siteLocations", id, updates)
                    }
                    onRemove={(id) => removeParameter("siteLocations", id)}
                    fields={[
                      { name: "name", label: "Site Name", type: "text" },
                      { name: "address", label: "Address", type: "text" },
                      { name: "latitude", label: "Latitude", type: "number" },
                      { name: "longitude", label: "Longitude", type: "number" },
                      {
                        name: "status",
                        label: "Status",
                        type: "select",
                        options: ["active", "pending", "inactive"],
                      },
                    ]}
                  />
                )}

                {selectedType === "compliance_dashboard" && (
                  <ParameterSection
                    title="Compliance Items"
                    type="complianceItems"
                    items={parameters.complianceItems}
                    onAdd={() => addParameter("complianceItems")}
                    onUpdate={(id, updates) =>
                      updateParameter("complianceItems", id, updates)
                    }
                    onRemove={(id) => removeParameter("complianceItems", id)}
                    fields={[
                      { name: "category", label: "Category", type: "text" },
                      { name: "requirement", label: "Requirement", type: "text" },
                      {
                        name: "status",
                        label: "Status",
                        type: "select",
                        options: ["compliant", "in_progress", "pending"],
                      },
                      {
                        name: "priority",
                        label: "Priority",
                        type: "select",
                        options: ["low", "medium", "high", "critical"],
                      },
                    ]}
                  />
                )}

                {selectedType === "timeline_visualization" && (
                  <ParameterSection
                    title="Timeline Events"
                    type="timelineEvents"
                    items={parameters.timelineEvents}
                    onAdd={() => addParameter("timelineEvents")}
                    onUpdate={(id, updates) =>
                      updateParameter("timelineEvents", id, updates)
                    }
                    onRemove={(id) => removeParameter("timelineEvents", id)}
                    fields={[
                      { name: "title", label: "Event Title", type: "text" },
                      { name: "description", label: "Description", type: "text" },
                      { name: "date", label: "Date", type: "date" },
                      {
                        name: "milestone",
                        label: "Is Milestone",
                        type: "checkbox",
                      },
                    ]}
                  />
                )}

                {selectedType === "team_introduction" && (
                  <ParameterSection
                    title="Team Members"
                    type="teamMembers"
                    items={parameters.teamMembers}
                    onAdd={() => addParameter("teamMembers")}
                    onUpdate={(id, updates) =>
                      updateParameter("teamMembers", id, updates)
                    }
                    onRemove={(id) => removeParameter("teamMembers", id)}
                    fields={[
                      { name: "name", label: "Name", type: "text" },
                      { name: "role", label: "Role", type: "text" },
                      { name: "bio", label: "Biography", type: "textarea" },
                    ]}
                  />
                )}

                {selectedType === "qa_preparation" && (
                  <ParameterSection
                    title="Q&A Topics"
                    type="qaTopics"
                    items={parameters.qaTopics}
                    onAdd={() => addParameter("qaTopics")}
                    onUpdate={(id, updates) =>
                      updateParameter("qaTopics", id, updates)
                    }
                    onRemove={(id) => removeParameter("qaTopics", id)}
                    fields={[
                      { name: "question", label: "Question", type: "textarea" },
                      { name: "answer", label: "Answer", type: "textarea" },
                      { name: "category", label: "Category", type: "text" },
                      {
                        name: "difficulty",
                        label: "Difficulty",
                        type: "select",
                        options: ["easy", "medium", "hard"],
                      },
                    ]}
                  />
                )}

                {(selectedType === "executive_summary" ||
                  selectedType === "data_visualization") && (
                  <ParameterSection
                    title="Data Points"
                    type="dataPoints"
                    items={parameters.dataPoints}
                    onAdd={() => addParameter("dataPoints")}
                    onUpdate={(id, updates) =>
                      updateParameter("dataPoints", id, updates)
                    }
                    onRemove={(id) => removeParameter("dataPoints", id)}
                    fields={[
                      { name: "label", label: "Label", type: "text" },
                      { name: "value", label: "Value", type: "text" },
                      {
                        name: "type",
                        label: "Type",
                        type: "select",
                        options: ["number", "percentage", "currency", "text"],
                      },
                    ]}
                  />
                )}
              </TabsContent>

              <TabsContent value="preview" className="space-y-4">
                <div className="bg-gray-50 rounded-lg p-6">
                  <h3 className="font-semibold mb-4">Content Preview</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-sm text-gray-500">Type:</span>
                      <span className="ml-2 font-medium">
                        {selectedType.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase())}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Title:</span>
                      <span className="ml-2 font-medium">
                        {formData.title || "No title set"}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Audience:</span>
                      <span className="ml-2 font-medium">
                        {formData.targetAudience || "Not specified"}
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Duration:</span>
                      <span className="ml-2 font-medium">
                        {formData.duration} minutes
                      </span>
                    </div>
                    <div>
                      <span className="text-sm text-gray-500">Theme:</span>
                      <Badge variant="secondary" className="ml-2">
                        {formData.theme}
                      </Badge>
                    </div>
                  </div>

                  {/* Parameter Summary */}
                  <div className="mt-6 space-y-2">
                    {Object.entries(parameters).map(([key, items]: [string, any[]]) => {
                      if (items.length === 0) return null;
                      return (
                        <div key={key} className="flex items-center gap-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm">
                            {items.length} {key.replace(/([A-Z])/g, " $1").toLowerCase()}
                          </span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                <Button
                  onClick={handleSubmit}
                  className="w-full"
                  disabled={!formData.title || !formData.targetAudience}
                >
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Content
                </Button>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>

        {/* Recent Content */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Content</CardTitle>
            <CardDescription>
              Previously generated content for this tender
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <ContentItem
                title="Executive Presentation Deck"
                type="presentation_deck"
                status="completed"
                createdAt={Date.now() - 86400000}
                onView={() => {}}
                onExport={() => {}}
                onAssess={() => {}}
              />
              <ContentItem
                title="Site Coverage Heat Map"
                type="site_heatmap"
                status="processing"
                createdAt={Date.now() - 3600000}
                progress={65}
              />
              <ContentItem
                title="Q&A Preparation Guide"
                type="qa_preparation"
                status="completed"
                createdAt={Date.now() - 172800000}
                onView={() => {}}
                onExport={() => {}}
                qualityScore={0.92}
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

// Parameter Section Component
function ParameterSection({
  title,
  type,
  items,
  onAdd,
  onUpdate,
  onRemove,
  fields,
}: {
  title: string;
  type: string;
  items: any[];
  onAdd: () => void;
  onUpdate: (id: string, updates: any) => void;
  onRemove: (id: string) => void;
  fields: Array<{
    name: string;
    label: string;
    type: string;
    options?: string[];
  }>;
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h4 className="font-medium">{title}</h4>
        <Button onClick={onAdd} size="sm" variant="outline">
          <PlusCircle className="w-4 h-4 mr-1" />
          Add
        </Button>
      </div>

      <div className="space-y-3">
        {items.map((item, index) => (
          <div key={item.id} className="border rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{title} #{index + 1}</span>
              <Button
                onClick={() => onRemove(item.id)}
                size="sm"
                variant="ghost"
                className="text-red-500 hover:text-red-600"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {fields.map((field) => (
                <div key={field.name} className={field.type === "textarea" ? "col-span-2" : ""}>
                  <Label className="text-xs">{field.label}</Label>
                  {field.type === "text" && (
                    <Input
                      value={item[field.name] || ""}
                      onChange={(e) =>
                        onUpdate(item.id, { [field.name]: e.target.value })
                      }
                      className="mt-1"
                    />
                  )}
                  {field.type === "number" && (
                    <Input
                      type="number"
                      value={item[field.name] || ""}
                      onChange={(e) =>
                        onUpdate(item.id, {
                          [field.name]: parseFloat(e.target.value),
                        })
                      }
                      className="mt-1"
                    />
                  )}
                  {field.type === "textarea" && (
                    <Textarea
                      value={item[field.name] || ""}
                      onChange={(e) =>
                        onUpdate(item.id, { [field.name]: e.target.value })
                      }
                      className="mt-1"
                      rows={2}
                    />
                  )}
                  {field.type === "select" && (
                    <Select
                      value={item[field.name] || ""}
                      onValueChange={(value) =>
                        onUpdate(item.id, { [field.name]: value })
                      }
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {field.options?.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option.charAt(0).toUpperCase() + option.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                  {field.type === "date" && (
                    <Input
                      type="date"
                      value={
                        item[field.name]
                          ? new Date(item[field.name]).toISOString().split("T")[0]
                          : ""
                      }
                      onChange={(e) =>
                        onUpdate(item.id, {
                          [field.name]: new Date(e.target.value).getTime(),
                        })
                      }
                      className="mt-1"
                    />
                  )}
                  {field.type === "checkbox" && (
                    <div className="mt-1">
                      <input
                        type="checkbox"
                        checked={item[field.name] || false}
                        onChange={(e) =>
                          onUpdate(item.id, { [field.name]: e.target.checked })
                        }
                        className="rounded"
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}

        {items.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            No {title.toLowerCase()} added yet
          </div>
        )}
      </div>
    </div>
  );
}

// Content Item Component
function ContentItem({
  title,
  type,
  status,
  createdAt,
  progress,
  qualityScore,
  onView,
  onExport,
  onAssess,
}: {
  title: string;
  type: string;
  status: string;
  createdAt: number;
  progress?: number;
  qualityScore?: number;
  onView?: () => void;
  onExport?: () => void;
  onAssess?: () => void;
}) {
  const Icon = contentTypeIcons[type as keyof typeof contentTypeIcons] || FileText;

  return (
    <div className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center gap-3">
        <div className="p-2 bg-gray-100 rounded-lg">
          <Icon className="w-5 h-5 text-gray-600" />
        </div>
        <div>
          <div className="font-medium">{title}</div>
          <div className="flex items-center gap-2 mt-1">
            <span className="text-xs text-gray-500">
              {new Date(createdAt).toLocaleDateString()}
            </span>
            {status === "completed" && qualityScore && (
              <>
                <span className="text-xs text-gray-400">•</span>
                <span className="text-xs text-green-600 font-medium">
                  {(qualityScore * 100).toFixed(0)}% quality
                </span>
              </>
            )}
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2">
        {status === "processing" && progress !== undefined && (
          <div className="flex items-center gap-2">
            <Progress value={progress} className="w-20 h-2" />
            <span className="text-xs text-gray-500">{progress}%</span>
          </div>
        )}

        {status === "completed" && (
          <>
            {onView && (
              <Button onClick={onView} size="sm" variant="ghost">
                <Eye className="w-4 h-4" />
              </Button>
            )}
            {onExport && (
              <Button onClick={onExport} size="sm" variant="ghost">
                <Download className="w-4 h-4" />
              </Button>
            )}
            {onAssess && !qualityScore && (
              <Button onClick={onAssess} size="sm" variant="ghost">
                <BarChart3 className="w-4 h-4" />
              </Button>
            )}
          </>
        )}

        <Badge
          variant={
            status === "completed"
              ? "success"
              : status === "processing"
              ? "secondary"
              : "destructive"
          }
        >
          {status}
        </Badge>
      </div>
    </div>
  );
}