'use client';

import React, { useState, useMemo } from 'react';
import {
  LayoutTemplate as Template,
  X,
  Search,
  Star,
  FileText,
  Target,
  Users,
  DollarSign,
  Clock,
  Award,
  Shield,
  Zap,
  Heart,
  Building,
  Truck,
  Settings,
  Eye,
  Copy,
  Download,
} from 'lucide-react';

interface SectionTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  content: string;
  tags: string[];
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  wordCount: number;
  popularity: number;
  lastUpdated: Date;
  sectionTypes: string[];
}

interface SectionTemplatesProps {
  sectionType?: string;
  onSelect: (template: SectionTemplate) => void;
  onClose: () => void;
}

const templates: SectionTemplate[] = [
  {
    id: '1',
    name: 'Executive Summary',
    category: 'Summary',
    description: 'Comprehensive executive summary template for tender proposals',
    content: `<h2>Executive Summary</h2>
<p><strong>Project Overview:</strong> [Brief description of the project and our understanding]</p>
<p><strong>Our Approach:</strong> [High-level approach and methodology]</p>
<p><strong>Key Benefits:</strong></p>
<ul>
  <li>[Benefit 1 - quantified where possible]</li>
  <li>[Benefit 2 - quantified where possible]</li>
  <li>[Benefit 3 - quantified where possible]</li>
</ul>
<p><strong>Why Choose Us:</strong> [Unique value proposition and competitive advantages]</p>
<p><strong>Investment:</strong> [Total investment and value for money statement]</p>
<p><strong>Timeline:</strong> [Key milestones and delivery commitment]</p>`,
    tags: ['executive', 'summary', 'overview', 'benefits'],
    difficulty: 'intermediate',
    wordCount: 350,
    popularity: 95,
    lastUpdated: new Date('2024-01-15'),
    sectionTypes: ['executive_summary', 'overview'],
  },
  {
    id: '2',
    name: 'Technical Capability',
    category: 'Capability',
    description: 'Template for demonstrating technical expertise and capabilities',
    content: `<h2>Technical Capability</h2>
<p><strong>Technical Expertise:</strong> [Overview of technical competencies]</p>
<p><strong>Relevant Experience:</strong></p>
<ul>
  <li><strong>Project Name:</strong> [Brief description, outcomes, and relevance]</li>
  <li><strong>Project Name:</strong> [Brief description, outcomes, and relevance]</li>
  <li><strong>Project Name:</strong> [Brief description, outcomes, and relevance]</li>
</ul>
<p><strong>Technical Resources:</strong></p>
<ul>
  <li>[Resource/Tool 1 and how it benefits the client]</li>
  <li>[Resource/Tool 2 and how it benefits the client]</li>
  <li>[Resource/Tool 3 and how it benefits the client]</li>
</ul>
<p><strong>Quality Assurance:</strong> [QA processes and standards]</p>
<p><strong>Innovation:</strong> [Innovative approaches or technologies we bring]</p>`,
    tags: ['technical', 'capability', 'expertise', 'experience'],
    difficulty: 'advanced',
    wordCount: 450,
    popularity: 88,
    lastUpdated: new Date('2024-01-20'),
    sectionTypes: ['technical_capability', 'expertise'],
  },
  {
    id: '3',
    name: 'Project Management',
    category: 'Management',
    description: 'Project management methodology and approach template',
    content: `<h2>Project Management Approach</h2>
<p><strong>Methodology:</strong> [Project management methodology - Agile, Waterfall, etc.]</p>
<p><strong>Project Structure:</strong></p>
<ul>
  <li><strong>Project Manager:</strong> [Name and key qualifications]</li>
  <li><strong>Team Structure:</strong> [Organizational structure and roles]</li>
  <li><strong>Reporting Lines:</strong> [Clear accountability structure]</li>
</ul>
<p><strong>Project Phases:</strong></p>
<ol>
  <li><strong>Phase 1:</strong> [Description, deliverables, timeline]</li>
  <li><strong>Phase 2:</strong> [Description, deliverables, timeline]</li>
  <li><strong>Phase 3:</strong> [Description, deliverables, timeline]</li>
</ol>
<p><strong>Risk Management:</strong> [Risk identification and mitigation strategies]</p>
<p><strong>Communication Plan:</strong> [Stakeholder communication and reporting schedule]</p>`,
    tags: ['project management', 'methodology', 'structure', 'phases'],
    difficulty: 'intermediate',
    wordCount: 400,
    popularity: 92,
    lastUpdated: new Date('2024-01-18'),
    sectionTypes: ['project_management', 'methodology'],
  },
  {
    id: '4',
    name: 'Team Experience',
    category: 'Team',
    description: 'Template for showcasing team qualifications and experience',
    content: `<h2>Team Experience and Qualifications</h2>
<p><strong>Team Overview:</strong> [Brief introduction to the proposed team]</p>
<p><strong>Key Personnel:</strong></p>
<div>
  <h3>[Name] - [Position]</h3>
  <ul>
    <li><strong>Qualifications:</strong> [Relevant qualifications and certifications]</li>
    <li><strong>Experience:</strong> [Years of experience and key achievements]</li>
    <li><strong>Role:</strong> [Specific role and responsibilities on this project]</li>
  </ul>
</div>
<p><strong>Team Strengths:</strong></p>
<ul>
  <li>[Strength 1 - how it benefits the project]</li>
  <li>[Strength 2 - how it benefits the project]</li>
  <li>[Strength 3 - how it benefits the project]</li>
</ul>
<p><strong>Collaboration Approach:</strong> [How the team works together effectively]</p>
<p><strong>Continuous Development:</strong> [Commitment to ongoing skill development]</p>`,
    tags: ['team', 'experience', 'qualifications', 'personnel'],
    difficulty: 'beginner',
    wordCount: 300,
    popularity: 85,
    lastUpdated: new Date('2024-01-22'),
    sectionTypes: ['team_experience', 'personnel'],
  },
  {
    id: '5',
    name: 'Value Proposition',
    category: 'Value',
    description: 'Template for articulating unique value and benefits',
    content: `<h2>Value Proposition</h2>
<p><strong>Understanding Your Needs:</strong> [Demonstration of client need understanding]</p>
<p><strong>Our Unique Value:</strong></p>
<ul>
  <li><strong>Differentiator 1:</strong> [What makes us unique and the benefit]</li>
  <li><strong>Differentiator 2:</strong> [What makes us unique and the benefit]</li>
  <li><strong>Differentiator 3:</strong> [What makes us unique and the benefit]</li>
</ul>
<p><strong>Quantified Benefits:</strong></p>
<ul>
  <li>[Benefit 1 with specific metrics/savings]</li>
  <li>[Benefit 2 with specific metrics/savings]</li>
  <li>[Benefit 3 with specific metrics/savings]</li>
</ul>
<p><strong>Return on Investment:</strong> [Clear ROI calculation and value statement]</p>
<p><strong>Long-term Partnership:</strong> [Commitment to ongoing relationship and value]</p>`,
    tags: ['value', 'benefits', 'roi', 'differentiators'],
    difficulty: 'intermediate',
    wordCount: 350,
    popularity: 90,
    lastUpdated: new Date('2024-01-25'),
    sectionTypes: ['value_proposition', 'benefits'],
  },
  {
    id: '6',
    name: 'Implementation Timeline',
    category: 'Timeline',
    description: 'Detailed implementation timeline and milestones template',
    content: `<h2>Implementation Timeline</h2>
<p><strong>Project Duration:</strong> [Total project duration and key dates]</p>
<p><strong>Implementation Phases:</strong></p>
<table>
  <tr>
    <th>Phase</th>
    <th>Duration</th>
    <th>Key Activities</th>
    <th>Deliverables</th>
  </tr>
  <tr>
    <td>Phase 1: [Name]</td>
    <td>[Duration]</td>
    <td>[Key activities]</td>
    <td>[Deliverables]</td>
  </tr>
  <tr>
    <td>Phase 2: [Name]</td>
    <td>[Duration]</td>
    <td>[Key activities]</td>
    <td>[Deliverables]</td>
  </tr>
</table>
<p><strong>Critical Milestones:</strong></p>
<ul>
  <li><strong>[Date]:</strong> [Milestone and significance]</li>
  <li><strong>[Date]:</strong> [Milestone and significance]</li>
  <li><strong>[Date]:</strong> [Milestone and significance]</li>
</ul>
<p><strong>Dependencies:</strong> [Key dependencies and mitigation strategies]</p>
<p><strong>Flexibility:</strong> [How we accommodate changes and client needs]</p>`,
    tags: ['timeline', 'implementation', 'milestones', 'schedule'],
    difficulty: 'intermediate',
    wordCount: 400,
    popularity: 87,
    lastUpdated: new Date('2024-01-20'),
    sectionTypes: ['timeline', 'implementation'],
  },
];

export default function SectionTemplates({
  sectionType,
  onSelect,
  onClose,
}: SectionTemplatesProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [selectedDifficulty, setSelectedDifficulty] = useState('All');
  const [previewTemplate, setPreviewTemplate] = useState<SectionTemplate | null>(null);

  const categories = useMemo(() => {
    const cats = ['All', ...new Set(templates.map(t => t.category))];
    return cats;
  }, []);

  const filteredTemplates = useMemo(() => {
    return templates.filter(template => {
      const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          template.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'All' || template.category === selectedCategory;
      const matchesDifficulty = selectedDifficulty === 'All' || template.difficulty === selectedDifficulty;
      const matchesSectionType = !sectionType || template.sectionTypes.includes(sectionType);
      
      return matchesSearch && matchesCategory && matchesDifficulty && matchesSectionType;
    });
  }, [searchTerm, selectedCategory, selectedDifficulty, sectionType]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner':
        return 'text-green-400 bg-green-900/20';
      case 'intermediate':
        return 'text-yellow-400 bg-yellow-900/20';
      case 'advanced':
        return 'text-red-400 bg-red-900/20';
      default:
        return 'text-gray-400 bg-gray-900/20';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Summary':
        return FileText;
      case 'Capability':
        return Target;
      case 'Management':
        return Settings;
      case 'Team':
        return Users;
      case 'Value':
        return DollarSign;
      case 'Timeline':
        return Clock;
      default:
        return Template;
    }
  };

  const handleTemplateSelect = (template: SectionTemplate) => {
    onSelect(template);
    onClose();
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4 max-h-[600px] overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center space-x-2">
          <Template size={20} />
          <span>Section Templates</span>
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white p-1"
        >
          <X size={16} />
        </button>
      </div>

      {/* Search and Filters */}
      <div className="mb-4 space-y-3">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-3 text-gray-400" />
          <input
            type="text"
            placeholder="Search templates..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-gray-800 border border-gray-600 rounded-lg text-sm focus:outline-none focus:border-blue-400"
          />
        </div>

        <div className="flex space-x-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>

          <select
            value={selectedDifficulty}
            onChange={(e) => setSelectedDifficulty(e.target.value)}
            className="px-3 py-2 bg-gray-800 border border-gray-600 rounded text-sm"
          >
            <option value="All">All Levels</option>
            <option value="beginner">Beginner</option>
            <option value="intermediate">Intermediate</option>
            <option value="advanced">Advanced</option>
          </select>
        </div>
      </div>

      {/* Template List */}
      <div className="space-y-3">
        {filteredTemplates.length === 0 ? (
          <div className="text-center text-gray-400 py-8">
            <Template size={48} className="mx-auto mb-4 opacity-50" />
            <p>No templates found matching your criteria</p>
          </div>
        ) : (
          filteredTemplates.map((template) => {
            const CategoryIcon = getCategoryIcon(template.category);
            return (
              <div
                key={template.id}
                className="bg-gray-800 rounded-lg p-4 border border-gray-700 hover:border-gray-600 transition-all"
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <CategoryIcon size={16} className="text-blue-400" />
                    <h4 className="font-semibold text-white">{template.name}</h4>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-xs px-2 py-1 rounded ${getDifficultyColor(template.difficulty)}`}>
                      {template.difficulty}
                    </span>
                    <div className="flex items-center space-x-1 text-xs text-gray-400">
                      <Star size={12} />
                      <span>{template.popularity}</span>
                    </div>
                  </div>
                </div>

                <p className="text-sm text-gray-300 mb-3">{template.description}</p>

                <div className="flex items-center justify-between text-xs text-gray-400 mb-3">
                  <div className="flex items-center space-x-4">
                    <span>{template.wordCount} words</span>
                    <span>Category: {template.category}</span>
                  </div>
                  <span>Updated: {template.lastUpdated.toLocaleDateString()}</span>
                </div>

                <div className="flex flex-wrap gap-1 mb-3">
                  {template.tags.map((tag) => (
                    <span
                      key={tag}
                      className="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <button
                    onClick={() => setPreviewTemplate(template)}
                    className="flex items-center space-x-1 text-sm text-blue-400 hover:text-blue-300"
                  >
                    <Eye size={14} />
                    <span>Preview</span>
                  </button>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => navigator.clipboard.writeText(template.content)}
                      className="flex items-center space-x-1 px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm"
                    >
                      <Copy size={14} />
                      <span>Copy</span>
                    </button>
                    <button
                      onClick={() => handleTemplateSelect(template)}
                      className="flex items-center space-x-1 px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
                    >
                      <Template size={14} />
                      <span>Use Template</span>
                    </button>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>

      {/* Preview Modal */}
      {previewTemplate && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-hidden">
            <div className="p-4 border-b border-gray-700 flex items-center justify-between">
              <h3 className="text-lg font-bold">Preview: {previewTemplate.name}</h3>
              <button
                onClick={() => setPreviewTemplate(null)}
                className="text-gray-400 hover:text-white p-1"
              >
                <X size={16} />
              </button>
            </div>
            <div className="p-4 overflow-y-auto max-h-96">
              <div
                className="prose prose-sm max-w-none text-gray-300"
                dangerouslySetInnerHTML={{ __html: previewTemplate.content }}
              />
            </div>
            <div className="p-4 border-t border-gray-700 flex justify-end space-x-2">
              <button
                onClick={() => setPreviewTemplate(null)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
              >
                Close
              </button>
              <button
                onClick={() => handleTemplateSelect(previewTemplate)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm"
              >
                Use This Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}