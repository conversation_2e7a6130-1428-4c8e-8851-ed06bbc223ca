"use client";

import { ConvexAuthProvider } from "@convex-dev/auth/react";
import { ConvexReactClient, ConvexProvider } from "convex/react";
import { Toaster, toast } from "sonner";
import { useEffect, useState } from "react";

// Check if Convex URL is properly configured
const convexUrl = process.env.NEXT_PUBLIC_CONVEX_URL;
const isPlaceholderUrl = !convexUrl || convexUrl === "https://your-convex-deployment.convex.cloud";

if (isPlaceholderUrl) {
  console.error("⚠️ Convex URL not configured. Please run 'npx convex dev' to set up your Convex deployment.");
}

// Initialize Convex client
const convex = new ConvexReactClient(convexUrl || "");

export function ConvexClientProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [showSetupError, setShowSetupError] = useState(false);

  useEffect(() => {
    if (isPlaceholderUrl) {
      setShowSetupError(true);
    }
  }, []);

  if (showSetupError) {
    return (
      <div className="flex items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-lg w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8">
          <div className="flex items-center mb-4">
            <svg className="w-8 h-8 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Convex Setup Required
            </h2>
          </div>
          
          <div className="space-y-4">
            <p className="text-gray-600 dark:text-gray-300">
              The application needs to be connected to a Convex deployment. Please follow these steps:
            </p>
            
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                1. Run in your terminal:
              </p>
              <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto">
                npx convex dev
              </pre>
            </div>
            
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                2. Update your .env file with the deployment URL:
              </p>
              <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto">
                NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud
              </pre>
            </div>
            
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-200 mb-2">
                3. Restart your development server:
              </p>
              <pre className="bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto">
                npm run dev
              </pre>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <strong>Need help?</strong> Check the{" "}
              <a href="https://docs.convex.dev/quickstart" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-800 dark:hover:text-blue-200">
                Convex documentation
              </a>{" "}
              for detailed setup instructions.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ConvexAuthProvider client={convex}>
      <ConvexProvider client={convex}>
        {children}
        <Toaster />
      </ConvexProvider>
    </ConvexAuthProvider>
  );
}