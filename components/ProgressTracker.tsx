'use client';

import React, { useMemo } from 'react';
import {
  Target,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  X,
  Award,
  Users,
  FileText,
} from 'lucide-react';

interface Section {
  _id: string;
  title: string;
  content: string;
  wordCount: number;
  wordLimit: number;
  scoreWeight: number;
  status: string;
  priority?: string;
  lastModified?: number;
}

interface ProgressTrackerProps {
  sections: Section[];
  activeSectionId: string | null;
  onClose: () => void;
}

export default function ProgressTracker({
  sections,
  activeSectionId,
  onClose,
}: ProgressTrackerProps) {
  const stats = useMemo(() => {
    const totalSections = sections.length;
    const completedSections = sections.filter(s => s.status === 'approved').length;
    const inProgressSections = sections.filter(s => s.status === 'in_progress').length;
    const reviewSections = sections.filter(s => s.status === 'review').length;
    const needsRevisionSections = sections.filter(s => s.status === 'needs_revision').length;
    const notStartedSections = sections.filter(s => s.status === 'not_started').length;

    const totalWords = sections.reduce((sum, section) => sum + section.wordCount, 0);
    const totalWordLimit = sections.reduce((sum, section) => sum + (section.wordLimit || 0), 0);
    const totalWeight = sections.reduce((sum, section) => sum + section.scoreWeight, 0);
    const completedWeight = sections
      .filter(s => s.status === 'approved')
      .reduce((sum, section) => sum + section.scoreWeight, 0);

    const overallProgress = totalSections > 0 ? (completedSections / totalSections) * 100 : 0;
    const weightedProgress = totalWeight > 0 ? (completedWeight / totalWeight) * 100 : 0;
    const wordProgress = totalWordLimit > 0 ? (totalWords / totalWordLimit) * 100 : 0;

    return {
      totalSections,
      completedSections,
      inProgressSections,
      reviewSections,
      needsRevisionSections,
      notStartedSections,
      totalWords,
      totalWordLimit,
      totalWeight,
      completedWeight,
      overallProgress,
      weightedProgress,
      wordProgress,
    };
  }, [sections]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400';
      case 'review':
        return 'text-blue-400';
      case 'needs_revision':
        return 'text-red-400';
      case 'in_progress':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return CheckCircle;
      case 'review':
        return Clock;
      case 'needs_revision':
        return AlertCircle;
      case 'in_progress':
        return TrendingUp;
      default:
        return FileText;
    }
  };

  const getPriorityColor = (priority?: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-400';
      case 'medium':
        return 'border-yellow-400';
      case 'low':
        return 'border-green-400';
      default:
        return 'border-gray-600';
    }
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center space-x-2">
          <Target size={20} />
          <span>Progress Tracker</span>
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white p-1"
        >
          <X size={16} />
        </button>
      </div>

      {/* Overall Progress */}
      <div className="mb-6">
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-gray-800 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-400">Section Progress</span>
              <BarChart3 size={16} className="text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-blue-400">
              {Math.round(stats.overallProgress)}%
            </div>
            <div className="text-xs text-gray-500">
              {stats.completedSections} of {stats.totalSections} sections
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-gray-400">Weighted Score</span>
              <Award size={16} className="text-green-400" />
            </div>
            <div className="text-2xl font-bold text-green-400">
              {Math.round(stats.weightedProgress)}%
            </div>
            <div className="text-xs text-gray-500">
              {stats.completedWeight} of {stats.totalWeight} points
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-400 mb-1">
            <span>Overall Progress</span>
            <span>{Math.round(stats.overallProgress)}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-blue-400 to-green-400 h-3 rounded-full transition-all duration-300"
              style={{ width: `${stats.overallProgress}%` }}
            />
          </div>
        </div>

        {/* Word Count Progress */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-400 mb-1">
            <span>Word Count</span>
            <span>{stats.totalWords} / {stats.totalWordLimit || '∞'}</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-purple-400 h-2 rounded-full transition-all duration-300"
              style={{ width: `${Math.min(stats.wordProgress, 100)}%` }}
            />
          </div>
        </div>
      </div>

      {/* Status Distribution */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold mb-3 text-gray-300">Status Distribution</h4>
        <div className="space-y-2">
          {[
            { status: 'approved', label: 'Approved', count: stats.completedSections },
            { status: 'review', label: 'In Review', count: stats.reviewSections },
            { status: 'in_progress', label: 'In Progress', count: stats.inProgressSections },
            { status: 'needs_revision', label: 'Needs Revision', count: stats.needsRevisionSections },
            { status: 'not_started', label: 'Not Started', count: stats.notStartedSections },
          ].map(({ status, label, count }) => {
            const StatusIcon = getStatusIcon(status);
            return (
              <div key={status} className="flex items-center justify-between text-sm">
                <div className="flex items-center space-x-2">
                  <StatusIcon size={16} className={getStatusColor(status)} />
                  <span className="text-gray-300">{label}</span>
                </div>
                <span className="text-gray-400">{count}</span>
              </div>
            );
          })}
        </div>
      </div>

      {/* Section List */}
      <div className="mb-4">
        <h4 className="text-sm font-semibold mb-3 text-gray-300">Sections</h4>
        <div className="space-y-2 max-h-64 overflow-y-auto">
          {sections.map((section) => {
            const StatusIcon = getStatusIcon(section.status);
            const isActive = section._id === activeSectionId;
            const completionPercentage = section.wordLimit
              ? Math.min((section.wordCount / section.wordLimit) * 100, 100)
              : section.wordCount > 0 ? 100 : 0;

            return (
              <div
                key={section._id}
                className={`p-3 rounded-lg border-l-4 ${
                  isActive ? 'bg-gray-800 border-l-blue-400' : 'bg-gray-800/50'
                } ${getPriorityColor(section.priority)}`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <StatusIcon size={14} className={getStatusColor(section.status)} />
                    <span className="text-sm font-medium text-gray-200 truncate">
                      {section.title}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-xs text-gray-400">
                      {section.scoreWeight}%
                    </span>
                    {section.priority && (
                      <span className={`text-xs px-2 py-1 rounded ${
                        section.priority === 'high' ? 'bg-red-900/20 text-red-400' :
                        section.priority === 'medium' ? 'bg-yellow-900/20 text-yellow-400' :
                        'bg-green-900/20 text-green-400'
                      }`}>
                        {section.priority}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center justify-between text-xs text-gray-400 mb-1">
                  <span>{section.wordCount} / {section.wordLimit || '∞'} words</span>
                  <span>{Math.round(completionPercentage)}%</span>
                </div>
                
                <div className="w-full bg-gray-700 rounded-full h-1">
                  <div
                    className={`h-1 rounded-full transition-all ${
                      section.status === 'approved' ? 'bg-green-400' :
                      section.status === 'review' ? 'bg-blue-400' :
                      section.status === 'in_progress' ? 'bg-yellow-400' :
                      section.status === 'needs_revision' ? 'bg-red-400' :
                      'bg-gray-600'
                    }`}
                    style={{ width: `${completionPercentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="border-t border-gray-700 pt-4">
        <div className="grid grid-cols-2 gap-2">
          <button
            className="flex items-center space-x-2 p-2 bg-gray-800 hover:bg-gray-700 rounded text-sm"
            onClick={() => {
              // Filter to show only incomplete sections
              console.log('Filter incomplete sections');
            }}
          >
            <AlertCircle size={14} />
            <span>Show Incomplete</span>
          </button>
          <button
            className="flex items-center space-x-2 p-2 bg-gray-800 hover:bg-gray-700 rounded text-sm"
            onClick={() => {
              // Sort by priority
              console.log('Sort by priority');
            }}
          >
            <TrendingUp size={14} />
            <span>Sort by Priority</span>
          </button>
        </div>
      </div>
    </div>
  );
}