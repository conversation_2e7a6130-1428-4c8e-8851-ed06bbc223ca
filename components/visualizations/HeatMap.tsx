"use client";

import React, { useEffect, useRef } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface Location {
  name: string;
  address: string;
  latitude: number;
  longitude: number;
  heat: number;
  status?: string;
  popup?: {
    title: string;
    details: string[];
  };
}

interface HeatMapProps {
  locations: Location[];
  center?: { lat: number; lng: number };
  zoom?: number;
  height?: string;
}

export function HeatMap({
  locations,
  center = { lat: -37.8136, lng: 144.9631 },
  zoom = 12,
  height = "400px",
}: HeatMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // In a real implementation, this would integrate with Google Maps or Mapbox
    // For now, we'll create a placeholder visualization
    if (mapRef.current) {
      // Initialize map here
    }
  }, [locations, center, zoom]);

  // Calculate statistics
  const totalLocations = locations.length;
  const activeLocations = locations.filter((loc) => loc.status === "active").length;
  const averageHeat = locations.reduce((sum, loc) => sum + loc.heat, 0) / totalLocations || 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Service Location Heat Map</CardTitle>
          <p className="text-sm text-gray-600 mt-1">
            Showing {totalLocations} locations across Melbourne CBD
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant="secondary">{activeLocations} Active</Badge>
          <Badge variant="outline">
            Avg Heat: {averageHeat.toFixed(0)}%
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {/* Map Container */}
        <div
          ref={mapRef}
          className="relative bg-gray-100 rounded-lg overflow-hidden"
          style={{ height }}
        >
          {/* Placeholder Map Visualization */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <svg
                className="w-full h-full max-w-md"
                viewBox="0 0 400 300"
                xmlns="http://www.w3.org/2000/svg"
              >
                {/* Simple grid background */}
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path
                    d="M 40 0 L 0 0 0 40"
                    fill="none"
                    stroke="rgba(0,0,0,0.05)"
                    strokeWidth="1"
                  />
                </pattern>
                <rect width="100%" height="100%" fill="url(#grid)" />

                {/* Plot locations as circles with heat intensity */}
                {locations.map((location, index) => {
                  // Convert lat/lng to x/y coordinates (simplified)
                  const x = ((location.longitude - 144.9) / 0.2) * 400;
                  const y = 300 - ((location.latitude + 38) / 0.3) * 300;
                  const radius = 10 + (location.heat / 100) * 20;
                  const opacity = 0.3 + (location.heat / 100) * 0.5;
                  const color =
                    location.heat > 75
                      ? "#EF4444"
                      : location.heat > 50
                      ? "#F59E0B"
                      : location.heat > 25
                      ? "#FCD34D"
                      : "#86EFAC";

                  return (
                    <g key={index}>
                      <circle
                        cx={x}
                        cy={y}
                        r={radius}
                        fill={color}
                        opacity={opacity}
                        className="cursor-pointer hover:opacity-100 transition-opacity"
                      />
                      <circle
                        cx={x}
                        cy={y}
                        r="3"
                        fill="#1F2937"
                        opacity="0.8"
                      />
                    </g>
                  );
                })}
              </svg>
            </div>
          </div>

          {/* Legend */}
          <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-lg p-3">
            <h4 className="text-sm font-semibold mb-2">Service Intensity</h4>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: "#86EFAC" }} />
                <span className="text-xs">Light (0-25%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: "#FCD34D" }} />
                <span className="text-xs">Moderate (26-50%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: "#F59E0B" }} />
                <span className="text-xs">High (51-75%)</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 rounded" style={{ backgroundColor: "#EF4444" }} />
                <span className="text-xs">Critical (76-100%)</span>
              </div>
            </div>
          </div>

          {/* Controls */}
          <div className="absolute top-4 right-4 flex flex-col gap-2">
            <button className="bg-white rounded-lg shadow p-2 hover:bg-gray-50">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </button>
            <button className="bg-white rounded-lg shadow p-2 hover:bg-gray-50">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
              </svg>
            </button>
          </div>
        </div>

        {/* Location List */}
        <div className="mt-4">
          <h4 className="font-semibold mb-2">Top Service Locations</h4>
          <div className="space-y-2">
            {locations
              .sort((a, b) => b.heat - a.heat)
              .slice(0, 5)
              .map((location, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded-lg"
                >
                  <div>
                    <div className="font-medium text-sm">{location.name}</div>
                    <div className="text-xs text-gray-500">{location.address}</div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div
                      className="w-12 h-2 bg-gray-200 rounded-full overflow-hidden"
                      title={`${location.heat}% intensity`}
                    >
                      <div
                        className="h-full transition-all"
                        style={{
                          width: `${location.heat}%`,
                          backgroundColor:
                            location.heat > 75
                              ? "#EF4444"
                              : location.heat > 50
                              ? "#F59E0B"
                              : location.heat > 25
                              ? "#FCD34D"
                              : "#86EFAC",
                        }}
                      />
                    </div>
                    <span className="text-xs font-medium">{location.heat}%</span>
                  </div>
                </div>
              ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}