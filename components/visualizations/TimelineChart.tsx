"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Flag, CheckCircle, Clock, AlertTriangle } from "lucide-react";

interface TimelineEvent {
  date: number;
  title: string;
  description: string;
  milestone: boolean;
  status?: "completed" | "in_progress" | "upcoming" | "overdue";
  category?: string;
}

interface TimelineChartProps {
  events: TimelineEvent[];
  title?: string;
  showFilters?: boolean;
}

export function TimelineChart({
  events,
  title = "Project Timeline",
  showFilters = true,
}: TimelineChartProps) {
  // Sort events by date
  const sortedEvents = [...events].sort((a, b) => a.date - b.date);

  // Calculate timeline statistics
  const totalEvents = events.length;
  const milestones = events.filter((e) => e.milestone).length;
  const completedEvents = events.filter((e) => e.status === "completed").length;
  const upcomingEvents = events.filter((e) => e.status === "upcoming" || !e.status).length;

  // Determine event status if not provided
  const enrichedEvents = sortedEvents.map((event) => {
    if (!event.status) {
      const now = Date.now();
      if (event.date < now) {
        return { ...event, status: "completed" as const };
      } else {
        return { ...event, status: "upcoming" as const };
      }
    }
    return event;
  });

  // Get date range
  const startDate = Math.min(...events.map((e) => e.date));
  const endDate = Math.max(...events.map((e) => e.date));
  const totalDuration = endDate - startDate;

  // Status configuration
  const statusConfig = {
    completed: {
      color: "text-green-600",
      bgColor: "bg-green-100",
      borderColor: "border-green-300",
      icon: CheckCircle,
    },
    in_progress: {
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      borderColor: "border-blue-300",
      icon: Clock,
    },
    upcoming: {
      color: "text-gray-600",
      bgColor: "bg-gray-100",
      borderColor: "border-gray-300",
      icon: Calendar,
    },
    overdue: {
      color: "text-red-600",
      bgColor: "bg-red-100",
      borderColor: "border-red-300",
      icon: AlertTriangle,
    },
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>{title}</CardTitle>
          <div className="flex gap-2">
            <Badge variant="secondary">{totalEvents} Events</Badge>
            <Badge variant="outline">{milestones} Milestones</Badge>
          </div>
        </div>
        {/* Timeline Stats */}
        <div className="grid grid-cols-4 gap-4 mt-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{completedEvents}</div>
            <div className="text-xs text-gray-500">Completed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {events.filter((e) => e.status === "in_progress").length}
            </div>
            <div className="text-xs text-gray-500">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">{upcomingEvents}</div>
            <div className="text-xs text-gray-500">Upcoming</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">
              {events.filter((e) => e.status === "overdue").length}
            </div>
            <div className="text-xs text-gray-500">Overdue</div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {/* Timeline Visualization */}
        <div className="relative">
          {/* Main timeline line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gray-300" />

          <div className="space-y-6">
            {enrichedEvents.map((event, index) => {
              const config = statusConfig[event.status];
              const Icon = config.icon;
              const isMilestone = event.milestone;

              // Calculate position along timeline
              const position = totalDuration > 0 ? ((event.date - startDate) / totalDuration) * 100 : 0;

              return (
                <div key={index} className="relative flex items-start gap-4">
                  {/* Timeline node */}
                  <div
                    className={`relative z-10 flex items-center justify-center w-8 h-8 rounded-full border-2 ${config.borderColor} ${config.bgColor}`}
                  >
                    {isMilestone ? (
                      <Flag className={`w-4 h-4 ${config.color}`} />
                    ) : (
                      <Icon className={`w-4 h-4 ${config.color}`} />
                    )}
                  </div>

                  {/* Event content */}
                  <div className="flex-1 min-w-0 pb-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-semibold text-sm">{event.title}</h4>
                          {isMilestone && (
                            <Badge variant="secondary" className="text-xs">
                              Milestone
                            </Badge>
                          )}
                          <Badge
                            variant={event.status === "completed" ? "success" : "outline"}
                            className="text-xs capitalize"
                          >
                            {event.status?.replace("_", " ")}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mb-2">{event.description}</p>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            {new Date(event.date).toLocaleDateString()}
                          </span>
                          {event.category && (
                            <Badge variant="outline" className="text-xs">
                              {event.category}
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Progress bar for this event */}
                    <div className="mt-2 h-1 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className={`h-full transition-all duration-500 ${
                          event.status === "completed"
                            ? "bg-green-500"
                            : event.status === "in_progress"
                            ? "bg-blue-500"
                            : event.status === "overdue"
                            ? "bg-red-500"
                            : "bg-gray-400"
                        }`}
                        style={{
                          width:
                            event.status === "completed"
                              ? "100%"
                              : event.status === "in_progress"
                              ? "50%"
                              : "0%",
                        }}
                      />
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Timeline Overview */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-semibold mb-3">Timeline Overview</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Project Duration:</span>
              <span className="ml-2 font-medium">
                {Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24))} days
              </span>
            </div>
            <div>
              <span className="text-gray-600">Progress:</span>
              <span className="ml-2 font-medium">
                {totalEvents > 0 ? ((completedEvents / totalEvents) * 100).toFixed(0) : 0}%
              </span>
            </div>
            <div>
              <span className="text-gray-600">Start Date:</span>
              <span className="ml-2 font-medium">{new Date(startDate).toLocaleDateString()}</span>
            </div>
            <div>
              <span className="text-gray-600">End Date:</span>
              <span className="ml-2 font-medium">{new Date(endDate).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Critical Path & Dependencies */}
        {enrichedEvents.some((e) => e.milestone) && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">Key Milestones</h4>
            <div className="space-y-1">
              {enrichedEvents
                .filter((e) => e.milestone)
                .map((milestone, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm text-blue-700">
                    <Flag className="w-3 h-3" />
                    <span>{milestone.title}</span>
                    <span className="text-blue-600">
                      - {new Date(milestone.date).toLocaleDateString()}
                    </span>
                  </div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}