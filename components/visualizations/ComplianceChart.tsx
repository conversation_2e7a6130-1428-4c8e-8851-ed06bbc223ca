"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, Clock, AlertCircle, XCircle } from "lucide-react";

interface ComplianceItem {
  category: string;
  requirement: string;
  status: "compliant" | "in_progress" | "pending" | "non_compliant";
  priority: "low" | "medium" | "high" | "critical";
  dueDate?: number;
}

interface ComplianceChartProps {
  items: ComplianceItem[];
  title?: string;
}

export function ComplianceChart({ items, title = "Compliance Dashboard" }: ComplianceChartProps) {
  // Calculate statistics
  const totalItems = items.length;
  const compliantItems = items.filter((item) => item.status === "compliant").length;
  const inProgressItems = items.filter((item) => item.status === "in_progress").length;
  const pendingItems = items.filter((item) => item.status === "pending").length;
  const nonCompliantItems = items.filter((item) => item.status === "non_compliant").length;

  const compliancePercentage = totalItems > 0 ? (compliantItems / totalItems) * 100 : 0;

  // Group by category
  const categories = items.reduce((acc, item) => {
    if (!acc[item.category]) {
      acc[item.category] = [];
    }
    acc[item.category].push(item);
    return acc;
  }, {} as Record<string, ComplianceItem[]>);

  // Status colors and icons
  const statusConfig = {
    compliant: {
      color: "text-green-600",
      bgColor: "bg-green-100",
      icon: CheckCircle,
      label: "Compliant",
    },
    in_progress: {
      color: "text-blue-600",
      bgColor: "bg-blue-100",
      icon: Clock,
      label: "In Progress",
    },
    pending: {
      color: "text-yellow-600",
      bgColor: "bg-yellow-100",
      icon: AlertCircle,
      label: "Pending",
    },
    non_compliant: {
      color: "text-red-600",
      bgColor: "bg-red-100",
      icon: XCircle,
      label: "Non-Compliant",
    },
  };

  // Priority colors
  const priorityConfig = {
    low: "border-l-gray-400",
    medium: "border-l-yellow-400",
    high: "border-l-orange-400",
    critical: "border-l-red-500",
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          {title}
          <Badge variant={compliancePercentage >= 90 ? "success" : compliancePercentage >= 70 ? "secondary" : "destructive"}>
            {compliancePercentage.toFixed(0)}% Compliant
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Overview Stats */}
        <div className="grid grid-cols-4 gap-4 mb-6">
          {Object.entries(statusConfig).map(([status, config]) => {
            const count = items.filter((item) => item.status === status).length;
            const Icon = config.icon;
            
            return (
              <div key={status} className={`p-4 rounded-lg ${config.bgColor}`}>
                <div className="flex items-center gap-2 mb-2">
                  <Icon className={`w-5 h-5 ${config.color}`} />
                  <span className="text-sm font-medium">{config.label}</span>
                </div>
                <div className={`text-2xl font-bold ${config.color}`}>{count}</div>
                <div className="text-xs text-gray-600">
                  {totalItems > 0 ? ((count / totalItems) * 100).toFixed(0) : 0}% of total
                </div>
              </div>
            );
          })}
        </div>

        {/* Overall Progress */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Overall Compliance Progress</span>
            <span className="text-sm text-gray-600">{compliancePercentage.toFixed(1)}%</span>
          </div>
          <Progress value={compliancePercentage} className="h-3" />
        </div>

        {/* Category Breakdown */}
        <div className="space-y-4">
          <h4 className="font-semibold">Compliance by Category</h4>
          {Object.entries(categories).map(([category, categoryItems]) => {
            const categoryCompliant = categoryItems.filter((item) => item.status === "compliant").length;
            const categoryTotal = categoryItems.length;
            const categoryPercentage = (categoryCompliant / categoryTotal) * 100;

            return (
              <div key={category} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <h5 className="font-medium">{category}</h5>
                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-600">
                      {categoryCompliant}/{categoryTotal}
                    </span>
                    <Badge variant="outline">{categoryPercentage.toFixed(0)}%</Badge>
                  </div>
                </div>
                
                <Progress value={categoryPercentage} className="mb-3 h-2" />
                
                <div className="space-y-2">
                  {categoryItems.map((item, index) => {
                    const config = statusConfig[item.status];
                    const Icon = config.icon;
                    const isOverdue = item.dueDate && item.dueDate < Date.now() && item.status !== "compliant";

                    return (
                      <div
                        key={index}
                        className={`flex items-start gap-3 p-3 bg-gray-50 rounded-lg border-l-4 ${priorityConfig[item.priority]}`}
                      >
                        <Icon className={`w-4 h-4 mt-0.5 ${config.color}`} />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-sm font-medium">{item.requirement}</p>
                              {item.dueDate && (
                                <p className={`text-xs mt-1 ${isOverdue ? "text-red-600" : "text-gray-500"}`}>
                                  Due: {new Date(item.dueDate).toLocaleDateString()}
                                  {isOverdue && " (Overdue)"}
                                </p>
                              )}
                            </div>
                            <div className="flex items-center gap-1 ml-2">
                              <Badge
                                variant={item.priority === "critical" ? "destructive" : "secondary"}
                                className="text-xs"
                              >
                                {item.priority}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {config.label}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>

        {/* Critical Items Alert */}
        {items.some((item) => item.priority === "critical" && item.status !== "compliant") && (
          <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 mb-2">
              <AlertCircle className="w-5 h-5 text-red-600" />
              <h4 className="font-semibold text-red-800">Critical Items Requiring Attention</h4>
            </div>
            <div className="space-y-2">
              {items
                .filter((item) => item.priority === "critical" && item.status !== "compliant")
                .map((item, index) => (
                  <div key={index} className="text-sm text-red-700">
                    • {item.requirement}
                    {item.dueDate && (
                      <span className="ml-2 text-red-600 font-medium">
                        (Due: {new Date(item.dueDate).toLocaleDateString()})
                      </span>
                    )}
                  </div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}