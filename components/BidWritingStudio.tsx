'use client';

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  FileText,
  Lightbulb,
  RefreshCw,
  Save,
  Clock,
  Target,
  AlertCircle,
  CheckCircle,
  Download,
  Layout as Template,
  History,
  Users,
  Settings,
  Palette,
  ChevronDown,
  ChevronUp,
  Eye,
  EyeOff,
} from 'lucide-react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';
import { useHotkeys } from 'react-hotkeys-hook';
import { format, isAfter, differenceInDays } from 'date-fns';
import TenderUpload from './TenderUpload';
import RichTextEditor from './RichTextEditor';
import SectionTemplates from './SectionTemplates';
import ProgressTracker from './ProgressTracker';
import AIAssistant from './AIAssistant';
import AutoSave from './AutoSave';
import ExportPanel from './ExportPanel';
import CollaborationPanel from './CollaborationPanel';
import { useBidStudioStore } from '../hooks/useBidStudioStore';

interface BidWritingStudioProps {
  className?: string;
}

export default function BidWritingStudio({ className }: BidWritingStudioProps) {
  const tenders = useQuery(api.tenders.list);
  const [activeTenderId, setActiveTenderId] = useState<string | null>(null);
  const [activeSectionId, setActiveSectionId] = useState<string | null>(null);
  const [isGenerating, setIsGenerating] = useState<string | null>(null);
  const [showTemplates, setShowTemplates] = useState(false);
  const [showProgress, setShowProgress] = useState(true);
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [showExportPanel, setShowExportPanel] = useState(false);
  const [showCollaboration, setShowCollaboration] = useState(false);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [isAutoSaving, setIsAutoSaving] = useState(false);

  // Store integration
  const {
    editorContent,
    setEditorContent,
    autoSaveEnabled,
    setAutoSaveEnabled,
    recentVersions,
    addVersion,
    selectedTemplate,
    setSelectedTemplate,
  } = useBidStudioStore();

  // Mutations and actions
  const generateContent = useAction(api.ai.generateContent);
  const updateSectionContent = useMutation(api.bidSections.update);

  // Auto-select first tender
  useEffect(() => {
    if (tenders && tenders.length > 0 && !activeTenderId) {
      setActiveTenderId(tenders[0]._id);
    }
  }, [tenders, activeTenderId]);

  // Get active tender data
  const tenderData = useQuery(
    api.tenders.get,
    activeTenderId ? { id: activeTenderId as any } : 'skip'
  );

  // Auto-select first section
  useEffect(() => {
    if (tenderData && tenderData.bidSections.length > 0) {
      const currentSectionIds = new Set(tenderData.bidSections.map(s => s._id));
      if (!activeSectionId || !currentSectionIds.has(activeSectionId as any)) {
        setActiveSectionId(tenderData.bidSections[0]._id);
      }
    } else if (!tenderData) {
      setActiveSectionId(null);
    }
  }, [tenderData, activeSectionId]);

  const bidSections = useMemo(() => tenderData?.bidSections ?? [], [tenderData]);

  const activeSection = useMemo(() =>
    bidSections.find((s) => s._id === activeSectionId), [bidSections, activeSectionId]
  );

  // Auto-save functionality
  const handleAutoSave = useCallback(async (content: string) => {
    if (!activeSectionId || !autoSaveEnabled) return;
    
    setIsAutoSaving(true);
    try {
      await updateSectionContent({ id: activeSectionId as any, content });
      setLastSaved(new Date());
      addVersion({
        id: Date.now().toString(),
        content,
        timestamp: new Date(),
        sectionId: activeSectionId,
      });
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsAutoSaving(false);
    }
  }, [activeSectionId, autoSaveEnabled, updateSectionContent, addVersion]);

  // Manual save
  const handleSave = useCallback(async () => {
    if (!activeSectionId || !editorContent) return;
    
    try {
      await updateSectionContent({ id: activeSectionId as any, content: editorContent });
      setLastSaved(new Date());
      toast.success('Content saved successfully');
    } catch (error) {
      toast.error('Failed to save content');
      console.error(error);
    }
  }, [activeSectionId, editorContent, updateSectionContent]);

  // AI content generation
  const handleGenerate = async (sectionId: string) => {
    if (!tenderData || !activeSection) return;
    
    setIsGenerating(sectionId);
    try {
      await generateContent({
        sectionId: sectionId as any,
        tenderName: (tenderData as any).name,
        clientName: (tenderData as any).clientName,
        sectionTitle: activeSection.title,
        currentContent: editorContent,
        template: selectedTemplate,
      });
      toast.success(`Enhanced content for "${activeSection.title}" generated!`);
    } catch (error) {
      toast.error('Failed to generate content.');
      console.error(error);
    } finally {
      setIsGenerating(null);
    }
  };

  // Content change handler
  const handleContentChange = useCallback((content: string) => {
    setEditorContent(content);
    
    // Auto-save after 2 seconds of no changes
    if (autoSaveEnabled) {
      const timeoutId = setTimeout(() => {
        handleAutoSave(content);
      }, 2000);
      
      return () => clearTimeout(timeoutId);
    }
  }, [setEditorContent, autoSaveEnabled, handleAutoSave]);

  // Keyboard shortcuts
  useHotkeys('ctrl+s, cmd+s', (e) => {
    e.preventDefault();
    handleSave();
  }, { enableOnFormTags: true });

  useHotkeys('ctrl+g, cmd+g', (e) => {
    e.preventDefault();
    if (activeSection) {
      handleGenerate(activeSection._id);
    }
  }, { enableOnFormTags: true });

  useHotkeys('ctrl+t, cmd+t', (e) => {
    e.preventDefault();
    setShowTemplates(!showTemplates);
  }, { enableOnFormTags: true });

  // Status color helper
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400 bg-green-900/20 border-green-400';
      case 'review':
        return 'text-blue-400 bg-blue-900/20 border-blue-400';
      case 'needs_revision':
        return 'text-red-400 bg-red-900/20 border-red-400';
      case 'in_progress':
        return 'text-yellow-400 bg-yellow-900/20 border-yellow-400';
      default:
        return 'text-gray-400 bg-gray-900/20 border-gray-600';
    }
  };

  // Priority color helper
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-400';
      case 'medium':
        return 'text-yellow-400';
      case 'low':
        return 'text-green-400';
      default:
        return 'text-gray-400';
    }
  };

  // Deadline warning
  const getDeadlineStatus = (dueDate: string) => {
    const deadline = new Date(dueDate);
    const today = new Date();
    const daysLeft = differenceInDays(deadline, today);
    
    if (daysLeft < 0) {
      return { status: 'overdue', color: 'text-red-400', icon: AlertCircle };
    } else if (daysLeft <= 3) {
      return { status: 'urgent', color: 'text-orange-400', icon: AlertCircle };
    } else if (daysLeft <= 7) {
      return { status: 'warning', color: 'text-yellow-400', icon: Clock };
    } else {
      return { status: 'normal', color: 'text-green-400', icon: CheckCircle };
    }
  };

  // Loading states
  if (tenders === undefined) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
      </div>
    );
  }

  if (tenders.length === 0) {
    return <TenderUpload />;
  }

  if (!tenderData) {
    return (
      <div className="flex justify-center items-center h-96">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-white"></div>
      </div>
    );
  }

  const deadlineStatus = getDeadlineStatus((tenderData as any).dueDate);
  const DeadlineIcon = deadlineStatus.icon;

  return (
    <div className={`h-[800px] flex border border-gray-800 rounded-lg ${className}`}>
      {/* Enhanced Sidebar */}
      <div className={`${sidebarCollapsed ? 'w-16' : 'w-80'} bg-gray-900 border-r border-gray-700 flex flex-col rounded-l-lg transition-all duration-300`}>
        {/* Header */}
        <div className="p-4 border-b border-gray-700">
          <div className="flex items-center justify-between mb-2">
            <h2 className={`font-bold ${sidebarCollapsed ? 'text-sm' : 'text-lg'}`}>
              {sidebarCollapsed ? 'BWS' : 'BID WRITING STUDIO'}
            </h2>
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className="p-1 hover:bg-gray-800 rounded"
            >
              {sidebarCollapsed ? <ChevronDown size={16} /> : <ChevronUp size={16} />}
            </button>
          </div>
          
          {!sidebarCollapsed && (
            <>
              <select
                value={activeTenderId ?? ''}
                onChange={(e) => setActiveTenderId(e.target.value)}
                className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-sm mb-2"
              >
                {tenders.map((tender) => (
                  <option key={tender._id} value={tender._id}>
                    {tender.name}
                  </option>
                ))}
              </select>

              {/* Deadline Status */}
              <div className={`flex items-center space-x-2 text-sm ${deadlineStatus.color}`}>
                <DeadlineIcon size={16} />
                <span>Due: {format(new Date((tenderData as any).dueDate), 'MMM dd, yyyy')}</span>
              </div>
            </>
          )}
        </div>

        {/* Quick Actions */}
        {!sidebarCollapsed && (
          <div className="p-4 border-b border-gray-700">
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => setShowTemplates(!showTemplates)}
                className="flex items-center space-x-1 p-2 bg-gray-800 hover:bg-gray-700 rounded text-xs"
              >
                <Template size={14} />
                <span>Templates</span>
              </button>
              <button
                onClick={() => setShowProgress(!showProgress)}
                className="flex items-center space-x-1 p-2 bg-gray-800 hover:bg-gray-700 rounded text-xs"
              >
                <Target size={14} />
                <span>Progress</span>
              </button>
              <button
                onClick={() => setShowAIAssistant(!showAIAssistant)}
                className="flex items-center space-x-1 p-2 bg-gray-800 hover:bg-gray-700 rounded text-xs"
              >
                <Lightbulb size={14} />
                <span>AI Help</span>
              </button>
              <button
                onClick={() => setShowExportPanel(!showExportPanel)}
                className="flex items-center space-x-1 p-2 bg-gray-800 hover:bg-gray-700 rounded text-xs"
              >
                <Download size={14} />
                <span>Export</span>
              </button>
            </div>
          </div>
        )}

        {/* Section List */}
        <div className="flex-1 overflow-y-auto">
          {bidSections.map((section) => (
            <div
              key={section._id}
              onClick={() => setActiveSectionId(section._id)}
              className={`p-4 border-b border-gray-700 cursor-pointer transition-all ${
                activeSectionId === section._id
                  ? 'bg-gray-800 border-l-4 border-l-blue-400'
                  : 'hover:bg-gray-800/50'
              }`}
            >
              {sidebarCollapsed ? (
                <div className="text-center">
                  <div className="w-8 h-8 mx-auto mb-1 bg-gray-700 rounded-full flex items-center justify-center">
                    <FileText size={16} />
                  </div>
                  <div className="text-xs text-gray-400">
                    {section.wordCount}/{section.wordLimit || '∞'}
                  </div>
                </div>
              ) : (
                <>
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="font-bold text-sm">{section.title}</h3>
                    <div className="flex items-center space-x-2">
                      {section.priority && (
                        <span className={`text-xs ${getPriorityColor(section.priority)}`}>
                          ●
                        </span>
                      )}
                      <div
                        className={`px-2 py-1 rounded text-xs font-bold border ${getStatusColor(
                          section.status
                        )}`}
                      >
                        {section.status.toUpperCase()}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-400 mb-3">
                    <span>Words: {section.wordCount}/{section.wordLimit || '∞'}</span>
                    <span>Weight: {section.scoreWeight}%</span>
                  </div>

                  <div className="w-full bg-gray-700 rounded-full h-2 mb-2">
                    <div
                      className={`h-2 rounded-full transition-all ${
                        section.wordCount > 0 ? 'bg-green-400' : 'bg-gray-600'
                      }`}
                      style={{
                        width: section.wordLimit
                          ? `${Math.min(
                              (section.wordCount / section.wordLimit) * 100,
                              100
                            )}%`
                          : section.wordCount > 0
                          ? '100%'
                          : '0%',
                      }}
                    />
                  </div>

                  {section.lastModified && (
                    <div className="text-xs text-gray-500">
                      Updated: {format(new Date(section.lastModified), 'MMM dd, HH:mm')}
                    </div>
                  )}
                </>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col">
        {activeSection ? (
          <>
            {/* Enhanced Header */}
            <div className="p-4 border-b border-gray-700 bg-gray-900">
              <div className="flex items-center justify-between mb-3">
                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-1">
                    {activeSection.title}
                  </h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-400">
                    <span>Weight: {activeSection.scoreWeight}%</span>
                    <span>
                      Words: {activeSection.wordCount}/{activeSection.wordLimit || '∞'}
                    </span>
                    <span>Status: {activeSection.status.toUpperCase()}</span>
                    {activeSection.priority && (
                      <span className={getPriorityColor(activeSection.priority)}>
                        Priority: {activeSection.priority.toUpperCase()}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  {/* Auto-save indicator */}
                  <div className="flex items-center space-x-1 text-xs text-gray-400">
                    {isAutoSaving ? (
                      <>
                        <RefreshCw size={12} className="animate-spin" />
                        <span>Saving...</span>
                      </>
                    ) : lastSaved ? (
                      <>
                        <CheckCircle size={12} className="text-green-400" />
                        <span>Saved {format(lastSaved, 'HH:mm')}</span>
                      </>
                    ) : null}
                  </div>

                  {/* Action buttons */}
                  <button
                    onClick={handleSave}
                    className="flex items-center space-x-1 px-3 py-1 bg-gray-700 text-white rounded text-sm hover:bg-gray-600"
                  >
                    <Save size={14} />
                    <span>Save</span>
                  </button>
                  
                  <button
                    onClick={() => handleGenerate(activeSection._id)}
                    disabled={isGenerating === activeSection._id}
                    className="flex items-center space-x-1 px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
                  >
                    {isGenerating === activeSection._id ? (
                      <RefreshCw size={14} className="animate-spin" />
                    ) : (
                      <Lightbulb size={14} />
                    )}
                    <span>AI Generate</span>
                  </button>
                </div>
              </div>
            </div>

            {/* Content Editor */}
            <div className="flex-1 flex overflow-hidden">
              <div className="flex-1 p-4">
                <RichTextEditor
                  content={editorContent}
                  onChange={handleContentChange}
                  placeholder={`Start writing the ${activeSection.title} section...`}
                  className="h-full"
                />
              </div>

              {/* Side panels */}
              <div className="w-80 border-l border-gray-700 bg-gray-900 flex flex-col">
                {showProgress && (
                  <ProgressTracker
                    sections={bidSections as any}
                    activeSectionId={activeSectionId}
                    onClose={() => setShowProgress(false)}
                  />
                )}
                
                {showTemplates && (
                  <SectionTemplates
                    sectionType={(activeSection as any)?.type}
                    onSelect={setSelectedTemplate}
                    onClose={() => setShowTemplates(false)}
                  />
                )}
                
                {showAIAssistant && (
                  <AIAssistant
                    section={activeSection as any}
                    tender={tenderData as any}
                    onClose={() => setShowAIAssistant(false)}
                  />
                )}
                
                {showExportPanel && (
                  <ExportPanel
                    tender={tenderData as any}
                    sections={bidSections as any}
                    onClose={() => setShowExportPanel(false)}
                  />
                )}
                
                {showCollaboration && (
                  <CollaborationPanel
                    sectionId={activeSection!._id}
                    onClose={() => setShowCollaboration(false)}
                  />
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="flex-1 flex items-center justify-center">
            <div className="text-center">
              <FileText size={48} className="mx-auto mb-4 text-gray-400" />
              <h3 className="text-xl font-bold mb-2">SELECT A BID SECTION</h3>
              <p className="text-gray-400">
                Choose a section from the sidebar to start writing
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Auto-save component */}
      <AutoSave
        enabled={autoSaveEnabled}
        onToggle={setAutoSaveEnabled}
        lastSaved={lastSaved}
        isAutoSaving={isAutoSaving}
      />
    </div>
  );
}