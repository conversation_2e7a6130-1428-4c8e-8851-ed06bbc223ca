'use client';

import React, { useCallback, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Focus from '@tiptap/extension-focus';
import Typography from '@tiptap/extension-typography';
import CharacterCount from '@tiptap/extension-character-count';
import Color from '@tiptap/extension-color';
import TextStyle from '@tiptap/extension-text-style';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import History from '@tiptap/extension-history';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Palette,
  Highlighter,
  Type,
  Minus,
} from 'lucide-react';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  limit?: number;
}

export default function RichTextEditor({
  content,
  onChange,
  placeholder = 'Start writing...',
  className = '',
  limit = 10000,
}: RichTextEditorProps) {
  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        history: false,
      }),
      Placeholder.configure({
        placeholder,
      }),
      Focus.configure({
        className: 'has-focus',
        mode: 'all',
      }),
      Typography,
      CharacterCount.configure({
        limit,
      }),
      Color.configure({
        types: ['textStyle'],
      }),
      TextStyle,
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      History.configure({
        depth: 10,
        newGroupDelay: 1000,
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
    },
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-full p-4 bg-gray-950 text-gray-100 rounded-lg border border-gray-700',
      },
    },
  });

  useEffect(() => {
    if (editor && content !== editor.getHTML()) {
      editor.commands.setContent(content);
    }
  }, [editor, content]);

  const setColor = useCallback((color: string) => {
    if (editor) {
      editor.chain().focus().setColor(color).run();
    }
  }, [editor]);

  const setHighlight = useCallback((color: string) => {
    if (editor) {
      editor.chain().focus().toggleHighlight({ color }).run();
    }
  }, [editor]);

  if (!editor) {
    return null;
  }

  const wordCount = editor.storage.characterCount.words();
  const characterCount = editor.storage.characterCount.characters();

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Toolbar */}
      <div className="border-b border-gray-700 bg-gray-900 p-2 flex flex-wrap items-center gap-1 rounded-t-lg">
        {/* Text Formatting */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-700">
          <button
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('bold') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Bold (Ctrl+B)"
          >
            <Bold size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('italic') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Italic (Ctrl+I)"
          >
            <Italic size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleStrike().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('strike') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Strikethrough"
          >
            <Strikethrough size={16} />
          </button>
        </div>

        {/* Lists */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-700">
          <button
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('bulletList') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Bullet List"
          >
            <List size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('orderedList') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Numbered List"
          >
            <ListOrdered size={16} />
          </button>
        </div>

        {/* Alignment */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-700">
          <button
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive({ textAlign: 'left' }) ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Align Left"
          >
            <AlignLeft size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive({ textAlign: 'center' }) ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Align Center"
          >
            <AlignCenter size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive({ textAlign: 'right' }) ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Align Right"
          >
            <AlignRight size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive({ textAlign: 'justify' }) ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Justify"
          >
            <AlignJustify size={16} />
          </button>
        </div>

        {/* Block Formats */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-700">
          <button
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={`p-2 rounded hover:bg-gray-800 ${
              editor.isActive('blockquote') ? 'bg-gray-800 text-blue-400' : 'text-gray-300'
            }`}
            title="Quote"
          >
            <Quote size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().setHorizontalRule().run()}
            className="p-2 rounded hover:bg-gray-800 text-gray-300"
            title="Horizontal Rule"
          >
            <Minus size={16} />
          </button>
        </div>

        {/* Colors */}
        <div className="flex items-center gap-1 pr-2 border-r border-gray-700">
          <div className="relative group">
            <button
              className="p-2 rounded hover:bg-gray-800 text-gray-300"
              title="Text Color"
            >
              <Palette size={16} />
            </button>
            <div className="absolute top-full left-0 mt-1 hidden group-hover:block bg-gray-800 border border-gray-700 rounded p-2 z-10">
              <div className="grid grid-cols-4 gap-1">
                {['#ffffff', '#000000', '#ef4444', '#3b82f6', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'].map((color) => (
                  <button
                    key={color}
                    onClick={() => setColor(color)}
                    className="w-6 h-6 rounded border-2 border-gray-600 hover:border-gray-400"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
          </div>
          <div className="relative group">
            <button
              className="p-2 rounded hover:bg-gray-800 text-gray-300"
              title="Highlight"
            >
              <Highlighter size={16} />
            </button>
            <div className="absolute top-full left-0 mt-1 hidden group-hover:block bg-gray-800 border border-gray-700 rounded p-2 z-10">
              <div className="grid grid-cols-4 gap-1">
                {['#fef3c7', '#dbeafe', '#dcfce7', '#fce7f3', '#f3e8ff', '#fed7e2', '#fef2e2', '#f0f0f0'].map((color) => (
                  <button
                    key={color}
                    onClick={() => setHighlight(color)}
                    className="w-6 h-6 rounded border-2 border-gray-600 hover:border-gray-400"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* History */}
        <div className="flex items-center gap-1">
          <button
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            className="p-2 rounded hover:bg-gray-800 text-gray-300 disabled:opacity-50"
            title="Undo (Ctrl+Z)"
          >
            <Undo size={16} />
          </button>
          <button
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            className="p-2 rounded hover:bg-gray-800 text-gray-300 disabled:opacity-50"
            title="Redo (Ctrl+Y)"
          >
            <Redo size={16} />
          </button>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-y-auto bg-gray-950 rounded-b-lg">
        <EditorContent 
          editor={editor}
          className="h-full"
        />
      </div>

      {/* Status Bar */}
      <div className="border-t border-gray-700 bg-gray-900 p-2 flex items-center justify-between text-sm text-gray-400">
        <div className="flex items-center space-x-4">
          <span>Words: {wordCount}</span>
          <span>Characters: {characterCount}</span>
          {limit && (
            <span className={characterCount > limit ? 'text-red-400' : ''}>
              Limit: {limit}
            </span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <span>Ctrl+S to save</span>
          <span>•</span>
          <span>Ctrl+G to generate</span>
        </div>
      </div>
    </div>
  );
}