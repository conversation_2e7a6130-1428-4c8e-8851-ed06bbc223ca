'use client';

import React, { useState, useEffect } from 'react';
import {
  Users,
  X,
  MessageCircle,
  UserPlus,
  Edit,
  Eye,
  Clock,
  CheckCircle,
  AlertCircle,
  Settings,
  Bell,
  Share2,
  Lock,
  Unlock,
  Crown,
  Shield,
  Send,
  AtSign,
} from 'lucide-react';
import { format } from 'date-fns';

interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role: 'owner' | 'editor' | 'reviewer' | 'viewer';
  lastSeen: Date;
  isOnline: boolean;
}

interface Comment {
  id: string;
  userId: string;
  content: string;
  timestamp: Date;
  resolved: boolean;
  mentions: string[];
  position?: {
    start: number;
    end: number;
  };
}

interface Activity {
  id: string;
  userId: string;
  type: 'edit' | 'comment' | 'status_change' | 'share';
  description: string;
  timestamp: Date;
}

interface CollaborationPanelProps {
  sectionId: string;
  onClose: () => void;
}

const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'owner',
    lastSeen: new Date(),
    isOnline: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'editor',
    lastSeen: new Date(Date.now() - 300000), // 5 minutes ago
    isOnline: false,
  },
  {
    id: '3',
    name: 'Mike Chen',
    email: '<EMAIL>',
    role: 'reviewer',
    lastSeen: new Date(Date.now() - 600000), // 10 minutes ago
    isOnline: true,
  },
];

const mockComments: Comment[] = [
  {
    id: '1',
    userId: '2',
    content: 'This section needs more detail about our technical capabilities.',
    timestamp: new Date(Date.now() - 3600000),
    resolved: false,
    mentions: ['1'],
  },
  {
    id: '2',
    userId: '3',
    content: 'Great improvement! The value proposition is much clearer now.',
    timestamp: new Date(Date.now() - 1800000),
    resolved: true,
    mentions: [],
  },
];

const mockActivities: Activity[] = [
  {
    id: '1',
    userId: '1',
    type: 'edit',
    description: 'Updated section content',
    timestamp: new Date(Date.now() - 900000),
  },
  {
    id: '2',
    userId: '2',
    type: 'comment',
    description: 'Added a comment',
    timestamp: new Date(Date.now() - 3600000),
  },
  {
    id: '3',
    userId: '3',
    type: 'status_change',
    description: 'Changed status to "Review"',
    timestamp: new Date(Date.now() - 7200000),
  },
];

export default function CollaborationPanel({ sectionId, onClose }: CollaborationPanelProps) {
  const [activeTab, setActiveTab] = useState<'users' | 'comments' | 'activity'>('users');
  const [users, setUsers] = useState<User[]>(mockUsers);
  const [comments, setComments] = useState<Comment[]>(mockComments);
  const [activities, setActivities] = useState<Activity[]>(mockActivities);
  const [newComment, setNewComment] = useState('');
  const [isLocked, setIsLocked] = useState(false);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState<'editor' | 'reviewer' | 'viewer'>('viewer');

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner':
        return Crown;
      case 'editor':
        return Edit;
      case 'reviewer':
        return Eye;
      case 'viewer':
        return Eye;
      default:
        return Users;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'owner':
        return 'text-yellow-400';
      case 'editor':
        return 'text-blue-400';
      case 'reviewer':
        return 'text-green-400';
      case 'viewer':
        return 'text-gray-400';
      default:
        return 'text-gray-400';
    }
  };

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    const comment: Comment = {
      id: Date.now().toString(),
      userId: '1', // Current user
      content: newComment,
      timestamp: new Date(),
      resolved: false,
      mentions: [], // Would extract @mentions from content
    };

    setComments(prev => [comment, ...prev]);
    setNewComment('');

    // Add activity
    const activity: Activity = {
      id: Date.now().toString(),
      userId: '1',
      type: 'comment',
      description: 'Added a comment',
      timestamp: new Date(),
    };
    setActivities(prev => [activity, ...prev]);
  };

  const handleResolveComment = (commentId: string) => {
    setComments(prev => 
      prev.map(comment => 
        comment.id === commentId 
          ? { ...comment, resolved: !comment.resolved }
          : comment
      )
    );
  };

  const handleInviteUser = () => {
    if (!inviteEmail.trim()) return;

    const newUser: User = {
      id: Date.now().toString(),
      name: inviteEmail.split('@')[0],
      email: inviteEmail,
      role: inviteRole,
      lastSeen: new Date(),
      isOnline: false,
    };

    setUsers(prev => [...prev, newUser]);
    setInviteEmail('');
    setInviteRole('viewer');
    setShowInviteModal(false);
  };

  const handleRemoveUser = (userId: string) => {
    setUsers(prev => prev.filter(user => user.id !== userId));
  };

  const handleChangeUserRole = (userId: string, newRole: User['role']) => {
    setUsers(prev => 
      prev.map(user => 
        user.id === userId 
          ? { ...user, role: newRole }
          : user
      )
    );
  };

  const getUserName = (userId: string) => {
    const user = users.find(u => u.id === userId);
    return user ? user.name : 'Unknown User';
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'edit':
        return Edit;
      case 'comment':
        return MessageCircle;
      case 'status_change':
        return CheckCircle;
      case 'share':
        return Share2;
      default:
        return Clock;
    }
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4 max-h-[600px] overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center space-x-2">
          <Users size={20} />
          <span>Collaboration</span>
        </h3>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setIsLocked(!isLocked)}
            className={`p-1 rounded ${isLocked ? 'text-red-400' : 'text-gray-400'}`}
            title={isLocked ? 'Section is locked' : 'Section is unlocked'}
          >
            {isLocked ? <Lock size={16} /> : <Unlock size={16} />}
          </button>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white p-1"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Status */}
      <div className="mb-4 p-3 bg-gray-800 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
            <span className="text-sm text-gray-300">
              {users.filter(u => u.isOnline).length} users online
            </span>
          </div>
          <div className="flex items-center space-x-2">
            {isLocked && (
              <span className="text-xs bg-red-900/20 text-red-400 px-2 py-1 rounded">
                LOCKED
              </span>
            )}
            <span className="text-xs text-gray-400">
              Last updated: {format(new Date(), 'HH:mm')}
            </span>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 mb-4 bg-gray-800 rounded-lg p-1">
        {[
          { id: 'users', label: 'Users', icon: Users },
          { id: 'comments', label: 'Comments', icon: MessageCircle },
          { id: 'activity', label: 'Activity', icon: Clock },
        ].map(({ id, label, icon: Icon }) => (
          <button
            key={id}
            onClick={() => setActiveTab(id as any)}
            className={`flex-1 flex items-center justify-center space-x-2 py-2 px-3 rounded-md text-sm transition-all ${
              activeTab === id
                ? 'bg-blue-600 text-white'
                : 'text-gray-400 hover:text-gray-300'
            }`}
          >
            <Icon size={16} />
            <span>{label}</span>
            {id === 'comments' && comments.filter(c => !c.resolved).length > 0 && (
              <span className="bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs">
                {comments.filter(c => !c.resolved).length}
              </span>
            )}
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="space-y-4">
        {/* Users Tab */}
        {activeTab === 'users' && (
          <div>
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-sm font-semibold text-gray-300">Team Members</h4>
              <button
                onClick={() => setShowInviteModal(true)}
                className="flex items-center space-x-1 px-2 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm"
              >
                <UserPlus size={14} />
                <span>Invite</span>
              </button>
            </div>

            <div className="space-y-2">
              {users.map(user => {
                const RoleIcon = getRoleIcon(user.role);
                return (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 bg-gray-800 rounded-lg"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        <div className="w-8 h-8 bg-gray-700 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium">
                            {user.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                        {user.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-green-400 rounded-full border-2 border-gray-800" />
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-white">{user.name}</div>
                        <div className="text-xs text-gray-400">{user.email}</div>
                        <div className="text-xs text-gray-500">
                          {user.isOnline ? 'Online' : `Last seen ${format(user.lastSeen, 'MMM dd, HH:mm')}`}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RoleIcon size={16} className={getRoleColor(user.role)} />
                      <select
                        value={user.role}
                        onChange={(e) => handleChangeUserRole(user.id, e.target.value as User['role'])}
                        className="text-xs bg-gray-700 border border-gray-600 rounded px-2 py-1"
                        disabled={user.role === 'owner'}
                      >
                        <option value="owner">Owner</option>
                        <option value="editor">Editor</option>
                        <option value="reviewer">Reviewer</option>
                        <option value="viewer">Viewer</option>
                      </select>
                      {user.role !== 'owner' && (
                        <button
                          onClick={() => handleRemoveUser(user.id)}
                          className="text-red-400 hover:text-red-300 p-1"
                        >
                          <X size={14} />
                        </button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Comments Tab */}
        {activeTab === 'comments' && (
          <div>
            <div className="mb-4">
              <div className="flex space-x-2">
                <textarea
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  placeholder="Add a comment..."
                  className="flex-1 p-2 bg-gray-800 border border-gray-600 rounded text-sm resize-none focus:outline-none focus:border-blue-400"
                  rows={2}
                />
                <button
                  onClick={handleAddComment}
                  disabled={!newComment.trim()}
                  className="px-3 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded text-sm"
                >
                  <Send size={14} />
                </button>
              </div>
            </div>

            <div className="space-y-3">
              {comments.map(comment => (
                <div
                  key={comment.id}
                  className={`p-3 rounded-lg border ${
                    comment.resolved 
                      ? 'bg-gray-800/50 border-gray-700 opacity-75' 
                      : 'bg-gray-800 border-gray-600'
                  }`}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gray-700 rounded-full flex items-center justify-center">
                        <span className="text-xs font-medium">
                          {getUserName(comment.userId).charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <span className="text-sm font-medium text-white">
                        {getUserName(comment.userId)}
                      </span>
                      <span className="text-xs text-gray-400">
                        {format(comment.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    <button
                      onClick={() => handleResolveComment(comment.id)}
                      className={`text-sm px-2 py-1 rounded ${
                        comment.resolved 
                          ? 'bg-green-900/20 text-green-400' 
                          : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                      }`}
                    >
                      {comment.resolved ? 'Resolved' : 'Resolve'}
                    </button>
                  </div>
                  <p className="text-sm text-gray-300 mb-2">{comment.content}</p>
                  {comment.mentions.length > 0 && (
                    <div className="flex items-center space-x-1 text-xs text-blue-400">
                      <AtSign size={12} />
                      <span>Mentioned: {comment.mentions.map(getUserName).join(', ')}</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Activity Tab */}
        {activeTab === 'activity' && (
          <div className="space-y-3">
            {activities.map(activity => {
              const ActivityIcon = getActivityIcon(activity.type);
              return (
                <div
                  key={activity.id}
                  className="flex items-center space-x-3 p-3 bg-gray-800 rounded-lg"
                >
                  <ActivityIcon size={16} className="text-blue-400" />
                  <div className="flex-1">
                    <div className="text-sm text-white">
                      <span className="font-medium">{getUserName(activity.userId)}</span>
                      <span className="text-gray-300"> {activity.description}</span>
                    </div>
                    <div className="text-xs text-gray-400">
                      {format(activity.timestamp, 'MMM dd, HH:mm')}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-900 border border-gray-700 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-bold mb-4">Invite Team Member</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-sm focus:outline-none focus:border-blue-400"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Role
                </label>
                <select
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value as any)}
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-sm"
                >
                  <option value="viewer">Viewer - Can view only</option>
                  <option value="reviewer">Reviewer - Can view and comment</option>
                  <option value="editor">Editor - Can edit and comment</option>
                </select>
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowInviteModal(false)}
                className="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded text-sm"
              >
                Cancel
              </button>
              <button
                onClick={handleInviteUser}
                disabled={!inviteEmail.trim()}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 rounded text-sm"
              >
                Send Invite
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}