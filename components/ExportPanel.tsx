'use client';

import React, { useState } from 'react';
import {
  Download,
  X,
  FileText,
  File,
  Image,
  Printer,
  Settings,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Layout,
  Palette,
  Eye,
} from 'lucide-react';
import { jsPDF } from 'jspdf';
import html2canvas from 'html2canvas';

interface Section {
  _id: string;
  title: string;
  content: string;
  wordCount: number;
  scoreWeight: number;
  status: string;
}

interface Tender {
  _id: string;
  name: string;
  clientName: string;
  dueDate: string;
  estimatedValue?: number;
  projectLocation?: string;
}

interface ExportPanelProps {
  tender: Tender;
  sections: Section[];
  onClose: () => void;
}

export default function ExportPanel({ tender, sections, onClose }: ExportPanelProps) {
  const [exportFormat, setExportFormat] = useState<'pdf' | 'word' | 'html'>('pdf');
  const [selectedSections, setSelectedSections] = useState<string[]>(sections.map(s => s._id));
  const [includeMetadata, setIncludeMetadata] = useState(true);
  const [includeToc, setIncludeToc] = useState(true);
  const [includePageNumbers, setIncludePageNumbers] = useState(true);
  const [templateStyle, setTemplateStyle] = useState<'professional' | 'modern' | 'minimal'>('professional');
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);

  const handleSectionToggle = (sectionId: string) => {
    setSelectedSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const selectAllSections = () => {
    setSelectedSections(sections.map(s => s._id));
  };

  const clearAllSections = () => {
    setSelectedSections([]);
  };

  const generatePDF = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4'
      });

      // Add title page
      pdf.setFontSize(24);
      pdf.text(tender.name, 20, 30);
      pdf.setFontSize(14);
      pdf.text(`Prepared for: ${tender.clientName}`, 20, 45);
      pdf.text(`Date: ${new Date().toLocaleDateString()}`, 20, 55);
      
      if (tender.projectLocation) {
        pdf.text(`Location: ${tender.projectLocation}`, 20, 65);
      }

      setExportProgress(20);

      // Add table of contents if enabled
      if (includeToc) {
        pdf.addPage();
        pdf.setFontSize(18);
        pdf.text('Table of Contents', 20, 30);
        
        let yPosition = 50;
        const filteredSections = sections.filter(s => selectedSections.includes(s._id));
        
        filteredSections.forEach((section, index) => {
          pdf.setFontSize(12);
          pdf.text(`${index + 1}. ${section.title}`, 20, yPosition);
          yPosition += 10;
        });
      }

      setExportProgress(40);

      // Add sections
      const filteredSections = sections.filter(s => selectedSections.includes(s._id));
      
      for (let i = 0; i < filteredSections.length; i++) {
        const section = filteredSections[i];
        pdf.addPage();
        
        // Section title
        pdf.setFontSize(16);
        pdf.text(section.title, 20, 30);
        
        // Section content (simplified - would need proper HTML parsing)
        pdf.setFontSize(12);
        const lines = section.content.replace(/<[^>]*>/g, '').split('\n');
        let yPosition = 50;
        
        lines.forEach(line => {
          if (line.trim()) {
            const splitLines = pdf.splitTextToSize(line, 170);
            splitLines.forEach((splitLine: string) => {
              if (yPosition > 280) { // Near bottom of page
                pdf.addPage();
                yPosition = 30;
              }
              pdf.text(splitLine, 20, yPosition);
              yPosition += 7;
            });
          }
        });
        
        setExportProgress(40 + (i / filteredSections.length) * 50);
      }

      // Add metadata page if enabled
      if (includeMetadata) {
        pdf.addPage();
        pdf.setFontSize(16);
        pdf.text('Document Information', 20, 30);
        
        pdf.setFontSize(12);
        let yPos = 50;
        pdf.text(`Total Sections: ${filteredSections.length}`, 20, yPos);
        yPos += 10;
        pdf.text(`Total Words: ${filteredSections.reduce((sum, s) => sum + s.wordCount, 0)}`, 20, yPos);
        yPos += 10;
        pdf.text(`Generated: ${new Date().toLocaleString()}`, 20, yPos);
        yPos += 10;
        pdf.text(`Export Format: PDF`, 20, yPos);
      }

      setExportProgress(90);

      // Add page numbers if enabled
      if (includePageNumbers) {
        const pageCount = (pdf as any).internal.getNumberOfPages();
        for (let i = 1; i <= pageCount; i++) {
          pdf.setPage(i);
          pdf.setFontSize(10);
          pdf.text(`Page ${i} of ${pageCount}`, 190, 287, { align: 'right' });
        }
      }

      setExportProgress(100);

      // Save the PDF
      pdf.save(`${tender.name.replace(/[^a-zA-Z0-9]/g, '_')}.pdf`);
      
    } catch (error) {
      console.error('PDF generation failed:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsExporting(false);
      setExportProgress(0);
    }
  };

  const generateHTML = () => {
    const filteredSections = sections.filter(s => selectedSections.includes(s._id));
    
    let html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${tender.name}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
          .header { text-align: center; margin-bottom: 40px; }
          .section { margin-bottom: 40px; page-break-inside: avoid; }
          .section-title { font-size: 1.5em; font-weight: bold; margin-bottom: 20px; color: #333; }
          .toc { margin-bottom: 40px; }
          .toc ul { list-style: none; padding: 0; }
          .toc li { margin-bottom: 10px; }
          .metadata { margin-top: 40px; padding-top: 20px; border-top: 1px solid #ccc; }
          @media print { .no-print { display: none; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${tender.name}</h1>
          <p>Prepared for: ${tender.clientName}</p>
          <p>Date: ${new Date().toLocaleDateString()}</p>
          ${tender.projectLocation ? `<p>Location: ${tender.projectLocation}</p>` : ''}
        </div>
    `;

    if (includeToc) {
      html += `
        <div class="toc">
          <h2>Table of Contents</h2>
          <ul>
            ${filteredSections.map((section, index) => 
              `<li>${index + 1}. ${section.title}</li>`
            ).join('')}
          </ul>
        </div>
      `;
    }

    filteredSections.forEach(section => {
      html += `
        <div class="section">
          <h2 class="section-title">${section.title}</h2>
          <div>${section.content}</div>
        </div>
      `;
    });

    if (includeMetadata) {
      html += `
        <div class="metadata">
          <h3>Document Information</h3>
          <p>Total Sections: ${filteredSections.length}</p>
          <p>Total Words: ${filteredSections.reduce((sum, s) => sum + s.wordCount, 0)}</p>
          <p>Generated: ${new Date().toLocaleString()}</p>
          <p>Export Format: HTML</p>
        </div>
      `;
    }

    html += `
      </body>
      </html>
    `;

    const blob = new Blob([html], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${tender.name.replace(/[^a-zA-Z0-9]/g, '_')}.html`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleExport = async () => {
    if (selectedSections.length === 0) {
      alert('Please select at least one section to export.');
      return;
    }

    switch (exportFormat) {
      case 'pdf':
        await generatePDF();
        break;
      case 'html':
        generateHTML();
        break;
      case 'word':
        // For Word export, we'll generate HTML with Word-compatible formatting
        alert('Word export is not implemented yet. Please use PDF or HTML format.');
        break;
      default:
        alert('Unsupported export format.');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'text-green-400';
      case 'review':
        return 'text-blue-400';
      case 'needs_revision':
        return 'text-red-400';
      case 'in_progress':
        return 'text-yellow-400';
      default:
        return 'text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return CheckCircle;
      case 'review':
        return RefreshCw;
      case 'needs_revision':
        return AlertCircle;
      default:
        return FileText;
    }
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4 max-h-[600px] overflow-y-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center space-x-2">
          <Download size={20} />
          <span>Export Document</span>
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white p-1"
        >
          <X size={16} />
        </button>
      </div>

      {/* Export Format Selection */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold mb-3 text-gray-300">Export Format</h4>
        <div className="grid grid-cols-3 gap-2">
          {[
            { value: 'pdf', label: 'PDF', icon: File },
            { value: 'html', label: 'HTML', icon: FileText },
            { value: 'word', label: 'Word', icon: FileText },
          ].map(({ value, label, icon: Icon }) => (
            <button
              key={value}
              onClick={() => setExportFormat(value as any)}
              className={`p-3 rounded-lg border transition-all ${
                exportFormat === value
                  ? 'bg-blue-600 border-blue-400 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              <Icon size={20} className="mx-auto mb-1" />
              <div className="text-sm">{label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Section Selection */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-3">
          <h4 className="text-sm font-semibold text-gray-300">Select Sections</h4>
          <div className="flex space-x-2">
            <button
              onClick={selectAllSections}
              className="text-xs text-blue-400 hover:text-blue-300"
            >
              Select All
            </button>
            <button
              onClick={clearAllSections}
              className="text-xs text-red-400 hover:text-red-300"
            >
              Clear All
            </button>
          </div>
        </div>
        
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {sections.map(section => {
            const StatusIcon = getStatusIcon(section.status);
            return (
              <div
                key={section._id}
                className={`p-3 rounded-lg border transition-all cursor-pointer ${
                  selectedSections.includes(section._id)
                    ? 'bg-gray-800 border-blue-400'
                    : 'bg-gray-800/50 border-gray-700 hover:bg-gray-800'
                }`}
                onClick={() => handleSectionToggle(section._id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      checked={selectedSections.includes(section._id)}
                      onChange={() => handleSectionToggle(section._id)}
                      className="rounded"
                    />
                    <div>
                      <div className="font-medium text-sm">{section.title}</div>
                      <div className="text-xs text-gray-400">
                        {section.wordCount} words • {section.scoreWeight}% weight
                      </div>
                    </div>
                  </div>
                  <StatusIcon size={16} className={getStatusColor(section.status)} />
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Export Options */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold mb-3 text-gray-300">Export Options</h4>
        <div className="space-y-3">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includeMetadata}
              onChange={(e) => setIncludeMetadata(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-300">Include document metadata</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includeToc}
              onChange={(e) => setIncludeToc(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-300">Include table of contents</span>
          </label>
          
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={includePageNumbers}
              onChange={(e) => setIncludePageNumbers(e.target.checked)}
              className="rounded"
            />
            <span className="text-sm text-gray-300">Include page numbers</span>
          </label>
        </div>
      </div>

      {/* Template Style */}
      <div className="mb-6">
        <h4 className="text-sm font-semibold mb-3 text-gray-300">Template Style</h4>
        <div className="grid grid-cols-3 gap-2">
          {[
            { value: 'professional', label: 'Professional' },
            { value: 'modern', label: 'Modern' },
            { value: 'minimal', label: 'Minimal' },
          ].map(({ value, label }) => (
            <button
              key={value}
              onClick={() => setTemplateStyle(value as any)}
              className={`p-2 rounded text-sm border transition-all ${
                templateStyle === value
                  ? 'bg-purple-600 border-purple-400 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      </div>

      {/* Export Progress */}
      {isExporting && (
        <div className="mb-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm text-gray-300">Exporting...</span>
            <span className="text-sm text-gray-400">{exportProgress}%</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-2">
            <div
              className="bg-blue-400 h-2 rounded-full transition-all"
              style={{ width: `${exportProgress}%` }}
            />
          </div>
        </div>
      )}

      {/* Export Summary */}
      <div className="mb-6 p-3 bg-gray-800 rounded-lg">
        <h4 className="text-sm font-semibold mb-2 text-gray-300">Export Summary</h4>
        <div className="text-sm text-gray-400 space-y-1">
          <div>Format: {exportFormat.toUpperCase()}</div>
          <div>Sections: {selectedSections.length} of {sections.length}</div>
          <div>
            Total Words: {sections
              .filter(s => selectedSections.includes(s._id))
              .reduce((sum, s) => sum + s.wordCount, 0)}
          </div>
          <div>Template: {templateStyle}</div>
        </div>
      </div>

      {/* Export Button */}
      <button
        onClick={handleExport}
        disabled={isExporting || selectedSections.length === 0}
        className="w-full py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-lg font-medium flex items-center justify-center space-x-2"
      >
        {isExporting ? (
          <>
            <RefreshCw size={16} className="animate-spin" />
            <span>Exporting...</span>
          </>
        ) : (
          <>
            <Download size={16} />
            <span>Export Document</span>
          </>
        )}
      </button>
    </div>
  );
}