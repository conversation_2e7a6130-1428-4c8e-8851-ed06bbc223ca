"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { 
  Eye,
  Download,
  Share2,
  Edit3,
  Trash2,
  X,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Maximize2,
  Minimize2,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Volume2,
  VolumeX,
  FileText,
  FileImage,
  FileVideo,
  FileAudio,
  FileSpreadsheet,
  Archive,
  File,
  Clock,
  User,
  HardDrive,
  Tag,
  Calendar,
  Hash,
  Shield,
  AlertCircle,
  CheckCircle,
  Loader2,
  Search,
  BookOpen,
  Target,
  BarChart3,
  TrendingUp,
  Activity,
  Layers,
  MessageSquare,
  Star,
  Flag,
  Bookmark,
  Copy,
  ExternalLink,
  Settings,
  Info,
  AlertTriangle,
  RefreshCw,
  Zap,
  Database,
  Monitor,
  Code,
  Globe,
  Lock,
  Unlock,
  Users,
  UserPlus,
  Link,
  QrCode,
  Mail,
  Phone,
  MapPin,
  Building,
  Briefcase,
  DollarSign,
  Percent,
  Type,
  AlignLeft,
  Image as ImageIcon,
  Video,
  Music,
  Archive as ArchiveIcon,
  Paperclip,
  Bell,
  BellOff,
  Megaphone,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Smartphone,
  Tablet,
  Laptop,
  Server,
  Cloud,
  CloudOff,
  Wifi,
  WifiOff,
  Bluetooth,
  BluetoothOff,
  Battery,
  BatteryLow,
  Power,
  PowerOff,
  Cpu,
  Memory,
  HardDisk,
  SdCard,
  Usb,
  Keyboard,
  Mouse,
  Headphones,
  Speaker,
  Microphone,
  Webcam,
  Printer,
  Scanner,
  Fax,
  Router,
  Modem,
  Antenna,
  Satellite,
  Radio,
  Television,
  GameController,
  Gamepad2,
  Joystick
} from 'lucide-react';
import { Id } from '../convex/_generated/dataModel';
import { FileUpload, DocumentAnalysis, DocumentRequirement } from '../types/file';

interface FilePreviewProps {
  fileId: Id<'files'>;
  onClose: () => void;
  onEdit?: (file: FileUpload) => void;
  onDelete?: (file: FileUpload) => void;
  onShare?: (file: FileUpload) => void;
  showActions?: boolean;
  className?: string;
}

interface PreviewPanelProps {
  file: FileUpload;
  analysis?: DocumentAnalysis;
  requirements?: DocumentRequirement[];
}

const FILE_TYPE_ICONS = {
  pdf: { icon: FileText, color: 'text-red-500' },
  doc: { icon: FileText, color: 'text-blue-500' },
  docx: { icon: FileText, color: 'text-blue-500' },
  txt: { icon: FileText, color: 'text-gray-500' },
  rtf: { icon: FileText, color: 'text-blue-500' },
  odt: { icon: FileText, color: 'text-blue-500' },
  xlsx: { icon: FileSpreadsheet, color: 'text-green-500' },
  xls: { icon: FileSpreadsheet, color: 'text-green-500' },
  csv: { icon: FileSpreadsheet, color: 'text-green-500' },
  pptx: { icon: FileText, color: 'text-orange-500' },
  ppt: { icon: FileText, color: 'text-orange-500' },
  image: { icon: FileImage, color: 'text-purple-500' },
  audio: { icon: FileAudio, color: 'text-pink-500' },
  video: { icon: FileVideo, color: 'text-indigo-500' },
  archive: { icon: Archive, color: 'text-yellow-500' },
  other: { icon: File, color: 'text-gray-500' },
};

const STATUS_COLORS = {
  uploading: 'bg-blue-100 text-blue-800',
  uploaded: 'bg-green-100 text-green-800',
  processing: 'bg-yellow-100 text-yellow-800',
  processed: 'bg-green-100 text-green-800',
  failed: 'bg-red-100 text-red-800',
  deleted: 'bg-gray-100 text-gray-800',
  quarantined: 'bg-red-100 text-red-800',
};

const PRIORITY_COLORS = {
  low: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800',
  high: 'bg-orange-100 text-orange-800',
  mandatory: 'bg-red-100 text-red-800',
};

export default function FilePreview({
  fileId,
  onClose,
  onEdit,
  onDelete,
  onShare,
  showActions = true,
  className = '',
}: FilePreviewProps) {
  const [activeTab, setActiveTab] = useState<'preview' | 'details' | 'analysis' | 'requirements' | 'versions' | 'comments'>('preview');
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);
  const [fullscreen, setFullscreen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [muted, setMuted] = useState(false);

  const file = useQuery(api.files.getFile, { id: fileId });
  const analysis = useQuery(api.files.getDocumentAnalysis, { fileId });
  const requirements = useQuery(api.files.getDocumentRequirements, { fileId });
  const versions = useQuery(api.files.getFileVersions, { fileId });
  const comments = useQuery(api.files.getFileComments, { fileId });
  const processingJobs = useQuery(api.files.getProcessingJobs, { fileId });

  const deleteFile = useMutation(api.files.deleteFile);
  const updateFile = useMutation(api.files.updateFile);

  const formatFileSize = (bytes: number): string => {
    const units = ['B', 'KB', 'MB', 'GB'];
    let size = bytes;
    let unitIndex = 0;
    
    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }
    
    return `${size.toFixed(1)} ${units[unitIndex]}`;
  };

  const formatDate = (timestamp: number): string => {
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getFileIcon = (type: string) => {
    const iconInfo = FILE_TYPE_ICONS[type as keyof typeof FILE_TYPE_ICONS] || FILE_TYPE_ICONS.other;
    return iconInfo.icon;
  };

  const getFileIconColor = (type: string) => {
    const iconInfo = FILE_TYPE_ICONS[type as keyof typeof FILE_TYPE_ICONS] || FILE_TYPE_ICONS.other;
    return iconInfo.color;
  };

  const handleDownload = () => {
    if (file?.downloadUrl) {
      window.open(file.downloadUrl, '_blank');
    }
  };

  const handleEdit = () => {
    if (file && onEdit) {
      onEdit(file);
    }
  };

  const handleDelete = async () => {
    if (file && onDelete) {
      try {
        await deleteFile({ id: fileId });
        onDelete(file);
        onClose();
      } catch (error) {
        console.error('Error deleting file:', error);
      }
    }
  };

  const handleShare = () => {
    if (file && onShare) {
      onShare(file);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const handleFullscreen = () => {
    setFullscreen(!fullscreen);
  };

  const renderPreview = () => {
    if (!file) return null;

    const IconComponent = getFileIcon(file.type);

    switch (file.type) {
      case 'image':
        return (
          <div className="flex items-center justify-center h-full bg-gray-100">
            {file.downloadUrl ? (
              <img
                src={file.downloadUrl}
                alt={file.name}
                className="max-w-full max-h-full object-contain"
                style={{
                  transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
                  transition: 'transform 0.3s ease',
                }}
              />
            ) : (
              <div className="text-center">
                <ImageIcon className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">Image preview not available</p>
              </div>
            )}
          </div>
        );

      case 'video':
        return (
          <div className="flex items-center justify-center h-full bg-black">
            {file.downloadUrl ? (
              <video
                src={file.downloadUrl}
                controls
                className="max-w-full max-h-full"
                style={{
                  transform: `scale(${zoom / 100})`,
                  transition: 'transform 0.3s ease',
                }}
              />
            ) : (
              <div className="text-center text-white">
                <Video className="w-16 h-16 mx-auto mb-4" />
                <p>Video preview not available</p>
              </div>
            )}
          </div>
        );

      case 'audio':
        return (
          <div className="flex items-center justify-center h-full bg-gray-100">
            <div className="text-center">
              <Music className="w-16 h-16 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-600 mb-4">{file.name}</p>
              {file.downloadUrl && (
                <audio
                  src={file.downloadUrl}
                  controls
                  className="w-full max-w-md"
                />
              )}
            </div>
          </div>
        );

      case 'pdf':
        return (
          <div className="flex items-center justify-center h-full bg-gray-100">
            {file.previewUrl ? (
              <iframe
                src={file.previewUrl}
                className="w-full h-full border-0"
                title={file.name}
              />
            ) : (
              <div className="text-center">
                <FileText className="w-16 h-16 mx-auto text-red-500 mb-4" />
                <p className="text-gray-600 mb-4">{file.name}</p>
                <button
                  onClick={handleDownload}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  Download to View
                </button>
              </div>
            )}
          </div>
        );

      case 'txt':
        return (
          <div className="h-full bg-white p-4 overflow-auto">
            {analysis?.textContent ? (
              <pre className="whitespace-pre-wrap text-sm font-mono">
                {analysis.textContent}
              </pre>
            ) : (
              <div className="text-center py-8">
                <FileText className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <p className="text-gray-500">Text extraction in progress...</p>
              </div>
            )}
          </div>
        );

      default:
        return (
          <div className="flex items-center justify-center h-full bg-gray-100">
            <div className="text-center">
              <IconComponent className={`w-16 h-16 mx-auto mb-4 ${getFileIconColor(file.type)}`} />
              <p className="text-gray-600 mb-4">{file.name}</p>
              <p className="text-gray-500 mb-4">
                {file.type.toUpperCase()} files are not previewable
              </p>
              <button
                onClick={handleDownload}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Download
              </button>
            </div>
          </div>
        );
    }
  };

  const renderDetails = () => {
    if (!file) return null;

    const IconComponent = getFileIcon(file.type);

    return (
      <div className="p-6 space-y-6">
        <div className="flex items-start space-x-4">
          <div className="flex-shrink-0">
            {file.thumbnailUrl ? (
              <img
                src={file.thumbnailUrl}
                alt={file.name}
                className="w-16 h-16 object-cover rounded"
              />
            ) : (
              <div className="w-16 h-16 bg-gray-100 rounded flex items-center justify-center">
                <IconComponent className={`w-8 h-8 ${getFileIconColor(file.type)}`} />
              </div>
            )}
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-900">{file.name}</h3>
            <p className="text-sm text-gray-500">{file.originalName}</p>
            <div className="flex items-center space-x-2 mt-2">
              <span className={`px-2 py-1 text-xs rounded ${STATUS_COLORS[file.status as keyof typeof STATUS_COLORS]}`}>
                {file.status}
              </span>
              <span className="text-xs text-gray-500">
                {file.type.toUpperCase()}
              </span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">File Information</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Size:</span>
                  <span>{formatFileSize(file.size)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Type:</span>
                  <span>{file.mimeType}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Category:</span>
                  <span className="capitalize">{file.category.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Access Level:</span>
                  <span className="capitalize">{file.accessLevel}</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Metadata</h4>
              <div className="space-y-2 text-sm">
                {file.metadata.pages && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Pages:</span>
                    <span>{file.metadata.pages}</span>
                  </div>
                )}
                {file.metadata.width && file.metadata.height && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Dimensions:</span>
                    <span>{file.metadata.width} × {file.metadata.height}</span>
                  </div>
                )}
                {file.metadata.duration && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Duration:</span>
                    <span>{Math.round(file.metadata.duration / 60)}:{(file.metadata.duration % 60).toString().padStart(2, '0')}</span>
                  </div>
                )}
                {file.metadata.language && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Language:</span>
                    <span className="uppercase">{file.metadata.language}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Upload Information</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Uploaded:</span>
                  <span>{formatDate(file.uploadedAt)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Modified:</span>
                  <span>{formatDate(file.lastModified)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Uploaded by:</span>
                  <span>{file.uploadedBy}</span>
                </div>
                {file.lastAccessed && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Last accessed:</span>
                    <span>{formatDate(file.lastAccessed)}</span>
                  </div>
                )}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Usage Statistics</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Downloads:</span>
                  <span>{file.downloadCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Views:</span>
                  <span>{file.viewCount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Virus Status:</span>
                  <span className={`capitalize ${file.virusStatus === 'clean' ? 'text-green-600' : file.virusStatus === 'infected' ? 'text-red-600' : 'text-yellow-600'}`}>
                    {file.virusStatus.replace('_', ' ')}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {file.tags && file.tags.length > 0 && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Tags</h4>
            <div className="flex flex-wrap gap-2">
              {file.tags.map((tag, index) => (
                <span
                  key={index}
                  className="px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        {file.description && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
            <p className="text-sm text-gray-600">{file.description}</p>
          </div>
        )}

        <div>
          <h4 className="text-sm font-medium text-gray-700 mb-2">Security</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">Checksum (SHA-256):</span>
              <span className="font-mono text-xs">{file.checksum.substring(0, 16)}...</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Storage Key:</span>
              <span className="font-mono text-xs">{file.storageKey}</span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderAnalysis = () => {
    if (!analysis) {
      return (
        <div className="p-6 text-center">
          <Loader2 className="w-8 h-8 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-500">Analyzing document...</p>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Analysis Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Status:</span>
                  <span className={`capitalize ${analysis.status === 'completed' ? 'text-green-600' : analysis.status === 'failed' ? 'text-red-600' : 'text-yellow-600'}`}>
                    {analysis.status}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Type:</span>
                  <span className="capitalize">{analysis.type.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Started:</span>
                  <span>{formatDate(analysis.startedAt)}</span>
                </div>
                {analysis.completedAt && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Completed:</span>
                    <span>{formatDate(analysis.completedAt)}</span>
                  </div>
                )}
                {analysis.metadata.processingTime && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Processing Time:</span>
                    <span>{(analysis.metadata.processingTime / 1000).toFixed(1)}s</span>
                  </div>
                )}
              </div>
            </div>

            {analysis.sentiment && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Sentiment Analysis</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Overall:</span>
                    <span className={`capitalize ${analysis.sentiment.overall === 'positive' ? 'text-green-600' : analysis.sentiment.overall === 'negative' ? 'text-red-600' : 'text-gray-600'}`}>
                      {analysis.sentiment.overall}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Score:</span>
                    <span>{analysis.sentiment.score.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Confidence:</span>
                    <span>{Math.round(analysis.sentiment.confidence * 100)}%</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-700 mb-2">Content Statistics</h4>
              <div className="space-y-2 text-sm">
                {analysis.wordCount && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Words:</span>
                    <span>{analysis.wordCount.toLocaleString()}</span>
                  </div>
                )}
                {analysis.characterCount && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Characters:</span>
                    <span>{analysis.characterCount.toLocaleString()}</span>
                  </div>
                )}
                {analysis.pageCount && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Pages:</span>
                    <span>{analysis.pageCount}</span>
                  </div>
                )}
                {analysis.language && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Language:</span>
                    <span className="uppercase">{analysis.language}</span>
                  </div>
                )}
                {analysis.confidence && (
                  <div className="flex justify-between">
                    <span className="text-gray-500">Confidence:</span>
                    <span>{Math.round(analysis.confidence * 100)}%</span>
                  </div>
                )}
              </div>
            </div>

            {analysis.keyPhrases && analysis.keyPhrases.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Key Phrases</h4>
                <div className="flex flex-wrap gap-2">
                  {analysis.keyPhrases.slice(0, 10).map((phrase, index) => (
                    <span
                      key={index}
                      className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full"
                    >
                      {phrase}
                    </span>
                  ))}
                  {analysis.keyPhrases.length > 10 && (
                    <span className="px-2 py-1 bg-gray-100 text-gray-500 text-xs rounded-full">
                      +{analysis.keyPhrases.length - 10} more
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {analysis.error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-sm font-medium text-red-700">Analysis Error</span>
            </div>
            <p className="text-sm text-red-600 mt-1">{analysis.error}</p>
          </div>
        )}
      </div>
    );
  };

  const renderRequirements = () => {
    if (!requirements) {
      return (
        <div className="p-6 text-center">
          <Loader2 className="w-8 h-8 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-500">Extracting requirements...</p>
        </div>
      );
    }

    if (requirements.length === 0) {
      return (
        <div className="p-6 text-center">
          <Target className="w-8 h-8 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">No requirements extracted from this document.</p>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700">
            Requirements ({requirements.length})
          </h4>
          <div className="flex items-center space-x-2">
            <span className="text-xs text-gray-500">
              {requirements.filter(r => r.priority === 'mandatory').length} mandatory
            </span>
            <span className="text-xs text-gray-500">
              {requirements.filter(r => r.priority === 'high').length} high priority
            </span>
          </div>
        </div>

        <div className="space-y-3">
          {requirements.map((requirement, index) => (
            <div
              key={requirement.id}
              className="p-4 border rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className={`px-2 py-1 text-xs rounded ${PRIORITY_COLORS[requirement.priority as keyof typeof PRIORITY_COLORS]}`}>
                      {requirement.priority}
                    </span>
                    <span className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                      {requirement.type.replace('_', ' ')}
                    </span>
                    <span className="text-xs text-gray-500">
                      Page {requirement.page}
                    </span>
                  </div>
                  <p className="text-sm text-gray-900 mb-2">{requirement.text}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Confidence: {Math.round(requirement.confidence * 100)}%</span>
                    <span>Words: {requirement.wordCount}</span>
                    <span>Complexity: {requirement.complexity}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {requirement.complianceStatus && (
                    <span className={`px-2 py-1 text-xs rounded ${
                      requirement.complianceStatus === 'compliant' ? 'bg-green-100 text-green-700' :
                      requirement.complianceStatus === 'non_compliant' ? 'bg-red-100 text-red-700' :
                      requirement.complianceStatus === 'partial' ? 'bg-yellow-100 text-yellow-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {requirement.complianceStatus.replace('_', ' ')}
                    </span>
                  )}
                </div>
              </div>
              
              {requirement.keywords && requirement.keywords.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-1">
                  {requirement.keywords.slice(0, 5).map((keyword, keyIndex) => (
                    <span
                      key={keyIndex}
                      className="px-2 py-1 bg-blue-50 text-blue-600 text-xs rounded"
                    >
                      {keyword}
                    </span>
                  ))}
                  {requirement.keywords.length > 5 && (
                    <span className="px-2 py-1 bg-gray-50 text-gray-500 text-xs rounded">
                      +{requirement.keywords.length - 5}
                    </span>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderVersions = () => {
    if (!versions) {
      return (
        <div className="p-6 text-center">
          <Loader2 className="w-8 h-8 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-500">Loading versions...</p>
        </div>
      );
    }

    if (versions.length === 0) {
      return (
        <div className="p-6 text-center">
          <Clock className="w-8 h-8 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">No versions available for this file.</p>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-4">
        <h4 className="text-sm font-medium text-gray-700">
          Versions ({versions.length})
        </h4>
        
        <div className="space-y-3">
          {versions.map((version, index) => (
            <div
              key={version.id}
              className={`p-4 border rounded-lg ${version.isActive ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium">
                      Version {version.version}
                    </span>
                    {version.isActive && (
                      <span className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                        Current
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>Created: {formatDate(version.createdAt)}</p>
                    <p>Created by: {version.createdBy}</p>
                    <p>Size: {formatFileSize(version.size)}</p>
                    {version.notes && (
                      <p className="text-xs text-gray-500 mt-2">{version.notes}</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => {
                      // Download version logic
                    }}
                    className="p-2 text-gray-400 hover:text-blue-600"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => {
                      // View version logic
                    }}
                    className="p-2 text-gray-400 hover:text-green-600"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  const renderComments = () => {
    if (!comments) {
      return (
        <div className="p-6 text-center">
          <Loader2 className="w-8 h-8 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-500">Loading comments...</p>
        </div>
      );
    }

    if (comments.length === 0) {
      return (
        <div className="p-6 text-center">
          <MessageSquare className="w-8 h-8 mx-auto text-gray-400 mb-4" />
          <p className="text-gray-500">No comments on this file yet.</p>
        </div>
      );
    }

    return (
      <div className="p-6 space-y-4">
        <h4 className="text-sm font-medium text-gray-700">
          Comments ({comments.length})
        </h4>
        
        <div className="space-y-4">
          {comments.map((comment, index) => (
            <div
              key={comment.id}
              className="p-4 border rounded-lg"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-sm font-medium">{comment.userId}</span>
                    <span className="text-xs text-gray-500">
                      {formatDate(comment.createdAt)}
                    </span>
                    <span className={`px-2 py-1 text-xs rounded ${
                      comment.type === 'general' ? 'bg-gray-100 text-gray-700' :
                      comment.type === 'suggestion' ? 'bg-blue-100 text-blue-700' :
                      comment.type === 'issue' ? 'bg-red-100 text-red-700' :
                      'bg-green-100 text-green-700'
                    }`}>
                      {comment.type}
                    </span>
                    {comment.isResolved && (
                      <span className="px-2 py-1 text-xs bg-green-100 text-green-700 rounded">
                        Resolved
                      </span>
                    )}
                  </div>
                  <p className="text-sm text-gray-900 mb-2">{comment.content}</p>
                  {comment.position && (
                    <p className="text-xs text-gray-500">
                      Page {comment.position.page} at ({comment.position.x}, {comment.position.y})
                    </p>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  {comment.reactions && comment.reactions.length > 0 && (
                    <div className="flex items-center space-x-1">
                      {comment.reactions.map((reaction, reactionIndex) => (
                        <span
                          key={reactionIndex}
                          className="text-xs bg-gray-100 rounded px-1"
                        >
                          {reaction.emoji} {reaction.count}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  if (!file) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6">
          <Loader2 className="w-8 h-8 mx-auto text-gray-400 mb-4 animate-spin" />
          <p className="text-gray-500">Loading file...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className={`bg-white rounded-lg shadow-xl ${fullscreen ? 'w-full h-full' : 'w-11/12 h-5/6 max-w-6xl'}`}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center space-x-3">
            <div className="flex-shrink-0">
              {file.thumbnailUrl ? (
                <img
                  src={file.thumbnailUrl}
                  alt={file.name}
                  className="w-8 h-8 object-cover rounded"
                />
              ) : (
                <div className="w-8 h-8 bg-gray-100 rounded flex items-center justify-center">
                  <File className="w-4 h-4 text-gray-400" />
                </div>
              )}
            </div>
            <div>
              <h2 className="text-lg font-semibold truncate">{file.name}</h2>
              <p className="text-sm text-gray-500">
                {formatFileSize(file.size)} • {formatDate(file.uploadedAt)}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {/* Preview Controls */}
            {(file.type === 'image' || file.type === 'pdf') && (
              <div className="flex items-center space-x-1 border-r pr-2">
                <button
                  onClick={handleZoomOut}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <ZoomOut className="w-4 h-4" />
                </button>
                <span className="text-sm text-gray-500 min-w-[3rem] text-center">
                  {zoom}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-1 text-gray-500 hover:text-gray-700"
                >
                  <ZoomIn className="w-4 h-4" />
                </button>
                {file.type === 'image' && (
                  <button
                    onClick={handleRotate}
                    className="p-1 text-gray-500 hover:text-gray-700"
                  >
                    <RotateCw className="w-4 h-4" />
                  </button>
                )}
              </div>
            )}
            
            {/* Actions */}
            {showActions && (
              <div className="flex items-center space-x-1">
                <button
                  onClick={handleDownload}
                  className="p-2 text-gray-500 hover:text-blue-600"
                >
                  <Download className="w-4 h-4" />
                </button>
                <button
                  onClick={handleShare}
                  className="p-2 text-gray-500 hover:text-green-600"
                >
                  <Share2 className="w-4 h-4" />
                </button>
                <button
                  onClick={handleEdit}
                  className="p-2 text-gray-500 hover:text-yellow-600"
                >
                  <Edit3 className="w-4 h-4" />
                </button>
                <button
                  onClick={handleDelete}
                  className="p-2 text-gray-500 hover:text-red-600"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            )}
            
            <button
              onClick={handleFullscreen}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              {fullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-full">
          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Tabs */}
            <div className="flex border-b">
              <button
                onClick={() => setActiveTab('preview')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'preview'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Preview
              </button>
              <button
                onClick={() => setActiveTab('details')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'details'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Details
              </button>
              <button
                onClick={() => setActiveTab('analysis')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'analysis'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Analysis
              </button>
              <button
                onClick={() => setActiveTab('requirements')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'requirements'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Requirements {requirements && requirements.length > 0 && (
                  <span className="ml-1 px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">
                    {requirements.length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('versions')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'versions'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Versions {versions && versions.length > 0 && (
                  <span className="ml-1 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    {versions.length}
                  </span>
                )}
              </button>
              <button
                onClick={() => setActiveTab('comments')}
                className={`px-4 py-2 text-sm font-medium ${
                  activeTab === 'comments'
                    ? 'border-b-2 border-blue-500 text-blue-600'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Comments {comments && comments.length > 0 && (
                  <span className="ml-1 px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                    {comments.length}
                  </span>
                )}
              </button>
            </div>

            {/* Tab Content */}
            <div className="flex-1 overflow-auto">
              {activeTab === 'preview' && renderPreview()}
              {activeTab === 'details' && renderDetails()}
              {activeTab === 'analysis' && renderAnalysis()}
              {activeTab === 'requirements' && renderRequirements()}
              {activeTab === 'versions' && renderVersions()}
              {activeTab === 'comments' && renderComments()}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}