"use client";

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../convex/_generated/api';
import { Id } from '../convex/_generated/dataModel';
import { Calendar, Clock, Users, MapPin, Bell, Video, Phone, Mail, MessageSquare, AlertCircle, CheckCircle, XCircle, ChevronRight, Plus, Filter, Search } from 'lucide-react';
import { 
  SchedulerContact, 
  SchedulerMeeting, 
  MeetingType, 
  ResponseStatus,
  ParticipantSuggestion,
  NotificationChannel
} from '../types/scheduler';

interface SchedulerInterfaceProps {
  tenderId?: Id<'tenders'>;
  projectContext?: {
    projectName: string;
    projectType: string;
    priority: string;
    requirements: string[];
  };
}

export default function SchedulerInterface({ tenderId, projectContext }: SchedulerInterfaceProps) {
  const [activeTab, setActiveTab] = useState<'meetings' | 'contacts' | 'analytics'>('meetings');
  const [showNewMeeting, setShowNewMeeting] = useState(false);
  const [selectedMeeting, setSelectedMeeting] = useState<Id<'scheduler_meetings'> | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState<MeetingType | 'all'>('all');
  
  // Form state
  const [meetingForm, setMeetingForm] = useState({
    title: '',
    description: '',
    type: 'internal_sync' as MeetingType,
    startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
    duration: 60,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    locationType: 'online' as 'physical' | 'online' | 'hybrid',
    locationValue: '',
    participants: [] as Array<{ contactId: Id<'scheduler_contacts'>; role: string; isRequired: boolean }>,
    reminders: [
      { type: 'notification', minutes: 15, channel: 'email' as NotificationChannel },
      { type: 'notification', minutes: 1440, channel: 'email' as NotificationChannel }
    ]
  });

  // Queries
  const meetings = useQuery(api.scheduler.getMeetings, {
    tenderId,
    type: filterType === 'all' ? undefined : filterType,
  });

  const contacts = useQuery(api.scheduler.getContacts, {
    isActive: true,
  });

  const suggestedParticipants = useQuery(api.scheduler.suggestParticipants, 
    showNewMeeting ? {
      tenderId,
      projectType: projectContext?.projectType || 'general',
      requirements: projectContext?.requirements || [],
      meetingType: meetingForm.type,
      preferredTime: new Date(meetingForm.startTime).getTime(),
      duration: meetingForm.duration * 60 * 1000,
    } : 'skip'
  );

  // Mutations
  const createMeeting = useMutation(api.scheduler.createMeeting);
  const updateRSVP = useMutation(api.scheduler.updateRSVP);
  const sendNudge = useMutation(api.scheduler.sendNudge);
  const syncToCalendar = useMutation(api.scheduler.syncToGoogleCalendar);
  const generateMeetingLink = useMutation(api.scheduler.generateMeetingLinks);

  const handleCreateMeeting = async () => {
    try {
      const startTime = new Date(meetingForm.startTime).getTime();
      const endTime = startTime + (meetingForm.duration * 60 * 1000);

      await createMeeting({
        title: meetingForm.title,
        description: meetingForm.description,
        type: meetingForm.type,
        tenderId,
        projectContext,
        startTime,
        endTime,
        timezone: meetingForm.timezone,
        location: {
          type: meetingForm.locationType,
          value: meetingForm.locationValue,
        },
        participants: meetingForm.participants.map(p => ({
          ...p,
          responseStatus: 'pending' as ResponseStatus,
        })),
        reminders: meetingForm.reminders,
      });

      setShowNewMeeting(false);
      resetForm();
    } catch (error) {
      console.error('Failed to create meeting:', error);
    }
  };

  const resetForm = () => {
    setMeetingForm({
      title: '',
      description: '',
      type: 'internal_sync',
      startTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
      duration: 60,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      locationType: 'online',
      locationValue: '',
      participants: [],
      reminders: [
        { type: 'notification', minutes: 15, channel: 'email' },
        { type: 'notification', minutes: 1440, channel: 'email' }
      ]
    });
  };

  const addParticipant = (suggestion: ParticipantSuggestion) => {
    if (!meetingForm.participants.find(p => p.contactId === suggestion.contact.id)) {
      setMeetingForm(prev => ({
        ...prev,
        participants: [...prev.participants, {
          contactId: suggestion.contact.id,
          role: suggestion.contact.role,
          isRequired: suggestion.isRequired,
        }]
      }));
    }
  };

  const removeParticipant = (contactId: Id<'scheduler_contacts'>) => {
    setMeetingForm(prev => ({
      ...prev,
      participants: prev.participants.filter(p => p.contactId !== contactId)
    }));
  };

  const getStatusIcon = (status: ResponseStatus) => {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'declined':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'tentative':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  };

  const getMeetingTypeColor = (type: MeetingType) => {
    const colors = {
      tender_kickoff: 'bg-purple-100 text-purple-800',
      technical_review: 'bg-blue-100 text-blue-800',
      bid_review: 'bg-green-100 text-green-800',
      client_meeting: 'bg-red-100 text-red-800',
      internal_sync: 'bg-gray-100 text-gray-800',
      training: 'bg-yellow-100 text-yellow-800',
      workshop: 'bg-indigo-100 text-indigo-800',
      presentation: 'bg-pink-100 text-pink-800',
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-semibold text-gray-900">Meeting Scheduler</h2>
            <p className="text-sm text-gray-500 mt-1">
              Intelligent meeting coordination with automated notifications
            </p>
          </div>
          <button
            onClick={() => setShowNewMeeting(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Schedule Meeting
          </button>
        </div>

        {/* Tabs */}
        <div className="flex gap-6 mt-6">
          {(['meetings', 'contacts', 'analytics'] as const).map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`pb-2 px-1 border-b-2 transition-colors ${
                activeTab === tab
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {activeTab === 'meetings' && (
          <div className="h-full flex">
            {/* Meeting List */}
            <div className="w-1/3 border-r overflow-y-auto">
              {/* Search and Filter */}
              <div className="p-4 border-b bg-gray-50">
                <div className="flex gap-2 mb-3">
                  <div className="flex-1 relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search meetings..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border rounded-lg"
                    />
                  </div>
                  <button className="p-2 border rounded-lg hover:bg-gray-100">
                    <Filter className="w-4 h-4" />
                  </button>
                </div>
                <select
                  value={filterType}
                  onChange={(e) => setFilterType(e.target.value as MeetingType | 'all')}
                  className="w-full px-3 py-2 border rounded-lg"
                >
                  <option value="all">All Types</option>
                  <option value="tender_kickoff">Tender Kickoff</option>
                  <option value="technical_review">Technical Review</option>
                  <option value="bid_review">Bid Review</option>
                  <option value="client_meeting">Client Meeting</option>
                  <option value="internal_sync">Internal Sync</option>
                  <option value="training">Training</option>
                  <option value="workshop">Workshop</option>
                  <option value="presentation">Presentation</option>
                </select>
              </div>

              {/* Meetings */}
              <div className="divide-y">
                {meetings?.map((meeting) => (
                  <div
                    key={meeting._id}
                    onClick={() => setSelectedMeeting(meeting._id)}
                    className={`p-4 hover:bg-gray-50 cursor-pointer ${
                      selectedMeeting === meeting._id ? 'bg-blue-50' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium text-gray-900">{meeting.title}</h3>
                      <span className={`text-xs px-2 py-1 rounded-full ${getMeetingTypeColor(meeting.type)}`}>
                        {meeting.type.replace('_', ' ')}
                      </span>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3" />
                        {new Date(meeting.startTime).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        {new Date(meeting.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                    </div>
                    <div className="flex items-center gap-4 mt-2">
                      <div className="flex items-center gap-1 text-sm">
                        <Users className="w-3 h-3 text-gray-400" />
                        <span className="text-gray-600">{meeting.participants.length}</span>
                      </div>
                      <div className="flex gap-1">
                        {getStatusIcon('accepted')}
                        <span className="text-sm text-gray-600">{meeting.rsvpStats.accepted}</span>
                      </div>
                      <div className="flex gap-1">
                        {getStatusIcon('declined')}
                        <span className="text-sm text-gray-600">{meeting.rsvpStats.declined}</span>
                      </div>
                      <div className="flex gap-1">
                        {getStatusIcon('pending')}
                        <span className="text-sm text-gray-600">{meeting.rsvpStats.pending}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Meeting Details */}
            <div className="flex-1 overflow-y-auto">
              {selectedMeeting ? (
                <MeetingDetails 
                  meetingId={selectedMeeting}
                  onUpdateRSVP={updateRSVP}
                  onSendNudge={sendNudge}
                  onSyncCalendar={syncToCalendar}
                  onGenerateLink={generateMeetingLink}
                />
              ) : (
                <div className="h-full flex items-center justify-center text-gray-400">
                  <div className="text-center">
                    <Calendar className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>Select a meeting to view details</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'contacts' && (
          <ContactsView contacts={contacts} />
        )}

        {activeTab === 'analytics' && (
          <AnalyticsView />
        )}
      </div>

      {/* New Meeting Modal */}
      {showNewMeeting && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-3xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b">
              <h3 className="text-xl font-semibold">Schedule New Meeting</h3>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
              {/* Meeting Form */}
              <div className="space-y-6">
                {/* Basic Information */}
                <div>
                  <h4 className="font-medium mb-4">Basic Information</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Meeting Title
                      </label>
                      <input
                        type="text"
                        value={meetingForm.title}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full px-3 py-2 border rounded-lg"
                        placeholder="Enter meeting title"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Meeting Type
                      </label>
                      <select
                        value={meetingForm.type}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, type: e.target.value as MeetingType }))}
                        className="w-full px-3 py-2 border rounded-lg"
                      >
                        <option value="tender_kickoff">Tender Kickoff</option>
                        <option value="technical_review">Technical Review</option>
                        <option value="bid_review">Bid Review</option>
                        <option value="client_meeting">Client Meeting</option>
                        <option value="internal_sync">Internal Sync</option>
                        <option value="training">Training</option>
                        <option value="workshop">Workshop</option>
                        <option value="presentation">Presentation</option>
                      </select>
                    </div>
                  </div>
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={meetingForm.description}
                      onChange={(e) => setMeetingForm(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full px-3 py-2 border rounded-lg"
                      rows={3}
                      placeholder="Meeting description and agenda"
                    />
                  </div>
                </div>

                {/* Date and Time */}
                <div>
                  <h4 className="font-medium mb-4">Date and Time</h4>
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Start Date & Time
                      </label>
                      <input
                        type="datetime-local"
                        value={meetingForm.startTime}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, startTime: e.target.value }))}
                        className="w-full px-3 py-2 border rounded-lg"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Duration (minutes)
                      </label>
                      <input
                        type="number"
                        value={meetingForm.duration}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, duration: parseInt(e.target.value) }))}
                        className="w-full px-3 py-2 border rounded-lg"
                        min="15"
                        step="15"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Timezone
                      </label>
                      <input
                        type="text"
                        value={meetingForm.timezone}
                        className="w-full px-3 py-2 border rounded-lg bg-gray-50"
                        readOnly
                      />
                    </div>
                  </div>
                </div>

                {/* Location */}
                <div>
                  <h4 className="font-medium mb-4">Location</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Location Type
                      </label>
                      <select
                        value={meetingForm.locationType}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, locationType: e.target.value as any }))}
                        className="w-full px-3 py-2 border rounded-lg"
                      >
                        <option value="online">Online</option>
                        <option value="physical">Physical</option>
                        <option value="hybrid">Hybrid</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Location Details
                      </label>
                      <input
                        type="text"
                        value={meetingForm.locationValue}
                        onChange={(e) => setMeetingForm(prev => ({ ...prev, locationValue: e.target.value }))}
                        className="w-full px-3 py-2 border rounded-lg"
                        placeholder={meetingForm.locationType === 'online' ? 'Meeting platform' : 'Address or room'}
                      />
                    </div>
                  </div>
                </div>

                {/* Participants */}
                <div>
                  <h4 className="font-medium mb-4">Participants</h4>
                  
                  {/* Selected Participants */}
                  {meetingForm.participants.length > 0 && (
                    <div className="mb-4">
                      <p className="text-sm text-gray-600 mb-2">Selected Participants</p>
                      <div className="space-y-2">
                        {meetingForm.participants.map((participant) => {
                          const contact = contacts?.find(c => c._id === participant.contactId);
                          return (
                            <div key={participant.contactId} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <span className="text-sm font-medium text-blue-600">
                                    {contact?.name.charAt(0)}
                                  </span>
                                </div>
                                <div>
                                  <p className="font-medium text-sm">{contact?.name}</p>
                                  <p className="text-xs text-gray-500">{contact?.role}</p>
                                </div>
                              </div>
                              <button
                                onClick={() => removeParticipant(participant.contactId)}
                                className="text-red-500 hover:text-red-700"
                              >
                                <XCircle className="w-4 h-4" />
                              </button>
                            </div>
                          );
                        })}
                      </div>
                    </div>
                  )}

                  {/* Suggested Participants */}
                  <div>
                    <p className="text-sm text-gray-600 mb-2">Suggested Participants</p>
                    <div className="space-y-2 max-h-60 overflow-y-auto">
                      {suggestedParticipants?.map((suggestion) => (
                        <div key={suggestion.contact.id} className="p-3 border rounded-lg hover:bg-gray-50">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h5 className="font-medium">{suggestion.contact.name}</h5>
                                {suggestion.isRequired && (
                                  <span className="text-xs px-2 py-0.5 bg-red-100 text-red-700 rounded">Required</span>
                                )}
                                {suggestion.isRecommended && !suggestion.isRequired && (
                                  <span className="text-xs px-2 py-0.5 bg-green-100 text-green-700 rounded">Recommended</span>
                                )}
                              </div>
                              <p className="text-sm text-gray-600">{suggestion.contact.role} - {suggestion.contact.department}</p>
                              <div className="mt-1">
                                {suggestion.reasons.map((reason, idx) => (
                                  <p key={idx} className="text-xs text-gray-500">• {reason}</p>
                                ))}
                              </div>
                              <div className="mt-1 flex items-center gap-2">
                                <span className={`text-xs ${suggestion.availability.isAvailable ? 'text-green-600' : 'text-red-600'}`}>
                                  {suggestion.availability.reason}
                                </span>
                                <span className="text-xs text-gray-400">Score: {suggestion.score}</span>
                              </div>
                            </div>
                            <button
                              onClick={() => addParticipant(suggestion)}
                              disabled={meetingForm.participants.some(p => p.contactId === suggestion.contact.id)}
                              className="ml-3 p-1.5 text-blue-600 hover:bg-blue-50 rounded disabled:opacity-50"
                            >
                              <Plus className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="p-6 border-t flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowNewMeeting(false);
                  resetForm();
                }}
                className="px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateMeeting}
                disabled={!meetingForm.title || meetingForm.participants.length === 0}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
              >
                Schedule Meeting
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// Meeting Details Component
function MeetingDetails({ 
  meetingId, 
  onUpdateRSVP, 
  onSendNudge,
  onSyncCalendar,
  onGenerateLink
}: { 
  meetingId: Id<'scheduler_meetings'>;
  onUpdateRSVP: any;
  onSendNudge: any;
  onSyncCalendar: any;
  onGenerateLink: any;
}) {
  const meeting = useQuery(api.scheduler.getMeeting, { meetingId });
  const [activeSection, setActiveSection] = useState<'details' | 'participants' | 'resources'>('details');

  if (!meeting) return null;

  return (
    <div className="h-full flex flex-col">
      {/* Meeting Header */}
      <div className="p-6 border-b">
        <div className="flex items-start justify-between">
          <div>
            <h2 className="text-2xl font-semibold">{meeting.title}</h2>
            <div className="flex items-center gap-4 mt-2 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <Calendar className="w-4 h-4" />
                {new Date(meeting.startTime).toLocaleDateString()}
              </div>
              <div className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {new Date(meeting.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - 
                {new Date(meeting.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="flex items-center gap-1">
                <MapPin className="w-4 h-4" />
                {meeting.location.value}
              </div>
            </div>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => onGenerateLink({ meetingId, platform: 'teams' })}
              className="p-2 border rounded-lg hover:bg-gray-50"
              title="Generate Teams Link"
            >
              <Video className="w-4 h-4" />
            </button>
            <button
              onClick={() => onSyncCalendar({ meetingId, calendarId: 'primary', accessToken: 'mock-token' })}
              className="p-2 border rounded-lg hover:bg-gray-50"
              title="Sync to Calendar"
            >
              <Calendar className="w-4 h-4" />
            </button>
            <button
              onClick={() => onSendNudge({ meetingId })}
              className="p-2 border rounded-lg hover:bg-gray-50"
              title="Send Reminders"
            >
              <Bell className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Section Tabs */}
        <div className="flex gap-6 mt-6">
          {(['details', 'participants', 'resources'] as const).map((section) => (
            <button
              key={section}
              onClick={() => setActiveSection(section)}
              className={`pb-2 px-1 border-b-2 transition-colors ${
                activeSection === section
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {section.charAt(0).toUpperCase() + section.slice(1)}
            </button>
          ))}
        </div>
      </div>

      {/* Meeting Content */}
      <div className="flex-1 overflow-y-auto p-6">
        {activeSection === 'details' && (
          <div className="space-y-6">
            <div>
              <h3 className="font-medium mb-2">Description</h3>
              <p className="text-gray-600">{meeting.description}</p>
            </div>

            {meeting.agenda && meeting.agenda.length > 0 && (
              <div>
                <h3 className="font-medium mb-2">Agenda</h3>
                <div className="space-y-2">
                  {meeting.agenda.map((item, idx) => (
                    <div key={idx} className="flex items-center gap-3 p-3 bg-gray-50 rounded">
                      <span className="text-sm font-medium text-gray-500">{idx + 1}.</span>
                      <div className="flex-1">
                        <p className="font-medium">{item.item}</p>
                        <p className="text-sm text-gray-500">{item.duration} minutes</p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {meeting.meetingLinks && Object.keys(meeting.meetingLinks).length > 0 && (
              <div>
                <h3 className="font-medium mb-2">Meeting Links</h3>
                <div className="space-y-2">
                  {Object.entries(meeting.meetingLinks).map(([platform, link]) => (
                    <a
                      key={platform}
                      href={link as string}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
                    >
                      <ChevronRight className="w-4 h-4" />
                      Join via {platform}
                    </a>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeSection === 'participants' && (
          <div className="space-y-4">
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-green-700">
                  <CheckCircle className="w-5 h-5" />
                  <span className="font-medium">Accepted</span>
                </div>
                <p className="text-2xl font-bold text-green-900 mt-1">{meeting.rsvpStats.accepted}</p>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-red-700">
                  <XCircle className="w-5 h-5" />
                  <span className="font-medium">Declined</span>
                </div>
                <p className="text-2xl font-bold text-red-900 mt-1">{meeting.rsvpStats.declined}</p>
              </div>
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-yellow-700">
                  <AlertCircle className="w-5 h-5" />
                  <span className="font-medium">Tentative</span>
                </div>
                <p className="text-2xl font-bold text-yellow-900 mt-1">{meeting.rsvpStats.tentative}</p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-2 text-gray-700">
                  <Clock className="w-5 h-5" />
                  <span className="font-medium">Pending</span>
                </div>
                <p className="text-2xl font-bold text-gray-900 mt-1">{meeting.rsvpStats.pending}</p>
              </div>
            </div>

            <div className="space-y-2">
              {meeting.participants.map((participant) => (
                <ParticipantCard
                  key={participant.contactId}
                  participant={participant}
                  onUpdateRSVP={(response: ResponseStatus) => 
                    onUpdateRSVP({ 
                      meetingId, 
                      contactId: participant.contactId, 
                      response 
                    })
                  }
                />
              ))}
            </div>
          </div>
        )}

        {activeSection === 'resources' && (
          <div className="space-y-4">
            {meeting.resources && meeting.resources.length > 0 ? (
              meeting.resources.map((resource, idx) => (
                <div key={idx} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{resource.name}</h4>
                      <p className="text-sm text-gray-500">{resource.type}</p>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-sm ${
                      resource.status === 'confirmed' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {resource.status}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <p className="text-gray-500 text-center py-8">No resources booked for this meeting</p>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

// Participant Card Component
function ParticipantCard({ 
  participant, 
  onUpdateRSVP 
}: { 
  participant: any;
  onUpdateRSVP: (response: ResponseStatus) => void;
}) {
  const contact = useQuery(api.scheduler.getContact, { contactId: participant.contactId });

  if (!contact) return null;

  return (
    <div className="p-4 border rounded-lg hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
            <span className="text-sm font-medium text-blue-600">
              {contact.name.charAt(0)}
            </span>
          </div>
          <div>
            <div className="flex items-center gap-2">
              <p className="font-medium">{contact.name}</p>
              {participant.isRequired && (
                <span className="text-xs px-2 py-0.5 bg-red-100 text-red-700 rounded">Required</span>
              )}
            </div>
            <p className="text-sm text-gray-500">{contact.role} - {contact.department}</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {contact.notificationPreferences.email && <Mail className="w-4 h-4 text-gray-400" />}
            {contact.notificationPreferences.sms && <Phone className="w-4 h-4 text-gray-400" />}
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(participant.responseStatus)}
            <span className="text-sm text-gray-600">{participant.responseStatus}</span>
          </div>
        </div>
      </div>
    </div>
  );

  function getStatusIcon(status: ResponseStatus) {
    switch (status) {
      case 'accepted':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'declined':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'tentative':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-400" />;
    }
  }
}

// Contacts View Component
function ContactsView({ contacts }: { contacts: SchedulerContact[] | undefined }) {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterDepartment, setFilterDepartment] = useState<string>('all');

  const filteredContacts = contacts?.filter(contact => {
    const matchesSearch = contact.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         contact.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || contact.role === filterRole;
    const matchesDepartment = filterDepartment === 'all' || contact.department === filterDepartment;
    return matchesSearch && matchesRole && matchesDepartment;
  });

  const roles = [...new Set(contacts?.map(c => c.role) || [])];
  const departments = [...new Set(contacts?.map(c => c.department) || [])];

  return (
    <div className="p-6">
      {/* Filters */}
      <div className="flex gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search contacts..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-3 py-2 border rounded-lg"
            />
          </div>
        </div>
        <select
          value={filterRole}
          onChange={(e) => setFilterRole(e.target.value)}
          className="px-3 py-2 border rounded-lg"
        >
          <option value="all">All Roles</option>
          {roles.map(role => (
            <option key={role} value={role}>{role}</option>
          ))}
        </select>
        <select
          value={filterDepartment}
          onChange={(e) => setFilterDepartment(e.target.value)}
          className="px-3 py-2 border rounded-lg"
        >
          <option value="all">All Departments</option>
          {departments.map(dept => (
            <option key={dept} value={dept}>{dept}</option>
          ))}
        </select>
      </div>

      {/* Contacts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredContacts?.map(contact => (
          <div key={contact.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-lg font-medium text-blue-600">
                    {contact.name.charAt(0)}
                  </span>
                </div>
                <div>
                  <h3 className="font-medium">{contact.name}</h3>
                  <p className="text-sm text-gray-500">{contact.role}</p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded-full text-xs ${
                contact.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
              }`}>
                {contact.isActive ? 'Active' : 'Inactive'}
              </span>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2 text-gray-600">
                <Mail className="w-3 h-3" />
                {contact.email}
              </div>
              {contact.phone && (
                <div className="flex items-center gap-2 text-gray-600">
                  <Phone className="w-3 h-3" />
                  {contact.phone}
                </div>
              )}
              <div className="flex items-center gap-2 text-gray-600">
                <Users className="w-3 h-3" />
                {contact.department}
              </div>
              {contact.state && (
                <div className="flex items-center gap-2 text-gray-600">
                  <MapPin className="w-3 h-3" />
                  {contact.state}
                </div>
              )}
            </div>

            <div className="mt-3 pt-3 border-t">
              <p className="text-xs text-gray-500">Expertise:</p>
              <div className="flex flex-wrap gap-1 mt-1">
                {contact.expertise.map((exp, idx) => (
                  <span key={idx} className="text-xs px-2 py-0.5 bg-gray-100 rounded">
                    {exp}
                  </span>
                ))}
              </div>
            </div>

            <div className="mt-3 flex gap-2">
              <div className="flex items-center gap-1">
                {contact.notificationPreferences.email && (
                  <Mail className="w-3 h-3 text-gray-400" title="Email notifications enabled" />
                )}
                {contact.notificationPreferences.sms && (
                  <MessageSquare className="w-3 h-3 text-gray-400" title="SMS notifications enabled" />
                )}
                {contact.notificationPreferences.inApp && (
                  <Bell className="w-3 h-3 text-gray-400" title="In-app notifications enabled" />
                )}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// Analytics View Component  
function AnalyticsView() {
  return (
    <div className="p-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-500">Total Meetings</h3>
            <Calendar className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-3xl font-bold text-gray-900">124</p>
          <p className="text-sm text-green-600 mt-2">+12% from last month</p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-500">Avg Attendance Rate</h3>
            <Users className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-3xl font-bold text-gray-900">87%</p>
          <p className="text-sm text-green-600 mt-2">+5% from last month</p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-500">RSVP Response Rate</h3>
            <CheckCircle className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-3xl font-bold text-gray-900">92%</p>
          <p className="text-sm text-green-600 mt-2">+3% from last month</p>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-sm font-medium text-gray-500">Conflict Rate</h3>
            <AlertCircle className="w-5 h-5 text-gray-400" />
          </div>
          <p className="text-3xl font-bold text-gray-900">8%</p>
          <p className="text-sm text-red-600 mt-2">-2% from last month</p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-medium mb-4">Meeting Types Distribution</h3>
          <div className="space-y-3">
            {[
              { type: 'Internal Sync', count: 45, percentage: 36 },
              { type: 'Technical Review', count: 28, percentage: 23 },
              { type: 'Client Meeting', count: 20, percentage: 16 },
              { type: 'Bid Review', count: 15, percentage: 12 },
              { type: 'Training', count: 10, percentage: 8 },
              { type: 'Other', count: 6, percentage: 5 },
            ].map(item => (
              <div key={item.type} className="flex items-center gap-3">
                <span className="text-sm w-32">{item.type}</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${item.percentage}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600 w-12 text-right">{item.count}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border">
          <h3 className="text-lg font-medium mb-4">Platform Usage</h3>
          <div className="space-y-3">
            {[
              { platform: 'Microsoft Teams', usage: 65 },
              { platform: 'Zoom', usage: 20 },
              { platform: 'Google Meet', usage: 10 },
              { platform: 'In-Person', usage: 5 },
            ].map(item => (
              <div key={item.platform} className="flex items-center gap-3">
                <span className="text-sm w-32">{item.platform}</span>
                <div className="flex-1 bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-green-600 h-2 rounded-full" 
                    style={{ width: `${item.usage}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600 w-12 text-right">{item.usage}%</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}