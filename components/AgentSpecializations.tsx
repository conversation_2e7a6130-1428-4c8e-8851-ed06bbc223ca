'use client';

import React, { useState } from 'react';
import { 
  Brain, 
  FileText, 
  Search, 
  CheckCircle, 
  TrendingUp, 
  Shield, 
  Target,
  Zap,
  Award,
  BookOpen,
  PenTool,
  BarChart3,
  Users,
  Clock,
  Star,
  Lightbulb,
  FileCheck,
  Layers,
  Rocket,
  Gauge,
  Trophy,
  RefreshCw,
  Settings,
  Play,
  Pause,
  AlertTriangle,
  Info,
  CheckSquare,
  Edit3,
  Eye,
  MessageSquare,
  Bot,
  Sparkles,
  Activity,
  Timer,
  Hash,
  Filter,
  Download,
  Share2,
  Plus,
  Minus,
  MoreVertical,
  ArrowRight,
  ArrowUp,
  ArrowDown,
  ChevronRight,
  ChevronDown,
  Maximize2,
  Minimize2,
  ExternalLink,
  Copy,
  RotateCcw,
  Save,
  Upload,
  Trash2,
  Archive,
  Pin,
  Bookmark,
  Flag,
  Bell,
  Volume2,
  VolumeX,
  WiFi,
  WifiOff,
  Battery,
  BatteryLow,
  Signal,
  Smartphone,
  Tablet,
  Monitor,
  Laptop,
  Headphones,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  Video,
  VideoOff,
  Phone,
  PhoneOff,
  Mail,
  Send,
  Inbox,
  DraftingCompass,
  Calendar,
  MapPin,
  Navigation,
  Compass,
  Map,
  Route,
  Car,
  Plane,
  Ship,
  Train,
  Bike,
  Walk,
  Home,
  Building,
  Store,
  Factory,
  Warehouse,
  Office,
  School,
  Hospital,
  Bank,
  Hotel,
  Restaurant,
  Cafe,
  Gas,
  Parking,
  Park,
  Tree,
  Flower,
  Sun,
  Moon,
  Cloud,
  Rain,
  Snow,
  Wind,
  Thermometer,
  Umbrella,
  Sunglasses,
  Shirt,
  Jacket,
  Hat,
  Shoes,
  Watch,
  Ring,
  Necklace,
  Glasses,
  Bag,
  Wallet,
  Key,
  Lock,
  Unlock,
  CreditCard,
  Banknote,
  Coins,
  Gift,
  ShoppingCart,
  ShoppingBag,
  Tag,
  Percent,
  DollarSign,
  Euro,
  Pound,
  Yen,
  Bitcoin,
  Smartphone as SmartphoneIcon,
  Tablet as TabletIcon,
  Monitor as MonitorIcon,
  Laptop as LaptopIcon,
  Desktop,
  Server,
  Database,
  HardDrive,
  Cpu,
  Memory,
  CircuitBoard,
  Motherboard,
  GraphicsCard,
  PowerSupply,
  Fan,
  Cable,
  Usb,
  Ethernet,
  Bluetooth,
  Wifi as WifiIcon,
  Antenna,
  Radio,
  Speaker,
  Microphone,
  Camera as CameraIcon,
  Webcam,
  Printer,
  Scanner,
  Fax,
  Keyboard,
  Mouse,
  Trackpad,
  Stylus,
  Pen,
  Pencil,
  Eraser,
  Ruler,
  Scissors,
  Glue,
  Paperclip,
  Pin as PinIcon,
  Stapler,
  Calculator,
  Abacus,
  Scale,
  Stopwatch,
  Timer as TimerIcon,
  Alarm,
  Bell as BellIcon,
  Megaphone,
  Whistle,
  Horn,
  Siren,
  Music,
  Note,
  Piano,
  Guitar,
  Violin,
  Drum,
  Trumpet,
  Saxophone,
  Flute,
  Microphone as MicrophoneIcon,
  Headphones as HeadphonesIcon,
  Speaker as SpeakerIcon,
  VolumeX as VolumeXIcon,
  Volume2 as Volume2Icon,
  Pause as PauseIcon,
  Play as PlayIcon,
  Stop,
  Rewind,
  FastForward,
  SkipBack,
  SkipForward,
  Shuffle,
  Repeat,
  Repeat1,
  List,
  Grid,
  Columns,
  Rows,
  Layout,
  Sidebar,
  Menu,
  MoreHorizontal,
  MoreVertical as MoreVerticalIcon,
  ChevronLeft,
  ChevronRight as ChevronRightIcon,
  ChevronUp,
  ChevronDown as ChevronDownIcon,
  ChevronsLeft,
  ChevronsRight,
  ChevronsUp,
  ChevronsDown,
  ArrowLeft,
  ArrowRight as ArrowRightIcon,
  ArrowUp as ArrowUpIcon,
  ArrowDown as ArrowDownIcon,
  ArrowUpLeft,
  ArrowUpRight,
  ArrowDownLeft,
  ArrowDownRight,
  CornerUpLeft,
  CornerUpRight,
  CornerDownLeft,
  CornerDownRight,
  Move,
  MoveHorizontal,
  MoveVertical,
  MoveDiagonal,
  MoveDiagonal2,
  Rotate3d,
  RotateCcw as RotateCcwIcon,
  RotateCw,
  FlipHorizontal,
  FlipVertical,
  Scale3d,
  Maximize,
  Minimize,
  Expand,
  Shrink,
  ZoomIn,
  ZoomOut,
  Focus,
  Crosshair,
  Locate,
  LocateFixed,
  LocateOff,
  Navigation as NavigationIcon,
  Navigation2,
  Compass as CompassIcon,
  Map as MapIcon,
  MapPin as MapPinIcon,
  Route as RouteIcon,
  Milestone,
  Flag as FlagIcon,
  Waypoints,
  Signpost,
  TreePine,
  TreeDeciduous,
  Mountain,
  MountainSnow,
  Volcano,
  Island,
  Beach,
  Waves,
  Droplets,
  Snowflake,
  CloudRain,
  CloudSnow,
  CloudLightning,
  CloudDrizzle,
  CloudHail,
  Cloudy,
  PartlyCloudy,
  Sunrise,
  Sunset,
  Flame,
  Zap as ZapIcon,
  Bolt,
  Flashlight,
  Lightbulb as LightbulbIcon,
  Candle,
  Lamp,
  LampCeiling,
  LampDesk,
  LampFloor,
  LampWall,
  Lantern,
  Torch,
  FireExtinguisher,
  Smoke,
  Cigarette,
  CigaretteOff,
  Wine,
  Beer,
  Coffee,
  Tea,
  Milk,
  Soup,
  Utensils,
  Fork,
  Spoon,
  Knife,
  ChefHat,
  Cookie,
  Cake,
  Pizza,
  IceCream,
  Popcorn,
  Candy,
  Lollipop,
  Donut,
  Croissant,
  Bagel,
  Pretzel,
  Sandwich,
  Hamburger,
  HotDog,
  Taco,
  Burrito,
  Sushi,
  Ramen,
  Salad,
  Apple,
  Banana,
  Orange,
  Lemon,
  Lime,
  Grape,
  Strawberry,
  Cherry,
  Peach,
  Pear,
  Pineapple,
  Coconut,
  Avocado,
  Tomato,
  Cucumber,
  Carrot,
  Pepper,
  Onion,
  Garlic,
  Potato,
  Corn,
  Broccoli,
  Lettuce,
  Spinach,
  Cabbage,
  Kale,
  Celery,
  Asparagus,
  Artichoke,
  Mushroom,
  Eggplant,
  Zucchini,
  Squash,
  Pumpkin,
  Watermelon,
  Melon,
  Kiwi,
  Mango,
  Papaya,
  Guava,
  Passion,
  Dragon,
  Star,
  Rambutan,
  Lychee,
  Longan,
  Durian,
  Jackfruit,
  Breadfruit,
  Plantain,
  Yam,
  Cassava,
  Taro,
  Sweet,
  Beet,
  Radish,
  Turnip,
  Parsnip,
  Ginger,
  Turmeric,
  Galangal,
  Lemongrass,
  Basil,
  Oregano,
  Thyme,
  Rosemary,
  Sage,
  Parsley,
  Cilantro,
  Dill,
  Mint,
  Lavender,
  Chives,
  Scallion,
  Leek,
  Shallot,
  Fennel,
  Anise,
  Cardamom,
  Cinnamon,
  Nutmeg,
  Cloves,
  Allspice,
  Pepper as PepperIcon,
  Salt,
  Sugar,
  Honey,
  Syrup,
  Molasses,
  Vanilla,
  Chocolate,
  Cocoa,
  Coffee as CoffeeIcon,
  Tea as TeaIcon,
  Milk as MilkIcon,
  Cream,
  Butter,
  Cheese,
  Yogurt,
  IceCream as IceCreamIcon,
  Sorbet,
  Gelato,
  Popsicle,
  Smoothie,
  Juice,
  Soda,
  Water,
  Coconut as CoconutIcon,
  Almond,
  Walnut,
  Pecan,
  Cashew,
  Pistachio,
  Hazelnut,
  Macadamia,
  Brazil,
  Pine,
  Chestnut,
  Acorn,
  Seed,
  Sunflower,
  Pumpkin as PumpkinIcon,
  Sesame,
  Poppy,
  Flax,
  Chia,
  Hemp,
  Quinoa,
  Amaranth,
  Buckwheat,
  Millet,
  Barley,
  Oats,
  Rye,
  Wheat,
  Rice,
  Corn as CornIcon,
  Sorghum,
  Teff,
  Fonio,
  Bulgur,
  Freekeh,
  Kamut,
  Spelt,
  Einkorn,
  Emmer,
  Farro,
  Pasta,
  Noodles,
  Ramen as RamenIcon,
  Udon,
  Soba,
  Pho,
  Pad,
  Lo,
  Chow,
  Fried,
  Stir,
  Steam,
  Boil,
  Bake,
  Roast,
  Grill,
  Broil,
  Saute,
  Simmer,
  Poach,
  Braise,
  Stew,
  Curry,
  Sauce,
  Gravy,
  Marinade,
  Dressing,
  Vinaigrette,
  Mayo,
  Mustard,
  Ketchup,
  Relish,
  Pickle,
  Olive,
  Caper,
  Anchovy,
  Sardine,
  Tuna,
  Salmon,
  Cod,
  Halibut,
  Sole,
  Flounder,
  Snapper,
  Grouper,
  Bass,
  Trout,
  Catfish,
  Tilapia,
  Mahi,
  Swordfish,
  Shark,
  Ray,
  Eel,
  Octopus,
  Squid,
  Cuttlefish,
  Shrimp,
  Prawn,
  Lobster,
  Crab,
  Crayfish,
  Scallop,
  Oyster,
  Mussel,
  Clam,
  Abalone,
  Conch,
  Whelk,
  Periwinkle,
  Cockle,
  Razor,
  Geoduck,
  Sea,
  Urchin,
  Cucumber,
  Anemone,
  Starfish,
  Sand,
  Dollar,
  Coral,
  Sponge,
  Jellyfish,
  Seahorse,
  Dolphin,
  Whale,
  Shark as SharkIcon,
  Barracuda,
  Marlin,
  Sailfish,
  Wahoo,
  Dorado,
  Yellowtail,
  Amberjack,
  Kingfish,
  Mackerel,
  Bonito,
  Skipjack,
  Bluefin,
  Yellowfin,
  Bigeye,
  Albacore,
  Blackfin,
  Longtail,
  Bullet,
  Frigate,
  Little,
  Kawakawa,
  Eastern,
  Pacific,
  Southern,
  Northern,
  Atlantic,
  Indian,
  Mediterranean,
  Black,
  Red,
  White,
  Blue,
  Green,
  Yellow,
  Orange,
  Purple,
  Pink,
  Brown,
  Gray,
  Silver,
  Gold,
  Copper,
  Bronze,
  Brass,
  Steel,
  Iron,
  Aluminum,
  Titanium,
  Platinum,
  Palladium,
  Rhodium,
  Iridium,
  Osmium,
  Ruthenium,
  Rhenium,
  Tungsten,
  Molybdenum,
  Niobium,
  Tantalum,
  Vanadium,
  Chromium,
  Manganese,
  Cobalt,
  Nickel,
  Zinc,
  Gallium,
  Germanium,
  Arsenic,
  Selenium,
  Bromine,
  Krypton,
  Rubidium,
  Strontium,
  Yttrium,
  Zirconium,
  Technetium,
  Cadmium,
  Indium,
  Tin,
  Antimony,
  Tellurium,
  Iodine,
  Xenon,
  Cesium,
  Barium,
  Lanthanum,
  Cerium,
  Praseodymium,
  Neodymium,
  Promethium,
  Samarium,
  Europium,
  Gadolinium,
  Terbium,
  Dysprosium,
  Holmium,
  Erbium,
  Thulium,
  Ytterbium,
  Lutetium,
  Hafnium,
  Thorium,
  Protactinium,
  Uranium,
  Neptunium,
  Plutonium,
  Americium,
  Curium,
  Berkelium,
  Californium,
  Einsteinium,
  Fermium,
  Mendelevium,
  Nobelium,
  Lawrencium,
  Rutherfordium,
  Dubnium,
  Seaborgium,
  Bohrium,
  Hassium,
  Meitnerium,
  Darmstadtium,
  Roentgenium,
  Copernicium,
  Nihonium,
  Flerovium,
  Moscovium,
  Livermorium,
  Tennessine,
  Oganesson
} from 'lucide-react';

interface AgentSpecialization {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: string;
  capabilities: string[];
  systemPrompt: string;
  examples: string[];
  metrics: {
    accuracy: number;
    speed: number;
    creativity: number;
    compliance: number;
  };
  tags: string[];
}

const agentSpecializations: AgentSpecialization[] = [
  {
    id: 'content-writer',
    name: 'Content Writer',
    description: 'Expert in creating compelling bid content, technical documentation, and persuasive writing',
    icon: <PenTool size={24} />,
    category: 'Writing',
    capabilities: [
      'Executive Summary Writing',
      'Technical Content Creation',
      'Proposal Narrative Development',
      'Benefits & Features Articulation',
      'Value Proposition Crafting',
      'Stakeholder Communication',
    ],
    systemPrompt: `You are a professional bid writer with expertise in creating compelling tender responses. Your role is to:

1. Craft persuasive and professional content that addresses all tender requirements
2. Ensure clarity, coherence, and compelling narrative flow
3. Highlight unique value propositions and competitive advantages
4. Maintain appropriate tone and style for the target audience
5. Structure content logically with clear headings and sections
6. Use active voice and action-oriented language
7. Incorporate relevant keywords and terminology from the tender
8. Ensure content aligns with evaluation criteria and scoring weightings

Focus on creating content that not only meets requirements but stands out from competitors through clear articulation of benefits, evidence-based claims, and professional presentation.`,
    examples: [
      'Draft an executive summary for a facilities management tender',
      'Write a technical approach section for IT services',
      'Create a company overview highlighting relevant experience',
      'Develop a methodology section for consulting services',
      'Craft a benefits realization statement',
    ],
    metrics: {
      accuracy: 0.92,
      speed: 0.85,
      creativity: 0.94,
      compliance: 0.88,
    },
    tags: ['writing', 'content', 'proposals', 'technical-writing'],
  },
  {
    id: 'compliance-reviewer',
    name: 'Compliance Reviewer',
    description: 'Ensures all content meets tender requirements, regulations, and compliance standards',
    icon: <Shield size={24} />,
    category: 'Quality Assurance',
    capabilities: [
      'Requirement Mapping & Verification',
      'Compliance Gap Analysis',
      'Regulatory Adherence Checking',
      'Mandatory Criteria Validation',
      'Document Structure Review',
      'Risk Assessment & Mitigation',
    ],
    systemPrompt: `You are a compliance specialist focused on ensuring tender responses meet all requirements and regulations. Your responsibilities include:

1. Systematically verify each tender requirement is addressed
2. Check compliance with all mandatory criteria and conditions
3. Validate adherence to specified formats, word limits, and structures
4. Identify potential compliance gaps or risks
5. Ensure all certifications, licenses, and credentials are referenced
6. Verify accuracy of claims and statements
7. Check consistency across different sections
8. Flag any missing or inadequate responses to evaluation criteria

Your primary goal is to eliminate any possibility of non-compliance that could result in tender rejection or reduced scoring.`,
    examples: [
      'Review executive summary against evaluation criteria',
      'Validate technical specifications compliance',
      'Check mandatory requirements coverage',
      'Assess risk management approach adequacy',
      'Verify certification and license requirements',
    ],
    metrics: {
      accuracy: 0.98,
      speed: 0.78,
      creativity: 0.65,
      compliance: 0.99,
    },
    tags: ['compliance', 'quality-assurance', 'requirements', 'validation'],
  },
  {
    id: 'market-analyst',
    name: 'Market Analyst',
    description: 'Analyzes market trends, competitive landscape, and pricing strategies for optimal positioning',
    icon: <TrendingUp size={24} />,
    category: 'Analysis',
    capabilities: [
      'Market Research & Analysis',
      'Competitive Intelligence Gathering',
      'Pricing Strategy Development',
      'Industry Trend Identification',
      'SWOT Analysis Execution',
      'Benchmarking & Positioning',
    ],
    systemPrompt: `You are a market analyst specializing in tender and procurement intelligence. Your expertise includes:

1. Analyze market conditions and industry trends relevant to the tender
2. Research and assess competitive landscape and positioning
3. Develop competitive pricing strategies based on market data
4. Identify market opportunities and threats
5. Provide insights on client industry challenges and needs
6. Benchmark solutions against market standards
7. Assess value propositions against competitor offerings
8. Recommend positioning strategies for maximum competitive advantage

Focus on providing data-driven insights that inform strategic decision-making and competitive positioning in tender responses.`,
    examples: [
      'Analyze competitive pricing for FM services tender',
      'Research market trends in digital transformation',
      'Assess competitor strengths and weaknesses',
      'Identify industry-specific value drivers',
      'Develop competitive positioning strategy',
    ],
    metrics: {
      accuracy: 0.89,
      speed: 0.76,
      creativity: 0.82,
      compliance: 0.85,
    },
    tags: ['analysis', 'market-research', 'competitive-intelligence', 'pricing'],
  },
  {
    id: 'technical-specialist',
    name: 'Technical Specialist',
    description: 'Provides deep technical expertise and solution architecture for complex requirements',
    icon: <Cpu size={24} />,
    category: 'Technical',
    capabilities: [
      'Solution Architecture Design',
      'Technical Specification Development',
      'System Integration Planning',
      'Technology Assessment & Selection',
      'Implementation Methodology',
      'Technical Risk Analysis',
    ],
    systemPrompt: `You are a technical specialist with deep expertise in solution design and technical architecture. Your role encompasses:

1. Design comprehensive technical solutions that meet all requirements
2. Develop detailed technical specifications and architectures
3. Ensure technical feasibility and scalability of proposed solutions
4. Address integration requirements and technical dependencies
5. Identify and mitigate technical risks and challenges
6. Provide detailed implementation methodologies and timelines
7. Ensure compliance with technical standards and best practices
8. Articulate complex technical concepts in accessible language

Your focus is on creating technically sound, innovative, and implementable solutions that demonstrate deep technical competency.`,
    examples: [
      'Design cloud migration architecture',
      'Develop API integration specifications',
      'Create technical implementation roadmap',
      'Assess cybersecurity requirements and solutions',
      'Design scalable infrastructure architecture',
    ],
    metrics: {
      accuracy: 0.95,
      speed: 0.72,
      creativity: 0.88,
      compliance: 0.92,
    },
    tags: ['technical', 'architecture', 'engineering', 'solutions'],
  },
  {
    id: 'project-coordinator',
    name: 'Project Coordinator',
    description: 'Manages workflow coordination, resource allocation, and cross-functional collaboration',
    icon: <Target size={24} />,
    category: 'Management',
    capabilities: [
      'Project Planning & Scheduling',
      'Resource Allocation & Management',
      'Stakeholder Coordination',
      'Risk Management & Mitigation',
      'Quality Assurance Oversight',
      'Communication & Reporting',
    ],
    systemPrompt: `You are a project coordinator specializing in tender response management and delivery coordination. Your responsibilities include:

1. Develop comprehensive project plans and timelines for tender responses
2. Coordinate activities across different team members and specialists
3. Manage resource allocation and workload distribution
4. Identify and mitigate project risks and dependencies
5. Ensure quality standards and deadlines are met
6. Facilitate communication between stakeholders
7. Monitor progress and provide status updates
8. Optimize workflows for maximum efficiency and quality

Your goal is to ensure seamless coordination and successful delivery of high-quality tender responses within specified timeframes.`,
    examples: [
      'Create tender response project timeline',
      'Coordinate multi-disciplinary team efforts',
      'Manage stakeholder review and approval processes',
      'Develop risk mitigation strategies',
      'Optimize resource allocation across sections',
    ],
    metrics: {
      accuracy: 0.87,
      speed: 0.91,
      creativity: 0.75,
      compliance: 0.93,
    },
    tags: ['coordination', 'project-management', 'planning', 'optimization'],
  },
  {
    id: 'financial-analyst',
    name: 'Financial Analyst',
    description: 'Develops pricing models, cost analysis, and financial projections for tender responses',
    icon: <BarChart3 size={24} />,
    category: 'Financial',
    capabilities: [
      'Cost Modeling & Analysis',
      'Pricing Strategy Development',
      'Financial Risk Assessment',
      'ROI & Value Analysis',
      'Budget Planning & Forecasting',
      'Commercial Terms Optimization',
    ],
    systemPrompt: `You are a financial analyst specializing in tender pricing and commercial strategy. Your expertise includes:

1. Develop comprehensive cost models and pricing strategies
2. Analyze financial requirements and commercial terms
3. Create accurate budget forecasts and financial projections
4. Assess financial risks and develop mitigation strategies
5. Calculate ROI and value propositions for clients
6. Optimize pricing for competitiveness while maintaining profitability
7. Ensure compliance with financial and commercial requirements
8. Provide clear financial justifications and explanations

Focus on creating financially sound and competitive commercial proposals that demonstrate value and viability.`,
    examples: [
      'Develop comprehensive pricing model for 5-year contract',
      'Calculate total cost of ownership for proposed solution',
      'Analyze financial risks and mitigation strategies',
      'Create value for money analysis',
      'Develop payment terms and commercial structure',
    ],
    metrics: {
      accuracy: 0.96,
      speed: 0.79,
      creativity: 0.71,
      compliance: 0.94,
    },
    tags: ['financial', 'pricing', 'analysis', 'commercial'],
  },
  {
    id: 'industry-expert',
    name: 'Industry Expert',
    description: 'Provides deep sector knowledge and industry-specific insights for specialized tenders',
    icon: <BookOpen size={24} />,
    category: 'Specialist',
    capabilities: [
      'Sector-Specific Knowledge',
      'Industry Trend Analysis',
      'Regulatory Compliance Expertise',
      'Best Practice Identification',
      'Innovation & Emerging Technologies',
      'Stakeholder Landscape Understanding',
    ],
    systemPrompt: `You are an industry expert with deep knowledge across various sectors including healthcare, education, government, finance, and technology. Your role involves:

1. Provide sector-specific insights and expertise
2. Identify industry trends, challenges, and opportunities
3. Ensure compliance with industry-specific regulations and standards
4. Recommend best practices and innovative approaches
5. Understand unique stakeholder needs and priorities
6. Apply industry knowledge to solution design and positioning
7. Identify relevant case studies and success stories
8. Ensure terminology and approach align with industry expectations

Your expertise helps ensure tender responses demonstrate deep understanding of the client's industry and specific challenges.`,
    examples: [
      'Provide healthcare sector compliance requirements',
      'Identify education technology trends and opportunities',
      'Analyze government procurement best practices',
      'Assess financial services regulatory requirements',
      'Recommend industry-specific innovation approaches',
    ],
    metrics: {
      accuracy: 0.93,
      speed: 0.84,
      creativity: 0.86,
      compliance: 0.90,
    },
    tags: ['industry', 'expertise', 'knowledge', 'specialization'],
  },
  {
    id: 'risk-assessor',
    name: 'Risk Assessor',
    description: 'Identifies, analyzes, and develops mitigation strategies for project and delivery risks',
    icon: <AlertTriangle size={24} />,
    category: 'Risk Management',
    capabilities: [
      'Risk Identification & Analysis',
      'Risk Matrix Development',
      'Mitigation Strategy Planning',
      'Contingency Planning',
      'Impact Assessment',
      'Risk Monitoring & Reporting',
    ],
    systemPrompt: `You are a risk management specialist focused on identifying and mitigating risks in tender responses and project delivery. Your responsibilities include:

1. Systematically identify potential risks across all project dimensions
2. Assess probability and impact of identified risks
3. Develop comprehensive risk mitigation strategies
4. Create contingency plans for high-impact scenarios
5. Ensure risk management approach meets tender requirements
6. Provide clear risk communication and reporting frameworks
7. Balance risk mitigation with cost and schedule constraints
8. Demonstrate proactive risk management capabilities

Your goal is to provide confidence to clients that risks are well understood and effectively managed.`,
    examples: [
      'Develop comprehensive project risk register',
      'Assess cybersecurity risks and mitigation strategies',
      'Create business continuity and disaster recovery plans',
      'Analyze supply chain and vendor risks',
      'Develop risk monitoring and reporting framework',
    ],
    metrics: {
      accuracy: 0.91,
      speed: 0.82,
      creativity: 0.77,
      compliance: 0.96,
    },
    tags: ['risk', 'assessment', 'mitigation', 'contingency'],
  },
  {
    id: 'sustainability-advisor',
    name: 'Sustainability Advisor',
    description: 'Ensures environmental and social sustainability requirements are addressed comprehensively',
    icon: <Layers size={24} />,
    category: 'Sustainability',
    capabilities: [
      'Environmental Impact Assessment',
      'Sustainability Strategy Development',
      'Carbon Footprint Analysis',
      'Social Value Creation',
      'Circular Economy Principles',
      'ESG Compliance & Reporting',
    ],
    systemPrompt: `You are a sustainability specialist focused on environmental and social sustainability in tender responses. Your expertise includes:

1. Assess environmental impact and develop mitigation strategies
2. Create comprehensive sustainability strategies and commitments
3. Calculate and minimize carbon footprint of proposed solutions
4. Develop social value and community benefit programs
5. Apply circular economy principles to solution design
6. Ensure compliance with ESG requirements and standards
7. Identify sustainability innovation opportunities
8. Provide clear sustainability metrics and reporting frameworks

Your role is to demonstrate genuine commitment to sustainability while meeting all environmental and social requirements.`,
    examples: [
      'Develop carbon neutral delivery strategy',
      'Create social value and community benefit plan',
      'Assess environmental impact of proposed solutions',
      'Design circular economy waste management approach',
      'Develop sustainability KPIs and reporting framework',
    ],
    metrics: {
      accuracy: 0.88,
      speed: 0.75,
      creativity: 0.92,
      compliance: 0.89,
    },
    tags: ['sustainability', 'environment', 'social-value', 'ESG'],
  },
  {
    id: 'innovation-strategist',
    name: 'Innovation Strategist',
    description: 'Identifies and integrates cutting-edge technologies and innovative approaches',
    icon: <Lightbulb size={24} />,
    category: 'Innovation',
    capabilities: [
      'Technology Trend Analysis',
      'Innovation Framework Development',
      'Digital Transformation Strategy',
      'Emerging Technology Integration',
      'Innovation Roadmap Creation',
      'Future-Proofing Solutions',
    ],
    systemPrompt: `You are an innovation strategist specializing in identifying and integrating cutting-edge technologies and innovative approaches. Your role involves:

1. Identify relevant emerging technologies and innovation opportunities
2. Develop comprehensive innovation strategies and frameworks
3. Design digital transformation approaches and roadmaps
4. Integrate AI, IoT, blockchain, and other emerging technologies
5. Create future-proof solutions that adapt to changing needs
6. Balance innovation with practical implementation considerations
7. Demonstrate thought leadership and forward-thinking approaches
8. Ensure innovation adds genuine value and competitive advantage

Your focus is on positioning solutions as innovative leaders while ensuring practical viability and client value.`,
    examples: [
      'Integrate AI and machine learning capabilities',
      'Design IoT-enabled smart building solutions',
      'Develop blockchain-based transparency frameworks',
      'Create digital twin and predictive analytics strategies',
      'Design human-centered innovation approaches',
    ],
    metrics: {
      accuracy: 0.85,
      speed: 0.88,
      creativity: 0.97,
      compliance: 0.83,
    },
    tags: ['innovation', 'technology', 'digital-transformation', 'emerging-tech'],
  }
];

interface AgentSpecializationsProps {
  className?: string;
  onSelectSpecialization?: (specialization: AgentSpecialization) => void;
  selectedSpecializations?: string[];
}

export default function AgentSpecializations({ 
  className = '', 
  onSelectSpecialization,
  selectedSpecializations = []
}: AgentSpecializationsProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedCard, setExpandedCard] = useState<string | null>(null);

  const categories = ['All', ...Array.from(new Set(agentSpecializations.map(spec => spec.category)))];

  const filteredSpecializations = agentSpecializations.filter(spec => {
    const matchesCategory = selectedCategory === 'All' || spec.category === selectedCategory;
    const matchesSearch = searchQuery === '' || 
      spec.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spec.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spec.capabilities.some(cap => cap.toLowerCase().includes(searchQuery.toLowerCase())) ||
      spec.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesSearch;
  });

  const renderMetricBar = (label: string, value: number, color: string) => (
    <div className="space-y-1">
      <div className="flex justify-between text-xs">
        <span className="text-text-tertiary">{label}</span>
        <span className="font-medium">{Math.round(value * 100)}%</span>
      </div>
      <div className="w-full bg-surface-tertiary rounded-full h-2">
        <div
          className={`h-2 rounded-full transition-all duration-500 ${color}`}
          style={{ width: `${value * 100}%` }}
        />
      </div>
    </div>
  );

  const renderSpecializationCard = (spec: AgentSpecialization) => {
    const isSelected = selectedSpecializations.includes(spec.id);
    const isExpanded = expandedCard === spec.id;

    return (
      <div
        key={spec.id}
        className={`border rounded-lg p-6 transition-all duration-200 hover:shadow-lg ${
          isSelected 
            ? 'border-primary bg-primary/5 shadow-md' 
            : 'border-border hover:border-primary/50'
        }`}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              isSelected ? 'bg-primary text-white' : 'bg-surface-secondary text-text-primary'
            }`}>
              {spec.icon}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-text-primary">{spec.name}</h3>
              <span className="text-sm text-text-tertiary bg-surface-secondary px-2 py-1 rounded">
                {spec.category}
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setExpandedCard(isExpanded ? null : spec.id)}
              className="p-1 hover:bg-surface-secondary rounded transition-colors"
            >
              {isExpanded ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            {onSelectSpecialization && (
              <button
                onClick={() => onSelectSpecialization(spec)}
                className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                  isSelected
                    ? 'bg-primary text-white hover:bg-primary-700'
                    : 'bg-surface-secondary text-text-primary hover:bg-surface-tertiary'
                }`}
              >
                {isSelected ? 'Selected' : 'Select'}
              </button>
            )}
          </div>
        </div>

        {/* Description */}
        <p className="text-text-secondary mb-4 leading-relaxed">
          {spec.description}
        </p>

        {/* Capabilities */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-text-primary mb-2">Key Capabilities</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {spec.capabilities.slice(0, isExpanded ? spec.capabilities.length : 4).map((capability, index) => (
              <div key={index} className="flex items-center space-x-2 text-sm">
                <CheckCircle size={12} className="text-success flex-shrink-0" />
                <span className="text-text-secondary">{capability}</span>
              </div>
            ))}
          </div>
          {!isExpanded && spec.capabilities.length > 4 && (
            <button
              onClick={() => setExpandedCard(spec.id)}
              className="text-sm text-primary hover:text-primary-700 mt-2"
            >
              +{spec.capabilities.length - 4} more capabilities
            </button>
          )}
        </div>

        {/* Performance Metrics */}
        <div className="mb-4">
          <h4 className="text-sm font-medium text-text-primary mb-3">Performance Metrics</h4>
          <div className="grid grid-cols-2 gap-4">
            {renderMetricBar('Accuracy', spec.metrics.accuracy, 'bg-primary')}
            {renderMetricBar('Speed', spec.metrics.speed, 'bg-accent')}
            {renderMetricBar('Creativity', spec.metrics.creativity, 'bg-secondary')}
            {renderMetricBar('Compliance', spec.metrics.compliance, 'bg-success')}
          </div>
        </div>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="space-y-4 border-t border-border pt-4">
            {/* System Prompt Preview */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-2">AI Configuration</h4>
              <div className="bg-surface-secondary p-3 rounded text-sm text-text-secondary">
                <div className="flex items-center space-x-2 mb-2">
                  <Bot size={12} />
                  <span className="font-medium">System Prompt Preview</span>
                </div>
                <p className="line-clamp-3">
                  {spec.systemPrompt.substring(0, 200)}...
                </p>
              </div>
            </div>

            {/* Example Tasks */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-2">Example Tasks</h4>
              <div className="space-y-2">
                {spec.examples.map((example, index) => (
                  <div key={index} className="flex items-start space-x-2 text-sm">
                    <Lightbulb size={12} className="text-warning flex-shrink-0 mt-0.5" />
                    <span className="text-text-secondary">{example}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h4 className="text-sm font-medium text-text-primary mb-2">Tags</h4>
              <div className="flex flex-wrap gap-2">
                {spec.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="px-2 py-1 bg-surface-tertiary text-text-tertiary rounded-full text-xs"
                  >
                    #{tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-text-primary mb-2">
          AI Agent Specializations
        </h2>
        <p className="text-text-tertiary max-w-2xl mx-auto">
          Choose from our specialized AI agents, each expertly trained for specific aspects 
          of bid writing and tender response development.
        </p>
      </div>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        {/* Search */}
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={16} />
          <input
            type="text"
            placeholder="Search specializations..."
            className="w-full pl-10 pr-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Category Filter */}
        <div className="flex items-center space-x-2">
          <Filter size={16} className="text-text-tertiary" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
          >
            {categories.map(category => (
              <option key={category} value={category}>
                {category}
              </option>
            ))}
          </select>
        </div>

        {/* Stats */}
        <div className="text-sm text-text-tertiary">
          {filteredSpecializations.length} of {agentSpecializations.length} specializations
        </div>
      </div>

      {/* Specialization Cards Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredSpecializations.map(renderSpecializationCard)}
      </div>

      {/* Empty State */}
      {filteredSpecializations.length === 0 && (
        <div className="text-center py-12">
          <Search size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-lg font-medium text-text-primary mb-2">
            No specializations found
          </h3>
          <p className="text-text-tertiary">
            Try adjusting your search query or category filter
          </p>
        </div>
      )}

      {/* Quick Stats */}
      <div className="bg-surface-secondary rounded-lg p-6">
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Agent Performance Overview
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-primary mb-1">
              {Math.round(agentSpecializations.reduce((sum, spec) => sum + spec.metrics.accuracy, 0) / agentSpecializations.length * 100)}%
            </div>
            <div className="text-sm text-text-tertiary">Avg Accuracy</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-accent mb-1">
              {Math.round(agentSpecializations.reduce((sum, spec) => sum + spec.metrics.speed, 0) / agentSpecializations.length * 100)}%
            </div>
            <div className="text-sm text-text-tertiary">Avg Speed</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-secondary mb-1">
              {Math.round(agentSpecializations.reduce((sum, spec) => sum + spec.metrics.creativity, 0) / agentSpecializations.length * 100)}%
            </div>
            <div className="text-sm text-text-tertiary">Avg Creativity</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-success mb-1">
              {Math.round(agentSpecializations.reduce((sum, spec) => sum + spec.metrics.compliance, 0) / agentSpecializations.length * 100)}%
            </div>
            <div className="text-sm text-text-tertiary">Avg Compliance</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export { agentSpecializations };
export type { AgentSpecialization };