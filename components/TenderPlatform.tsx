'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useAuthActions } from '@convex-dev/auth/react';
import { useQuery } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';

// Layout components
import Header from './layout/Header';
import Sidebar from './layout/Sidebar';
import TabNavigation from './layout/TabNavigation';
import LoadingSpinner from './layout/LoadingSpinner';
import ErrorBoundary, { SimpleErrorFallback } from './layout/ErrorBoundary';

// Tab components
import Dashboard from './layout/tabs/Dashboard';
import BidStudio from './layout/tabs/BidStudio';
import Files from './layout/tabs/Files';
import SearchTab from './layout/tabs/SearchTab';
import Agents from './layout/tabs/Agents';
import Workflow from './layout/tabs/Workflow';

// Zero-Touch System
import ZeroTouchDashboard from './ZeroTouchDashboard';

// Legacy components (for backward compatibility)
import BidWritingStudio from './BidWritingStudio';
import TenderUpload from './TenderUpload';

interface TenderPlatformProps {
  className?: string;
}

interface PlatformState {
  activeTab: string;
  isMobileMenuOpen: boolean;
  sidebarCollapsed: boolean;
  isLoading: boolean;
  hasError: boolean;
  errorMessage?: string;
}

export default function TenderPlatform({ className = '' }: TenderPlatformProps) {
  const { signOut } = useAuthActions();
  
  // Platform state management
  const [state, setState] = useState<PlatformState>({
    activeTab: 'dashboard',
    isMobileMenuOpen: false,
    sidebarCollapsed: false,
    isLoading: true,
    hasError: false,
  });

  // Convex queries
  const tenders = useQuery(api.tenders.list);
  const user = useQuery(api.auth.currentUser);

  // Initialize platform state
  useEffect(() => {
    // Load saved tab from localStorage
    const savedTab = localStorage.getItem('tenderPlatform:activeTab');
    if (savedTab) {
      setState(prev => ({ ...prev, activeTab: savedTab }));
    }

    // Load sidebar state from localStorage
    const sidebarState = localStorage.getItem('tenderPlatform:sidebarCollapsed');
    if (sidebarState === 'true') {
      setState(prev => ({ ...prev, sidebarCollapsed: true }));
    }

    // Set loading to false after initialization
    setState(prev => ({ ...prev, isLoading: false }));
  }, []);

  // Handle tab changes
  const handleTabChange = (tab: string) => {
    setState(prev => ({ ...prev, activeTab: tab }));
    localStorage.setItem('tenderPlatform:activeTab', tab);
    
    // Close mobile menu if open
    if (state.isMobileMenuOpen) {
      setState(prev => ({ ...prev, isMobileMenuOpen: false }));
    }
  };

  // Handle mobile menu toggle
  const handleMobileMenuToggle = () => {
    setState(prev => ({ ...prev, isMobileMenuOpen: !prev.isMobileMenuOpen }));
  };

  // Handle mobile menu close
  const handleMobileMenuClose = () => {
    setState(prev => ({ ...prev, isMobileMenuOpen: false }));
  };

  // Handle sidebar toggle
  const handleSidebarToggle = () => {
    const newCollapsed = !state.sidebarCollapsed;
    setState(prev => ({ ...prev, sidebarCollapsed: newCollapsed }));
    localStorage.setItem('tenderPlatform:sidebarCollapsed', newCollapsed.toString());
  };

  // Handle errors
  const handleError = (error: Error) => {
    console.error('Platform error:', error);
    setState(prev => ({ 
      ...prev, 
      hasError: true, 
      errorMessage: error.message 
    }));
    toast.error('An error occurred. Please try again.');
  };

  // Reset error state
  const resetError = () => {
    setState(prev => ({ ...prev, hasError: false, errorMessage: undefined }));
  };

  // Render tab content
  const renderTabContent = () => {
    // Handle special cases first
    if (state.activeTab === 'legacy-bid-studio') {
      return <BidWritingStudio />;
    }
    
    if (state.activeTab === 'new-tender' || state.activeTab === 'upload-docs') {
      return <TenderUpload />;
    }

    // Main tab content
    switch (state.activeTab) {
      case 'dashboard':
        return <Dashboard />;
      case 'bid-studio':
        return <BidStudio />;
      case 'files':
        return <Files />;
      case 'search':
        return <SearchTab />;
      case 'agents':
        return <Agents />;
      case 'workflow':
        return <Workflow />;
      case 'zero-touch':
        return <ZeroTouchDashboard />;
      case 'settings':
        return (
          <div className="p-6">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl font-bold text-text-primary mb-4">Settings</h1>
              <p className="text-text-tertiary">System configuration and preferences - Coming soon</p>
            </div>
          </div>
        );
      case 'help':
        return (
          <div className="p-6">
            <div className="max-w-4xl mx-auto text-center">
              <h1 className="text-3xl font-bold text-text-primary mb-4">Help & Support</h1>
              <p className="text-text-tertiary">Documentation and support resources - Coming soon</p>
            </div>
          </div>
        );
      default:
        return <Dashboard />;
    }
  };

  // Handle loading state
  if (state.isLoading) {
    return (
      <div className="h-screen flex items-center justify-center bg-background">
        <LoadingSpinner 
          size="lg" 
          variant="primary" 
          message="Loading Tender Platform..." 
        />
      </div>
    );
  }

  // Handle error state
  if (state.hasError) {
    return (
      <div className="h-screen flex items-center justify-center bg-background p-4">
        <div className="max-w-md w-full">
          <SimpleErrorFallback
            error={new Error(state.errorMessage || 'An unexpected error occurred')}
            errorInfo={{ componentStack: '' }}
            resetError={resetError}
          />
        </div>
      </div>
    );
  }

  // Main platform layout
  return (
    <ErrorBoundary onError={handleError}>
      <div className={`min-h-screen bg-background text-text-primary ${className}`}>
        {/* Header */}
        <Header
          onMobileMenuToggle={handleMobileMenuToggle}
          isMobileMenuOpen={state.isMobileMenuOpen}
          activeTab={state.activeTab}
          onTabChange={handleTabChange}
        />

        {/* Main Layout */}
        <div className="flex h-[calc(100vh-64px)]">
          {/* Sidebar */}
          <Sidebar
            activeTab={state.activeTab}
            onTabChange={handleTabChange}
            isMobileMenuOpen={state.isMobileMenuOpen}
            onMobileMenuClose={handleMobileMenuClose}
          />

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Tab Navigation */}
            <TabNavigation
              activeTab={state.activeTab}
              onTabChange={handleTabChange}
              className="flex-shrink-0"
            />

            {/* Content Area */}
            <main className="flex-1 overflow-y-auto bg-background">
              <ErrorBoundary fallback={SimpleErrorFallback}>
                <Suspense
                  fallback={
                    <div className="h-full flex items-center justify-center">
                      <LoadingSpinner 
                        size="lg" 
                        variant="primary" 
                        message="Loading content..." 
                      />
                    </div>
                  }
                >
                  {renderTabContent()}
                </Suspense>
              </ErrorBoundary>
            </main>
          </div>
        </div>

        {/* Global Keyboard Shortcuts */}
        <KeyboardShortcuts onTabChange={handleTabChange} />
      </div>
    </ErrorBoundary>
  );
}

// Keyboard shortcuts component
function KeyboardShortcuts({ onTabChange }: { onTabChange: (tab: string) => void }) {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Only handle shortcuts when no input is focused
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      // Handle Ctrl/Cmd + key combinations
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case '1':
            event.preventDefault();
            onTabChange('dashboard');
            break;
          case '2':
            event.preventDefault();
            onTabChange('bid-studio');
            break;
          case '3':
            event.preventDefault();
            onTabChange('files');
            break;
          case '4':
            event.preventDefault();
            onTabChange('search');
            break;
          case '5':
            event.preventDefault();
            onTabChange('agents');
            break;
          case '6':
            event.preventDefault();
            onTabChange('workflow');
            break;
          case 'n':
            event.preventDefault();
            onTabChange('new-tender');
            break;
          case 'u':
            event.preventDefault();
            onTabChange('upload-docs');
            break;
          default:
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [onTabChange]);

  return null;
}