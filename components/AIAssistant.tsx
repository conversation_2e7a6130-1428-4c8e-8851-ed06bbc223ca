'use client';

import React, { useState, useRef, useEffect } from 'react';
import {
  Lightbulb,
  X,
  Send,
  Wand2,
  Brain,
  Target,
  CheckCircle,
  AlertCircle,
  MessageSquare,
  Sparkles,
  TrendingUp,
  FileText,
  Zap,
  RefreshCw,
} from 'lucide-react';
import { useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import { toast } from 'sonner';

interface Section {
  _id: string;
  title: string;
  content: string;
  wordCount: number;
  wordLimit: number;
  scoreWeight: number;
  status: string;
  requirements?: string[];
  type?: string;
}

interface Tender {
  _id: string;
  name: string;
  clientName: string;
  type?: string;
  projectLocation?: string;
  estimatedValue?: number;
  dueDate: string;
}

interface AIAssistantProps {
  section: Section;
  tender: Tender;
  onClose: () => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
}

const aiPromptTemplates = {
  improve: 'Improve this section content by enhancing clarity, persuasiveness, and addressing key requirements',
  expand: 'Expand this section with more detailed explanations, examples, and supporting evidence',
  summarize: 'Summarize this section content while maintaining key points and persuasive elements',
  restructure: 'Restructure this section for better flow and logical organization',
  requirements: 'Ensure this section addresses all requirements and evaluation criteria',
  strengthen: 'Strengthen the competitive advantages and value proposition in this section',
  proofread: 'Proofread and refine this section for grammar, clarity, and professional tone',
  keyword_optimize: 'Optimize this section to include relevant keywords and industry terms',
};

export default function AIAssistant({ section, tender, onClose }: AIAssistantProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const generateAIResponse = useAction(api.ai.generateAIAssistantResponse);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    // Add welcome message
    const welcomeMessage: ChatMessage = {
      id: '1',
      type: 'assistant',
      content: `Hello! I'm your AI writing assistant for the "${section.title}" section. I can help you improve your content, provide suggestions, and ensure you meet all requirements. How can I assist you today?`,
      timestamp: new Date(),
      suggestions: [
        'Improve current content',
        'Check requirements coverage',
        'Suggest competitive advantages',
        'Optimize for evaluation criteria',
      ],
    };
    setMessages([welcomeMessage]);
  }, [section.title]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsGenerating(true);

    try {
      const response = await generateAIResponse({
        sectionId: section._id as any,
        userMessage: inputMessage,
        sectionContent: section.content,
        sectionTitle: section.title,
        sectionRequirements: section.requirements || [],
        tenderName: tender.name,
        clientName: tender.clientName,
        context: {
          wordCount: section.wordCount,
          wordLimit: section.wordLimit,
          scoreWeight: section.scoreWeight,
          status: section.status,
          tenderType: tender.type,
        },
      });

      const aiMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.content,
        timestamp: new Date(),
        suggestions: response.suggestions,
      };

      setMessages(prev => [...prev, aiMessage]);
    } catch (error) {
      console.error('AI Assistant error:', error);
      toast.error('Failed to get AI response');
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date(),
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTemplateSelect = (template: string) => {
    setSelectedTemplate(template);
    setInputMessage(aiPromptTemplates[template as keyof typeof aiPromptTemplates]);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <div className="bg-gray-900 border-b border-gray-700 p-4 flex flex-col h-96">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-bold flex items-center space-x-2">
          <Brain size={20} className="text-purple-400" />
          <span>AI Assistant</span>
        </h3>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white p-1"
        >
          <X size={16} />
        </button>
      </div>

      {/* Section Info */}
      <div className="bg-gray-800 rounded-lg p-3 mb-4">
        <div className="flex items-center space-x-2 mb-2">
          <FileText size={16} className="text-blue-400" />
          <span className="text-sm font-medium">{section.title}</span>
        </div>
        <div className="grid grid-cols-2 gap-2 text-xs text-gray-400">
          <div>Words: {section.wordCount}/{section.wordLimit || '∞'}</div>
          <div>Weight: {section.scoreWeight}%</div>
          <div>Status: {section.status}</div>
          <div>Requirements: {section.requirements?.length || 0}</div>
        </div>
      </div>

      {/* AI Prompt Templates */}
      <div className="mb-4">
        <h4 className="text-sm font-semibold mb-2 text-gray-300">Quick Actions</h4>
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(aiPromptTemplates).slice(0, 8).map(([key, template]) => (
            <button
              key={key}
              onClick={() => handleTemplateSelect(key)}
              className={`p-2 text-xs rounded border transition-all ${
                selectedTemplate === key
                  ? 'bg-purple-600 border-purple-400 text-white'
                  : 'bg-gray-800 border-gray-600 text-gray-300 hover:bg-gray-700'
              }`}
            >
              {key.replace('_', ' ').toUpperCase()}
            </button>
          ))}
        </div>
      </div>

      {/* Chat Messages */}
      <div className="flex-1 overflow-y-auto mb-4 space-y-3">
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-xs p-3 rounded-lg ${
                message.type === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-800 text-gray-100'
              }`}
            >
              <div className="text-sm">{message.content}</div>
              <div className="text-xs mt-1 opacity-70">
                {message.timestamp.toLocaleTimeString()}
              </div>
              
              {message.suggestions && (
                <div className="mt-3 space-y-1">
                  <div className="text-xs text-gray-400">Suggestions:</div>
                  {message.suggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => handleSuggestionClick(suggestion)}
                      className="block w-full text-left text-xs p-2 bg-gray-700 hover:bg-gray-600 rounded"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {isGenerating && (
          <div className="flex justify-start">
            <div className="bg-gray-800 text-gray-100 p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <RefreshCw size={16} className="animate-spin" />
                <span className="text-sm">Thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Area */}
      <div className="flex items-center space-x-2">
        <div className="flex-1 relative">
          <textarea
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about this section..."
            className="w-full p-3 bg-gray-800 border border-gray-600 rounded-lg text-sm resize-none focus:outline-none focus:border-purple-400"
            rows={2}
          />
          <div className="absolute bottom-2 right-2 text-xs text-gray-400">
            Enter to send
          </div>
        </div>
        <button
          onClick={handleSendMessage}
          disabled={!inputMessage.trim() || isGenerating}
          className="p-3 bg-purple-600 hover:bg-purple-700 disabled:opacity-50 rounded-lg text-white"
        >
          <Send size={16} />
        </button>
      </div>

      {/* Quick Stats */}
      <div className="mt-4 grid grid-cols-3 gap-2 text-xs">
        <div className="bg-gray-800 rounded p-2 text-center">
          <div className="text-green-400 font-bold">85%</div>
          <div className="text-gray-400">Coverage</div>
        </div>
        <div className="bg-gray-800 rounded p-2 text-center">
          <div className="text-blue-400 font-bold">7.2</div>
          <div className="text-gray-400">Readability</div>
        </div>
        <div className="bg-gray-800 rounded p-2 text-center">
          <div className="text-purple-400 font-bold">92%</div>
          <div className="text-gray-400">Quality</div>
        </div>
      </div>
    </div>
  );
}