'use client';

import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useAction } from 'convex/react';
import { api } from '../convex/_generated/api';
import {
  Zap,
  FileText,
  Clock,
  CheckCircle,
  AlertCircle,
  Users,
  Calendar,
  MessageSquare,
  Play,
  Pause,
  RotateCcw,
  Eye,
  Download,
  Settings,
  Activity,
  TrendingUp,
  AlertTriangle,
  XCircle,
} from 'lucide-react';
import { toast } from 'sonner';

interface WorkflowStage {
  id: string;
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  duration?: number;
  error?: string;
}

interface WorkflowInstance {
  _id: string;
  sourceType: string;
  stage: string;
  status: string;
  outcome?: string;
  createdAt: number;
  updatedAt: number;
  tenderId?: string;
  meetingId?: string;
  error?: string;
}

export default function ZeroTouchDashboard() {
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  const [showSettings, setShowSettings] = useState(false);

  // Queries
  const activeWorkflows = useQuery(api.zeroTouchWorkflow.listActiveWorkflows);
  const workflowEvents = useQuery(
    api.zeroTouchWorkflow.getWorkflowEvents,
    selectedWorkflow ? { workflowId: selectedWorkflow } : 'skip'
  );

  // Actions
  const initiateWorkflow = useAction(api.zeroTouchWorkflow.initiateZeroTouchWorkflow);

  const handleFileUpload = async (files: FileList) => {
    if (!files.length) return;

    const file = files[0];
    toast.info('Starting zero-touch workflow...');

    try {
      // Upload file first (assuming existing file upload system)
      // Then initiate workflow
      const result = await initiateWorkflow({
        sourceType: 'manual',
        sourceData: { fileName: file.name, fileSize: file.size },
        priority: 'high',
      });

      if (result.success) {
        toast.success('Zero-touch workflow initiated successfully!');
        setSelectedWorkflow(result.workflowId);
      } else {
        toast.error(`Workflow failed: ${result.error}`);
      }
    } catch (error) {
      toast.error('Failed to initiate workflow');
      console.error(error);
    }
  };

  const getStageIcon = (stage: string, status: string) => {
    if (status === 'failed') return <XCircle className="w-5 h-5 text-red-400" />;
    if (status === 'completed') return <CheckCircle className="w-5 h-5 text-green-400" />;
    if (status === 'in_progress') return <Activity className="w-5 h-5 text-blue-400 animate-spin" />;
    return <Clock className="w-5 h-5 text-gray-400" />;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20 border-green-400';
      case 'in_progress': return 'text-blue-400 bg-blue-900/20 border-blue-400';
      case 'failed': return 'text-red-400 bg-red-900/20 border-red-400';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-600';
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header */}
      <div className="border-b border-border bg-surface p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary-600 rounded-lg">
              <Zap className="w-6 h-6 text-white" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-text-primary">Zero-Touch Tender System</h1>
              <p className="text-sm text-text-secondary">Automated tender processing and kick-off management</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 hover:bg-surface-hover rounded-lg transition-colors"
            >
              <Settings className="w-5 h-5 text-text-secondary" />
            </button>
            
            <label className="btn-primary cursor-pointer">
              <FileText className="w-4 h-4" />
              Upload Tender
              <input
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
              />
            </label>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Workflow List */}
        <div className="w-96 border-r border-border bg-surface">
          <div className="p-4 border-b border-border">
            <h2 className="font-semibold text-text-primary mb-2">Active Workflows</h2>
            <div className="text-sm text-text-secondary">
              {activeWorkflows?.length || 0} running workflows
            </div>
          </div>

          <div className="overflow-y-auto">
            {activeWorkflows?.map((workflow: WorkflowInstance) => (
              <div
                key={workflow._id}
                onClick={() => setSelectedWorkflow(workflow._id)}
                className={`p-4 border-b border-border cursor-pointer transition-colors ${
                  selectedWorkflow === workflow._id ? 'bg-primary-900/20' : 'hover:bg-surface-hover'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="font-medium text-text-primary text-sm">
                    {workflow.sourceType.toUpperCase()} - {workflow.stage}
                  </div>
                  <div className={`px-2 py-1 rounded text-xs font-medium border ${getStatusColor(workflow.status)}`}>
                    {workflow.status.toUpperCase()}
                  </div>
                </div>
                
                <div className="text-xs text-text-secondary mb-2">
                  Started {new Date(workflow.createdAt).toLocaleString()}
                </div>
                
                <div className="flex items-center space-x-2 text-xs">
                  {getStageIcon(workflow.stage, workflow.status)}
                  <span className="text-text-secondary">{workflow.stage}</span>
                </div>

                {workflow.error && (
                  <div className="mt-2 text-xs text-red-400 bg-red-900/20 p-2 rounded">
                    {workflow.error}
                  </div>
                )}
              </div>
            ))}

            {(!activeWorkflows || activeWorkflows.length === 0) && (
              <div className="p-8 text-center">
                <Zap className="w-12 h-12 text-gray-400 mx-auto mb-3" />
                <p className="text-text-secondary">No active workflows</p>
                <p className="text-sm text-text-tertiary mt-1">Upload a tender to get started</p>
              </div>
            )}
          </div>
        </div>

        {/* Workflow Details */}
        <div className="flex-1">
          {selectedWorkflow ? (
            <WorkflowDetails workflowId={selectedWorkflow} events={workflowEvents} />
          ) : (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <Activity className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-text-primary mb-2">Select a Workflow</h3>
                <p className="text-text-secondary">Choose a workflow from the sidebar to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

function WorkflowDetails({ workflowId, events }: { workflowId: string; events: any[] }) {
  const workflow = useQuery(api.zeroTouchWorkflow.getWorkflowStatus, { workflowId });

  if (!workflow) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  const stages = [
    { id: 'INITIATED', name: 'Workflow Started', icon: Play },
    { id: 'PARSING', name: 'Document Processing', icon: FileText },
    { id: 'INGESTED', name: 'Data Extracted', icon: CheckCircle },
    { id: 'SCHEDULED', name: 'Meeting Scheduled', icon: Calendar },
    { id: 'CONTENT_READY', name: 'Content Generated', icon: MessageSquare },
    { id: 'MEETING_ACTIVE', name: 'Meeting in Progress', icon: Users },
    { id: 'POST_MEETING', name: 'Post-Meeting Processing', icon: Activity },
    { id: 'BID_DEVELOP', name: 'Bid Development', icon: TrendingUp },
    { id: 'ARCHIVE', name: 'Archived', icon: XCircle },
  ];

  const currentStageIndex = stages.findIndex(stage => stage.id === workflow.stage);

  return (
    <div className="h-full flex flex-col">
      {/* Workflow Header */}
      <div className="p-6 border-b border-border bg-surface">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-text-primary">
            Workflow: {workflow.sourceType.toUpperCase()}
          </h2>
          <div className={`px-3 py-1 rounded-lg text-sm font-medium border ${getStatusColor(workflow.status)}`}>
            {workflow.status.toUpperCase()}
          </div>
        </div>

        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-text-tertiary">Started</div>
            <div className="text-text-primary font-medium">
              {new Date(workflow.createdAt).toLocaleString()}
            </div>
          </div>
          <div>
            <div className="text-text-tertiary">Duration</div>
            <div className="text-text-primary font-medium">
              {formatDuration(Date.now() - workflow.createdAt)}
            </div>
          </div>
          <div>
            <div className="text-text-tertiary">Current Stage</div>
            <div className="text-text-primary font-medium">{workflow.stage}</div>
          </div>
          {workflow.outcome && (
            <div>
              <div className="text-text-tertiary">Outcome</div>
              <div className={`font-medium ${
                workflow.outcome === 'GO' ? 'text-green-400' :
                workflow.outcome === 'NO_GO' ? 'text-red-400' : 'text-yellow-400'
              }`}>
                {workflow.outcome}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Progress Timeline */}
      <div className="p-6 border-b border-border">
        <h3 className="font-medium text-text-primary mb-4">Progress Timeline</h3>
        <div className="space-y-4">
          {stages.map((stage, index) => {
            const isPassed = index <= currentStageIndex;
            const isCurrent = index === currentStageIndex;
            const StageIcon = stage.icon;

            return (
              <div key={stage.id} className="flex items-center space-x-3">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                  isPassed ? 'bg-primary-600' : 'bg-gray-700'
                } ${isCurrent ? 'ring-2 ring-primary-400' : ''}`}>
                  <StageIcon className={`w-4 h-4 ${isPassed ? 'text-white' : 'text-gray-400'}`} />
                </div>
                <div className="flex-1">
                  <div className={`font-medium ${isPassed ? 'text-text-primary' : 'text-text-secondary'}`}>
                    {stage.name}
                  </div>
                </div>
                {isPassed && (
                  <CheckCircle className="w-5 h-5 text-green-400" />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Event Log */}
      <div className="flex-1 p-6 overflow-y-auto">
        <h3 className="font-medium text-text-primary mb-4">Event Log</h3>
        <div className="space-y-3">
          {events?.map((event, index) => (
            <div key={index} className="flex items-start space-x-3 p-3 bg-surface rounded-lg">
              <div className="flex-shrink-0 w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <div className="flex items-center justify-between mb-1">
                  <div className="font-medium text-text-primary text-sm">{event.event}</div>
                  <div className="text-xs text-text-tertiary">
                    {new Date(event.timestamp).toLocaleTimeString()}
                  </div>
                </div>
                <div className="text-sm text-text-secondary">{event.stage}</div>
                {event.data && (
                  <pre className="text-xs text-text-tertiary mt-2 bg-background p-2 rounded overflow-x-auto">
                    {JSON.stringify(event.data, null, 2)}
                  </pre>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  function getStatusColor(status: string) {
    switch (status) {
      case 'completed': return 'text-green-400 bg-green-900/20 border-green-400';
      case 'in_progress': return 'text-blue-400 bg-blue-900/20 border-blue-400';
      case 'failed': return 'text-red-400 bg-red-900/20 border-red-400';
      default: return 'text-gray-400 bg-gray-900/20 border-gray-600';
    }
  }

  function formatDuration(ms: number) {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  }
}