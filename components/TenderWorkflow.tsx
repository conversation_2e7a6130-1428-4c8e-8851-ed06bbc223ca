'use client';

import React, { useState, useCallback, useMemo } from 'react';
import {
  GitBranch,
  Play,
  Pause,
  Square,
  Settings,
  Plus,
  Trash2,
  Edit3,
  Save,
  Download,
  Upload,
  Clock,
  Users,
  CheckCircle,
  AlertCircle,
  ArrowRight,
  ArrowDown,
  Copy,
  Eye,
  BarChart3,
  Target,
  Calendar,
  Bell,
  Filter,
  Search,
  MoreVertical,
  Workflow as WorkflowIcon,
  Zap,
  Timer,
  FileText,
  MessageSquare,
  Shield,
  Lightbulb,
  TrendingUp,
  Activity,
  Database,
  Code,
  Globe,
  Mail,
  Smartphone,
  Webhook,
  RefreshCw,
  X,
  Check,
  ChevronRight,
  ChevronDown,
  Move,
  Link,
  Unlink,
  RotateCcw,
  FastForward,
  SkipForward,
  Repeat,
  PauseCircle,
  PlayCircle,
  StopCircle,
  Construction,
  Wrench,
  Cog,
  Layers,
  Network,
  Route,
  Merge,
  Split,
  ArrowUpRight,
  ArrowDownLeft,
  Maximize2,
  Minimize2,
  Grid,
  List,
  Kanban,
  Calendar as CalendarIcon,
  Timeline,
  Gantt,
  FlowC<PERSON>,
  Diagram,
  Flowchart
} from 'lucide-react';
import { 
  Workflow, 
  WorkflowStep, 
  WorkflowStepType, 
  WorkflowStatus, 
  WorkflowType,
  WorkflowExecution,
  WorkflowTemplate,
  WorkflowAnalytics,
  WorkflowOptimization
} from '../types/workflow';

interface TenderWorkflowProps {
  className?: string;
  workflows?: Workflow[];
  onWorkflowCreate?: (workflow: Workflow) => void;
  onWorkflowUpdate?: (workflow: Workflow) => void;
  onWorkflowDelete?: (workflowId: string) => void;
  onWorkflowExecute?: (workflowId: string) => void;
}

interface WorkflowNode {
  id: string;
  type: WorkflowStepType;
  position: { x: number; y: number };
  data: WorkflowStep;
  connections: string[];
}

interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  type: 'default' | 'conditional' | 'parallel';
  condition?: string;
}

interface WorkflowCanvas {
  nodes: WorkflowNode[];
  edges: WorkflowEdge[];
  viewport: { x: number; y: number; zoom: number };
}

// Workflow Templates
const WORKFLOW_TEMPLATES: WorkflowTemplate[] = [
  {
    id: 'tender-processing',
    name: 'Standard Tender Processing',
    description: 'Complete tender processing workflow from analysis to submission',
    category: 'Tender Management',
    type: 'tender_processing',
    version: '1.0.0',
    steps: [
      {
        id: 'analyze-tender',
        name: 'Analyze Tender Requirements',
        description: 'AI analysis of tender documents and requirements',
        type: 'task',
        order: 1,
        dependencies: [],
        configTemplate: {
          taskType: 'document_analysis',
          agentType: 'analysis_agent',
          parameters: {
            analysisDepth: 'comprehensive',
            extractRequirements: true,
            identifyRisks: true
          }
        },
        requiredFields: ['tenderId', 'documents'],
        optionalFields: ['priority', 'deadline']
      },
      {
        id: 'create-sections',
        name: 'Create Bid Sections',
        description: 'Generate initial bid sections based on requirements',
        type: 'parallel',
        order: 2,
        dependencies: ['analyze-tender'],
        configTemplate: {
          parallelTasks: [
            { id: 'executive-summary', name: 'Executive Summary', taskType: 'content_generation' },
            { id: 'technical-specs', name: 'Technical Specifications', taskType: 'content_generation' },
            { id: 'pricing-model', name: 'Pricing Model', taskType: 'content_generation' }
          ]
        },
        requiredFields: ['sectionTypes'],
        optionalFields: ['templates', 'customInstructions']
      },
      {
        id: 'review-content',
        name: 'Content Review',
        description: 'Human review of generated content',
        type: 'review',
        order: 3,
        dependencies: ['create-sections'],
        configTemplate: {
          approvers: ['content_reviewer'],
          validationRules: [
            { field: 'completeness', operator: 'greater_than', value: 80 },
            { field: 'quality_score', operator: 'greater_than', value: 75 }
          ]
        },
        requiredFields: ['reviewers'],
        optionalFields: ['reviewCriteria', 'deadline']
      },
      {
        id: 'final-approval',
        name: 'Final Approval',
        description: 'Final approval before submission',
        type: 'approval',
        order: 4,
        dependencies: ['review-content'],
        configTemplate: {
          approvers: ['bid_manager', 'senior_executive'],
          validationRules: [
            { field: 'all_sections_complete', operator: 'equals', value: true },
            { field: 'legal_review_complete', operator: 'equals', value: true }
          ]
        },
        requiredFields: ['approvers'],
        optionalFields: ['escalationRules']
      },
      {
        id: 'submit-tender',
        name: 'Submit Tender',
        description: 'Final submission of tender',
        type: 'task',
        order: 5,
        dependencies: ['final-approval'],
        configTemplate: {
          taskType: 'tender_submission',
          parameters: {
            formatValidation: true,
            documentPackaging: true,
            submissionMethod: 'portal'
          }
        },
        requiredFields: ['submissionPortal'],
        optionalFields: ['backupMethod', 'confirmationRequired']
      }
    ],
    variables: [
      { name: 'tenderId', type: 'string', description: 'Tender ID', required: true },
      { name: 'priority', type: 'string', description: 'Priority level', required: false, defaultValue: 'medium' },
      { name: 'deadline', type: 'number', description: 'Submission deadline', required: false }
    ],
    tags: ['tender', 'processing', 'standard'],
    metadata: {
      createdBy: 'system',
      createdAt: Date.now(),
      lastModified: Date.now(),
      usageCount: 0,
      rating: 4.8,
      isPublic: true
    }
  },
  {
    id: 'quality-assurance',
    name: 'Quality Assurance Review',
    description: 'Comprehensive QA workflow for bid documents',
    category: 'Quality Management',
    type: 'quality_assurance',
    version: '1.0.0',
    steps: [
      {
        id: 'document-check',
        name: 'Document Completeness Check',
        description: 'Verify all required documents are present',
        type: 'task',
        order: 1,
        dependencies: [],
        configTemplate: {
          taskType: 'document_validation',
          parameters: {
            checklistValidation: true,
            formatValidation: true
          }
        },
        requiredFields: ['documentChecklist'],
        optionalFields: []
      },
      {
        id: 'content-review',
        name: 'Content Quality Review',
        description: 'Review content quality and compliance',
        type: 'review',
        order: 2,
        dependencies: ['document-check'],
        configTemplate: {
          approvers: ['quality_reviewer'],
          validationRules: [
            { field: 'grammar_score', operator: 'greater_than', value: 90 },
            { field: 'compliance_score', operator: 'greater_than', value: 95 }
          ]
        },
        requiredFields: ['qualityStandards'],
        optionalFields: ['customCriteria']
      },
      {
        id: 'final-qa',
        name: 'Final QA Sign-off',
        description: 'Final quality assurance approval',
        type: 'approval',
        order: 3,
        dependencies: ['content-review'],
        configTemplate: {
          approvers: ['qa_manager'],
          validationRules: [
            { field: 'all_checks_passed', operator: 'equals', value: true }
          ]
        },
        requiredFields: ['qaManager'],
        optionalFields: []
      }
    ],
    variables: [
      { name: 'documentChecklist', type: 'array', description: 'List of required documents', required: true },
      { name: 'qualityStandards', type: 'object', description: 'Quality standards to apply', required: true }
    ],
    tags: ['quality', 'review', 'assurance'],
    metadata: {
      createdBy: 'system',
      createdAt: Date.now(),
      lastModified: Date.now(),
      usageCount: 0,
      rating: 4.6,
      isPublic: true
    }
  },
  {
    id: 'expedited-bid',
    name: 'Expedited Bid Process',
    description: 'Fast-track workflow for urgent tender submissions',
    category: 'Urgent Processing',
    type: 'bid_writing',
    version: '1.0.0',
    steps: [
      {
        id: 'rapid-analysis',
        name: 'Rapid Tender Analysis',
        description: 'Quick analysis focusing on key requirements',
        type: 'task',
        order: 1,
        dependencies: [],
        configTemplate: {
          taskType: 'rapid_analysis',
          parameters: {
            analysisDepth: 'focused',
            timeLimit: 30
          }
        },
        requiredFields: ['tenderId'],
        optionalFields: ['focusAreas']
      },
      {
        id: 'concurrent-writing',
        name: 'Concurrent Content Creation',
        description: 'Parallel creation of all bid sections',
        type: 'parallel',
        order: 2,
        dependencies: ['rapid-analysis'],
        configTemplate: {
          parallelTasks: [
            { id: 'exec-summary', name: 'Executive Summary', taskType: 'rapid_content_generation' },
            { id: 'tech-approach', name: 'Technical Approach', taskType: 'rapid_content_generation' },
            { id: 'pricing', name: 'Pricing', taskType: 'rapid_content_generation' },
            { id: 'team-profile', name: 'Team Profile', taskType: 'rapid_content_generation' }
          ]
        },
        requiredFields: ['sectionTypes'],
        optionalFields: ['templates']
      },
      {
        id: 'fast-review',
        name: 'Fast-Track Review',
        description: 'Expedited review process',
        type: 'review',
        order: 3,
        dependencies: ['concurrent-writing'],
        configTemplate: {
          approvers: ['senior_reviewer'],
          validationRules: [
            { field: 'completeness', operator: 'greater_than', value: 85 }
          ],
          timeLimit: 60
        },
        requiredFields: ['reviewer'],
        optionalFields: []
      },
      {
        id: 'immediate-submission',
        name: 'Immediate Submission',
        description: 'Direct submission without additional approvals',
        type: 'task',
        order: 4,
        dependencies: ['fast-review'],
        configTemplate: {
          taskType: 'immediate_submission',
          parameters: {
            skipAdditionalValidation: true
          }
        },
        requiredFields: ['submissionPortal'],
        optionalFields: []
      }
    ],
    variables: [
      { name: 'tenderId', type: 'string', description: 'Tender ID', required: true },
      { name: 'urgencyLevel', type: 'string', description: 'Urgency level', required: true, defaultValue: 'high' }
    ],
    tags: ['expedited', 'urgent', 'fast-track'],
    metadata: {
      createdBy: 'system',
      createdAt: Date.now(),
      lastModified: Date.now(),
      usageCount: 0,
      rating: 4.5,
      isPublic: true
    }
  }
];

// Mock workflow data
const MOCK_WORKFLOWS: Workflow[] = [
  {
    id: 'wf-001',
    name: 'Standard Tender Processing',
    description: 'Complete workflow for processing new tenders from analysis to submission',
    type: 'tender_processing',
    status: 'active',
    version: '1.2.0',
    steps: [
      {
        id: 'step-001',
        name: 'Analyze Tender Requirements',
        description: 'AI-powered analysis of tender documents',
        type: 'task',
        order: 1,
        status: 'completed',
        dependencies: [],
        configuration: {
          taskType: 'document_analysis',
          agentType: 'analysis_agent',
          parameters: {
            analysisDepth: 'comprehensive',
            extractRequirements: true
          }
        },
        timeouts: { execution: 300000, wait: 0 },
        retryPolicy: { maxAttempts: 3, backoffStrategy: 'exponential', delay: 1000 },
        metadata: {
          createdAt: Date.now() - 86400000,
          executionCount: 45,
          successCount: 42,
          failureCount: 3,
          averageExecutionTime: 120000
        }
      },
      {
        id: 'step-002',
        name: 'Generate Initial Sections',
        description: 'Create initial bid sections based on requirements',
        type: 'parallel',
        order: 2,
        status: 'in_progress',
        dependencies: ['step-001'],
        configuration: {
          parallelTasks: [
            { id: 'exec-summary', name: 'Executive Summary', taskType: 'content_generation', required: true },
            { id: 'tech-specs', name: 'Technical Specifications', taskType: 'content_generation', required: true },
            { id: 'pricing', name: 'Pricing Model', taskType: 'content_generation', required: true }
          ]
        },
        timeouts: { execution: 600000, wait: 0 },
        retryPolicy: { maxAttempts: 2, backoffStrategy: 'linear', delay: 2000 },
        metadata: {
          createdAt: Date.now() - 86400000,
          executionCount: 38,
          successCount: 35,
          failureCount: 3,
          averageExecutionTime: 240000
        }
      },
      {
        id: 'step-003',
        name: 'Content Review',
        description: 'Human review of generated content',
        type: 'review',
        order: 3,
        status: 'pending',
        dependencies: ['step-002'],
        configuration: {
          approvers: ['content_reviewer'],
          validationRules: [
            { field: 'completeness', operator: 'greater_than', value: 80, message: 'Content must be at least 80% complete' },
            { field: 'quality_score', operator: 'greater_than', value: 75, message: 'Quality score must be above 75%' }
          ]
        },
        timeouts: { execution: 3600000, wait: 0 },
        retryPolicy: { maxAttempts: 1, backoffStrategy: 'fixed', delay: 0 },
        metadata: {
          createdAt: Date.now() - 86400000,
          executionCount: 35,
          successCount: 33,
          failureCount: 2,
          averageExecutionTime: 1800000
        }
      },
      {
        id: 'step-004',
        name: 'Final Approval',
        description: 'Final approval before submission',
        type: 'approval',
        order: 4,
        status: 'pending',
        dependencies: ['step-003'],
        configuration: {
          approvers: ['bid_manager', 'senior_executive'],
          validationRules: [
            { field: 'all_sections_complete', operator: 'equals', value: true, message: 'All sections must be complete' }
          ]
        },
        timeouts: { execution: 7200000, wait: 0 },
        retryPolicy: { maxAttempts: 1, backoffStrategy: 'fixed', delay: 0 },
        metadata: {
          createdAt: Date.now() - 86400000,
          executionCount: 33,
          successCount: 31,
          failureCount: 2,
          averageExecutionTime: 2400000
        }
      },
      {
        id: 'step-005',
        name: 'Submit Tender',
        description: 'Final submission of tender',
        type: 'task',
        order: 5,
        status: 'pending',
        dependencies: ['step-004'],
        configuration: {
          taskType: 'tender_submission',
          parameters: {
            formatValidation: true,
            documentPackaging: true
          }
        },
        timeouts: { execution: 300000, wait: 0 },
        retryPolicy: { maxAttempts: 3, backoffStrategy: 'exponential', delay: 1000 },
        metadata: {
          createdAt: Date.now() - 86400000,
          executionCount: 31,
          successCount: 30,
          failureCount: 1,
          averageExecutionTime: 60000
        }
      }
    ],
    triggers: [
      {
        id: 'trigger-001',
        name: 'New Tender Event',
        type: 'event',
        isActive: true,
        conditions: [
          { field: 'event_type', operator: 'equals', value: 'tender_created' }
        ],
        configuration: {
          eventType: 'tender_created'
        },
        metadata: {
          createdAt: Date.now() - 86400000,
          triggerCount: 156
        }
      }
    ],
    variables: {
      tenderId: '',
      priority: 'medium',
      deadline: Date.now() + 86400000 * 7
    },
    metadata: {
      createdBy: 'system',
      createdAt: Date.now() - 86400000 * 30,
      lastModified: Date.now() - 86400000 * 7,
      lastModifiedBy: 'admin',
      executionCount: 156,
      successRate: 87.8,
      averageExecutionTime: 3600000
    },
    settings: {
      timeout: 7200000,
      maxRetries: 3,
      errorHandling: 'continue',
      notifications: [
        {
          type: 'email',
          recipients: ['<EMAIL>'],
          events: ['complete', 'error'],
          isActive: true
        }
      ],
      isActive: true
    }
  }
];

// Mock analytics data
const MOCK_ANALYTICS: WorkflowAnalytics = {
  workflowId: 'wf-001',
  period: {
    start: Date.now() - 86400000 * 30,
    end: Date.now()
  },
  metrics: {
    totalExecutions: 156,
    successfulExecutions: 137,
    failedExecutions: 19,
    averageExecutionTime: 3600000,
    minExecutionTime: 1800000,
    maxExecutionTime: 7200000,
    throughput: 5.2,
    errorRate: 12.2
  },
  breakdown: {
    byStatus: {
      completed: 137,
      failed: 19,
      active: 0,
      paused: 0,
      cancelled: 0,
      draft: 0,
      suspended: 0
    },
    byTrigger: {
      event: 145,
      manual: 11,
      scheduled: 0,
      condition: 0,
      webhook: 0
    },
    byStep: {
      'step-001': {
        stepId: 'step-001',
        executionCount: 156,
        successCount: 153,
        failureCount: 3,
        averageExecutionTime: 120000,
        errorRate: 1.9,
        bottleneck: false
      },
      'step-002': {
        stepId: 'step-002',
        executionCount: 153,
        successCount: 145,
        failureCount: 8,
        averageExecutionTime: 240000,
        errorRate: 5.2,
        bottleneck: true
      },
      'step-003': {
        stepId: 'step-003',
        executionCount: 145,
        successCount: 142,
        failureCount: 3,
        averageExecutionTime: 1800000,
        errorRate: 2.1,
        bottleneck: false
      },
      'step-004': {
        stepId: 'step-004',
        executionCount: 142,
        successCount: 139,
        failureCount: 3,
        averageExecutionTime: 2400000,
        errorRate: 2.1,
        bottleneck: false
      },
      'step-005': {
        stepId: 'step-005',
        executionCount: 139,
        successCount: 137,
        failureCount: 2,
        averageExecutionTime: 60000,
        errorRate: 1.4,
        bottleneck: false
      }
    },
    byTimeOfDay: {
      '00-06': 12,
      '06-12': 45,
      '12-18': 67,
      '18-24': 32
    }
  },
  trends: {
    executionTime: [3.2, 3.5, 3.8, 3.4, 3.6, 3.9, 3.7],
    successRate: [85.2, 86.8, 88.1, 87.5, 89.2, 87.8, 88.5],
    errorRate: [14.8, 13.2, 11.9, 12.5, 10.8, 12.2, 11.5]
  }
};

// Mock optimization data
const MOCK_OPTIMIZATION: WorkflowOptimization = {
  workflowId: 'wf-001',
  recommendations: [
    {
      type: 'parallel_execution',
      description: 'Execute document analysis and initial section generation in parallel to reduce overall execution time',
      impact: 'high',
      effort: 'medium',
      estimatedImprovement: 25,
      stepIds: ['step-001', 'step-002']
    },
    {
      type: 'timeout_adjustment',
      description: 'Reduce timeout for content review step based on historical completion times',
      impact: 'medium',
      effort: 'low',
      estimatedImprovement: 15,
      stepIds: ['step-003']
    },
    {
      type: 'condition_simplification',
      description: 'Simplify validation rules for final approval to reduce processing overhead',
      impact: 'low',
      effort: 'low',
      estimatedImprovement: 8,
      stepIds: ['step-004']
    }
  ],
  performance: {
    currentScore: 78.5,
    potentialScore: 92.3,
    improvementPercentage: 17.6
  },
  bottlenecks: [
    {
      stepId: 'step-002',
      reason: 'Parallel task execution contention',
      impact: 35,
      suggestedFix: 'Implement resource pooling for parallel tasks',
      frequency: 0.52
    }
  ],
  lastAnalysis: Date.now() - 86400000
};

export default function TenderWorkflow({ 
  className = '', 
  workflows = MOCK_WORKFLOWS,
  onWorkflowCreate,
  onWorkflowUpdate,
  onWorkflowDelete,
  onWorkflowExecute
}: TenderWorkflowProps) {
  const [selectedWorkflow, setSelectedWorkflow] = useState<Workflow | null>(workflows[0] || null);
  const [viewMode, setViewMode] = useState<'builder' | 'list' | 'analytics' | 'templates'>('builder');
  const [isEditing, setIsEditing] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [selectedStep, setSelectedStep] = useState<WorkflowStep | null>(null);
  const [workflowCanvas, setWorkflowCanvas] = useState<WorkflowCanvas>({
    nodes: [],
    edges: [],
    viewport: { x: 0, y: 0, zoom: 1 }
  });
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<WorkflowStatus | 'all'>('all');
  const [typeFilter, setTypeFilter] = useState<WorkflowType | 'all'>('all');
  const [draggedNode, setDraggedNode] = useState<WorkflowNode | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const [connectionMode, setConnectionMode] = useState(false);
  const [connectionSource, setConnectionSource] = useState<string | null>(null);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [showExecutionHistory, setShowExecutionHistory] = useState(false);
  const [showOptimizations, setShowOptimizations] = useState(false);

  // Initialize workflow canvas from selected workflow
  const initializeCanvas = useCallback((workflow: Workflow) => {
    const nodes: WorkflowNode[] = workflow.steps.map((step, index) => ({
      id: step.id,
      type: step.type,
      position: { 
        x: 100 + (index % 3) * 200, 
        y: 100 + Math.floor(index / 3) * 150 
      },
      data: step,
      connections: step.dependencies
    }));

    const edges: WorkflowEdge[] = [];
    workflow.steps.forEach(step => {
      step.dependencies.forEach(depId => {
        edges.push({
          id: `${depId}-${step.id}`,
          source: depId,
          target: step.id,
          type: 'default'
        });
      });
    });

    setWorkflowCanvas({
      nodes,
      edges,
      viewport: { x: 0, y: 0, zoom: 1 }
    });
  }, []);

  // Initialize canvas when workflow changes
  React.useEffect(() => {
    if (selectedWorkflow) {
      initializeCanvas(selectedWorkflow);
    }
  }, [selectedWorkflow, initializeCanvas]);

  // Filter workflows based on search and filters
  const filteredWorkflows = useMemo(() => {
    return workflows.filter(workflow => {
      const matchesSearch = workflow.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           workflow.description.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || workflow.status === statusFilter;
      const matchesType = typeFilter === 'all' || workflow.type === typeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    });
  }, [workflows, searchQuery, statusFilter, typeFilter]);

  // Get step type icon
  const getStepIcon = (type: WorkflowStepType) => {
    switch (type) {
      case 'start':
        return <PlayCircle size={16} className="text-accent" />;
      case 'task':
        return <Settings size={16} className="text-primary" />;
      case 'review':
        return <Eye size={16} className="text-warning" />;
      case 'approval':
        return <CheckCircle size={16} className="text-secondary" />;
      case 'condition':
        return <GitBranch size={16} className="text-info" />;
      case 'parallel':
        return <Layers size={16} className="text-accent" />;
      case 'end':
        return <StopCircle size={16} className="text-error" />;
      default:
        return <Circle size={16} className="text-text-tertiary" />;
    }
  };

  // Get status color
  const getStatusColor = (status: WorkflowStatus) => {
    switch (status) {
      case 'active':
        return 'text-accent bg-accent/10 border-accent/20';
      case 'draft':
        return 'text-text-tertiary bg-text-tertiary/10 border-text-tertiary/20';
      case 'paused':
        return 'text-warning bg-warning/10 border-warning/20';
      case 'completed':
        return 'text-secondary bg-secondary/10 border-secondary/20';
      case 'cancelled':
        return 'text-error bg-error/10 border-error/20';
      case 'failed':
        return 'text-error bg-error/10 border-error/20';
      case 'suspended':
        return 'text-warning bg-warning/10 border-warning/20';
      default:
        return 'text-text-tertiary bg-text-tertiary/10 border-text-tertiary/20';
    }
  };

  // Get step status color
  const getStepStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-accent bg-accent/10 border-accent/20';
      case 'in_progress':
        return 'text-primary bg-primary/10 border-primary/20';
      case 'pending':
        return 'text-warning bg-warning/10 border-warning/20';
      case 'failed':
        return 'text-error bg-error/10 border-error/20';
      case 'blocked':
        return 'text-error bg-error/10 border-error/20';
      case 'skipped':
        return 'text-text-tertiary bg-text-tertiary/10 border-text-tertiary/20';
      default:
        return 'text-text-tertiary bg-text-tertiary/10 border-text-tertiary/20';
    }
  };

  // Format duration
  const formatDuration = (ms: number) => {
    const hours = Math.floor(ms / (1000 * 60 * 60));
    const minutes = Math.floor((ms % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((ms % (1000 * 60)) / 1000);
    
    if (hours > 0) return `${hours}h ${minutes}m`;
    if (minutes > 0) return `${minutes}m ${seconds}s`;
    return `${seconds}s`;
  };

  // Format number
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat().format(num);
  };

  // Handle workflow execution
  const handleExecuteWorkflow = (workflowId: string) => {
    if (onWorkflowExecute) {
      onWorkflowExecute(workflowId);
    }
  };

  // Handle create workflow from template
  const handleCreateFromTemplate = (template: WorkflowTemplate) => {
    const newWorkflow: Workflow = {
      id: `wf-${Date.now()}`,
      name: template.name,
      description: template.description,
      type: template.type,
      status: 'draft',
      version: '1.0.0',
      steps: template.steps.map(stepTemplate => ({
        id: `step-${Date.now()}-${stepTemplate.id}`,
        name: stepTemplate.name,
        description: stepTemplate.description,
        type: stepTemplate.type,
        order: stepTemplate.order,
        status: 'pending',
        dependencies: stepTemplate.dependencies,
        configuration: stepTemplate.configTemplate as any,
        timeouts: { execution: 300000, wait: 0 },
        retryPolicy: { maxAttempts: 3, backoffStrategy: 'exponential', delay: 1000 },
        metadata: {
          createdAt: Date.now(),
          executionCount: 0,
          successCount: 0,
          failureCount: 0,
          averageExecutionTime: 0
        }
      })),
      triggers: [],
      variables: {},
      metadata: {
        createdBy: 'user',
        createdAt: Date.now(),
        lastModified: Date.now(),
        lastModifiedBy: 'user',
        executionCount: 0,
        successRate: 0,
        averageExecutionTime: 0
      },
      settings: {
        timeout: 7200000,
        maxRetries: 3,
        errorHandling: 'continue',
        notifications: [],
        isActive: false
      }
    };

    if (onWorkflowCreate) {
      onWorkflowCreate(newWorkflow);
    }
    setSelectedWorkflow(newWorkflow);
    setShowTemplateModal(false);
    setViewMode('builder');
  };

  // Render workflow builder
  const renderWorkflowBuilder = () => (
    <div className="flex-1 flex flex-col">
      {/* Builder Header */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-bold text-text-primary">
            {selectedWorkflow?.name || 'No Workflow Selected'}
          </h2>
          {selectedWorkflow && (
            <div className="flex items-center space-x-2">
              <span className={`px-2 py-1 text-xs font-medium rounded border ${getStatusColor(selectedWorkflow.status)}`}>
                {selectedWorkflow.status.toUpperCase()}
              </span>
              <span className="text-sm text-text-tertiary">
                v{selectedWorkflow.version}
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setConnectionMode(!connectionMode)}
            className={`p-2 rounded-lg transition-colors ${
              connectionMode 
                ? 'bg-primary text-white' 
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
            title="Connection Mode"
          >
            <Link size={16} />
          </button>
          <button
            onClick={() => setIsEditing(!isEditing)}
            className={`p-2 rounded-lg transition-colors ${
              isEditing 
                ? 'bg-primary text-white' 
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
          >
            <Edit3 size={16} />
          </button>
          <button
            onClick={() => setShowSettings(true)}
            className="p-2 bg-surface-secondary text-text-secondary hover:bg-surface-hover rounded-lg transition-colors"
          >
            <Settings size={16} />
          </button>
          {selectedWorkflow && (
            <button
              onClick={() => handleExecuteWorkflow(selectedWorkflow.id)}
              className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
            >
              <Play size={16} />
              <span>Execute</span>
            </button>
          )}
        </div>
      </div>

      {/* Canvas */}
      <div className="flex-1 relative overflow-hidden bg-surface-secondary">
        {selectedWorkflow ? (
          <div className="absolute inset-0 p-4">
            <div className="relative w-full h-full">
              {/* Workflow Steps */}
              {workflowCanvas.nodes.map((node) => (
                <div
                  key={node.id}
                  className={`absolute p-4 bg-surface border-2 rounded-lg shadow-sm cursor-pointer transition-all ${
                    selectedStep?.id === node.id 
                      ? 'border-primary shadow-lg' 
                      : 'border-border hover:border-primary/50'
                  }`}
                  style={{
                    left: node.position.x,
                    top: node.position.y,
                    width: '200px'
                  }}
                  onClick={() => setSelectedStep(node.data)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStepIcon(node.type)}
                      <span className="text-sm font-medium text-text-primary">
                        {node.data.name}
                      </span>
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded border ${getStepStatusColor(node.data.status)}`}>
                      {node.data.status.toUpperCase()}
                    </span>
                  </div>
                  <p className="text-xs text-text-tertiary mb-2">
                    {node.data.description}
                  </p>
                  <div className="flex items-center justify-between text-xs text-text-quaternary">
                    <span>Order: {node.data.order}</span>
                    <span>
                      {node.data.metadata.successCount}/{node.data.metadata.executionCount}
                    </span>
                  </div>
                </div>
              ))}

              {/* Connections */}
              <svg className="absolute inset-0 w-full h-full pointer-events-none">
                {workflowCanvas.edges.map((edge) => {
                  const sourceNode = workflowCanvas.nodes.find(n => n.id === edge.source);
                  const targetNode = workflowCanvas.nodes.find(n => n.id === edge.target);
                  
                  if (!sourceNode || !targetNode) return null;
                  
                  const sourceX = sourceNode.position.x + 200;
                  const sourceY = sourceNode.position.y + 50;
                  const targetX = targetNode.position.x;
                  const targetY = targetNode.position.y + 50;
                  
                  return (
                    <line
                      key={edge.id}
                      x1={sourceX}
                      y1={sourceY}
                      x2={targetX}
                      y2={targetY}
                      stroke="#3b82f6"
                      strokeWidth="2"
                      markerEnd="url(#arrowhead)"
                    />
                  );
                })}
                <defs>
                  <marker
                    id="arrowhead"
                    markerWidth="10"
                    markerHeight="7"
                    refX="9"
                    refY="3.5"
                    orient="auto"
                  >
                    <polygon
                      points="0 0, 10 3.5, 0 7"
                      fill="#3b82f6"
                    />
                  </marker>
                </defs>
              </svg>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <WorkflowIcon size={48} className="mx-auto text-text-quaternary mb-4" />
              <h3 className="text-xl font-bold text-text-primary mb-2">No Workflow Selected</h3>
              <p className="text-text-tertiary mb-6">Select a workflow to start building</p>
              <button
                onClick={() => setShowTemplateModal(true)}
                className="px-6 py-3 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                Create New Workflow
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Step Details Panel */}
      {selectedStep && (
        <div className="w-80 border-l border-border p-4 bg-surface">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-text-primary">Step Details</h3>
            <button
              onClick={() => setSelectedStep(null)}
              className="p-1 text-text-tertiary hover:text-text-primary"
            >
              <X size={16} />
            </button>
          </div>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Name
              </label>
              <input
                type="text"
                value={selectedStep.name}
                readOnly={!isEditing}
                className="w-full px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Description
              </label>
              <textarea
                value={selectedStep.description}
                readOnly={!isEditing}
                rows={3}
                className="w-full px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Type
              </label>
              <select
                value={selectedStep.type}
                disabled={!isEditing}
                className="w-full px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
              >
                <option value="start">Start</option>
                <option value="task">Task</option>
                <option value="review">Review</option>
                <option value="approval">Approval</option>
                <option value="condition">Condition</option>
                <option value="parallel">Parallel</option>
                <option value="end">End</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Status
              </label>
              <span className={`px-2 py-1 text-xs font-medium rounded border ${getStepStatusColor(selectedStep.status)}`}>
                {selectedStep.status.toUpperCase()}
              </span>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-text-primary mb-2">
                Statistics
              </label>
              <div className="grid grid-cols-2 gap-2">
                <div className="p-2 bg-surface-secondary rounded">
                  <p className="text-xs text-text-tertiary">Executions</p>
                  <p className="text-sm font-medium text-text-primary">
                    {formatNumber(selectedStep.metadata.executionCount)}
                  </p>
                </div>
                <div className="p-2 bg-surface-secondary rounded">
                  <p className="text-xs text-text-tertiary">Success Rate</p>
                  <p className="text-sm font-medium text-text-primary">
                    {selectedStep.metadata.executionCount > 0 
                      ? Math.round((selectedStep.metadata.successCount / selectedStep.metadata.executionCount) * 100)
                      : 0}%
                  </p>
                </div>
                <div className="p-2 bg-surface-secondary rounded">
                  <p className="text-xs text-text-tertiary">Avg Duration</p>
                  <p className="text-sm font-medium text-text-primary">
                    {formatDuration(selectedStep.metadata.averageExecutionTime)}
                  </p>
                </div>
                <div className="p-2 bg-surface-secondary rounded">
                  <p className="text-xs text-text-tertiary">Failures</p>
                  <p className="text-sm font-medium text-text-primary">
                    {formatNumber(selectedStep.metadata.failureCount)}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Render workflow list
  const renderWorkflowList = () => (
    <div className="flex-1 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-bold text-text-primary">Workflows</h2>
        <button
          onClick={() => setShowTemplateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>New Workflow</span>
        </button>
      </div>

      {/* Filters */}
      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1 relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" />
          <input
            type="text"
            placeholder="Search workflows..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value as WorkflowStatus | 'all')}
          className="px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
        >
          <option value="all">All Status</option>
          <option value="active">Active</option>
          <option value="draft">Draft</option>
          <option value="paused">Paused</option>
          <option value="completed">Completed</option>
        </select>
        <select
          value={typeFilter}
          onChange={(e) => setTypeFilter(e.target.value as WorkflowType | 'all')}
          className="px-3 py-2 bg-surface border border-border rounded-lg text-sm text-text-primary"
        >
          <option value="all">All Types</option>
          <option value="tender_processing">Tender Processing</option>
          <option value="bid_writing">Bid Writing</option>
          <option value="review_approval">Review & Approval</option>
          <option value="quality_assurance">Quality Assurance</option>
          <option value="submission">Submission</option>
          <option value="custom">Custom</option>
        </select>
      </div>

      {/* Workflow Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredWorkflows.map((workflow) => (
          <div
            key={workflow.id}
            className="bg-surface border border-border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => {
              setSelectedWorkflow(workflow);
              setViewMode('builder');
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <WorkflowIcon size={20} className="text-primary" />
                <h3 className="text-lg font-bold text-text-primary">
                  {workflow.name}
                </h3>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs font-medium rounded border ${getStatusColor(workflow.status)}`}>
                  {workflow.status.toUpperCase()}
                </span>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    // Show more options
                  }}
                  className="p-1 text-text-tertiary hover:text-text-primary"
                >
                  <MoreVertical size={16} />
                </button>
              </div>
            </div>

            <p className="text-sm text-text-tertiary mb-4">
              {workflow.description}
            </p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-text-tertiary">Steps</p>
                <p className="text-sm font-medium text-text-primary">
                  {workflow.steps.length}
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Success Rate</p>
                <p className="text-sm font-medium text-text-primary">
                  {workflow.metadata.successRate.toFixed(1)}%
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Executions</p>
                <p className="text-sm font-medium text-text-primary">
                  {formatNumber(workflow.metadata.executionCount)}
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Avg Duration</p>
                <p className="text-sm font-medium text-text-primary">
                  {formatDuration(workflow.metadata.averageExecutionTime)}
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-xs text-text-quaternary">
                v{workflow.version}
              </span>
              <div className="flex space-x-2">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleExecuteWorkflow(workflow.id);
                  }}
                  className="p-1 text-primary hover:bg-primary/10 rounded"
                >
                  <Play size={16} />
                </button>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    setSelectedWorkflow(workflow);
                    setViewMode('analytics');
                  }}
                  className="p-1 text-text-tertiary hover:bg-surface-secondary rounded"
                >
                  <BarChart3 size={16} />
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredWorkflows.length === 0 && (
        <div className="text-center py-12">
          <WorkflowIcon size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-xl font-bold text-text-primary mb-2">No Workflows Found</h3>
          <p className="text-text-tertiary mb-6">
            {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
              ? 'No workflows match your current filters'
              : 'Create your first workflow to get started'}
          </p>
          <button
            onClick={() => setShowTemplateModal(true)}
            className="px-6 py-3 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
          >
            Create New Workflow
          </button>
        </div>
      )}
    </div>
  );

  // Render analytics
  const renderAnalytics = () => (
    <div className="flex-1 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-text-primary">Workflow Analytics</h2>
          <p className="text-text-tertiary">
            {selectedWorkflow ? `Analytics for ${selectedWorkflow.name}` : 'Select a workflow to view analytics'}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowOptimizations(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-accent hover:bg-accent-700 text-white rounded-lg transition-colors"
          >
            <Lightbulb size={16} />
            <span>Optimizations</span>
          </button>
          <button
            onClick={() => setShowExecutionHistory(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-secondary hover:bg-secondary-700 text-white rounded-lg transition-colors"
          >
            <Clock size={16} />
            <span>Execution History</span>
          </button>
        </div>
      </div>

      {selectedWorkflow ? (
        <div className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-surface border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Activity size={20} className="text-primary" />
                </div>
                <TrendingUp size={16} className="text-accent" />
              </div>
              <h3 className="text-2xl font-bold text-text-primary">
                {formatNumber(MOCK_ANALYTICS.metrics.totalExecutions)}
              </h3>
              <p className="text-sm text-text-tertiary">Total Executions</p>
            </div>

            <div className="bg-surface border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-2">
                <div className="p-2 bg-accent/10 rounded-lg">
                  <CheckCircle size={20} className="text-accent" />
                </div>
                <TrendingUp size={16} className="text-accent" />
              </div>
              <h3 className="text-2xl font-bold text-text-primary">
                {((MOCK_ANALYTICS.metrics.successfulExecutions / MOCK_ANALYTICS.metrics.totalExecutions) * 100).toFixed(1)}%
              </h3>
              <p className="text-sm text-text-tertiary">Success Rate</p>
            </div>

            <div className="bg-surface border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-2">
                <div className="p-2 bg-secondary/10 rounded-lg">
                  <Timer size={20} className="text-secondary" />
                </div>
                <TrendingDown size={16} className="text-accent" />
              </div>
              <h3 className="text-2xl font-bold text-text-primary">
                {formatDuration(MOCK_ANALYTICS.metrics.averageExecutionTime)}
              </h3>
              <p className="text-sm text-text-tertiary">Avg Duration</p>
            </div>

            <div className="bg-surface border border-border rounded-lg p-6">
              <div className="flex items-center justify-between mb-2">
                <div className="p-2 bg-error/10 rounded-lg">
                  <AlertCircle size={20} className="text-error" />
                </div>
                <TrendingDown size={16} className="text-accent" />
              </div>
              <h3 className="text-2xl font-bold text-text-primary">
                {MOCK_ANALYTICS.metrics.errorRate.toFixed(1)}%
              </h3>
              <p className="text-sm text-text-tertiary">Error Rate</p>
            </div>
          </div>

          {/* Step Performance */}
          <div className="bg-surface border border-border rounded-lg p-6">
            <h3 className="text-lg font-bold text-text-primary mb-4">Step Performance</h3>
            <div className="space-y-4">
              {Object.entries(MOCK_ANALYTICS.breakdown.byStep).map(([stepId, analytics]) => {
                const step = selectedWorkflow.steps.find(s => s.id === stepId);
                if (!step) return null;

                return (
                  <div key={stepId} className="flex items-center justify-between p-4 bg-surface-secondary rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStepIcon(step.type)}
                      <div>
                        <h4 className="font-medium text-text-primary">{step.name}</h4>
                        <p className="text-sm text-text-tertiary">{step.description}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-right">
                        <p className="text-sm font-medium text-text-primary">
                          {formatNumber(analytics.executionCount)}
                        </p>
                        <p className="text-xs text-text-tertiary">Executions</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-text-primary">
                          {((analytics.successCount / analytics.executionCount) * 100).toFixed(1)}%
                        </p>
                        <p className="text-xs text-text-tertiary">Success Rate</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium text-text-primary">
                          {formatDuration(analytics.averageExecutionTime)}
                        </p>
                        <p className="text-xs text-text-tertiary">Avg Duration</p>
                      </div>
                      {analytics.bottleneck && (
                        <div className="px-2 py-1 bg-warning/10 text-warning rounded text-xs">
                          Bottleneck
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Execution Trends */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-surface border border-border rounded-lg p-6">
              <h3 className="text-lg font-bold text-text-primary mb-4">Success Rate Trend</h3>
              <div className="h-40 flex items-end space-x-2">
                {MOCK_ANALYTICS.trends.successRate.map((rate, index) => (
                  <div
                    key={index}
                    className="flex-1 bg-accent/20 rounded-t"
                    style={{ height: `${rate}%` }}
                    title={`${rate.toFixed(1)}%`}
                  />
                ))}
              </div>
            </div>

            <div className="bg-surface border border-border rounded-lg p-6">
              <h3 className="text-lg font-bold text-text-primary mb-4">Execution Time Trend</h3>
              <div className="h-40 flex items-end space-x-2">
                {MOCK_ANALYTICS.trends.executionTime.map((time, index) => (
                  <div
                    key={index}
                    className="flex-1 bg-primary/20 rounded-t"
                    style={{ height: `${(time / Math.max(...MOCK_ANALYTICS.trends.executionTime)) * 100}%` }}
                    title={`${time.toFixed(1)}h`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <BarChart3 size={48} className="mx-auto text-text-quaternary mb-4" />
          <h3 className="text-xl font-bold text-text-primary mb-2">No Workflow Selected</h3>
          <p className="text-text-tertiary">Select a workflow to view its analytics</p>
        </div>
      )}
    </div>
  );

  // Render templates
  const renderTemplates = () => (
    <div className="flex-1 p-6">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-text-primary">Workflow Templates</h2>
          <p className="text-text-tertiary">Pre-built workflows to get you started quickly</p>
        </div>
        <button
          onClick={() => setShowTemplateModal(true)}
          className="flex items-center space-x-2 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
        >
          <Plus size={16} />
          <span>Create Custom Template</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {WORKFLOW_TEMPLATES.map((template) => (
          <div
            key={template.id}
            className="bg-surface border border-border rounded-lg p-6 hover:shadow-md transition-shadow"
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <WorkflowIcon size={20} className="text-primary" />
                </div>
                <div>
                  <h3 className="text-lg font-bold text-text-primary">
                    {template.name}
                  </h3>
                  <p className="text-sm text-text-tertiary">
                    {template.category}
                  </p>
                </div>
              </div>
              <div className="flex items-center space-x-1">
                {Array.from({ length: 5 }).map((_, i) => (
                  <div
                    key={i}
                    className={`w-3 h-3 rounded-full ${
                      i < Math.floor(template.metadata.rating)
                        ? 'bg-accent'
                        : 'bg-surface-secondary'
                    }`}
                  />
                ))}
              </div>
            </div>

            <p className="text-sm text-text-tertiary mb-4">
              {template.description}
            </p>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-text-tertiary">Steps</p>
                <p className="text-sm font-medium text-text-primary">
                  {template.steps.length}
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Variables</p>
                <p className="text-sm font-medium text-text-primary">
                  {template.variables.length}
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Used</p>
                <p className="text-sm font-medium text-text-primary">
                  {formatNumber(template.metadata.usageCount)}
                </p>
              </div>
              <div>
                <p className="text-xs text-text-tertiary">Rating</p>
                <p className="text-sm font-medium text-text-primary">
                  {template.metadata.rating.toFixed(1)}
                </p>
              </div>
            </div>

            <div className="flex flex-wrap gap-2 mb-4">
              {template.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 bg-surface-secondary text-text-tertiary text-xs rounded"
                >
                  {tag}
                </span>
              ))}
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleCreateFromTemplate(template)}
                className="flex-1 px-4 py-2 bg-primary hover:bg-primary-700 text-white rounded-lg transition-colors"
              >
                Use Template
              </button>
              <button
                onClick={() => setSelectedTemplate(template)}
                className="px-4 py-2 bg-surface-secondary hover:bg-surface-hover text-text-primary rounded-lg transition-colors"
              >
                <Eye size={16} />
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={`flex flex-col h-full ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-6 border-b border-border">
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-primary/10 rounded-lg">
            <WorkflowIcon size={24} className="text-primary" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-text-primary">Workflow Management</h1>
            <p className="text-text-tertiary">Design, automate, and optimize your tender workflows</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setViewMode('templates')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              viewMode === 'templates'
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
          >
            Templates
          </button>
          <button
            onClick={() => setViewMode('list')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              viewMode === 'list'
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
          >
            List View
          </button>
          <button
            onClick={() => setViewMode('builder')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              viewMode === 'builder'
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
          >
            Builder
          </button>
          <button
            onClick={() => setViewMode('analytics')}
            className={`px-4 py-2 rounded-lg transition-colors ${
              viewMode === 'analytics'
                ? 'bg-primary text-white'
                : 'bg-surface-secondary text-text-secondary hover:bg-surface-hover'
            }`}
          >
            Analytics
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 flex">
        {/* Sidebar */}
        <div className="w-80 border-r border-border bg-surface-secondary">
          <div className="p-4 border-b border-border">
            <h3 className="text-lg font-bold text-text-primary mb-4">Workflows</h3>
            <div className="space-y-2">
              {workflows.map((workflow) => (
                <button
                  key={workflow.id}
                  onClick={() => setSelectedWorkflow(workflow)}
                  className={`w-full p-3 rounded-lg text-left transition-colors ${
                    selectedWorkflow?.id === workflow.id
                      ? 'bg-primary text-white'
                      : 'bg-surface hover:bg-surface-hover text-text-primary'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="font-medium">{workflow.name}</span>
                    <span className={`px-2 py-1 text-xs rounded ${
                      selectedWorkflow?.id === workflow.id
                        ? 'bg-white/20 text-white'
                        : getStatusColor(workflow.status)
                    }`}>
                      {workflow.status.toUpperCase()}
                    </span>
                  </div>
                  <p className={`text-sm ${
                    selectedWorkflow?.id === workflow.id
                      ? 'text-white/80'
                      : 'text-text-tertiary'
                  }`}>
                    {workflow.steps.length} steps • {workflow.metadata.successRate.toFixed(1)}% success
                  </p>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Main Content */}
        {viewMode === 'builder' && renderWorkflowBuilder()}
        {viewMode === 'list' && renderWorkflowList()}
        {viewMode === 'analytics' && renderAnalytics()}
        {viewMode === 'templates' && renderTemplates()}
      </div>

      {/* Template Modal */}
      {showTemplateModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-surface border border-border rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text-primary">Choose Template</h3>
              <button
                onClick={() => setShowTemplateModal(false)}
                className="p-2 text-text-tertiary hover:text-text-primary"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="space-y-4">
              {WORKFLOW_TEMPLATES.map((template) => (
                <div
                  key={template.id}
                  className="p-4 border border-border rounded-lg hover:bg-surface-secondary cursor-pointer"
                  onClick={() => handleCreateFromTemplate(template)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-text-primary">{template.name}</h4>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-text-tertiary">{template.steps.length} steps</span>
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <div
                            key={i}
                            className={`w-2 h-2 rounded-full ${
                              i < Math.floor(template.metadata.rating)
                                ? 'bg-accent'
                                : 'bg-surface-secondary'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  <p className="text-sm text-text-tertiary mb-2">{template.description}</p>
                  <div className="flex flex-wrap gap-2">
                    {template.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-2 py-1 bg-surface-secondary text-text-tertiary text-xs rounded"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Optimization Modal */}
      {showOptimizations && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-surface border border-border rounded-lg p-6 max-w-4xl w-full max-h-[80vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text-primary">Workflow Optimizations</h3>
              <button
                onClick={() => setShowOptimizations(false)}
                className="p-2 text-text-tertiary hover:text-text-primary"
              >
                <X size={20} />
              </button>
            </div>
            
            <div className="space-y-6">
              {/* Performance Score */}
              <div className="bg-surface-secondary rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-lg font-medium text-text-primary">Performance Score</h4>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-text-tertiary">Current</span>
                    <span className="text-lg font-bold text-text-primary">
                      {MOCK_OPTIMIZATION.performance.currentScore}
                    </span>
                    <ArrowRight size={16} className="text-text-tertiary" />
                    <span className="text-sm text-text-tertiary">Potential</span>
                    <span className="text-lg font-bold text-accent">
                      {MOCK_OPTIMIZATION.performance.potentialScore}
                    </span>
                  </div>
                </div>
                <div className="w-full bg-surface-tertiary rounded-full h-2">
                  <div
                    className="bg-accent h-2 rounded-full transition-all duration-300"
                    style={{ width: `${MOCK_OPTIMIZATION.performance.currentScore}%` }}
                  />
                </div>
                <p className="text-sm text-text-tertiary mt-2">
                  Potential improvement: {MOCK_OPTIMIZATION.performance.improvementPercentage.toFixed(1)}%
                </p>
              </div>

              {/* Recommendations */}
              <div>
                <h4 className="text-lg font-medium text-text-primary mb-4">Recommendations</h4>
                <div className="space-y-4">
                  {MOCK_OPTIMIZATION.recommendations.map((rec, index) => (
                    <div key={index} className="p-4 border border-border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-text-primary">{rec.type.replace('_', ' ').toUpperCase()}</h5>
                        <div className="flex items-center space-x-2">
                          <span className={`px-2 py-1 text-xs rounded ${
                            rec.impact === 'high' ? 'bg-accent/10 text-accent' :
                            rec.impact === 'medium' ? 'bg-warning/10 text-warning' :
                            'bg-surface-secondary text-text-tertiary'
                          }`}>
                            {rec.impact.toUpperCase()} IMPACT
                          </span>
                          <span className={`px-2 py-1 text-xs rounded ${
                            rec.effort === 'low' ? 'bg-accent/10 text-accent' :
                            rec.effort === 'medium' ? 'bg-warning/10 text-warning' :
                            'bg-error/10 text-error'
                          }`}>
                            {rec.effort.toUpperCase()} EFFORT
                          </span>
                        </div>
                      </div>
                      <p className="text-sm text-text-tertiary mb-2">{rec.description}</p>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-quaternary">
                          Affects: {rec.stepIds.join(', ')}
                        </span>
                        <span className="text-sm font-medium text-accent">
                          +{rec.estimatedImprovement}% improvement
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Bottlenecks */}
              <div>
                <h4 className="text-lg font-medium text-text-primary mb-4">Bottlenecks</h4>
                <div className="space-y-3">
                  {MOCK_OPTIMIZATION.bottlenecks.map((bottleneck, index) => (
                    <div key={index} className="p-4 border border-warning/20 bg-warning/5 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h5 className="font-medium text-text-primary">Step: {bottleneck.stepId}</h5>
                        <span className="text-sm text-warning">
                          {(bottleneck.frequency * 100).toFixed(0)}% frequency
                        </span>
                      </div>
                      <p className="text-sm text-text-tertiary mb-2">{bottleneck.reason}</p>
                      <p className="text-sm text-accent">{bottleneck.suggestedFix}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}