"use node";
import { action, internalMutation, internalQuery } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import OpenA<PERSON> from "openai";
import { createWorker } from "tesseract.js";
import PDFParser from "pdf2json";
import * as mammoth from "mammoth";
import * as XLSX from "xlsx";
// import sharp from "sharp"; // Not compatible with Convex runtime
import { Doc, Id } from "./_generated/dataModel";

// OpenAI client will be instantiated inside functions
const getOpenAI = () => new OpenAI({
  baseURL: process.env.CONVEX_OPENAI_BASE_URL,
  apiKey: process.env.CONVEX_OPENAI_API_KEY || process.env.OPENAI_API_KEY || "",
});

// Document parser agent configuration
const PARSER_CONFIG = {
  ocr: {
    language: "eng",
    confidence_threshold: 60,
    preprocessing: {
      resize: true,
      denoise: true,
      deskew: true,
      enhance_contrast: true,
    },
  },
  llm: {
    model: "gpt-4o-mini",
    temperature: 0.1,
    max_tokens: 4000,
    structured_output: true,
  },
  extraction: {
    tender_fields: [
      "tender_name",
      "client_name",
      "due_date",
      "estimated_value",
      "contract_duration",
      "locations",
      "building_count",
      "states",
      "compliance_requirements",
      "scope_of_work",
      "submission_requirements",
      "evaluation_criteria",
      "mandatory_requirements",
      "contact_information",
    ],
    confidence_weights: {
      high: 0.9,
      medium: 0.7,
      low: 0.5,
    },
  },
  validation: {
    date_formats: ["YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY", "DD MMM YYYY"],
    currency_patterns: ["$", "AUD", "AU$", "dollars"],
    state_patterns: ["NSW", "VIC", "QLD", "SA", "WA", "TAS", "NT", "ACT"],
  },
};

// Main document processing action
export const processDocumentWithOCR = action({
  args: {
    fileId: v.id("files"),
    processingOptions: v.optional(v.object({
      enableOCR: v.boolean(),
      ocrLanguage: v.optional(v.string()),
      extractStructure: v.boolean(),
      extractRequirements: v.boolean(),
      extractEntities: v.boolean(),
      customPrompt: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args): Promise<{ success: boolean; analysisId?: Id<"document_analysis">; error?: string }> => {
    // Get file details
    const file = await ctx.runQuery(internal.documentParser.getFileDetails, {
      fileId: args.fileId,
    });
    
    if (!file) {
      throw new Error("File not found");
    }

    // Create document analysis record
    const analysisId = await ctx.runMutation(internal.documentParser.createAnalysis, {
      fileId: args.fileId,
      type: "comprehensive_analysis",
    });

    try {
      // Get file blob from storage
      const blob = await ctx.storage.get(file.storageId);
      if (!blob) {
        throw new Error("File not found in storage");
      }
      
      const buffer = await blob.arrayBuffer();
      
      // Process based on file type
      let extractedData: any;
      
      switch (file.mimeType) {
        case "application/pdf":
          extractedData = await processPDF(buffer, args.processingOptions);
          break;
        case "application/vnd.openxmlformats-officedocument.wordprocessingml.document":
        case "application/msword":
          extractedData = await processDOCX(buffer, args.processingOptions);
          break;
        case "image/png":
        case "image/jpeg":
        case "image/jpg":
        case "image/tiff":
          extractedData = await processImage(buffer, args.processingOptions);
          break;
        case "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":
        case "application/vnd.ms-excel":
          extractedData = await processSpreadsheet(buffer, args.processingOptions);
          break;
        default:
          extractedData = await processGenericText(buffer, args.processingOptions);
      }

      // Apply LLM analysis for intelligent extraction
      const llmAnalysis = await analyzeTenderDocument(extractedData.text, args.processingOptions?.customPrompt);
      
      // Combine OCR and LLM results
      const finalResult = {
        ...extractedData,
        llmExtraction: llmAnalysis,
        confidence: calculateOverallConfidence(extractedData, llmAnalysis),
      };

      // Update analysis with results
      await ctx.runMutation(internal.documentParser.updateAnalysis, {
        analysisId,
        status: "completed",
        results: finalResult,
      });

      // Create tender if high confidence
      if (finalResult.confidence.overall > 0.8 && llmAnalysis.tender_data) {
        await ctx.runMutation(internal.documentParser.createTenderFromAnalysis, {
          analysisId,
          fileId: args.fileId,
          tenderData: llmAnalysis.tender_data,
        });
      }

      return {
        analysisId,
        success: true,
      };

    } catch (error) {
      await ctx.runMutation(internal.documentParser.updateAnalysis, {
        analysisId,
        status: "failed",
        error: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  },
});

// Process PDF files with OCR support
async function processPDF(buffer: ArrayBuffer, options?: any): Promise<any> {
  const pdfParser = new PDFParser();
  
  return new Promise((resolve, reject) => {
    let textContent = "";
    let pageCount = 0;
    let hasImages = false;
    
    pdfParser.on("pdfParser_dataError", (errData) => reject(errData.parserError));
    
    pdfParser.on("pdfParser_dataReady", async (pdfData) => {
      // Extract text content
      textContent = pdfParser.getRawTextContent();
      pageCount = pdfData.Pages.length;
      
      // Check for images that might need OCR
      for (const page of pdfData.Pages) {
        if ((page as any).Images && (page as any).Images.length > 0) {
          hasImages = true;
          break;
        }
      }
      
      // If text is minimal and has images, apply OCR
      if (options?.enableOCR && textContent.length < 500 && hasImages) {
        // Extract images and run OCR
        const ocrResults = await extractAndOCRImages(pdfData);
        textContent = combineTextResults(textContent, ocrResults);
      }
      
      resolve({
        text: textContent,
        pageCount,
        hasImages,
        metadata: {
          title: pdfData.Meta?.Title || "",
          author: pdfData.Meta?.Author || "",
          subject: pdfData.Meta?.Subject || "",
          keywords: pdfData.Meta?.Keywords || "",
        },
      });
    });
    
    pdfParser.parseBuffer(Buffer.from(buffer));
  });
}

// Process Word documents
async function processDOCX(buffer: ArrayBuffer, options?: any): Promise<any> {
  try {
    const result = await mammoth.extractRawText({ buffer: Buffer.from(buffer) });
    const htmlResult = await mammoth.convertToHtml({ buffer: Buffer.from(buffer) });
    
    // Extract structure from HTML
    const structure = extractDocumentStructure(htmlResult.value);
    
    return {
      text: result.value,
      html: htmlResult.value,
      structure,
      metadata: {
        messages: result.messages,
      },
    };
  } catch (error) {
    throw new Error(`Failed to process Word document: ${error}`);
  }
}

// Process images with OCR
async function processImage(buffer: ArrayBuffer, options?: any): Promise<any> {
  try {
    // Preprocess image for better OCR results
    const preprocessedBuffer = await preprocessImage(buffer);
    
    // Create Tesseract worker
    const worker = await createWorker({
      // langPath option has been removed in newer versions
      logger: (m: any) => console.log(m),
    });
    
    await worker.reinitialize(options?.ocrLanguage || "eng");
    
    // Set OCR parameters for better accuracy
    await worker.setParameters({
      tessedit_pageseg_mode: 1 as any, // Automatic page segmentation with OSD
      tessedit_char_whitelist: "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz .,;:!?-()[]{}/'\"@#$%&*+=",
    });
    
    const { data } = await worker.recognize(preprocessedBuffer);
    await worker.terminate();
    
    return {
      text: data.text,
      confidence: data.confidence,
      words: data.words,
      lines: data.lines,
      blocks: data.blocks,
      metadata: {
        // language and orientation are not available in the Page type
        // language: data.language,
        // orientation: data.orientation,
      },
    };
  } catch (error) {
    throw new Error(`Failed to process image: ${error}`);
  }
}

// Process spreadsheets
async function processSpreadsheet(buffer: ArrayBuffer, options?: any): Promise<any> {
  try {
    const workbook = XLSX.read(buffer, { type: "array" });
    let allText = "";
    const sheets: any[] = [];
    
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      const textData = XLSX.utils.sheet_to_txt(worksheet);
      
      allText += `\n--- Sheet: ${sheetName} ---\n${textData}`;
      sheets.push({
        name: sheetName,
        data: jsonData,
        text: textData,
      });
    }
    
    return {
      text: allText,
      sheets,
      metadata: {
        sheetCount: workbook.SheetNames.length,
        sheetNames: workbook.SheetNames,
      },
    };
  } catch (error) {
    throw new Error(`Failed to process spreadsheet: ${error}`);
  }
}

// Process generic text files
async function processGenericText(buffer: ArrayBuffer, options?: any): Promise<any> {
  const text = new TextDecoder("utf-8").decode(buffer);
  return {
    text,
    metadata: {
      encoding: "utf-8",
      lineCount: text.split("\n").length,
    },
  };
}

// Preprocess image for better OCR results
async function preprocessImage(buffer: ArrayBuffer): Promise<Buffer> {
  try {
    // Sharp is not compatible with Convex runtime
    // For now, use the original buffer without preprocessing
    return Buffer.from(buffer);
  } catch (error) {
    // If preprocessing fails, return original buffer
    return Buffer.from(buffer);
  }
}

// Extract document structure from HTML
function extractDocumentStructure(html: string): any {
  const structure = {
    headers: [] as any[],
    sections: [] as any[],
    lists: [] as any[],
    tables: [] as any[],
  };
  
  // Simple regex-based extraction (in production, use proper HTML parser)
  const headerRegex = /<h([1-6])>(.*?)<\/h\1>/gi;
  let match;
  
  while ((match = headerRegex.exec(html)) !== null) {
    structure.headers.push({
      level: parseInt(match[1]),
      text: match[2].replace(/<[^>]*>/g, "").trim(),
    });
  }
  
  return structure;
}

// Extract and OCR images from PDF
async function extractAndOCRImages(pdfData: any): Promise<string[]> {
  const ocrResults: string[] = [];
  
  // This is a placeholder - actual implementation would extract images
  // from PDF and process them with OCR
  
  return ocrResults;
}

// Combine text results from multiple sources
function combineTextResults(originalText: string, ocrResults: string[]): string {
  let combinedText = originalText;
  
  for (const ocrText of ocrResults) {
    if (ocrText && ocrText.length > 10) {
      combinedText += "\n\n[OCR Extracted Content]\n" + ocrText;
    }
  }
  
  return combinedText;
}

// Analyze tender document using LLM
async function analyzeTenderDocument(text: string, customPrompt?: string): Promise<any> {
  const systemPrompt = `You are an expert tender document analysis system for ARA Property Services.
Your task is to extract comprehensive information from tender documents with high accuracy.

Extract ALL of the following information if present:
1. Tender Details:
   - Tender name/title
   - Reference number
   - Client/organization name
   - Due date and time
   - Estimated contract value
   - Contract duration/period

2. Location Information:
   - Sites/locations mentioned
   - Building count
   - States/territories
   - Specific addresses

3. Scope and Requirements:
   - Services required
   - Scope of work
   - Key deliverables
   - Performance indicators

4. Compliance and Criteria:
   - Mandatory requirements
   - Compliance flags
   - Insurance requirements
   - Certification requirements
   - Evaluation criteria
   - Weightings

5. Submission Requirements:
   - Required sections
   - Page/word limits
   - Format requirements
   - Supporting documents

6. Important Dates:
   - Site visits
   - Q&A deadlines
   - Submission deadline
   - Contract start date

7. Contact Information:
   - Contact person
   - Email
   - Phone
   - Address

Provide confidence scores (0.0-1.0) for each extracted field.
Format dates as YYYY-MM-DD.
Extract monetary values as numbers only.`;

  const userPrompt = customPrompt || `Extract tender information from the following document text:

${text.substring(0, 30000)}

Provide the extracted information in the following JSON structure:
{
  "tender_data": {
    "tender_name": "",
    "reference_number": "",
    "client_name": "",
    "due_date": "",
    "estimated_value": 0,
    "contract_duration": "",
    "locations": {
      "sites": [],
      "building_count": 0,
      "states": []
    },
    "scope": {
      "services": [],
      "key_deliverables": [],
      "performance_indicators": []
    },
    "compliance": {
      "mandatory_requirements": [],
      "insurance_requirements": [],
      "certifications": [],
      "compliance_flags": []
    },
    "sections": [
      {
        "title": "",
        "word_limit": 0,
        "page_limit": 0,
        "weight": 0
      }
    ],
    "important_dates": {
      "site_visit": "",
      "qa_deadline": "",
      "submission_deadline": "",
      "contract_start": ""
    },
    "contact": {
      "name": "",
      "email": "",
      "phone": "",
      "address": ""
    }
  },
  "confidence_scores": {
    "overall": 0.0,
    "by_section": {}
  },
  "warnings": [],
  "suggestions": []
}`;

  try {
    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: PARSER_CONFIG.llm.model,
      messages: [
        { role: "system", content: systemPrompt },
        { role: "user", content: userPrompt }
      ],
      temperature: PARSER_CONFIG.llm.temperature,
      max_tokens: PARSER_CONFIG.llm.max_tokens,
      response_format: { type: "json_object" },
    });

    const result = response.choices[0].message.content;
    if (!result) {
      throw new Error("No response from LLM");
    }

    return JSON.parse(result);
  } catch (error) {
    throw new Error(`LLM analysis failed: ${error}`);
  }
}

// Calculate overall confidence score
function calculateOverallConfidence(ocrData: any, llmData: any): any {
  const confidence = {
    ocr: ocrData.confidence || 0,
    llm: llmData.confidence_scores?.overall || 0,
    overall: 0,
    factors: {} as any,
  };
  
  // Weight OCR and LLM confidence
  if (ocrData.confidence) {
    confidence.overall = (ocrData.confidence * 0.3 + llmData.confidence_scores?.overall * 0.7);
  } else {
    confidence.overall = llmData.confidence_scores?.overall || 0;
  }
  
  // Additional confidence factors
  confidence.factors = {
    text_quality: ocrData.text ? Math.min(ocrData.text.length / 1000, 1) : 0,
    field_completeness: calculateFieldCompleteness(llmData.tender_data),
    data_consistency: checkDataConsistency(llmData.tender_data),
  };
  
  return confidence;
}

// Calculate field completeness
function calculateFieldCompleteness(tenderData: any): number {
  if (!tenderData) return 0;
  
  const requiredFields = [
    "tender_name",
    "client_name",
    "due_date",
    "estimated_value",
  ];
  
  let filledFields = 0;
  for (const field of requiredFields) {
    if (tenderData[field] && tenderData[field] !== "") {
      filledFields++;
    }
  }
  
  return filledFields / requiredFields.length;
}

// Check data consistency
function checkDataConsistency(tenderData: any): number {
  if (!tenderData) return 0;
  
  let score = 1.0;
  
  // Check date consistency
  if (tenderData.due_date && !isValidDate(tenderData.due_date)) {
    score -= 0.2;
  }
  
  // Check value consistency
  if (tenderData.estimated_value && tenderData.estimated_value < 0) {
    score -= 0.2;
  }
  
  return Math.max(0, score);
}

// Validate date format
function isValidDate(dateString: string): boolean {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date.getTime());
}

// Internal queries and mutations
export const getFileDetails = internalQuery({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.fileId);
  },
});

export const createAnalysis = internalMutation({
  args: {
    fileId: v.id("files"),
    type: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("document_analysis", {
      fileId: args.fileId,
      status: "processing",
      type: args.type as any,
      startedAt: Date.now(),
      metadata: {
        processingTime: 0,
        model: PARSER_CONFIG.llm.model,
        version: "1.0.0",
      },
    });
  },
});

export const updateAnalysis = internalMutation({
  args: {
    analysisId: v.id("document_analysis"),
    status: v.string(),
    results: v.optional(v.any()),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      completedAt: Date.now(),
    };
    
    if (args.results) {
      const { llmExtraction, confidence, ...extractedData } = args.results;
      
      updates.textContent = extractedData.text;
      updates.wordCount = extractedData.text?.split(/\s+/).length || 0;
      updates.characterCount = extractedData.text?.length || 0;
      updates.pageCount = extractedData.pageCount || 1;
      updates.confidence = confidence.overall;
      
      if (llmExtraction?.tender_data) {
        updates.keyPhrases = [
          llmExtraction.tender_data.tender_name,
          llmExtraction.tender_data.client_name,
          ...(llmExtraction.tender_data.locations?.sites || []),
        ].filter(Boolean);
      }
      
      updates.metadata = {
        ...updates.metadata,
        processingTime: Date.now() - (await ctx.db.get(args.analysisId))!.startedAt,
        extractedData: llmExtraction,
        confidence: confidence,
      };
    }
    
    if (args.error) {
      updates.error = args.error;
    }
    
    await ctx.db.patch(args.analysisId, updates);
  },
});

export const createTenderFromAnalysis = internalMutation({
  args: {
    analysisId: v.id("document_analysis"),
    fileId: v.id("files"),
    tenderData: v.any(),
  },
  handler: async (ctx, args) => {
    const { tender_data } = args.tenderData;
    
    // Create tender
    const tenderId = await ctx.db.insert("tenders", {
      name: tender_data.tender_name || "Untitled Tender",
      clientName: tender_data.client_name || "Unknown Client",
      status: "draft",
      dueDate: tender_data.due_date || "",
      estimatedValue: tender_data.estimated_value || 0,
      contractDuration: tender_data.contract_duration,
      projectLocation: tender_data.locations?.sites?.[0],
      type: "cleaning_services",
      priority: "medium",
    });
    
    // Create sections if found
    if (tender_data.sections && tender_data.sections.length > 0) {
      for (const section of tender_data.sections) {
        await ctx.db.insert("bidSections", {
          tenderId,
          title: section.title,
          content: "",
          wordCount: 0,
          wordLimit: section.word_limit || 1000,
          scoreWeight: section.weight || 0,
          status: "pending",
          type: "custom",
          version: 1,
          requirements: tender_data.compliance?.mandatory_requirements || [],
        });
      }
    }
    
    // Link file to tender
    await ctx.db.patch(args.fileId, {
      tenderId,
    });
    
    return tenderId;
  },
});

// Email webhook handler for Gmail integration
export const processEmailAttachment = action({
  args: {
    emailId: v.string(),
    attachmentId: v.string(),
    fileName: v.string(),
    mimeType: v.string(),
    size: v.number(),
    base64Data: v.string(),
  },
  handler: async (ctx, args): Promise<{ success: boolean; fileId?: Id<"files">; error?: string }> => {
    // Decode base64 attachment
    const buffer = Buffer.from(args.base64Data, "base64");
    
    // Store file
    const blob = new Blob([buffer], { type: args.mimeType });
    const storageId = await ctx.storage.store(blob);
    
    // Create file record
    const fileId: Id<"files"> = await ctx.runMutation(internal.files.create, {
      name: args.fileName,
      originalName: args.fileName,
      type: getFileTypeFromMime(args.mimeType),
      mimeType: args.mimeType,
      size: args.size,
      storageId,
      category: "tender_document",
      metadata: {
        source: "email",
        emailId: args.emailId,
      },
    });
    
    // Automatically process the document
    const result = await processDocumentWithOCR(ctx, {
      fileId,
      processingOptions: {
        enableOCR: true,
        extractStructure: true,
        extractRequirements: true,
        extractEntities: true,
      },
    });
    
    return {
      fileId,
      success: result.success,
    };
  },
});

// Helper function to determine file type from MIME type
function getFileTypeFromMime(mimeType: string): string {
  const mimeMap: Record<string, string> = {
    "application/pdf": "pdf",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document": "docx",
    "application/msword": "doc",
    "text/plain": "txt",
    "image/png": "image",
    "image/jpeg": "image",
    "image/jpg": "image",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": "xlsx",
    "application/vnd.ms-excel": "xls",
  };
  
  return mimeMap[mimeType] || "other";
}