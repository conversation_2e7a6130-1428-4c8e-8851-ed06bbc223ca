import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Workflow mutations
export const createWorkflowInstance = mutation({
  args: {
    workflowId: v.id("workflows"),
    triggeredBy: v.string(),
    triggerData: v.object({}),
  },
  handler: async (ctx, args) => {
    const workflow = await ctx.db.get(args.workflowId);
    if (!workflow) {
      throw new Error("Workflow not found");
    }

    return await ctx.db.insert("workflow_instances", {
      workflowId: args.workflowId,
      status: "pending",
      startedAt: Date.now(),
      triggeredBy: args.triggeredBy,
      triggerData: args.triggerData,
      progress: 0,
      context: {
        variables: {},
        artifacts: [],
      },
      steps: [],
      metadata: {
        priority: "medium",
        tags: [],
        customFields: {},
      },
    });
  },
});

export const updateWorkflowInstance = mutation({
  args: {
    instanceId: v.id("workflow_instances"),
    status: v.string(),
    currentStep: v.optional(v.string()),
    progress: v.number(),
    context: v.object({
      variables: v.object({}),
      artifacts: v.array(v.object({
        name: v.string(),
        type: v.string(),
        url: v.optional(v.string()),
        data: v.optional(v.any()),
      })),
    }),
    steps: v.array(v.any()),
    error: v.optional(v.object({
      message: v.string(),
      step: v.string(),
      code: v.string(),
      details: v.object({}),
    })),
  },
  handler: async (ctx, args) => {
    const updates: any = {
      status: args.status,
      progress: args.progress,
      context: args.context,
      steps: args.steps,
    };

    if (args.currentStep) updates.currentStep = args.currentStep;
    if (args.error) updates.error = args.error;
    if (args.status === "completed" || args.status === "failed") {
      updates.completedAt = Date.now();
    }

    await ctx.db.patch(args.instanceId, updates);
  },
});

// Event mutations
export const createEvent = mutation({
  args: {
    type: v.string(),
    source: v.string(),
    payload: v.object({}),
    correlationId: v.optional(v.string()),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      userId: v.optional(v.string()),
      tags: v.array(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("events", {
      type: args.type,
      source: args.source,
      status: "pending",
      priority: "medium",
      payload: args.payload,
      correlationId: args.correlationId,
      createdAt: Date.now(),
      retryCount: 0,
      maxRetries: 3,
      handlers: [],
      metadata: args.metadata,
    });
  },
});

export const updateEventStatus = mutation({
  args: {
    eventId: v.id("events"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.eventId, {
      status: args.status,
      processedAt: Date.now(),
    });
  },
});

export const completeEvent = mutation({
  args: {
    eventId: v.id("events"),
    status: v.string(),
    handlers: v.array(v.object({
      name: v.string(),
      status: v.string(),
      startedAt: v.optional(v.number()),
      completedAt: v.optional(v.number()),
      error: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.eventId, {
      status: args.status,
      processedAt: Date.now(),
      handlers: args.handlers,
    });
  },
});

// Message queue mutations
export const enqueueMessage = mutation({
  args: {
    queue: v.string(),
    payload: v.object({}),
    headers: v.object({}),
    priority: v.number(),
    scheduledFor: v.optional(v.number()),
    metadata: v.object({
      correlationId: v.optional(v.string()),
      causationId: v.optional(v.string()),
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      tags: v.array(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("message_queue", {
      queue: args.queue,
      status: "pending",
      priority: args.priority,
      payload: args.payload,
      headers: args.headers,
      createdAt: Date.now(),
      scheduledFor: args.scheduledFor,
      retryCount: 0,
      maxRetries: 3,
      metadata: args.metadata,
    });
  },
});

export const updateMessageStatus = mutation({
  args: {
    messageId: v.id("message_queue"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.messageId, {
      status: args.status,
      processingStartedAt: Date.now(),
    });
  },
});

export const completeMessage = mutation({
  args: {
    messageId: v.id("message_queue"),
    result: v.any(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.messageId, {
      status: "completed",
      completedAt: Date.now(),
      result: args.result,
    });
  },
});

export const retryMessage = mutation({
  args: {
    messageId: v.id("message_queue"),
    error: v.object({
      message: v.string(),
      code: v.string(),
      stack: v.optional(v.string()),
    }),
    scheduledFor: v.number(),
  },
  handler: async (ctx, args) => {
    const message = await ctx.db.get(args.messageId);
    if (!message) return;

    await ctx.db.patch(args.messageId, {
      status: "pending",
      retryCount: message.retryCount + 1,
      scheduledFor: args.scheduledFor,
      error: args.error,
    });
  },
});

export const moveToDeadLetter = mutation({
  args: {
    messageId: v.id("message_queue"),
    deadLetterQueue: v.string(),
    error: v.object({
      message: v.string(),
      code: v.optional(v.string()),
      stack: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.messageId, {
      status: "dead_letter",
      queue: args.deadLetterQueue,
      error: args.error,
    });
  },
});

// Audit log mutations
export const createAuditLog = mutation({
  args: {
    action: v.string(),
    entityType: v.string(),
    entityId: v.string(),
    userId: v.optional(v.string()),
    agentId: v.optional(v.id("agents")),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    success: v.optional(v.boolean()),
    changes: v.optional(v.object({
      before: v.optional(v.object({})),
      after: v.optional(v.object({})),
      diff: v.optional(v.array(v.object({
        field: v.string(),
        oldValue: v.any(),
        newValue: v.any(),
      }))),
    })),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      eventId: v.optional(v.id("events")),
      requestId: v.optional(v.string()),
      sessionId: v.optional(v.string()),
      tags: v.array(v.string()),
      context: v.object({}),
    }),
    error: v.optional(v.object({
      message: v.string(),
      code: v.string(),
      details: v.object({}),
    })),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("audit_logs", {
      ...args,
      timestamp: Date.now(),
      success: args.success ?? true,
    });
  },
});

// Agent mutations
export const createAgent = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    type: v.string(),
    capabilities: v.array(v.string()),
    specializations: v.array(v.string()),
    model: v.string(),
    temperature: v.number(),
    maxTokens: v.number(),
    systemPrompt: v.string(),
    maxConcurrentTasks: v.number(),
    autoAssign: v.boolean(),
    qualityThreshold: v.number(),
    maxWordCount: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("agents", {
      ...args,
      status: "active",
      isActive: true,
      createdAt: Date.now(),
      version: "1.0.0",
      tasksCompleted: 0,
      averageQualityScore: 0,
      averageResponseTime: 0,
      successRate: 0,
      totalWords: 0,
      currentLoad: 0,
      priority: "medium",
    });
  },
});

export const updateAgentTaskStatus = mutation({
  args: {
    taskId: v.id("agent_tasks"),
    status: v.string(),
    startedAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const updates: any = { status: args.status };
    if (args.startedAt) updates.startedAt = args.startedAt;
    await ctx.db.patch(args.taskId, updates);
  },
});

export const completeAgentTask = mutation({
  args: {
    taskId: v.id("agent_tasks"),
    output: v.object({
      content: v.string(),
      wordCount: v.number(),
      confidence: v.number(),
      suggestions: v.optional(v.array(v.string())),
      warnings: v.optional(v.array(v.string())),
      processingTime: v.number(),
      revisionsNeeded: v.optional(v.boolean()),
      clarity: v.optional(v.number()),
      relevance: v.optional(v.number()),
      completeness: v.optional(v.number()),
      persuasiveness: v.optional(v.number()),
    }),
    qualityScore: v.number(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.taskId, {
      status: "completed",
      completedAt: Date.now(),
      output: args.output,
      qualityScore: args.qualityScore,
    });
  },
});

export const failAgentTask = mutation({
  args: {
    taskId: v.id("agent_tasks"),
    error: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.taskId, {
      status: "failed",
      completedAt: Date.now(),
      errorMessage: args.error,
    });
  },
});

export const updateAgentStatus = mutation({
  args: {
    agentId: v.id("agents"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.agentId, {
      status: args.status,
      lastActiveAt: Date.now(),
    });
  },
});

// Notification mutations
export const createNotification = mutation({
  args: {
    type: v.string(),
    channel: v.string(),
    recipient: v.object({
      type: v.string(),
      id: v.string(),
      name: v.optional(v.string()),
      metadata: v.object({}),
    }),
    subject: v.string(),
    content: v.object({
      text: v.optional(v.string()),
      html: v.optional(v.string()),
      template: v.optional(v.string()),
      variables: v.object({}),
    }),
    priority: v.optional(v.string()),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      eventId: v.optional(v.id("events")),
      tags: v.array(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("notifications", {
      ...args,
      status: "pending",
      priority: args.priority || "medium",
      createdAt: Date.now(),
      retryCount: 0,
      maxRetries: 3,
      metadata: {
        ...args.metadata,
        tracking: {
          opens: 0,
          clicks: 0,
        },
      },
    });
  },
});

// System health mutations
export const updateSystemHealth = mutation({
  args: {
    component: v.string(),
    status: v.string(),
    metrics: v.object({
      cpu: v.optional(v.number()),
      memory: v.optional(v.number()),
      diskUsage: v.optional(v.number()),
      queueDepth: v.optional(v.number()),
      errorRate: v.optional(v.number()),
      responseTime: v.optional(v.number()),
      throughput: v.optional(v.number()),
    }),
    checks: v.array(v.object({
      name: v.string(),
      status: v.string(),
      message: v.optional(v.string()),
      lastChecked: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("system_health")
      .filter(q => q.eq(q.field("component"), args.component))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        ...args,
        timestamp: Date.now(),
        alerts: [],
      });
    } else {
      await ctx.db.insert("system_health", {
        ...args,
        timestamp: Date.now(),
        alerts: [],
        dependencies: [],
      });
    }
  },
});

// Queries
export const getPendingEvents = query({
  args: {
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("events")
      .filter(q => q.eq(q.field("status"), "pending"))
      .order("asc")
      .take(args.limit);
  },
});

export const getEvent = query({
  args: {
    eventId: v.id("events"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.eventId);
  },
});

export const getEventStatus = query({
  args: {
    eventId: v.id("events"),
  },
  handler: async (ctx, args) => {
    const event = await ctx.db.get(args.eventId);
    return event?.status;
  },
});

export const queryEvents = query({
  args: {
    types: v.optional(v.array(v.string())),
    source: v.optional(v.string()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    correlationId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const baseQuery = ctx.db.query("events");

    const filteredQuery = args.correlationId
      ? baseQuery.filter(q => q.eq(q.field("correlationId"), args.correlationId))
      : baseQuery;

    const results = await filteredQuery.collect();

    return results.filter(event => {
      if (args.types && !args.types.includes(event.type)) return false;
      if (args.source && event.source !== args.source) return false;
      if (args.startTime && event.createdAt < args.startTime) return false;
      if (args.endTime && event.createdAt > args.endTime) return false;
      return true;
    });
  },
});

export const dequeueMessages = query({
  args: {
    queue: v.string(),
    limit: v.number(),
    visibilityTimeout: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    return await ctx.db
      .query("message_queue")
      .filter(q => 
        q.and(
          q.eq(q.field("queue"), args.queue),
          q.eq(q.field("status"), "pending"),
          q.or(
            q.eq(q.field("scheduledFor"), undefined),
            q.lte(q.field("scheduledFor"), now)
          )
        )
      )
      .order("desc", "priority")
      .take(args.limit);
  },
});

export const getQueueStats = query({
  args: {
    queue: v.string(),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("message_queue")
      .filter(q => q.eq(q.field("queue"), args.queue))
      .collect();

    const stats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      deadLetter: 0,
    };

    messages.forEach(msg => {
      switch (msg.status) {
        case "pending": stats.pending++; break;
        case "processing": stats.processing++; break;
        case "completed": stats.completed++; break;
        case "failed": stats.failed++; break;
        case "dead_letter": stats.deadLetter++; break;
      }
    });

    return stats;
  },
});

export const purgeQueue = mutation({
  args: {
    queue: v.string(),
  },
  handler: async (ctx, args) => {
    const messages = await ctx.db
      .query("message_queue")
      .filter(q => q.eq(q.field("queue"), args.queue))
      .collect();

    let count = 0;
    for (const message of messages) {
      await ctx.db.delete(message._id);
      count++;
    }

    return count;
  },
});

export const getAgentTask = query({
  args: {
    taskId: v.id("agent_tasks"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.taskId);
  },
});

export const getQueuedTasksForAgent = query({
  args: {
    agentId: v.id("agents"),
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("agent_tasks")
      .filter(q => 
        q.and(
          q.eq(q.field("agentId"), args.agentId),
          q.eq(q.field("status"), "queued")
        )
      )
      .order("desc", "priority")
      .take(args.limit);
  },
});

export const getAgentStatistics = query({
  args: {
    agentId: v.id("agents"),
  },
  handler: async (ctx, args) => {
    const agent = await ctx.db.get(args.agentId);
    if (!agent) return null;

    const tasks = await ctx.db
      .query("agent_tasks")
      .filter(q => q.eq(q.field("agentId"), args.agentId))
      .collect();

    const completed = tasks.filter(t => t.status === "completed");
    const totalResponseTime = completed.reduce((sum, t) => 
      sum + (t.completedAt! - t.startedAt!), 0
    );

    return {
      tasksCompleted: completed.length,
      averageResponseTime: completed.length > 0 ? totalResponseTime / completed.length : 0,
      successRate: tasks.length > 0 ? completed.length / tasks.length : 0,
      currentLoad: tasks.filter(t => t.status === "processing").length,
    };
  },
});

export const healthCheck = query({
  args: {},
  handler: async (ctx) => {
    return { status: "ok", timestamp: Date.now() };
  },
});