"use node";
import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import OpenAI from "openai";

// OpenAI client will be instantiated inside functions
const getOpenAI = () => new OpenAI({
  baseURL: process.env.CONVEX_OPENAI_BASE_URL,
  apiKey: process.env.CONVEX_OPENAI_API_KEY || process.env.OPENAI_API_KEY || "",
});

export const generateContent = action({
  args: {
    sectionId: v.id("bidSections"),
    tenderName: v.string(),
    clientName: v.string(),
    sectionTitle: v.string(),
    currentContent: v.optional(v.string()),
    template: v.optional(v.any()),
    wordLimit: v.optional(v.number()),
    scoreWeight: v.optional(v.number()),
    existingContent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const prompt = `
You are an expert bid writer for ARA Property Services, a professional cleaning and property maintenance company.

Generate a compelling draft for the "${args.sectionTitle}" section of a tender response.

TENDER DETAILS:
- Tender Name: ${args.tenderName}
- Client: ${args.clientName}
- Section: ${args.sectionTitle}
- Word Limit: ${args.wordLimit || 'No limit specified'}
- Score Weight: ${args.scoreWeight || 'Not specified'}%

COMPANY PROFILE - ARA Property Services:
- Established cleaning and property maintenance company
- Specializes in commercial cleaning, office cleaning, and facility management
- Strong focus on quality, reliability, and customer satisfaction
- Experienced team with modern equipment and eco-friendly practices
- Proven track record with government and corporate clients

WRITING GUIDELINES:
1. Write in a professional, confident tone
2. Highlight ARA's relevant experience and capabilities
3. Address the specific requirements implied by the section title
4. Use concrete examples and quantifiable achievements where possible
5. Demonstrate understanding of the client's needs
6. Focus on value proposition and competitive advantages
7. ${args.wordLimit ? `Keep within approximately ${args.wordLimit} words` : 'Write comprehensively'}

${args.existingContent ? `EXISTING CONTENT TO IMPROVE/EXPAND:
${args.existingContent}

Please improve and expand the existing content while maintaining its core message.` : ''}

Generate the section content without markdown formatting:
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("Failed to generate content");
    }

    await ctx.runMutation(internal.bidSections.updateInternal, {
      id: args.sectionId,
      content,
    });
  },
});

export const improveSectionContent = action({
  args: {
    sectionId: v.id("bidSections"),
    currentContent: v.string(),
    improvementType: v.string(), // "expand", "condense", "strengthen", "clarify"
    additionalInstructions: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const improvementPrompts = {
      expand: "Expand this content with more detail, examples, and supporting information while maintaining its core message.",
      condense: "Condense this content to be more concise while preserving all key points and impact.",
      strengthen: "Strengthen this content by adding more compelling language, stronger value propositions, and more persuasive arguments.",
      clarify: "Clarify this content to make it more readable, better structured, and easier to understand.",
    };

    const prompt = `
You are an expert bid writer for ARA Property Services. 
${improvementPrompts[args.improvementType as keyof typeof improvementPrompts]}

${args.additionalInstructions ? `Additional instructions: ${args.additionalInstructions}` : ''}

Current content:
---
${args.currentContent}
---

Provide the improved version without markdown formatting:
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("Failed to improve content");
    }

    await ctx.runMutation(internal.bidSections.updateInternal, {
      id: args.sectionId,
      content,
    });
  },
});

export const generateSectionSuggestions = action({
  args: {
    sectionTitle: v.string(),
    tenderName: v.string(),
    clientName: v.string(),
  },
  handler: async (ctx, args) => {
    const prompt = `
You are an expert bid writer for ARA Property Services.
Generate 3 different key points or approaches for the "${args.sectionTitle}" section of a tender response.

Tender: ${args.tenderName}
Client: ${args.clientName}

Provide 3 distinct bullet points that could be used as starting points or key themes for this section.
Focus on different aspects of ARA's capabilities and value proposition.
Format as a simple list without markdown.
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.8,
    });

    const suggestions = response.choices[0].message.content;
    if (!suggestions) {
      throw new Error("Failed to generate suggestions");
    }

    return suggestions;
  },
});

export const generateAIAssistantResponse = action({
  args: {
    sectionId: v.id("bidSections"),
    userMessage: v.string(),
    sectionContent: v.string(),
    sectionTitle: v.string(),
    sectionRequirements: v.optional(v.array(v.string())),
    tenderName: v.string(),
    clientName: v.string(),
    context: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const prompt = `
You are an AI writing assistant specifically designed to help with bid writing for ARA Property Services.

CONTEXT:
- Current section: "${args.sectionTitle}"
- Tender: ${args.tenderName}
- Client: ${args.clientName}
- Section requirements: ${args.sectionRequirements?.join(', ') || 'Not specified'}
- Section status: ${args.context?.status || 'Unknown'}
- Word count: ${args.context?.wordCount || 0}/${args.context?.wordLimit || '∞'}
- Score weight: ${args.context?.scoreWeight || 0}%

CURRENT SECTION CONTENT:
---
${args.sectionContent || 'No content yet'}
---

USER QUESTION/REQUEST:
${args.userMessage}

Please provide helpful, specific advice about this bid section. You can:
- Suggest improvements to the content
- Help with structure and organization
- Recommend key points to include
- Provide writing tips specific to this section type
- Help ensure requirements are met
- Suggest competitive advantages to highlight

Respond in a friendly, professional tone. Be specific and actionable in your advice.
If appropriate, provide 2-3 quick action suggestions at the end.
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.7,
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error("Failed to generate AI assistant response");
    }

    // Generate some quick suggestions based on the response
    const suggestionPrompt = `
Based on the following AI response about a bid section, generate 3 short, actionable suggestions (each under 10 words):

AI Response: ${content}

Format as a simple array of strings.
`;

    const suggestionResponse = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: suggestionPrompt }],
      temperature: 0.8,
    });

    let suggestions: string[] = [];
    try {
      const suggestionContent = suggestionResponse.choices[0].message.content;
      if (suggestionContent) {
        // Parse suggestions from the response
        suggestions = suggestionContent
          .split('\n')
          .filter(line => line.trim())
          .slice(0, 3)
          .map(line => line.replace(/^[-*]\s*/, '').trim());
      }
    } catch (error) {
      // Fallback suggestions
      suggestions = [
        'Review section requirements',
        'Add more specific examples',
        'Strengthen value proposition'
      ];
    }

    return {
      content,
      suggestions,
    };
  },
});

export const analyzeContentQuality = action({
  args: {
    content: v.string(),
    sectionTitle: v.string(),
    requirements: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const prompt = `
You are a bid quality assessment expert. Analyze the following content for the "${args.sectionTitle}" section:

CONTENT TO ANALYZE:
---
${args.content}
---

REQUIREMENTS (if provided):
${args.requirements?.join('\n') || 'No specific requirements provided'}

Provide a quality assessment including:
1. Coverage score (0-100) - how well it addresses the section requirements
2. Readability score (0-10) - how clear and well-written it is
3. Persuasiveness score (0-100) - how compelling and competitive it is
4. Key strengths (2-3 bullet points)
5. Areas for improvement (2-3 bullet points)

Format your response as JSON with these exact fields: coverage, readability, persuasiveness, strengths, improvements
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      temperature: 0.3,
    });

    const analysis = response.choices[0].message.content;
    if (!analysis) {
      throw new Error("Failed to analyze content quality");
    }

    try {
      return JSON.parse(analysis);
    } catch (error) {
      // Fallback response if JSON parsing fails
      return {
        coverage: 75,
        readability: 7.0,
        persuasiveness: 80,
        strengths: ["Professional tone", "Clear structure"],
        improvements: ["Add more examples", "Strengthen competitive advantages"]
      };
    }
  },
});
