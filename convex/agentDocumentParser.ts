import { mutation, query, action, internalQuery, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { api, internal } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Document Parser Agent Configuration
const DOCUMENT_PARSER_AGENT = {
  name: "Document Parser Agent",
  description: "Specialized agent for OCR and intelligent document processing of tender documents",
  type: "document_parser",
  capabilities: [
    "ocr_processing",
    "text_extraction",
    "structure_analysis",
    "entity_recognition",
    "requirement_extraction",
    "compliance_checking",
    "multi_format_support",
    "confidence_scoring"
  ],
  specializations: [
    "tender_documents",
    "rfp_analysis",
    "contract_parsing",
    "compliance_documents",
    "technical_specifications"
  ],
  model: "gpt-4o-mini",
  temperature: 0.1,
  maxTokens: 4000,
  systemPrompt: `You are an expert document parsing agent specialized in tender document analysis for ARA Property Services.

Your capabilities include:
1. OCR processing for scanned documents
2. Intelligent text extraction from various formats
3. Structured data extraction (dates, values, locations, contacts)
4. Requirement and compliance identification
5. Multi-format support (PDF, DOC, DOCX, images, spreadsheets)
6. Confidence scoring and validation

Focus on extracting:
- Tender details (name, reference, client, dates, values)
- Location information (sites, buildings, states)
- Scope and requirements
- Compliance and mandatory requirements
- Submission requirements and evaluation criteria
- Important dates and deadlines
- Contact information

Always provide confidence scores and flag any uncertainties or missing information.`,
  maxConcurrentTasks: 5,
  qualityThreshold: 0.85,
};

// Initialize the document parser agent
export const initializeDocumentParserAgent = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if agent already exists
    const existingAgent = await ctx.db
      .query("agents")
      .filter(q => q.eq(q.field("type"), "document_parser"))
      .first();
    
    if (existingAgent) {
      return existingAgent._id;
    }
    
    // Create the document parser agent
    const agentId = await ctx.db.insert("agents", {
      ...DOCUMENT_PARSER_AGENT,
      isActive: true,
      createdAt: Date.now(),
      version: "1.0.0",
      tasksCompleted: 0,
      averageQualityScore: 0,
      averageResponseTime: 0,
      successRate: 0,
      totalWords: 0,
      currentLoad: 0,
      autoAssign: true,
      priority: "high",
      status: "active",
    });
    
    return agentId;
  },
});

// Create a document parsing task
export const createDocumentParsingTask = mutation({
  args: {
    fileId: v.id("files"),
    tenderId: v.optional(v.id("tenders")),
    priority: v.optional(v.string()),
    processingOptions: v.optional(v.object({
      enableOCR: v.boolean(),
      extractStructure: v.boolean(),
      extractRequirements: v.boolean(),
      extractEntities: v.boolean(),
      confidenceThreshold: v.optional(v.number()),
      customInstructions: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    // Get or create document parser agent
    let agent = await ctx.db
      .query("agents")
      .filter(q => q.eq(q.field("type"), "document_parser"))
      .first();
    
    if (!agent) {
      const agentId = await initializeDocumentParserAgent(ctx, {});
      agent = await ctx.db.get(agentId);
    }
    
    if (!agent) {
      throw new Error("Failed to initialize document parser agent");
    }
    
    // Get file details
    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }
    
    // Create task
    const taskId = await ctx.db.insert("agent_tasks", {
      agentId: agent._id,
      type: "document_parsing",
      status: "assigned",
      priority: args.priority || "medium",
      tenderId: args.tenderId || ("" as any), // Temporary tender ID
      assignedAt: Date.now(),
      estimatedDuration: estimateProcessingTime(file.size, file.type),
      progress: 0,
      retryCount: 0,
      input: {
        content: file.name,
        instructions: `Parse and extract tender information from ${file.name}`,
        context: {
          tenderName: "To be extracted",
          clientName: "To be extracted",
          sectionTitle: "Document Analysis",
          requirements: ["Extract all tender details", "Identify key dates", "Find compliance requirements"],
        },
        parameters: {
          fileId: args.fileId,
          processingOptions: args.processingOptions || {
            enableOCR: true,
            extractStructure: true,
            extractRequirements: true,
            extractEntities: true,
          },
        },
      },
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
    });
    
    // Update agent load
    await ctx.db.patch(agent._id, {
      currentLoad: agent.currentLoad + 1,
      lastActiveAt: Date.now(),
    });
    
    // Schedule processing
    await ctx.scheduler.runAfter(0, api.agentDocumentParser.processDocumentParsingTask, { taskId });
    
    return taskId;
  },
});

// Process document parsing task
export const processDocumentParsingTask = action({
  args: { taskId: v.id("agent_tasks") },
  handler: async (ctx, args) => {
    const task = await ctx.runQuery(internal.agentDocumentParser.getTask, { taskId: args.taskId });
    if (!task || task.status !== "assigned") {
      return;
    }
    
    // Update status to in progress
    await ctx.runMutation(api.agents.updateTaskStatus, {
      taskId: args.taskId,
      status: "in_progress",
      progress: 10,
    });
    
    try {
      const fileId = task.input.parameters?.fileId;
      const processingOptions = task.input.parameters?.processingOptions;
      
      if (!fileId) {
        throw new Error("No file ID provided");
      }
      
      // Process document with OCR
      const result = await ctx.runAction(api.documentParser.processDocumentWithOCR, {
        fileId,
        processingOptions,
      });
      
      // Calculate quality scores
      const qualityMetrics = calculateQualityMetrics(result);
      
      // Prepare output
      const output = {
        content: JSON.stringify(result.extractedData, null, 2),
        wordCount: JSON.stringify(result.extractedData).split(/\s+/).length,
        confidence: result.confidence.overall,
        suggestions: generateSuggestions(result),
        warnings: generateWarnings(result),
        processingTime: Date.now() - task.assignedAt,
        revisionsNeeded: result.confidence.overall < 0.8,
        clarity: qualityMetrics.clarity,
        relevance: qualityMetrics.relevance,
        completeness: qualityMetrics.completeness,
        persuasiveness: qualityMetrics.persuasiveness,
      };
      
      // Update task with results
      await ctx.runMutation(api.agents.updateTaskStatus, {
        taskId: args.taskId,
        status: "completed",
        progress: 100,
        output,
      });
      
      // Update agent statistics
      await ctx.runMutation(api.agents.updateAgentStats, {
        agentId: task.agentId,
        taskCompleted: true,
        qualityScore: (output.clarity + output.relevance + output.completeness + output.persuasiveness) / 4,
        responseTime: output.processingTime,
        wordCount: output.wordCount,
      });
      
      // Send notification to chat if needed
      if (task.input.parameters?.notifyChat) {
        await ctx.runMutation(api.agents.sendAgentMessage, {
          threadId: task.input.parameters.threadId,
          agentId: task.agentId,
          content: `Document parsing completed for ${task.input.content}. 
          
Confidence: ${(result.confidence.overall * 100).toFixed(1)}%
Extracted: ${result.extractedData ? Object.keys(result.extractedData).length : 0} fields
Analysis ID: ${result.analysisId}

Key findings:
${generateSummary(result.extractedData)}`,
          metadata: {
            confidence: result.confidence.overall,
            model: "gpt-4o-mini",
            tokens: 0,
            taskId: args.taskId,
          },
        });
      }
      
    } catch (error) {
      // Update task status to failed
      await ctx.runMutation(api.agents.updateTaskStatus, {
        taskId: args.taskId,
        status: "failed",
        progress: 0,
        errorMessage: error instanceof Error ? error.message : "Document parsing failed",
      });
      
      // Retry if under retry limit
      if (task.retryCount < 3) {
        await ctx.runMutation(internal.agentDocumentParser.incrementRetryCount, {
          taskId: args.taskId,
        });
        
        // Reschedule with exponential backoff
        const delay = Math.pow(2, task.retryCount) * 5000; // 5s, 10s, 20s
        await ctx.scheduler.runAfter(delay, api.agentDocumentParser.processDocumentParsingTask, { taskId: args.taskId });
      }
    }
  },
});

// Helper functions
function estimateProcessingTime(fileSize: number, fileType: string): number {
  // Base time in milliseconds
  let baseTime = 5000;
  
  // Add time based on file size (1 second per MB)
  baseTime += (fileSize / 1048576) * 1000;
  
  // Add time based on file type
  const typeMultipliers: Record<string, number> = {
    pdf: 1.5,
    image: 2.0, // OCR takes longer
    docx: 1.2,
    xlsx: 1.3,
    other: 1.0,
  };
  
  baseTime *= typeMultipliers[fileType] || 1.0;
  
  return Math.min(baseTime, 300000); // Cap at 5 minutes
}

function calculateQualityMetrics(result: any): any {
  const metrics = {
    clarity: 0.8,
    relevance: 0.9,
    completeness: 0.7,
    persuasiveness: 0.75,
  };
  
  // Adjust based on confidence
  const confidenceFactor = result.confidence.overall;
  
  // Adjust completeness based on extracted fields
  if (result.extractedData) {
    const data = result.extractedData.tender_data || result.extractedData;
    const filledFields = countFilledFields(data);
    const totalFields = countTotalFields(data);
    metrics.completeness = totalFields > 0 ? filledFields / totalFields : 0;
  }
  
  // Apply confidence factor
  Object.keys(metrics).forEach(key => {
    metrics[key as keyof typeof metrics] *= confidenceFactor;
  });
  
  return metrics;
}

function countFilledFields(obj: any, count = 0): number {
  for (const key in obj) {
    if (obj[key] !== null && obj[key] !== undefined && obj[key] !== "") {
      if (typeof obj[key] === "object" && !Array.isArray(obj[key])) {
        count = countFilledFields(obj[key], count);
      } else {
        count++;
      }
    }
  }
  return count;
}

function countTotalFields(obj: any, count = 0): number {
  for (const key in obj) {
    if (typeof obj[key] === "object" && !Array.isArray(obj[key])) {
      count = countTotalFields(obj[key], count);
    } else {
      count++;
    }
  }
  return count;
}

function generateSuggestions(result: any): string[] {
  const suggestions: string[] = [];
  
  if (result.confidence.overall < 0.7) {
    suggestions.push("Consider manual review due to low confidence score");
  }
  
  if (result.extractedData?.warnings?.length > 0) {
    suggestions.push("Address warnings identified during extraction");
  }
  
  if (!result.extractedData?.tender_data?.due_date) {
    suggestions.push("Manually verify submission deadline");
  }
  
  if (!result.extractedData?.tender_data?.estimated_value) {
    suggestions.push("Check for contract value information");
  }
  
  return suggestions;
}

function generateWarnings(result: any): string[] {
  const warnings: string[] = [];
  
  if (result.confidence.factors?.ocr < 0.6) {
    warnings.push("Low OCR confidence - text may be inaccurate");
  }
  
  if (result.extractedData?.warnings) {
    warnings.push(...result.extractedData.warnings);
  }
  
  return warnings;
}

function generateSummary(extractedData: any): string {
  if (!extractedData || !extractedData.tender_data) {
    return "No tender data extracted";
  }
  
  const data = extractedData.tender_data;
  const summary: string[] = [];
  
  if (data.tender_name) {
    summary.push(`• Tender: ${data.tender_name}`);
  }
  
  if (data.client_name) {
    summary.push(`• Client: ${data.client_name}`);
  }
  
  if (data.due_date) {
    summary.push(`• Due: ${data.due_date}`);
  }
  
  if (data.estimated_value) {
    summary.push(`• Value: $${data.estimated_value.toLocaleString()}`);
  }
  
  if (data.locations?.sites?.length > 0) {
    summary.push(`• Sites: ${data.locations.sites.length} location(s)`);
  }
  
  return summary.join("\n");
}

// Internal queries and mutations
export const getTask = internalQuery({
  args: { taskId: v.id("agent_tasks") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.taskId);
  },
});

export const incrementRetryCount = internalMutation({
  args: { taskId: v.id("agent_tasks") },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    if (task) {
      await ctx.db.patch(args.taskId, {
        retryCount: task.retryCount + 1,
        status: "assigned", // Reset to assigned for retry
      });
    }
  },
});

// Query to get document parsing results
export const getDocumentParsingResults = query({
  args: {
    fileId: v.optional(v.id("files")),
    tenderId: v.optional(v.id("tenders")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    
    const baseQuery = ctx.db.query("document_analysis");
    
    const indexedQuery = args.fileId
      ? baseQuery.withIndex("by_file", q => q.eq("fileId", args.fileId))
      : baseQuery;
    
    const analyses = await indexedQuery
      .order("desc")
      .take(args.limit || 50);
    
    // Get associated files and tenders
    const results = await Promise.all(
      analyses.map(async (analysis) => {
        const file = await ctx.db.get(analysis.fileId);
        const tender = file?.tenderId ? await ctx.db.get(file.tenderId) : null;
        
        return {
          ...analysis,
          fileName: file?.name,
          fileMimeType: file?.mimeType,
          tenderName: tender?.name,
        };
      })
    );
    
    return results;
  },
});

// Batch document processing
export const batchProcessDocuments = action({
  args: {
    fileIds: v.array(v.id("files")),
    tenderId: v.optional(v.id("tenders")),
    processingOptions: v.optional(v.object({
      enableOCR: v.boolean(),
      extractStructure: v.boolean(),
      extractRequirements: v.boolean(),
      extractEntities: v.boolean(),
    })),
  },
  handler: async (ctx, args) => {
    const tasks: Id<"agent_tasks">[] = [];
    
    for (const fileId of args.fileIds) {
      const taskId = await ctx.runMutation(api.agentDocumentParser.createDocumentParsingTask, {
        fileId,
        tenderId: args.tenderId,
        priority: "high",
        processingOptions: args.processingOptions,
      });
      
      tasks.push(taskId);
    }
    
    return {
      tasksCreated: tasks.length,
      taskIds: tasks,
    };
  },
});