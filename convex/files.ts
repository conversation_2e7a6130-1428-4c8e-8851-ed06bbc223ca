import { query, mutation, action, internalMutation, internalAction } from "./_generated/server";
import { v } from "convex/values";
import { api, internal } from "./_generated/api";

// File upload and management
export const generateUploadUrl = mutation(async (ctx) => {
  return await ctx.storage.generateUploadUrl();
});

export const createFile = mutation({
  args: {
    name: v.string(),
    originalName: v.string(),
    type: v.string(),
    mimeType: v.string(),
    size: v.number(),
    storageId: v.id("_storage"),
    storageKey: v.string(),
    checksum: v.string(),
    category: v.string(),
    accessLevel: v.optional(v.string()),
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    tags: v.optional(v.array(v.string())),
    description: v.optional(v.string()),
    isVersioned: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const fileId = await ctx.db.insert("files", {
      name: args.name,
      originalName: args.originalName,
      type: args.type,
      mimeType: args.mimeType,
      size: args.size,
      status: "uploaded",
      category: args.category,
      accessLevel: args.accessLevel || "internal",
      storageId: args.storageId,
      storageKey: args.storageKey,
      checksum: args.checksum,
      uploadedBy: identity.subject,
      uploadedAt: Date.now(),
      lastModified: Date.now(),
      downloadCount: 0,
      viewCount: 0,
      tags: args.tags || [],
      description: args.description,
      isVersioned: args.isVersioned || false,
      tenderId: args.tenderId,
      folderId: args.folderId,
      virusStatus: "not_scanned",
      metadata: {},
    });

    // Schedule virus scan and analysis
    await ctx.scheduler.runAfter(0, internal.files.scheduleProcessing, {
      fileId,
      processingTypes: ["virus_scan", "thumbnail_generation", "text_extraction"],
    });

    return fileId;
  },
});

export const listFiles = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    category: v.optional(v.string()),
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("files");
    
    const indexedQuery = args.tenderId
      ? baseQuery.withIndex("by_tender", (q) => q.eq("tenderId", args.tenderId))
      : args.folderId
      ? baseQuery.withIndex("by_folder", (q) => q.eq("folderId", args.folderId))
      : args.category
      ? baseQuery.withIndex("by_category", (q) => q.eq("category", args.category))
      : args.type
      ? baseQuery.withIndex("by_type", (q) => q.eq("type", args.type))
      : args.status
      ? baseQuery.withIndex("by_status", (q) => q.eq("status", args.status))
      : baseQuery;

    const files = await indexedQuery
      .order("desc")
      .collect();

    // Filter by permissions and add download URLs
    const filesWithUrls = await Promise.all(
      files.map(async (file) => {
        const hasPermission = await checkFilePermission(ctx, file._id, identity.subject, "read");
        if (!hasPermission) return null;

        const downloadUrl = await ctx.storage.getUrl(file.storageId);
        return {
          ...file,
          downloadUrl,
        };
      })
    );

    return filesWithUrls.filter(Boolean);
  },
});

export const getFile = query({
  args: { id: v.id("files") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const file = await ctx.db.get(args.id);
    if (!file) {
      throw new Error("File not found");
    }

    const hasPermission = await checkFilePermission(ctx, args.id, identity.subject, "read");
    if (!hasPermission) {
      throw new Error("Access denied");
    }

    // Note: View count updates should be done via a separate mutation

    const downloadUrl = await ctx.storage.getUrl(file.storageId);
    
    // Get file versions
    const versions = await ctx.db
      .query("file_versions")
      .withIndex("by_file", (q) => q.eq("fileId", args.id))
      .order("desc")
      .collect();

    // Get file analysis
    const analysis = await ctx.db
      .query("document_analysis")
      .withIndex("by_file", (q) => q.eq("fileId", args.id))
      .order("desc")
      .first();

    return {
      ...file,
      downloadUrl,
      versions,
      analysis,
    };
  },
});

export const searchFiles = query({
  args: {
    query: v.string(),
    filters: v.optional(v.object({
      type: v.optional(v.string()),
      category: v.optional(v.string()),
      status: v.optional(v.string()),
      uploadedBy: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
    })),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const results = await ctx.db
      .query("files")
      .withSearchIndex("search_files", (q) => {
        const searchQuery = q.search("name", args.query);
        
        if (!args.filters) {
          return searchQuery;
        }
        
        let filteredQuery = searchQuery;
        
        if (args.filters.type) {
          filteredQuery = filteredQuery.eq("type", args.filters.type);
        }
        if (args.filters.category) {
          filteredQuery = filteredQuery.eq("category", args.filters.category);
        }
        if (args.filters.status) {
          filteredQuery = filteredQuery.eq("status", args.filters.status);
        }
        if (args.filters.uploadedBy) {
          filteredQuery = filteredQuery.eq("uploadedBy", args.filters.uploadedBy);
        }
        
        return filteredQuery;
      })
      .take(args.limit || 20);

    // Filter by permissions and add download URLs
    const filesWithUrls = await Promise.all(
      results.map(async (file) => {
        const hasPermission = await checkFilePermission(ctx, file._id, identity.subject, "read");
        if (!hasPermission) return null;

        const downloadUrl = await ctx.storage.getUrl(file.storageId);
        return {
          ...file,
          downloadUrl,
        };
      })
    );

    return filesWithUrls.filter(Boolean);
  },
});

export const updateFile = mutation({
  args: {
    id: v.id("files"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    tags: v.optional(v.array(v.string())),
    category: v.optional(v.string()),
    accessLevel: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const hasPermission = await checkFilePermission(ctx, args.id, identity.subject, "write");
    if (!hasPermission) {
      throw new Error("Access denied");
    }

    const updateData: any = {
      lastModified: Date.now(),
    };

    if (args.name) updateData.name = args.name;
    if (args.description !== undefined) updateData.description = args.description;
    if (args.tags) updateData.tags = args.tags;
    if (args.category) updateData.category = args.category;
    if (args.accessLevel) updateData.accessLevel = args.accessLevel;

    await ctx.db.patch(args.id, updateData);
  },
});

export const deleteFile = mutation({
  args: { id: v.id("files") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const hasPermission = await checkFilePermission(ctx, args.id, identity.subject, "delete");
    if (!hasPermission) {
      throw new Error("Access denied");
    }

    const file = await ctx.db.get(args.id);
    if (!file) {
      throw new Error("File not found");
    }

    // Delete from storage
    await ctx.storage.delete(file.storageId);

    // Delete all related data
    const versions = await ctx.db
      .query("file_versions")
      .withIndex("by_file", (q) => q.eq("fileId", args.id))
      .collect();

    for (const version of versions) {
      await ctx.storage.delete(version.storageId);
      await ctx.db.delete(version._id);
    }

    // Delete permissions, shares, comments, analysis, etc.
    await Promise.all([
      ctx.db.query("file_permissions").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
      ctx.db.query("file_shares").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
      ctx.db.query("file_comments").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
      ctx.db.query("document_analysis").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
      ctx.db.query("document_requirements").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
      ctx.db.query("file_processing_jobs").withIndex("by_file", (q) => q.eq("fileId", args.id)).collect().then(items => Promise.all(items.map(item => ctx.db.delete(item._id)))),
    ]);

    // Finally delete the file record
    await ctx.db.delete(args.id);
  },
});

// File processing and analysis
export const scheduleProcessing = internalMutation({
  args: {
    fileId: v.id("files"),
    processingTypes: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    for (const type of args.processingTypes) {
      await ctx.db.insert("file_processing_jobs", {
        fileId: args.fileId,
        type,
        status: "pending",
        priority: "medium",
        queuedAt: Date.now(),
        progress: 0,
        retryCount: 0,
        maxRetries: 3,
        parameters: {},
        metadata: {},
      });
    }
  },
});

export const processFile = action({
  args: {
    fileId: v.id("files"),
    type: v.string(),
  },
  handler: async (ctx, args) => {
    const file = await ctx.runQuery(api.files.getFile, { id: args.fileId });
    if (!file) {
      throw new Error("File not found");
    }

    // Update job status
    const jobs = await ctx.runQuery(internal.files.getProcessingJobs, { fileId: args.fileId });
    const job = jobs.find((j: any) => j.type === args.type && j.status === "pending");
    if (!job) {
      throw new Error("Processing job not found");
    }

    await ctx.runMutation(internal.files.updateProcessingJob, {
      jobId: job._id,
      status: "in_progress",
      startedAt: Date.now(),
      progress: 0,
    });

    try {
      let results = {};

      switch (args.type) {
        case "virus_scan":
          results = await performVirusScan(ctx, file);
          break;
        case "thumbnail_generation":
          results = await generateThumbnail(ctx, file);
          break;
        case "text_extraction":
          results = await extractText(ctx, file);
          break;
        case "analysis":
          results = await analyzeDocument(ctx, file);
          break;
        default:
          throw new Error(`Unknown processing type: ${args.type}`);
      }

      await ctx.runMutation(internal.files.updateProcessingJob, {
        jobId: job._id,
        status: "completed",
        completedAt: Date.now(),
        progress: 100,
        results,
      });

    } catch (error) {
      await ctx.runMutation(internal.files.updateProcessingJob, {
        jobId: job._id,
        status: "failed",
        completedAt: Date.now(),
        error: error instanceof Error ? error.message : String(error),
        retryCount: job.retryCount + 1,
      });

      // Retry if under max retries
      if (job.retryCount < job.maxRetries) {
        await ctx.scheduler.runAfter(60000, internal.files.retryProcessing, { jobId: job._id });
      }
    }
  },
});

export const getProcessingJobs = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("file_processing_jobs")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .order("desc")
      .collect();
  },
});

export const updateProcessingJob = internalMutation({
  args: {
    jobId: v.id("file_processing_jobs"),
    status: v.optional(v.string()),
    startedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    progress: v.optional(v.number()),
    error: v.optional(v.string()),
    retryCount: v.optional(v.number()),
    results: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const updateData: any = {};
    
    if (args.status) updateData.status = args.status;
    if (args.startedAt) updateData.startedAt = args.startedAt;
    if (args.completedAt) updateData.completedAt = args.completedAt;
    if (args.progress !== undefined) updateData.progress = args.progress;
    if (args.error) updateData.error = args.error;
    if (args.retryCount !== undefined) updateData.retryCount = args.retryCount;
    if (args.results) updateData.results = args.results;

    await ctx.db.patch(args.jobId, updateData);
  },
});

export const retryProcessing = internalMutation({
  args: { jobId: v.id("file_processing_jobs") },
  handler: async (ctx, args) => {
    const job = await ctx.db.get(args.jobId);
    if (!job) return;

    await ctx.db.patch(args.jobId, {
      status: "pending",
      error: undefined,
      progress: 0,
    });

    // Reschedule processing
    await ctx.scheduler.runAfter(0, internal.files.processFile, {
      fileId: job.fileId,
      type: job.type,
    });
  },
});

// Helper functions
async function checkFilePermission(ctx: any, fileId: string, userId: string, permission: string): Promise<boolean> {
  const file = await ctx.db.get(fileId);
  if (!file) return false;

  // File owner has all permissions
  if (file.uploadedBy === userId) return true;

  // Check explicit permissions
  const filePermission = await ctx.db
    .query("file_permissions")
    .withIndex("by_file", (q: any) => q.eq("fileId", fileId))
    .filter((q: any) => q.eq("userId", userId))
    .first();

  if (filePermission && (filePermission.permission === permission || filePermission.permission === "admin")) {
    // Check if permission is expired
    if (filePermission.expiresAt && filePermission.expiresAt < Date.now()) {
      return false;
    }
    return true;
  }

  // Check access level
  if (file.accessLevel === "public") return true;
  if (file.accessLevel === "internal") return true; // All authenticated users

  return false;
}

async function performVirusScan(ctx: any, file: any) {
  // Placeholder for virus scanning logic
  // In a real implementation, this would integrate with a virus scanning service
  await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate processing time
  
  return {
    scanResult: "clean",
    scanTime: Date.now(),
    scanEngine: "placeholder",
    threats: [],
  };
}

async function generateThumbnail(ctx: any, file: any) {
  // Placeholder for thumbnail generation
  // In a real implementation, this would generate thumbnails for images and PDFs
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  return {
    thumbnailGenerated: true,
    thumbnailUrl: file.downloadUrl, // Placeholder
    dimensions: { width: 200, height: 200 },
  };
}

async function extractText(ctx: any, file: any) {
  // Placeholder for text extraction
  // In a real implementation, this would use OCR or PDF parsing
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  return {
    textExtracted: true,
    wordCount: 1500,
    language: "en",
    confidence: 0.95,
    textContent: "Extracted text content would go here...",
  };
}

// File sharing and collaboration
export const createFileShare = mutation({
  args: {
    fileId: v.id("files"),
    isPublic: v.boolean(),
    requiresAuth: v.boolean(),
    password: v.optional(v.string()),
    permissions: v.array(v.string()),
    expiresAt: v.optional(v.number()),
    maxDownloads: v.optional(v.number()),
    allowedDomains: v.optional(v.array(v.string())),
    ipWhitelist: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const shareToken = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);

    return await ctx.db.insert("file_shares", {
      fileId: args.fileId,
      shareToken,
      sharedBy: identity.subject,
      sharedWith: undefined,
      permissions: args.permissions,
      isPublic: args.isPublic,
      requiresAuth: args.requiresAuth,
      password: args.password,
      expiresAt: args.expiresAt,
      maxDownloads: args.maxDownloads,
      downloadCount: 0,
      isActive: true,
      createdAt: Date.now(),
      accessCount: 0,
      ipWhitelist: args.ipWhitelist,
      allowedDomains: args.allowedDomains,
    });
  },
});

export const getFileShares = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("file_shares")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();
  },
});

export const updateFileShare = mutation({
  args: {
    shareId: v.id("file_shares"),
    isActive: v.optional(v.boolean()),
    expiresAt: v.optional(v.number()),
    maxDownloads: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const updateData: any = {};
    if (args.isActive !== undefined) updateData.isActive = args.isActive;
    if (args.expiresAt !== undefined) updateData.expiresAt = args.expiresAt;
    if (args.maxDownloads !== undefined) updateData.maxDownloads = args.maxDownloads;

    await ctx.db.patch(args.shareId, updateData);
  },
});

export const deleteFileShare = mutation({
  args: { shareId: v.id("file_shares") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    await ctx.db.delete(args.shareId);
  },
});

// File permissions
export const addFilePermission = mutation({
  args: {
    fileId: v.id("files"),
    userId: v.string(),
    permission: v.string(),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.insert("file_permissions", {
      fileId: args.fileId,
      userId: args.userId,
      permission: args.permission,
      grantedBy: identity.subject,
      grantedAt: Date.now(),
      expiresAt: args.expiresAt,
    });
  },
});

export const getFilePermissions = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("file_permissions")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();
  },
});

export const updateFilePermission = mutation({
  args: {
    permissionId: v.id("file_permissions"),
    permission: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    await ctx.db.patch(args.permissionId, {
      permission: args.permission,
    });
  },
});

export const removeFilePermission = mutation({
  args: { permissionId: v.id("file_permissions") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    await ctx.db.delete(args.permissionId);
  },
});

// File comments
export const addFileComment = mutation({
  args: {
    fileId: v.id("files"),
    content: v.string(),
    type: v.string(),
    position: v.optional(v.object({
      page: v.number(),
      x: v.number(),
      y: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    parentId: v.optional(v.id("file_comments")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.insert("file_comments", {
      fileId: args.fileId,
      userId: identity.subject,
      content: args.content,
      type: args.type,
      position: args.position,
      parentId: args.parentId,
      isResolved: false,
      createdAt: Date.now(),
      reactions: [],
      metadata: {},
    });
  },
});

export const getFileComments = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("file_comments")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .order("desc")
      .collect();
  },
});

// File versions
export const getFileVersions = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("file_versions")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .order("desc")
      .collect();
  },
});

// Document analysis
export const getDocumentAnalysis = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("document_analysis")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .order("desc")
      .first();
  },
});

export const getDocumentRequirements = query({
  args: { fileId: v.id("files") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("document_requirements")
      .withIndex("by_file", (q) => q.eq("fileId", args.fileId))
      .collect();
  },
});

// File analytics
export const getFileAnalytics = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    period: v.object({
      start: v.number(),
      end: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    // This is a simplified implementation
    // In a real app, you'd aggregate data from multiple tables
    const baseQuery = ctx.db.query("files");
    
    const indexedQuery = args.tenderId
      ? baseQuery.withIndex("by_tender", (q) => q.eq("tenderId", args.tenderId))
      : args.folderId
      ? baseQuery.withIndex("by_folder", (q) => q.eq("folderId", args.folderId))
      : baseQuery;

    const files = await indexedQuery.collect();
    
    const periodFiles = files.filter(file => 
      file.uploadedAt >= args.period.start && file.uploadedAt <= args.period.end
    );

    const totalSize = periodFiles.reduce((sum, file) => sum + file.size, 0);
    const totalDownloads = periodFiles.reduce((sum, file) => sum + file.downloadCount, 0);
    const totalViews = periodFiles.reduce((sum, file) => sum + file.viewCount, 0);
    const uniqueUsers = new Set(periodFiles.map(file => file.uploadedBy)).size;

    // File type breakdown
    const typeBreakdown: Record<string, any> = {};
    periodFiles.forEach(file => {
      if (!typeBreakdown[file.type]) {
        typeBreakdown[file.type] = {
          count: 0,
          totalSize: 0,
          averageSize: 0,
          downloadCount: 0,
          viewCount: 0,
        };
      }
      typeBreakdown[file.type].count += 1;
      typeBreakdown[file.type].totalSize += file.size;
      typeBreakdown[file.type].downloadCount += file.downloadCount;
      typeBreakdown[file.type].viewCount += file.viewCount;
    });

    // Calculate averages
    Object.values(typeBreakdown).forEach((metrics: any) => {
      metrics.averageSize = metrics.count > 0 ? metrics.totalSize / metrics.count : 0;
    });

    // User breakdown
    const userBreakdown: Record<string, any> = {};
    periodFiles.forEach(file => {
      if (!userBreakdown[file.uploadedBy]) {
        userBreakdown[file.uploadedBy] = {
          uploadCount: 0,
          downloadCount: 0,
          totalSize: 0,
          lastActivity: 0,
        };
      }
      userBreakdown[file.uploadedBy].uploadCount += 1;
      userBreakdown[file.uploadedBy].downloadCount += file.downloadCount;
      userBreakdown[file.uploadedBy].totalSize += file.size;
      userBreakdown[file.uploadedBy].lastActivity = Math.max(
        userBreakdown[file.uploadedBy].lastActivity,
        file.lastModified
      );
    });

    return {
      period: args.period,
      metrics: {
        totalUploads: periodFiles.length,
        totalSize,
        uniqueUsers,
        downloadCount: totalDownloads,
        viewCount: totalViews,
        averageFileSize: periodFiles.length > 0 ? totalSize / periodFiles.length : 0,
        storageGrowth: 15.2, // Placeholder calculation
      },
      breakdown: {
        byType: typeBreakdown,
        byCategory: {}, // Implement category breakdown
        byUser: userBreakdown,
        byDay: {}, // Implement daily breakdown
      },
      insights: {
        popularFiles: periodFiles
          .sort((a, b) => b.downloadCount - a.downloadCount)
          .slice(0, 5)
          .map(f => f.name),
        largestFiles: periodFiles
          .sort((a, b) => b.size - a.size)
          .slice(0, 5)
          .map(f => f.name),
        mostActiveUsers: Object.keys(userBreakdown)
          .sort((a, b) => userBreakdown[b].uploadCount - userBreakdown[a].uploadCount)
          .slice(0, 5),
        storageHotspots: Object.keys(typeBreakdown)
          .sort((a, b) => typeBreakdown[b].totalSize - typeBreakdown[a].totalSize)
          .slice(0, 3),
      },
    };
  },
});

export const getTopFiles = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    period: v.object({
      start: v.number(),
      end: v.number(),
    }),
    metric: v.string(),
    limit: v.number(),
  },
  handler: async (ctx, args) => {
    const baseQuery = ctx.db.query("files");
    
    const indexedQuery = args.tenderId
      ? baseQuery.withIndex("by_tender", (q) => q.eq("tenderId", args.tenderId))
      : args.folderId
      ? baseQuery.withIndex("by_folder", (q) => q.eq("folderId", args.folderId))
      : baseQuery;

    const files = await indexedQuery.collect();
    
    const periodFiles = files.filter(file => 
      file.uploadedAt >= args.period.start && file.uploadedAt <= args.period.end
    );

    // Sort by the requested metric
    let sortedFiles = [...periodFiles];
    switch (args.metric) {
      case 'downloads':
        sortedFiles.sort((a, b) => b.downloadCount - a.downloadCount);
        break;
      case 'views':
        sortedFiles.sort((a, b) => b.viewCount - a.viewCount);
        break;
      case 'size':
        sortedFiles.sort((a, b) => b.size - a.size);
        break;
      case 'shares':
        // Would need to join with shares table in real implementation
        sortedFiles.sort((a, b) => b.downloadCount - a.downloadCount);
        break;
      default:
        sortedFiles.sort((a, b) => b.downloadCount - a.downloadCount);
    }

    return sortedFiles.slice(0, args.limit).map(file => ({
      ...file,
      shareCount: 0, // Placeholder - would calculate from shares table
    }));
  },
});

export const getUserActivity = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    period: v.object({
      start: v.number(),
      end: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    // Placeholder implementation
    // In a real app, you'd track user activities in a separate table
    return [];
  },
});

async function analyzeDocument(ctx: any, file: any) {
  // Placeholder for document analysis using AI
  // In a real implementation, this would use OpenAI or other AI services
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  return {
    analysisComplete: true,
    documentType: "tender_document",
    keyPhrases: ["requirement", "specification", "compliance"],
    requirements: [],
    sentiment: { overall: "neutral", score: 0.0 },
  };
}

// Internal mutation for creating files (used by document parser)
export const create = internalMutation({
  args: {
    name: v.string(),
    originalName: v.string(),
    type: v.string(),
    mimeType: v.string(),
    size: v.number(),
    storageId: v.id("_storage"),
    category: v.string(),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    const fileId = await ctx.db.insert("files", {
      name: args.name,
      originalName: args.originalName,
      type: args.type,
      mimeType: args.mimeType,
      size: args.size,
      status: "uploaded",
      category: args.category,
      accessLevel: "internal",
      storageId: args.storageId,
      storageKey: args.storageId, // Using storageId as key for simplicity
      checksum: "pending", // Will be calculated during processing
      uploadedBy: "system",
      uploadedAt: Date.now(),
      lastModified: Date.now(),
      downloadCount: 0,
      viewCount: 0,
      tags: [],
      isVersioned: false,
      virusStatus: "not_scanned",
      metadata: args.metadata || {},
    });

    // Schedule processing
    await ctx.scheduler.runAfter(0, internal.files.scheduleProcessing, {
      fileId,
      processingTypes: ["virus_scan", "text_extraction"],
    });

    return fileId;
  },
});