import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

export default defineSchema({
  ...authTables,
  tenders: defineTable({
    name: v.string(),
    clientName: v.string(),
    status: v.string(),
    dueDate: v.string(),
    estimatedValue: v.number(),
    contractDuration: v.optional(v.string()),
    estimatedScore: v.optional(v.number()),
    priority: v.optional(v.string()),
    projectLocation: v.optional(v.string()),
    totalWordCount: v.optional(v.number()),
    type: v.optional(v.string()),
  }),
  bidSections: defineTable({
    tenderId: v.id("tenders"),
    title: v.string(),
    content: v.string(),
    wordCount: v.number(),
    wordLimit: v.number(),
    scoreWeight: v.number(),
    status: v.string(),
    assignedAgent: v.optional(v.string()),
    lastModified: v.optional(v.number()),
    priority: v.optional(v.string()),
    requirements: v.optional(v.array(v.string())),
    type: v.optional(v.string()),
    version: v.optional(v.number()),
  }).index("by_tender", ["tenderId"]),
  
  // File Management Tables
  files: defineTable({
    name: v.string(),
    originalName: v.string(),
    type: v.string(),
    mimeType: v.string(),
    size: v.number(),
    status: v.string(),
    category: v.string(),
    accessLevel: v.string(),
    storageId: v.id("_storage"),
    storageKey: v.string(),
    checksum: v.string(),
    uploadedBy: v.string(),
    uploadedAt: v.number(),
    lastModified: v.number(),
    lastAccessed: v.optional(v.number()),
    downloadCount: v.number(),
    viewCount: v.number(),
    tags: v.array(v.string()),
    description: v.optional(v.string()),
    expiresAt: v.optional(v.number()),
    isVersioned: v.boolean(),
    parentId: v.optional(v.id("files")),
    tenderId: v.optional(v.id("tenders")),
    folderId: v.optional(v.id("file_folders")),
    thumbnailUrl: v.optional(v.string()),
    previewUrl: v.optional(v.string()),
    virusStatus: v.string(),
    metadata: v.object({
      width: v.optional(v.number()),
      height: v.optional(v.number()),
      duration: v.optional(v.number()),
      pages: v.optional(v.number()),
      encoding: v.optional(v.string()),
      language: v.optional(v.string()),
      version: v.optional(v.string()),
    }),
  })
    .index("by_tender", ["tenderId"])
    .index("by_folder", ["folderId"])
    .index("by_uploader", ["uploadedBy"])
    .index("by_status", ["status"])
    .index("by_category", ["category"])
    .index("by_type", ["type"])
    .searchIndex("search_files", {
      searchField: "name",
      filterFields: ["type", "category", "status", "uploadedBy", "tags"],
    }),

  file_versions: defineTable({
    fileId: v.id("files"),
    version: v.string(),
    storageId: v.id("_storage"),
    size: v.number(),
    checksum: v.string(),
    createdAt: v.number(),
    createdBy: v.string(),
    notes: v.optional(v.string()),
    isActive: v.boolean(),
  }).index("by_file", ["fileId"]),

  file_permissions: defineTable({
    fileId: v.id("files"),
    userId: v.string(),
    permission: v.string(),
    grantedBy: v.string(),
    grantedAt: v.number(),
    expiresAt: v.optional(v.number()),
  }).index("by_file", ["fileId"]).index("by_user", ["userId"]),

  file_folders: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    parentId: v.optional(v.id("file_folders")),
    path: v.string(),
    isShared: v.boolean(),
    createdBy: v.string(),
    createdAt: v.number(),
    lastModified: v.number(),
    fileCount: v.number(),
    totalSize: v.number(),
    tenderId: v.optional(v.id("tenders")),
  })
    .index("by_parent", ["parentId"])
    .index("by_tender", ["tenderId"])
    .index("by_creator", ["createdBy"]),

  file_shares: defineTable({
    fileId: v.id("files"),
    shareToken: v.string(),
    sharedBy: v.string(),
    sharedWith: v.optional(v.array(v.string())),
    permissions: v.array(v.string()),
    isPublic: v.boolean(),
    requiresAuth: v.boolean(),
    password: v.optional(v.string()),
    expiresAt: v.optional(v.number()),
    maxDownloads: v.optional(v.number()),
    downloadCount: v.number(),
    lastAccessed: v.optional(v.number()),
    isActive: v.boolean(),
    createdAt: v.number(),
    accessCount: v.number(),
    ipWhitelist: v.optional(v.array(v.string())),
    allowedDomains: v.optional(v.array(v.string())),
  })
    .index("by_file", ["fileId"])
    .index("by_token", ["shareToken"])
    .index("by_sharer", ["sharedBy"]),

  file_comments: defineTable({
    fileId: v.id("files"),
    userId: v.string(),
    content: v.string(),
    type: v.string(),
    position: v.optional(v.object({
      page: v.number(),
      x: v.number(),
      y: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    parentId: v.optional(v.id("file_comments")),
    isResolved: v.boolean(),
    resolvedBy: v.optional(v.string()),
    resolvedAt: v.optional(v.number()),
    createdAt: v.number(),
    editedAt: v.optional(v.number()),
    reactions: v.array(v.object({
      emoji: v.string(),
      count: v.number(),
      users: v.array(v.string()),
    })),
    metadata: v.object({
      priority: v.optional(v.string()),
      category: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
    }),
  })
    .index("by_file", ["fileId"])
    .index("by_user", ["userId"])
    .index("by_parent", ["parentId"]),

  document_analysis: defineTable({
    fileId: v.id("files"),
    status: v.string(),
    type: v.string(),
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    error: v.optional(v.string()),
    textContent: v.optional(v.string()),
    wordCount: v.optional(v.number()),
    characterCount: v.optional(v.number()),
    pageCount: v.optional(v.number()),
    language: v.optional(v.string()),
    confidence: v.optional(v.number()),
    keyPhrases: v.optional(v.array(v.string())),
    sentiment: v.optional(v.object({
      overall: v.string(),
      score: v.number(),
      confidence: v.number(),
    })),
    metadata: v.object({
      processingTime: v.optional(v.number()),
      model: v.optional(v.string()),
      version: v.optional(v.string()),
    }),
  })
    .index("by_file", ["fileId"])
    .index("by_status", ["status"])
    .index("by_type", ["type"]),

  document_requirements: defineTable({
    fileId: v.id("files"),
    analysisId: v.id("document_analysis"),
    text: v.string(),
    type: v.string(),
    priority: v.string(),
    section: v.optional(v.string()),
    page: v.number(),
    confidence: v.number(),
    relatedRequirements: v.array(v.id("document_requirements")),
    complianceStatus: v.optional(v.string()),
    wordCount: v.number(),
    keywords: v.array(v.string()),
    category: v.string(),
    complexity: v.string(),
    position: v.optional(v.object({
      page: v.number(),
      x: v.number(),
      y: v.number(),
      width: v.number(),
      height: v.number(),
    })),
    extractedAt: v.number(),
  })
    .index("by_file", ["fileId"])
    .index("by_analysis", ["analysisId"])
    .index("by_type", ["type"])
    .index("by_priority", ["priority"]),

  file_processing_jobs: defineTable({
    fileId: v.id("files"),
    type: v.string(),
    status: v.string(),
    priority: v.string(),
    queuedAt: v.number(),
    startedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    progress: v.number(),
    error: v.optional(v.string()),
    retryCount: v.number(),
    maxRetries: v.number(),
    parameters: v.object({}),
    results: v.optional(v.object({})),
    metadata: v.object({
      processingTime: v.optional(v.number()),
      cpuUsage: v.optional(v.number()),
      memoryUsage: v.optional(v.number()),
      estimatedCompletion: v.optional(v.number()),
    }),
  })
    .index("by_file", ["fileId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"]),

  // Chat Management Tables
  chat_threads: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    type: v.string(),
    status: v.string(),
    createdBy: v.string(),
    createdAt: v.number(),
    lastActivity: v.number(),
    lastMessageId: v.optional(v.id("chat_messages")),
    messageCount: v.number(),
    participantCount: v.number(),
    isPrivate: v.boolean(),
    allowFileUploads: v.boolean(),
    allowAgentAccess: v.boolean(),
    retentionDays: v.optional(v.number()),
    moderationLevel: v.string(),
    tenderId: v.optional(v.id("tenders")),
    sectionId: v.optional(v.id("bidSections")),
    projectId: v.optional(v.string()),
    tags: v.array(v.string()),
    metadata: v.object({
      unreadCount: v.number(),
      isNotificationEnabled: v.boolean(),
      pinnedMessageIds: v.array(v.id("chat_messages")),
    }),
  })
    .index("by_creator", ["createdBy"])
    .index("by_tender", ["tenderId"])
    .index("by_section", ["sectionId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .searchIndex("search_threads", {
      searchField: "name",
      filterFields: ["type", "status", "createdBy", "tags"],
    }),

  chat_messages: defineTable({
    threadId: v.id("chat_threads"),
    senderId: v.string(),
    senderType: v.string(),
    type: v.string(),
    content: v.string(),
    timestamp: v.number(),
    status: v.string(),
    editedAt: v.optional(v.number()),
    replyToId: v.optional(v.id("chat_messages")),
    mentions: v.array(v.string()),
    attachments: v.array(v.object({
      id: v.string(),
      type: v.string(),
      name: v.string(),
      url: v.string(),
      size: v.number(),
      mimeType: v.string(),
      thumbnail: v.optional(v.string()),
    })),
    reactions: v.array(v.object({
      emoji: v.string(),
      count: v.number(),
      users: v.array(v.string()),
    })),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      sectionId: v.optional(v.id("bidSections")),
      agentTaskId: v.optional(v.string()),
      workflowId: v.optional(v.string()),
      priority: v.optional(v.string()),
      tags: v.array(v.string()),
      context: v.optional(v.object({})),
    }),
    formatting: v.optional(v.object({
      bold: v.boolean(),
      italic: v.boolean(),
      code: v.boolean(),
      markdown: v.boolean(),
    })),
    ai: v.optional(v.object({
      generated: v.boolean(),
      model: v.optional(v.string()),
      confidence: v.optional(v.number()),
      tokens: v.optional(v.number()),
    })),
  })
    .index("by_thread", ["threadId"])
    .index("by_sender", ["senderId"])
    .index("by_type", ["type"])
    .index("by_timestamp", ["timestamp"])
    .searchIndex("search_messages", {
      searchField: "content",
      filterFields: ["threadId", "senderId", "type", "senderType"],
    }),

  chat_participants: defineTable({
    threadId: v.id("chat_threads"),
    userId: v.string(),
    type: v.string(),
    name: v.string(),
    avatar: v.optional(v.string()),
    role: v.string(),
    permissions: v.array(v.string()),
    joinedAt: v.number(),
    lastSeen: v.optional(v.number()),
    isOnline: v.boolean(),
    status: v.string(),
    messageCount: v.number(),
    unreadCount: v.number(),
    mutedUntil: v.optional(v.number()),
    notifications: v.boolean(),
    soundEnabled: v.boolean(),
    mentionOnly: v.boolean(),
  })
    .index("by_thread", ["threadId"])
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"]),

  // Agent Management Tables
  agents: defineTable({
    name: v.string(),
    description: v.string(),
    type: v.string(),
    status: v.string(),
    capabilities: v.array(v.string()),
    specializations: v.array(v.string()),
    model: v.string(),
    temperature: v.number(),
    maxTokens: v.number(),
    systemPrompt: v.string(),
    isActive: v.boolean(),
    createdAt: v.number(),
    lastActiveAt: v.optional(v.number()),
    version: v.string(),
    tasksCompleted: v.number(),
    averageQualityScore: v.number(),
    averageResponseTime: v.number(),
    successRate: v.number(),
    totalWords: v.number(),
    currentLoad: v.number(),
    maxConcurrentTasks: v.number(),
    autoAssign: v.boolean(),
    priority: v.string(),
    workingHours: v.optional(v.object({
      start: v.string(),
      end: v.string(),
      timezone: v.string(),
    })),
    maxWordCount: v.optional(v.number()),
    qualityThreshold: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_specialization", ["specializations"])
    .searchIndex("search_agents", {
      searchField: "name",
      filterFields: ["type", "status", "capabilities", "specializations"],
    }),

  agent_tasks: defineTable({
    agentId: v.id("agents"),
    type: v.string(),
    status: v.string(),
    priority: v.string(),
    tenderId: v.id("tenders"),
    sectionId: v.optional(v.id("bidSections")),
    assignedAt: v.number(),
    startedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    dueDate: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()),
    actualDuration: v.optional(v.number()),
    progress: v.number(),
    retryCount: v.number(),
    errorMessage: v.optional(v.string()),
    qualityScore: v.optional(v.number()),
    reviewNotes: v.optional(v.string()),
    inputTokens: v.optional(v.number()),
    outputTokens: v.optional(v.number()),
    totalTokens: v.optional(v.number()),
    input: v.object({
      content: v.optional(v.string()),
      instructions: v.string(),
      context: v.object({
        tenderName: v.string(),
        clientName: v.string(),
        sectionTitle: v.string(),
        wordLimit: v.optional(v.number()),
        scoreWeight: v.optional(v.number()),
        requirements: v.optional(v.array(v.string())),
        existingContent: v.optional(v.string()),
      }),
      parameters: v.optional(v.object({
        tone: v.optional(v.string()),
        style: v.optional(v.string()),
        focus: v.optional(v.array(v.string())),
        keywords: v.optional(v.array(v.string())),
      })),
    }),
    output: v.optional(v.object({
      content: v.string(),
      wordCount: v.number(),
      confidence: v.number(),
      suggestions: v.optional(v.array(v.string())),
      warnings: v.optional(v.array(v.string())),
      processingTime: v.number(),
      revisionsNeeded: v.optional(v.boolean()),
      clarity: v.number(),
      relevance: v.number(),
      completeness: v.number(),
      persuasiveness: v.number(),
    })),
  })
    .index("by_agent", ["agentId"])
    .index("by_tender", ["tenderId"])
    .index("by_section", ["sectionId"])
    .index("by_status", ["status"])
    .index("by_type", ["type"])
    .index("by_priority", ["priority"]),

  agent_performance: defineTable({
    agentId: v.id("agents"),
    periodStart: v.number(),
    periodEnd: v.number(),
    tasksCompleted: v.number(),
    averageQualityScore: v.number(),
    averageResponseTime: v.number(),
    successRate: v.number(),
    totalWords: v.number(),
    errorRate: v.number(),
    customerSatisfaction: v.optional(v.number()),
    breakdown: v.object({
      byTaskType: v.object({}),
      bySectionType: v.object({}),
      byPriority: v.object({}),
    }),
    generatedAt: v.number(),
  })
    .index("by_agent", ["agentId"])
    .index("by_period", ["periodStart", "periodEnd"]),

  chat_analytics: defineTable({
    threadId: v.optional(v.id("chat_threads")),
    periodStart: v.number(),
    periodEnd: v.number(),
    totalMessages: v.number(),
    totalThreads: v.number(),
    activeParticipants: v.number(),
    averageResponseTime: v.number(),
    messagesByType: v.object({}),
    messagesByParticipant: v.object({}),
    peakActivity: v.object({
      hour: v.number(),
      day: v.string(),
      count: v.number(),
    }),
    dailyMessages: v.array(v.number()),
    participantGrowth: v.array(v.number()),
    responseTimes: v.array(v.number()),
    mostActiveParticipants: v.array(v.string()),
    popularTopics: v.array(v.string()),
    averageThreadLifespan: v.number(),
    engagementScore: v.number(),
    generatedAt: v.number(),
  })
    .index("by_thread", ["threadId"])
    .index("by_period", ["periodStart", "periodEnd"]),

  chat_exports: defineTable({
    name: v.string(),
    description: v.string(),
    type: v.string(),
    format: v.string(),
    threadIds: v.optional(v.array(v.id("chat_threads"))),
    userIds: v.optional(v.array(v.string())),
    dateStart: v.optional(v.number()),
    dateEnd: v.optional(v.number()),
    includeAttachments: v.boolean(),
    includeDeleted: v.boolean(),
    status: v.string(),
    createdBy: v.string(),
    createdAt: v.number(),
    completedAt: v.optional(v.number()),
    downloadUrl: v.optional(v.string()),
    expiresAt: v.optional(v.number()),
    messageCount: v.number(),
    fileSize: v.number(),
    error: v.optional(v.string()),
  })
    .index("by_creator", ["createdBy"])
    .index("by_status", ["status"])
    .index("by_type", ["type"]),

  // Scheduler Tables
  scheduler_contacts: defineTable({
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    role: v.string(),
    department: v.string(),
    state: v.optional(v.string()),
    timezone: v.string(),
    notificationPreferences: v.object({
      email: v.boolean(),
      sms: v.boolean(),
      inApp: v.boolean(),
      preferredChannel: v.string(),
    }),
    availability: v.object({
      workingHours: v.object({
        start: v.string(),
        end: v.string(),
      }),
      daysOfWeek: v.array(v.number()),
      blockedTimes: v.array(v.object({
        start: v.string(),
        end: v.string(),
        reason: v.optional(v.string()),
      })),
    }),
    expertise: v.array(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    lastModified: v.number(),
    createdBy: v.string(),
  })
    .index("by_role", ["role"])
    .index("by_department", ["department"])
    .index("by_state", ["state"])
    .searchIndex("search_contacts", {
      searchField: "name",
      filterFields: ["role", "department", "state", "expertise"],
    }),

  scheduler_meetings: defineTable({
    title: v.string(),
    description: v.string(),
    type: v.string(),
    tenderId: v.optional(v.id("tenders")),
    projectContext: v.optional(v.object({
      projectName: v.string(),
      projectType: v.string(),
      priority: v.string(),
      requirements: v.array(v.string()),
    })),
    startTime: v.number(),
    endTime: v.number(),
    timezone: v.string(),
    location: v.object({
      type: v.string(),
      value: v.string(),
      details: v.optional(v.string()),
    }),
    participants: v.array(v.object({
      contactId: v.id("scheduler_contacts"),
      role: v.string(),
      isRequired: v.boolean(),
      responseStatus: v.string(),
    })),
    recurrence: v.optional(v.object({
      pattern: v.string(),
      interval: v.number(),
      endDate: v.optional(v.number()),
      exceptions: v.array(v.number()),
    })),
    reminders: v.array(v.object({
      type: v.string(),
      minutes: v.number(),
      channel: v.string(),
    })),
    resources: v.optional(v.array(v.object({
      type: v.string(),
      name: v.string(),
      quantity: v.number(),
      status: v.string(),
    }))),
    agenda: v.optional(v.array(v.object({
      item: v.string(),
      duration: v.number(),
      presenter: v.optional(v.id("scheduler_contacts")),
    }))),
    status: v.string(),
    organizer: v.string(),
    createdAt: v.number(),
    lastModified: v.number(),
    conflicts: v.array(v.object({
      contactId: v.id("scheduler_contacts"),
      conflictingMeetings: v.array(v.object({
        id: v.id("scheduler_meetings"),
        title: v.string(),
        start: v.number(),
        end: v.number(),
      })),
    })),
    meetingLinks: v.object({}),
    rsvpStats: v.object({
      accepted: v.number(),
      declined: v.number(),
      tentative: v.number(),
      pending: v.number(),
    }),
  })
    .index("by_tender", ["tenderId"])
    .index("by_organizer", ["organizer"])
    .index("by_status", ["status"])
    .index("by_start_time", ["startTime"])
    .index("by_type", ["type"]),

  scheduler_rsvps: defineTable({
    meetingId: v.id("scheduler_meetings"),
    contactId: v.id("scheduler_contacts"),
    response: v.string(),
    comment: v.optional(v.string()),
    proposedTime: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
    respondedAt: v.number(),
    remindersSent: v.number(),
    lastReminderAt: v.optional(v.number()),
  })
    .index("by_meeting", ["meetingId"])
    .index("by_contact", ["contactId"])
    .index("by_response", ["response"]),

  scheduler_resources: defineTable({
    name: v.string(),
    type: v.string(),
    capacity: v.number(),
    location: v.string(),
    features: v.array(v.string()),
    availability: v.object({
      workingHours: v.object({
        start: v.string(),
        end: v.string(),
      }),
      daysOfWeek: v.array(v.number()),
      blockedDates: v.array(v.object({
        date: v.number(),
        reason: v.string(),
      })),
    }),
    bookingRules: v.object({
      minDuration: v.number(),
      maxDuration: v.number(),
      advanceBooking: v.number(),
      requiresApproval: v.boolean(),
      approvers: v.array(v.string()),
    }),
    isActive: v.boolean(),
    createdAt: v.number(),
    lastModified: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_location", ["location"]),

  scheduler_bookings: defineTable({
    resourceId: v.id("scheduler_resources"),
    meetingId: v.id("scheduler_meetings"),
    startTime: v.number(),
    endTime: v.number(),
    status: v.string(),
    bookedBy: v.string(),
    bookedAt: v.number(),
    approvedBy: v.optional(v.string()),
    approvedAt: v.optional(v.number()),
    notes: v.optional(v.string()),
  })
    .index("by_resource", ["resourceId"])
    .index("by_meeting", ["meetingId"])
    .index("by_status", ["status"])
    .index("by_time", ["startTime", "endTime"]),

  scheduler_templates: defineTable({
    name: v.string(),
    description: v.string(),
    type: v.string(),
    isActive: v.boolean(),
    settings: v.object({
      duration: v.number(),
      location: v.object({
        type: v.string(),
        value: v.string(),
      }),
      participantRoles: v.array(v.object({
        role: v.string(),
        isRequired: v.boolean(),
        department: v.optional(v.string()),
        expertise: v.optional(v.array(v.string())),
      })),
      reminders: v.array(v.object({
        type: v.string(),
        minutes: v.number(),
        channel: v.string(),
      })),
      agenda: v.optional(v.array(v.object({
        item: v.string(),
        duration: v.number(),
      }))),
    }),
    createdBy: v.string(),
    createdAt: v.number(),
    lastUsed: v.optional(v.number()),
    useCount: v.number(),
  })
    .index("by_type", ["type"])
    .index("by_creator", ["createdBy"]),

  scheduler_analytics: defineTable({
    periodStart: v.number(),
    periodEnd: v.number(),
    metrics: v.object({
      totalMeetings: v.number(),
      averageAttendance: v.number(),
      rsvpResponseRate: v.number(),
      averageLeadTime: v.number(),
      conflictRate: v.number(),
      cancellationRate: v.number(),
      platformUsage: v.object({}),
      departmentBreakdown: v.object({}),
      meetingTypeBreakdown: v.object({}),
      peakTimes: v.array(v.object({
        hour: v.number(),
        day: v.string(),
        count: v.number(),
      })),
      topOrganizers: v.array(v.object({
        userId: v.string(),
        meetingCount: v.number(),
      })),
      resourceUtilization: v.object({}),
    }),
    generatedAt: v.number(),
  })
    .index("by_period", ["periodStart", "periodEnd"]),

  // Meeting Processing Tables
  meeting_transcripts: defineTable({
    meetingId: v.id("scheduler_meetings"),
    transcript: v.object({
      rawText: v.string(),
      speakers: v.array(v.object({
        id: v.string(),
        name: v.string(),
        role: v.optional(v.string()),
      })),
      segments: v.array(v.object({
        speakerId: v.string(),
        text: v.string(),
        startTime: v.number(),
        endTime: v.number(),
        confidence: v.optional(v.number()),
      })),
      duration: v.number(),
      language: v.string(),
    }),
    status: v.string(),
    processedAt: v.number(),
    wordCount: v.number(),
    speakerCount: v.number(),
    duration: v.number(),
    language: v.string(),
    summaryTaskId: v.optional(v.id("agent_tasks")),
    lastUpdated: v.optional(v.number()),
    analyzedAt: v.optional(v.number()),
    analysisResults: v.optional(v.object({
      keyTopics: v.array(v.string()),
      decisions: v.array(v.any()),
      actionItems: v.array(v.any()),
      risks: v.array(v.any()),
      participantMetrics: v.any(),
      sentimentAnalysis: v.any(),
      engagementScore: v.number(),
    })),
  })
    .index("by_meeting", ["meetingId"])
    .index("by_status", ["status"]),

  meeting_action_items: defineTable({
    meetingId: v.id("scheduler_meetings"),
    tenderId: v.optional(v.id("tenders")),
    description: v.string(),
    assignee: v.string(),
    createdBy: v.string(),
    priority: v.string(),
    category: v.string(),
    status: v.string(),
    createdAt: v.number(),
    dueDate: v.optional(v.number()),
    source: v.object({
      type: v.string(),
      timestamp: v.number(),
      originalText: v.string(),
    }),
    tags: v.array(v.string()),
    notificationsSent: v.number(),
    lastNotificationAt: v.optional(v.number()),
  })
    .index("by_meeting", ["meetingId"])
    .index("by_assignee", ["assignee"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"]),

  meeting_tasks: defineTable({
    actionItemId: v.id("meeting_action_items"),
    meetingId: v.id("scheduler_meetings"),
    tenderId: v.optional(v.id("tenders")),
    title: v.string(),
    description: v.string(),
    assignee: v.string(),
    assigneeContactId: v.optional(v.id("scheduler_contacts")),
    priority: v.string(),
    category: v.string(),
    status: v.string(),
    progress: v.number(),
    createdAt: v.number(),
    createdBy: v.string(),
    lastUpdated: v.optional(v.number()),
    lastUpdatedBy: v.optional(v.string()),
    dueDate: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    smartGoal: v.object({
      title: v.string(),
      description: v.string(),
      specific: v.string(),
      measurable: v.array(v.string()),
      achievable: v.object({
        resources: v.array(v.string()),
        skills: v.array(v.string()),
        dependencies: v.array(v.string()),
      }),
      relevant: v.object({
        businessValue: v.string(),
        alignment: v.string(),
      }),
      timeBound: v.object({
        dueDate: v.optional(v.number()),
        milestones: v.array(v.object({
          name: v.string(),
          dueDate: v.number(),
          status: v.string(),
        })),
      }),
      estimatedHours: v.number(),
    }),
    tags: v.array(v.string()),
    linkedDocuments: v.array(v.id("files")),
    notifications: v.object({
      frequency: v.string(),
      lastSent: v.optional(v.number()),
      nextScheduled: v.optional(v.number()),
      escalationLevel: v.number(),
    }),
    metrics: v.object({
      estimatedHours: v.number(),
      actualHours: v.number(),
      blockers: v.array(v.object({
        description: v.string(),
        severity: v.string(),
        createdAt: v.number(),
      })),
      dependencies: v.array(v.string()),
    }),
    externalIds: v.optional(v.object({})),
    lastSynced: v.optional(v.number()),
  })
    .index("by_action_item", ["actionItemId"])
    .index("by_meeting", ["meetingId"])
    .index("by_assignee", ["assignee"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"])
    .index("by_due_date", ["dueDate"]),

  task_notifications: defineTable({
    taskId: v.id("meeting_tasks"),
    type: v.string(),
    subject: v.string(),
    content: v.string(),
    channels: v.array(v.string()),
    status: v.string(),
    createdAt: v.number(),
    channelStatus: v.object({}),
  })
    .index("by_task", ["taskId"])
    .index("by_type", ["type"])
    .index("by_status", ["status"]),

  in_app_notifications: defineTable({
    userId: v.string(),
    taskId: v.id("meeting_tasks"),
    subject: v.string(),
    content: v.string(),
    priority: v.string(),
    read: v.boolean(),
    createdAt: v.number(),
    readAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_task", ["taskId"])
    .index("by_read", ["read"])
    .index("by_priority", ["priority"]),

  task_progress_notes: defineTable({
    taskId: v.id("meeting_tasks"),
    note: v.string(),
    progress: v.number(),
    createdBy: v.string(),
    createdAt: v.number(),
  })
    .index("by_task", ["taskId"])
    .index("by_creator", ["createdBy"]),

  task_escalations: defineTable({
    taskId: v.id("meeting_tasks"),
    reason: v.string(),
    escalatedTo: v.array(v.any()),
    level: v.number(),
    createdAt: v.number(),
    resolved: v.boolean(),
    resolvedAt: v.optional(v.number()),
    resolvedBy: v.optional(v.string()),
    resolutionNotes: v.optional(v.string()),
  })
    .index("by_task", ["taskId"])
    .index("by_resolved", ["resolved"])
    .index("by_level", ["level"]),

  // Platform Integration Tables
  platform_integrations: defineTable({
    platform: v.string(),
    credentials: v.object({
      apiKey: v.optional(v.string()),
      apiSecret: v.optional(v.string()),
      accessToken: v.optional(v.string()),
      refreshToken: v.optional(v.string()),
      webhookUrl: v.optional(v.string()),
      workspaceId: v.optional(v.string()),
      organizationId: v.optional(v.string()),
    }),
    settings: v.object({
      syncEnabled: v.boolean(),
      autoCreateTasks: v.boolean(),
      defaultProject: v.optional(v.string()),
      defaultList: v.optional(v.string()),
      notificationChannels: v.array(v.string()),
      syncFrequency: v.string(),
      taskMapping: v.object({
        priorityField: v.optional(v.string()),
        dueDateField: v.optional(v.string()),
        assigneeField: v.optional(v.string()),
        customFields: v.optional(v.object({})),
      }),
    }),
    status: v.string(),
    createdAt: v.number(),
    createdBy: v.string(),
    lastUpdated: v.number(),
    updatedBy: v.string(),
    lastSync: v.optional(v.number()),
    syncErrors: v.array(v.string()),
    syncStats: v.optional(v.object({
      totalSuccess: v.number(),
      totalErrors: v.number(),
      lastSyncSuccess: v.number(),
      lastSyncErrors: v.number(),
    })),
  })
    .index("by_platform", ["platform"])
    .index("by_status", ["status"]),

  sync_logs: defineTable({
    integrationId: v.id("platform_integrations"),
    taskId: v.id("meeting_tasks"),
    operation: v.string(),
    status: v.string(),
    externalId: v.optional(v.string()),
    error: v.optional(v.string()),
    timestamp: v.number(),
  })
    .index("by_integration", ["integrationId"])
    .index("by_task", ["taskId"])
    .index("by_status", ["status"])
    .index("by_timestamp", ["timestamp"]),

  notification_logs: defineTable({
    platform: v.string(),
    recipients: v.array(v.string()),
    subject: v.string(),
    taskId: v.optional(v.id("meeting_tasks")),
    status: v.string(),
    error: v.optional(v.string()),
    timestamp: v.number(),
  })
    .index("by_platform", ["platform"])
    .index("by_task", ["taskId"])
    .index("by_status", ["status"])
    .index("by_timestamp", ["timestamp"]),

  // Workflow Orchestration Tables
  workflow_executions: defineTable({
    workflowId: v.string(),
    executionId: v.string(),
    templateId: v.string(),
    tenderId: v.id("tenders"),
    userId: v.string(),
    status: v.string(),
    context: v.object({
      userId: v.string(),
      priority: v.string(),
      deadline: v.optional(v.number()),
      metadata: v.object({
        clientName: v.string(),
        tenderName: v.string(),
        submissionDeadline: v.optional(v.number()),
        estimatedValue: v.optional(v.number()),
        tags: v.array(v.string()),
      }),
    }),
    variables: v.object({}),
    startTime: v.number(),
    endTime: v.optional(v.number()),
    lastUpdateTime: v.optional(v.number()),
    currentStepId: v.optional(v.string()),
    completedSteps: v.array(v.string()),
    failedSteps: v.array(v.string()),
    results: v.object({}),
    errors: v.array(v.object({
      id: v.string(),
      code: v.string(),
      message: v.string(),
      timestamp: v.number(),
      severity: v.string(),
    })),
    metrics: v.object({
      stepsCompleted: v.number(),
      stepsFailed: v.number(),
      stepsSkipped: v.number(),
      totalDuration: v.number(),
      agentUtilization: v.object({}),
      resourceUsage: v.object({
        tokensUsed: v.number(),
        apiCalls: v.number(),
        storageUsed: v.number(),
      }),
      qualityScores: v.object({}),
    }),
  })
    .index("by_tender", ["tenderId"])
    .index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_template", ["templateId"]),

  workflow_checkpoints: defineTable({
    executionId: v.id("workflow_executions"),
    stepId: v.string(),
    description: v.string(),
    state: v.object({
      variables: v.object({}),
      results: v.object({}),
      completedSteps: v.array(v.string()),
    }),
    createdAt: v.number(),
    createdBy: v.string(),
  })
    .index("by_execution", ["executionId"])
    .index("by_step", ["stepId"]),

  workflow_events: defineTable({
    type: v.string(),
    workflowId: v.string(),
    executionId: v.string(),
    timestamp: v.number(),
    data: v.object({}),
    source: v.string(),
  })
    .index("by_workflow", ["workflowId"])
    .index("by_execution", ["executionId"])
    .index("by_type", ["type"])
    .index("by_timestamp", ["timestamp"]),

  workflow_health: defineTable({
    workflowId: v.string(),
    status: v.string(),
    metrics: v.object({
      successRate: v.number(),
      averageExecutionTime: v.number(),
      errorRate: v.number(),
      throughput: v.number(),
    }),
    issues: v.array(v.object({
      type: v.string(),
      severity: v.string(),
      message: v.string(),
      affectedComponents: v.array(v.string()),
      recommendation: v.optional(v.string()),
    })),
    lastCheck: v.number(),
  })
    .index("by_workflow", ["workflowId"])
    .index("by_status", ["status"]),

  // Content Builder Tables
  content_requests: defineTable({
    tenderId: v.id("tenders"),
    contentType: v.string(),
    theme: v.string(),
    title: v.string(),
    description: v.string(),
    targetAudience: v.string(),
    meetingType: v.string(),
    duration: v.number(),
    parameters: v.object({
      includeVoiceScript: v.optional(v.boolean()),
      includeBranding: v.optional(v.boolean()),
      dataPoints: v.optional(v.array(v.object({
        label: v.string(),
        value: v.union(v.string(), v.number()),
        type: v.optional(v.string()),
      }))),
      siteLocations: v.optional(v.array(v.object({
        name: v.string(),
        address: v.string(),
        latitude: v.number(),
        longitude: v.number(),
        status: v.optional(v.string()),
      }))),
      complianceItems: v.optional(v.array(v.object({
        category: v.string(),
        requirement: v.string(),
        status: v.string(),
        priority: v.string(),
        dueDate: v.optional(v.number()),
      }))),
      timelineEvents: v.optional(v.array(v.object({
        date: v.number(),
        title: v.string(),
        description: v.string(),
        milestone: v.boolean(),
      }))),
      teamMembers: v.optional(v.array(v.object({
        name: v.string(),
        role: v.string(),
        bio: v.string(),
        imageUrl: v.optional(v.string()),
      }))),
      qaTopics: v.optional(v.array(v.object({
        question: v.string(),
        answer: v.string(),
        category: v.string(),
        difficulty: v.string(),
      }))),
    }),
    status: v.string(),
    createdBy: v.string(),
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    contentId: v.optional(v.id("generated_content")),
    progress: v.optional(v.number()),
    error: v.optional(v.string()),
  })
    .index("by_tender", ["tenderId"])
    .index("by_status", ["status"])
    .index("by_type", ["contentType"])
    .index("by_creator", ["createdBy"]),

  generated_content: defineTable({
    requestId: v.id("content_requests"),
    content: v.any(),
    generatedAt: v.number(),
    version: v.number(),
  })
    .index("by_request", ["requestId"]),

  content_exports: defineTable({
    contentId: v.id("generated_content"),
    format: v.string(),
    fileUrl: v.string(),
    fileName: v.string(),
    fileSize: v.number(),
    exportedAt: v.number(),
  })
    .index("by_content", ["contentId"])
    .index("by_format", ["format"]),

  content_quality_assessments: defineTable({
    contentId: v.id("generated_content"),
    assessment: v.any(),
    assessedAt: v.number(),
  })
    .index("by_content", ["contentId"]),

  // Slidev presentations
  slidev_presentations: defineTable({
    tenderId: v.id("tenders"),
    slidesPath: v.string(), // Path to generated slides.md file
    exports: v.any(), // Object with export format paths {pdf: "/path/to.pdf", pptx: "/path/to.pptx"}
    voiceScript: v.optional(v.string()), // Generated voice script
    presentationUrl: v.optional(v.string()), // Live presentation server URL
    status: v.string(), // "generating", "ready", "failed", "archived"
    error: v.optional(v.string()), // Error message if generation failed
    createdAt: v.number(),
    updatedAt: v.optional(v.number()),
  })
    .index("by_tender", ["tenderId"])
    .index("by_status", ["status"]),
} as any);