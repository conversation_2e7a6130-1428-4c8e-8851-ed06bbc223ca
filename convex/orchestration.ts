/**
 * Convex Orchestration Integration
 * 
 * Integrates the LangGraph orchestration engine with Convex backend
 */

import { v } from "convex/values";
import { mutation, query, action, internalMutation, internalQuery } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Workflow execution tracking
export const createWorkflowExecution = mutation({
  args: {
    workflowId: v.string(),
    executionId: v.string(),
    templateId: v.string(),
    tenderId: v.id("tenders"),
    context: v.object({
      userId: v.string(),
      priority: v.string(),
      deadline: v.optional(v.number()),
      metadata: v.object({
        clientName: v.string(),
        tenderName: v.string(),
        submissionDeadline: v.optional(v.number()),
        estimatedValue: v.optional(v.number()),
        tags: v.array(v.string()),
      }),
    }),
    variables: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const executionId = await ctx.db.insert("workflow_executions", {
      workflowId: args.workflowId,
      executionId: args.executionId,
      templateId: args.templateId,
      tenderId: args.tenderId,
      userId: identity.subject,
      status: "active",
      context: args.context,
      variables: args.variables || {},
      startTime: Date.now(),
      completedSteps: [],
      failedSteps: [],
      results: {},
      errors: [],
      metrics: {
        stepsCompleted: 0,
        stepsFailed: 0,
        stepsSkipped: 0,
        totalDuration: 0,
        agentUtilization: {},
        resourceUsage: {
          tokensUsed: 0,
          apiCalls: 0,
          storageUsed: 0,
        },
        qualityScores: {},
      },
    });

    return executionId;
  },
});

export const updateWorkflowExecution = mutation({
  args: {
    executionId: v.id("workflow_executions"),
    updates: v.object({
      status: v.optional(v.string()),
      currentStepId: v.optional(v.string()),
      completedSteps: v.optional(v.array(v.string())),
      failedSteps: v.optional(v.array(v.string())),
      results: v.optional(v.object({})),
      errors: v.optional(v.array(v.object({
        id: v.string(),
        code: v.string(),
        message: v.string(),
        timestamp: v.number(),
        severity: v.string(),
      }))),
      metrics: v.optional(v.object({
        stepsCompleted: v.number(),
        stepsFailed: v.number(),
        stepsSkipped: v.number(),
        totalDuration: v.number(),
        agentUtilization: v.object({}),
        resourceUsage: v.object({
          tokensUsed: v.number(),
          apiCalls: v.number(),
          storageUsed: v.number(),
        }),
        qualityScores: v.object({}),
      })),
      endTime: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const { executionId, updates } = args;
    await ctx.db.patch(executionId, {
      ...updates,
      lastUpdateTime: Date.now(),
    });

    return executionId;
  },
});

export const getWorkflowExecution = query({
  args: { executionId: v.id("workflow_executions") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const execution = await ctx.db.get(args.executionId);
    if (!execution) {
      throw new Error("Workflow execution not found");
    }

    // Get related tender info
    const tender = await ctx.db.get(execution.tenderId);

    return {
      ...execution,
      tenderName: tender?.name,
    };
  },
});

export const listWorkflowExecutions = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("workflow_executions");

    const tenderFilteredQuery = args.tenderId
      ? baseQuery.filter(q => q.eq(q.field("tenderId"), args.tenderId))
      : baseQuery;

    const statusFilteredQuery = args.status
      ? tenderFilteredQuery.filter(q => q.eq(q.field("status"), args.status))
      : tenderFilteredQuery;

    const executions = await statusFilteredQuery
      .order("desc")
      .take(args.limit || 50);

    // Get tender names
    const executionsWithTenders = await Promise.all(
      executions.map(async (execution) => {
        const tender = await ctx.db.get(execution.tenderId);
        return {
          ...execution,
          tenderName: tender?.name,
        };
      })
    );

    return executionsWithTenders;
  },
});

// Workflow checkpoints
export const createWorkflowCheckpoint = mutation({
  args: {
    executionId: v.id("workflow_executions"),
    stepId: v.string(),
    description: v.string(),
    state: v.object({
      variables: v.object({}),
      results: v.object({}),
      completedSteps: v.array(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const checkpointId = await ctx.db.insert("workflow_checkpoints", {
      executionId: args.executionId,
      stepId: args.stepId,
      description: args.description,
      state: args.state,
      createdAt: Date.now(),
      createdBy: identity.subject,
    });

    return checkpointId;
  },
});

export const getWorkflowCheckpoint = query({
  args: { checkpointId: v.id("workflow_checkpoints") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.get(args.checkpointId);
  },
});

// Workflow events
export const recordWorkflowEvent = internalMutation({
  args: {
    type: v.string(),
    workflowId: v.string(),
    executionId: v.string(),
    data: v.object({}),
    source: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("workflow_events", {
      type: args.type,
      workflowId: args.workflowId,
      executionId: args.executionId,
      timestamp: Date.now(),
      data: args.data,
      source: args.source,
    });
  },
});

export const getWorkflowEvents = query({
  args: {
    executionId: v.optional(v.string()),
    type: v.optional(v.string()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("workflow_events");

    const executionFilteredQuery = args.executionId
      ? baseQuery.filter(q => q.eq(q.field("executionId"), args.executionId))
      : baseQuery;

    const typeFilteredQuery = args.type
      ? executionFilteredQuery.filter(q => q.eq(q.field("type"), args.type))
      : executionFilteredQuery;

    const startTimeFilteredQuery = args.startTime
      ? typeFilteredQuery.filter(q => q.gte(q.field("timestamp"), args.startTime))
      : typeFilteredQuery;

    const endTimeFilteredQuery = args.endTime
      ? startTimeFilteredQuery.filter(q => q.lte(q.field("timestamp"), args.endTime))
      : startTimeFilteredQuery;

    return await endTimeFilteredQuery
      .order("desc")
      .take(args.limit || 100);
  },
});

// Workflow health and monitoring
export const updateWorkflowHealth = internalMutation({
  args: {
    workflowId: v.string(),
    status: v.string(),
    metrics: v.object({
      successRate: v.number(),
      averageExecutionTime: v.number(),
      errorRate: v.number(),
      throughput: v.number(),
    }),
    issues: v.array(v.object({
      type: v.string(),
      severity: v.string(),
      message: v.string(),
      affectedComponents: v.array(v.string()),
      recommendation: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const existing = await ctx.db
      .query("workflow_health")
      .filter(q => q.eq(q.field("workflowId"), args.workflowId))
      .first();

    if (existing) {
      await ctx.db.patch(existing._id, {
        status: args.status,
        metrics: args.metrics,
        issues: args.issues,
        lastCheck: Date.now(),
      });
    } else {
      await ctx.db.insert("workflow_health", {
        workflowId: args.workflowId,
        status: args.status,
        metrics: args.metrics,
        issues: args.issues,
        lastCheck: Date.now(),
      });
    }
  },
});

export const getWorkflowHealth = query({
  args: { workflowId: v.string() },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("workflow_health")
      .filter(q => q.eq(q.field("workflowId"), args.workflowId))
      .first();
  },
});

// Orchestration actions (bridge to LangGraph engine)
export const startWorkflow = action({
  args: {
    templateId: v.string(),
    tenderId: v.id("tenders"),
    variables: v.optional(v.object({})),
  },
  handler: async (ctx, args) => {
    // Get authenticated user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get tender details
    const tender = await ctx.runQuery(api.tenders.getTender, { tenderId: args.tenderId });
    if (!tender) {
      throw new Error("Tender not found");
    }

    // Get available agents
    const agents = await ctx.runQuery(api.agents.getAvailableAgents, {});

    // Create execution record
    const executionId = `exec-${Date.now()}-${Math.random()}`;
    const workflowId = tender._id;

    await ctx.runMutation(api.orchestration.createWorkflowExecution, {
      workflowId,
      executionId,
      templateId: args.templateId,
      tenderId: args.tenderId,
      context: {
        userId: identity.subject,
        priority: tender.priority,
        deadline: tender.deadline,
        metadata: {
          clientName: tender.clientName,
          tenderName: tender.name,
          submissionDeadline: tender.deadline,
          estimatedValue: tender.estimatedValue,
          tags: tender.tags || [],
        },
      },
      variables: args.variables,
    });

    // In a real implementation, this would call the actual orchestrator
    // For now, we'll simulate the workflow execution
    await ctx.scheduler.runAfter(0, api.orchestration.simulateWorkflowExecution, {
      executionId,
      templateId: args.templateId,
    });

    return { executionId, status: "started" };
  },
});

// Simulate workflow execution (placeholder for actual LangGraph integration)
export const simulateWorkflowExecution = action({
  args: {
    executionId: v.string(),
    templateId: v.string(),
  },
  handler: async (ctx, args) => {
    // This is a placeholder that simulates workflow execution
    // In production, this would integrate with the actual LangGraph engine
    
    const steps = [
      "ingestion",
      "analysis",
      "scheduling",
      "content_generation",
      "review",
      "meeting_execution",
      "post_processing",
      "finalization",
    ];

    // Simulate step execution
    for (const step of steps) {
      // Record step started
      await ctx.runMutation(api.orchestration.recordWorkflowEvent, {
        type: "step.started",
        workflowId: args.executionId,
        executionId: args.executionId,
        data: { stepId: step },
        source: "simulator",
      });

      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Record step completed
      await ctx.runMutation(api.orchestration.recordWorkflowEvent, {
        type: "step.completed",
        workflowId: args.executionId,
        executionId: args.executionId,
        data: { stepId: step, duration: 1000 },
        source: "simulator",
      });

      // Update execution
      const execution = await ctx.runQuery(api.orchestration.getWorkflowExecution, {
        executionId: args.executionId as Id<"workflow_executions">,
      });

      if (execution) {
        await ctx.runMutation(api.orchestration.updateWorkflowExecution, {
          executionId: execution._id,
          updates: {
            currentStepId: step,
            completedSteps: [...execution.completedSteps, step],
            metrics: {
              ...execution.metrics,
              stepsCompleted: execution.metrics.stepsCompleted + 1,
              totalDuration: Date.now() - execution.startTime,
            },
          },
        });
      }
    }

    // Mark workflow as completed
    const finalExecution = await ctx.runQuery(api.orchestration.getWorkflowExecution, {
      executionId: args.executionId as Id<"workflow_executions">,
    });

    if (finalExecution) {
      await ctx.runMutation(api.orchestration.updateWorkflowExecution, {
        executionId: finalExecution._id,
        updates: {
          status: "completed",
          endTime: Date.now(),
          results: {
            summary: "Workflow completed successfully",
            documentsGenerated: 5,
            qualityScore: 0.92,
          },
        },
      });
    }

    // Record completion event
    await ctx.runMutation(api.orchestration.recordWorkflowEvent, {
      type: "workflow.completed",
      workflowId: args.executionId,
      executionId: args.executionId,
      data: { duration: 10000 },
      source: "simulator",
    });
  },
});

// Get workflow metrics
export const getWorkflowMetrics = query({
  args: {
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("workflow_executions");

    const startTimeFilteredQuery = args.startTime
      ? baseQuery.filter(q => q.gte(q.field("startTime"), args.startTime))
      : baseQuery;

    const endTimeFilteredQuery = args.endTime
      ? startTimeFilteredQuery.filter(q => q.lte(q.field("startTime"), args.endTime))
      : startTimeFilteredQuery;

    const executions = await endTimeFilteredQuery.collect();

    // Calculate aggregate metrics
    const totalExecutions = executions.length;
    const completedExecutions = executions.filter(e => e.status === "completed").length;
    const failedExecutions = executions.filter(e => e.status === "failed").length;
    const activeExecutions = executions.filter(e => e.status === "active").length;

    const avgDuration = executions
      .filter(e => e.endTime)
      .reduce((sum, e) => sum + (e.endTime! - e.startTime), 0) / completedExecutions || 0;

    const avgStepsCompleted = executions
      .reduce((sum, e) => sum + e.metrics.stepsCompleted, 0) / totalExecutions || 0;

    const totalTokensUsed = executions
      .reduce((sum, e) => sum + e.metrics.resourceUsage.tokensUsed, 0);

    return {
      totalExecutions,
      completedExecutions,
      failedExecutions,
      activeExecutions,
      successRate: totalExecutions > 0 ? (completedExecutions / totalExecutions) * 100 : 0,
      averageDuration: avgDuration,
      averageStepsCompleted: avgStepsCompleted,
      totalTokensUsed,
    };
  },
});