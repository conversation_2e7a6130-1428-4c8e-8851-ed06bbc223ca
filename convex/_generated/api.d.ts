/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as agentDocumentParser from "../agentDocumentParser.js";
import type * as agents_contentBuilder from "../agents/contentBuilder.js";
import type * as agents from "../agents.js";
import type * as ai from "../ai.js";
import type * as auth from "../auth.js";
import type * as bidSections from "../bidSections.js";
import type * as chat from "../chat.js";
import type * as documentParser from "../documentParser.js";
import type * as emailListener from "../emailListener.js";
import type * as files from "../files.js";
import type * as http from "../http.js";
import type * as meetingIntegrations from "../meetingIntegrations.js";
import type * as meetingSummarizer from "../meetingSummarizer.js";
import type * as meetingTaskManager from "../meetingTaskManager.js";
import type * as orchestration from "../orchestration.js";
import type * as router from "../router.js";
import type * as scheduler from "../scheduler.js";
import type * as schedulerAgent from "../schedulerAgent.js";
import type * as slidevIntegration from "../slidevIntegration.js";
import type * as tenderProcessing from "../tenderProcessing.js";
import type * as tenders from "../tenders.js";
import type * as zeroTouch from "../zeroTouch.js";
import type * as zeroTouchSchema from "../zeroTouchSchema.js";
import type * as zeroTouchWorkflow from "../zeroTouchWorkflow.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  agentDocumentParser: typeof agentDocumentParser;
  "agents/contentBuilder": typeof agents_contentBuilder;
  agents: typeof agents;
  ai: typeof ai;
  auth: typeof auth;
  bidSections: typeof bidSections;
  chat: typeof chat;
  documentParser: typeof documentParser;
  emailListener: typeof emailListener;
  files: typeof files;
  http: typeof http;
  meetingIntegrations: typeof meetingIntegrations;
  meetingSummarizer: typeof meetingSummarizer;
  meetingTaskManager: typeof meetingTaskManager;
  orchestration: typeof orchestration;
  router: typeof router;
  scheduler: typeof scheduler;
  schedulerAgent: typeof schedulerAgent;
  slidevIntegration: typeof slidevIntegration;
  tenderProcessing: typeof tenderProcessing;
  tenders: typeof tenders;
  zeroTouch: typeof zeroTouch;
  zeroTouchSchema: typeof zeroTouchSchema;
  zeroTouchWorkflow: typeof zeroTouchWorkflow;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
