import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Contact and role management
export const createContact = mutation({
  args: {
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    role: v.string(),
    department: v.string(),
    state: v.optional(v.string()),
    timezone: v.string(),
    notificationPreferences: v.object({
      email: v.boolean(),
      sms: v.boolean(),
      inApp: v.boolean(),
      preferredChannel: v.string(),
    }),
    availability: v.object({
      workingHours: v.object({
        start: v.string(),
        end: v.string(),
      }),
      daysOfWeek: v.array(v.number()),
      blockedTimes: v.array(v.object({
        start: v.string(),
        end: v.string(),
        reason: v.optional(v.string()),
      })),
    }),
    expertise: v.array(v.string()),
    isActive: v.boolean(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const contactId = await ctx.db.insert("scheduler_contacts", {
      ...args,
      createdAt: Date.now(),
      lastModified: Date.now(),
      createdBy: identity.subject,
    });

    return contactId;
  },
});

export const getContacts = query({
  args: {
    role: v.optional(v.string()),
    department: v.optional(v.string()),
    state: v.optional(v.string()),
    expertise: v.optional(v.array(v.string())),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let contactsQuery = ctx.db.query("scheduler_contacts");

    if (args.role) {
      contactsQuery = contactsQuery.filter(q => q.eq(q.field("role"), args.role));
    }
    if (args.department) {
      contactsQuery = contactsQuery.filter(q => q.eq(q.field("department"), args.department));
    }
    if (args.state) {
      contactsQuery = contactsQuery.filter(q => q.eq(q.field("state"), args.state));
    }
    if (args.isActive !== undefined) {
      contactsQuery = contactsQuery.filter(q => q.eq(q.field("isActive"), args.isActive));
    }

    const contacts = await contactsQuery.collect();

    // Filter by expertise if provided
    if (args.expertise && args.expertise.length > 0) {
      return contacts.filter(contact => 
        args.expertise!.some(exp => contact.expertise.includes(exp))
      );
    }

    return contacts;
  },
});

// Meeting scheduling
export const createMeeting = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    type: v.string(),
    tenderId: v.optional(v.id("tenders")),
    projectContext: v.optional(v.object({
      projectName: v.string(),
      projectType: v.string(),
      priority: v.string(),
      requirements: v.array(v.string()),
    })),
    startTime: v.number(),
    endTime: v.number(),
    timezone: v.string(),
    location: v.object({
      type: v.string(),
      value: v.string(),
      details: v.optional(v.string()),
    }),
    participants: v.array(v.object({
      contactId: v.id("scheduler_contacts"),
      role: v.string(),
      isRequired: v.boolean(),
      responseStatus: v.string(),
    })),
    recurrence: v.optional(v.object({
      pattern: v.string(),
      interval: v.number(),
      endDate: v.optional(v.number()),
      exceptions: v.array(v.number()),
    })),
    reminders: v.array(v.object({
      type: v.string(),
      minutes: v.number(),
      channel: v.string(),
    })),
    resources: v.optional(v.array(v.object({
      type: v.string(),
      name: v.string(),
      quantity: v.number(),
      status: v.string(),
    }))),
    agenda: v.optional(v.array(v.object({
      item: v.string(),
      duration: v.number(),
      presenter: v.optional(v.id("scheduler_contacts")),
    }))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check for conflicts
    const conflicts = await checkConflicts(ctx, args.participants, args.startTime, args.endTime);
    
    const meetingId = await ctx.db.insert("scheduler_meetings", {
      ...args,
      status: "scheduled",
      organizer: identity.subject,
      createdAt: Date.now(),
      lastModified: Date.now(),
      conflicts: conflicts,
      meetingLinks: {},
      rsvpStats: {
        accepted: 0,
        declined: 0,
        tentative: 0,
        pending: args.participants.length,
      },
    });

    // Schedule invitations
    await ctx.scheduler.runAfter(0, api.scheduler.sendInvitations, { 
      meetingId,
      isInitial: true,
    });

    // Schedule reminders
    for (const reminder of args.reminders) {
      const reminderTime = args.startTime - (reminder.minutes * 60 * 1000);
      if (reminderTime > Date.now()) {
        await ctx.scheduler.runAt(reminderTime, api.scheduler.sendReminder, {
          meetingId,
          reminderType: reminder.type,
          channel: reminder.channel,
        });
      }
    }

    return meetingId;
  },
});

// Smart participant selection
export const suggestParticipants = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    projectType: v.string(),
    requirements: v.array(v.string()),
    meetingType: v.string(),
    preferredTime: v.number(),
    duration: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Get all active contacts
    const contacts = await ctx.db
      .query("scheduler_contacts")
      .filter(q => q.eq(q.field("isActive"), true))
      .collect();

    // Score and rank contacts based on criteria
    const scoredContacts = contacts.map(contact => {
      let score = 0;
      let reasons = [];

      // Role-based scoring
      if (args.projectType.includes("ESG") && contact.role === "ESG Manager") {
        score += 30;
        reasons.push("ESG expertise required");
      }

      if (args.projectType.includes("Operations") && contact.role === "Operations Manager") {
        score += 30;
        reasons.push("Operations expertise required");
      }

      // State-based scoring for state-specific projects
      if (args.requirements.some(req => req.includes("state-specific"))) {
        if (contact.state && args.requirements.some(req => req.includes(contact.state))) {
          score += 25;
          reasons.push(`State-specific knowledge for ${contact.state}`);
        }
      }

      // Expertise matching
      const matchingExpertise = contact.expertise.filter(exp => 
        args.requirements.some(req => req.toLowerCase().includes(exp.toLowerCase()))
      );
      score += matchingExpertise.length * 10;
      if (matchingExpertise.length > 0) {
        reasons.push(`Expertise in: ${matchingExpertise.join(", ")}`);
      }

      // Availability scoring
      const availability = checkAvailability(contact, args.preferredTime, args.duration);
      if (availability.isAvailable) {
        score += 15;
      } else {
        score -= 10;
        reasons.push("Scheduling conflict");
      }

      // Meeting type relevance
      if (args.meetingType === "technical_review" && contact.expertise.includes("technical")) {
        score += 10;
        reasons.push("Technical expertise for review");
      }

      return {
        contact,
        score,
        reasons,
        availability,
        isRecommended: score > 20,
        isRequired: score > 40,
      };
    });

    // Sort by score and return top recommendations
    return scoredContacts
      .sort((a, b) => b.score - a.score)
      .filter(sc => sc.score > 0);
  },
});

// RSVP management
export const updateRSVP = mutation({
  args: {
    meetingId: v.id("scheduler_meetings"),
    contactId: v.id("scheduler_contacts"),
    response: v.string(),
    comment: v.optional(v.string()),
    proposedTime: v.optional(v.object({
      start: v.number(),
      end: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.db.get(args.meetingId);
    if (!meeting) {
      throw new Error("Meeting not found");
    }

    const rsvpId = await ctx.db.insert("scheduler_rsvps", {
      meetingId: args.meetingId,
      contactId: args.contactId,
      response: args.response,
      comment: args.comment,
      proposedTime: args.proposedTime,
      respondedAt: Date.now(),
      remindersSent: 0,
    });

    // Update meeting RSVP stats
    const participants = meeting.participants.map(p => {
      if (p.contactId === args.contactId) {
        return { ...p, responseStatus: args.response };
      }
      return p;
    });

    const rsvpStats = {
      accepted: participants.filter(p => p.responseStatus === "accepted").length,
      declined: participants.filter(p => p.responseStatus === "declined").length,
      tentative: participants.filter(p => p.responseStatus === "tentative").length,
      pending: participants.filter(p => p.responseStatus === "pending").length,
    };

    await ctx.db.patch(args.meetingId, {
      participants,
      rsvpStats,
      lastModified: Date.now(),
    });

    // Handle proposed time changes
    if (args.proposedTime && args.response === "tentative") {
      await ctx.scheduler.runAfter(0, api.scheduler.handleTimeProposal, {
        meetingId: args.meetingId,
        contactId: args.contactId,
        proposedTime: args.proposedTime,
      });
    }

    return rsvpId;
  },
});

// Automated nudging
export const sendNudge = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    targetContacts: v.optional(v.array(v.id("scheduler_contacts"))),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) {
      throw new Error("Meeting not found");
    }

    // Identify non-responders
    const nonResponders = meeting.participants.filter(p => 
      p.responseStatus === "pending" &&
      (!args.targetContacts || args.targetContacts.includes(p.contactId))
    );

    for (const participant of nonResponders) {
      const contact = await ctx.runQuery(api.scheduler.getContact, { 
        contactId: participant.contactId 
      });

      if (!contact) continue;

      // Send nudge based on preferred channel
      const message = {
        type: "meeting_nudge",
        meetingId: args.meetingId,
        meetingTitle: meeting.title,
        meetingTime: meeting.startTime,
        organizerName: meeting.organizer,
        urgency: calculateUrgency(meeting.startTime),
      };

      if (contact.notificationPreferences.email) {
        await sendEmailNudge(contact, meeting, message);
      }

      if (contact.notificationPreferences.sms && contact.phone) {
        await sendSMSNudge(contact, meeting, message);
      }

      // Update nudge count
      await ctx.runMutation(api.scheduler.incrementNudgeCount, {
        meetingId: args.meetingId,
        contactId: participant.contactId,
      });
    }
  },
});

// Calendar integration
export const syncToGoogleCalendar = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    calendarId: v.string(),
    accessToken: v.string(),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) {
      throw new Error("Meeting not found");
    }

    // Create Google Calendar event
    const event = {
      summary: meeting.title,
      description: meeting.description,
      start: {
        dateTime: new Date(meeting.startTime).toISOString(),
        timeZone: meeting.timezone,
      },
      end: {
        dateTime: new Date(meeting.endTime).toISOString(),
        timeZone: meeting.timezone,
      },
      attendees: await Promise.all(
        meeting.participants.map(async (p) => {
          const contact = await ctx.runQuery(api.scheduler.getContact, { 
            contactId: p.contactId 
          });
          return {
            email: contact.email,
            responseStatus: p.responseStatus,
            optional: !p.isRequired,
          };
        })
      ),
      reminders: {
        useDefault: false,
        overrides: meeting.reminders.map(r => ({
          method: r.channel === "email" ? "email" : "popup",
          minutes: r.minutes,
        })),
      },
      conferenceData: meeting.location.type === "online" ? {
        createRequest: {
          requestId: meeting._id,
          conferenceSolutionKey: { type: "hangoutsMeet" },
        },
      } : undefined,
    };

    // Mock Google Calendar API call
    const googleEventId = `google_${meeting._id}_${Date.now()}`;
    
    // Update meeting with Google Calendar link
    await ctx.runMutation(api.scheduler.updateMeetingLinks, {
      meetingId: args.meetingId,
      links: {
        googleCalendar: `https://calendar.google.com/event?eid=${googleEventId}`,
        googleEventId,
      },
    });

    return googleEventId;
  },
});

// Meeting platform integration
export const generateMeetingLinks = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    platform: v.string(),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) {
      throw new Error("Meeting not found");
    }

    let meetingLink = "";
    let meetingDetails = {};

    switch (args.platform) {
      case "teams":
        // Mock Teams API integration
        meetingLink = `https://teams.microsoft.com/meet/${meeting._id}`;
        meetingDetails = {
          joinUrl: meetingLink,
          meetingId: `teams_${meeting._id}`,
          dialInNumber: "+1-555-0123",
          conferenceId: "123456789",
        };
        break;

      case "zoom":
        // Mock Zoom API integration
        meetingLink = `https://zoom.us/j/${Date.now()}`;
        meetingDetails = {
          joinUrl: meetingLink,
          meetingId: `zoom_${meeting._id}`,
          password: generateMeetingPassword(),
          dialInNumbers: ["+1-555-0124", "+1-555-0125"],
        };
        break;

      case "meet":
        // Google Meet integration
        meetingLink = `https://meet.google.com/${generateMeetingCode()}`;
        meetingDetails = {
          joinUrl: meetingLink,
          meetingCode: meetingLink.split("/").pop(),
        };
        break;

      default:
        throw new Error(`Unsupported platform: ${args.platform}`);
    }

    // Update meeting with platform details
    await ctx.runMutation(api.scheduler.updateMeetingLinks, {
      meetingId: args.meetingId,
      links: {
        ...meeting.meetingLinks,
        [args.platform]: meetingLink,
        [`${args.platform}Details`]: meetingDetails,
      },
    });

    return { meetingLink, meetingDetails };
  },
});

// Conflict detection
async function checkConflicts(
  ctx: any,
  participants: any[],
  startTime: number,
  endTime: number
) {
  const conflicts = [];

  for (const participant of participants) {
    // Check existing meetings
    const existingMeetings = await ctx.db
      .query("scheduler_meetings")
      .filter((q: any) => 
        q.and(
          q.neq(q.field("status"), "cancelled"),
          q.or(
            q.and(
              q.lte(q.field("startTime"), startTime),
              q.gt(q.field("endTime"), startTime)
            ),
            q.and(
              q.lt(q.field("startTime"), endTime),
              q.gte(q.field("endTime"), endTime)
            ),
            q.and(
              q.gte(q.field("startTime"), startTime),
              q.lte(q.field("endTime"), endTime)
            )
          )
        )
      )
      .collect();

    const participantConflicts = existingMeetings.filter((meeting: any) =>
      meeting.participants.some((p: any) => p.contactId === participant.contactId)
    );

    if (participantConflicts.length > 0) {
      conflicts.push({
        contactId: participant.contactId,
        conflictingMeetings: participantConflicts.map((m: any) => ({
          id: m._id,
          title: m.title,
          start: m.startTime,
          end: m.endTime,
        })),
      });
    }
  }

  return conflicts;
}

function checkAvailability(
  contact: any,
  startTime: number,
  duration: number
) {
  const startDate = new Date(startTime);
  const endTime = startTime + duration;
  const dayOfWeek = startDate.getDay();
  const timeString = startDate.toTimeString().substr(0, 5);

  // Check if day is in working days
  if (!contact.availability.daysOfWeek.includes(dayOfWeek)) {
    return {
      isAvailable: false,
      reason: "Outside working days",
    };
  }

  // Check working hours
  const workStart = contact.availability.workingHours.start;
  const workEnd = contact.availability.workingHours.end;
  
  if (timeString < workStart || timeString > workEnd) {
    return {
      isAvailable: false,
      reason: "Outside working hours",
    };
  }

  // Check blocked times
  for (const blocked of contact.availability.blockedTimes) {
    const blockedStart = new Date(`${startDate.toDateString()} ${blocked.start}`).getTime();
    const blockedEnd = new Date(`${startDate.toDateString()} ${blocked.end}`).getTime();
    
    if (
      (startTime >= blockedStart && startTime < blockedEnd) ||
      (endTime > blockedStart && endTime <= blockedEnd)
    ) {
      return {
        isAvailable: false,
        reason: blocked.reason || "Blocked time",
      };
    }
  }

  return {
    isAvailable: true,
    reason: "Available",
  };
}

function calculateUrgency(meetingTime: number): string {
  const hoursUntilMeeting = (meetingTime - Date.now()) / (1000 * 60 * 60);
  
  if (hoursUntilMeeting < 24) return "high";
  if (hoursUntilMeeting < 48) return "medium";
  return "low";
}

function generateMeetingPassword(): string {
  return Math.random().toString(36).substring(2, 10);
}

function generateMeetingCode(): string {
  const chars = "abcdefghijklmnopqrstuvwxyz";
  let code = "";
  for (let i = 0; i < 3; i++) {
    if (i > 0) code += "-";
    for (let j = 0; j < 4; j++) {
      code += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  return code;
}

// Mock notification functions (would integrate with real services)
async function sendEmailNudge(contact: any, meeting: any, message: any) {
  console.log(`Sending email nudge to ${contact.email} for meeting ${meeting.title}`);
  // Integration with email service (SendGrid, AWS SES, etc.)
}

async function sendSMSNudge(contact: any, meeting: any, message: any) {
  console.log(`Sending SMS nudge to ${contact.phone} for meeting ${meeting.title}`);
  // Integration with Twilio or similar SMS service
}

// Helper queries and mutations
export const getMeeting = query({
  args: { meetingId: v.id("scheduler_meetings") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    return await ctx.db.get(args.meetingId);
  },
});

export const getContact = query({
  args: { contactId: v.id("scheduler_contacts") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }
    return await ctx.db.get(args.contactId);
  },
});

export const updateMeetingLinks = mutation({
  args: {
    meetingId: v.id("scheduler_meetings"),
    links: v.object({}),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.meetingId, {
      meetingLinks: args.links,
      lastModified: Date.now(),
    });
  },
});

export const incrementNudgeCount = mutation({
  args: {
    meetingId: v.id("scheduler_meetings"),
    contactId: v.id("scheduler_contacts"),
  },
  handler: async (ctx, args) => {
    const rsvp = await ctx.db
      .query("scheduler_rsvps")
      .filter(q => 
        q.and(
          q.eq(q.field("meetingId"), args.meetingId),
          q.eq(q.field("contactId"), args.contactId)
        )
      )
      .first();

    if (rsvp) {
      await ctx.db.patch(rsvp._id, {
        remindersSent: rsvp.remindersSent + 1,
        lastReminderAt: Date.now(),
      });
    }
  },
});

// Notification actions
export const sendInvitations = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    isInitial: v.boolean(),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) return;

    for (const participant of meeting.participants) {
      const contact = await ctx.runQuery(api.scheduler.getContact, { 
        contactId: participant.contactId 
      });

      if (!contact) continue;

      const invitation = {
        meetingId: meeting._id,
        meetingTitle: meeting.title,
        meetingDescription: meeting.description,
        startTime: meeting.startTime,
        endTime: meeting.endTime,
        timezone: meeting.timezone,
        location: meeting.location,
        organizer: meeting.organizer,
        rsvpUrl: `${process.env.APP_URL}/rsvp/${meeting._id}/${participant.contactId}`,
        calendarLinks: meeting.meetingLinks,
      };

      // Send via preferred channels
      if (contact.notificationPreferences.email) {
        await sendEmailInvitation(contact, invitation);
      }

      if (contact.notificationPreferences.sms && contact.phone) {
        await sendSMSInvitation(contact, invitation);
      }
    }
  },
});

export const sendReminder = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    reminderType: v.string(),
    channel: v.string(),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) return;

    const confirmedParticipants = meeting.participants.filter(
      p => p.responseStatus === "accepted"
    );

    for (const participant of confirmedParticipants) {
      const contact = await ctx.runQuery(api.scheduler.getContact, { 
        contactId: participant.contactId 
      });

      if (!contact) continue;

      const reminder = {
        meetingTitle: meeting.title,
        startTime: meeting.startTime,
        location: meeting.location,
        joinLink: meeting.meetingLinks[meeting.location.type] || meeting.location.value,
      };

      if (args.channel === "email" && contact.notificationPreferences.email) {
        await sendEmailReminder(contact, reminder);
      }

      if (args.channel === "sms" && contact.notificationPreferences.sms && contact.phone) {
        await sendSMSReminder(contact, reminder);
      }
    }
  },
});

export const handleTimeProposal = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    contactId: v.id("scheduler_contacts"),
    proposedTime: v.object({
      start: v.number(),
      end: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    // Check if proposed time works for other participants
    const meeting = await ctx.runQuery(api.scheduler.getMeeting, { 
      meetingId: args.meetingId 
    });

    if (!meeting) return;

    const otherParticipants = meeting.participants.filter(
      p => p.contactId !== args.contactId
    );

    const conflicts = await checkConflicts(
      ctx,
      otherParticipants,
      args.proposedTime.start,
      args.proposedTime.end
    );

    if (conflicts.length === 0) {
      // Notify organizer of proposed time that works for everyone
      console.log(`Proposed time works for all participants`);
      // Send notification to organizer
    } else {
      // Find alternative times
      console.log(`Proposed time has conflicts, finding alternatives`);
      // Run optimization algorithm to find best alternative times
    }
  },
});

// Mock notification implementations
async function sendEmailInvitation(contact: any, invitation: any) {
  console.log(`Sending email invitation to ${contact.email}`);
  // Implement actual email sending
}

async function sendSMSInvitation(contact: any, invitation: any) {
  console.log(`Sending SMS invitation to ${contact.phone}`);
  // Implement actual SMS sending
}

async function sendEmailReminder(contact: any, reminder: any) {
  console.log(`Sending email reminder to ${contact.email}`);
  // Implement actual email reminder
}

async function sendSMSReminder(contact: any, reminder: any) {
  console.log(`Sending SMS reminder to ${contact.phone}`);
  // Implement actual SMS reminder
}