"use node";
import { httpAction } from "./_generated/server";
import { api } from "./_generated/api";
import { google } from "googleapis";
import crypto from "crypto";

// Gmail webhook configuration
const GMAIL_CONFIG = {
  clientId: process.env.GMAIL_CLIENT_ID,
  clientSecret: process.env.GMAIL_CLIENT_SECRET,
  redirectUri: process.env.GMAIL_REDIRECT_URI,
  pubsubTopic: process.env.GMAIL_PUBSUB_TOPIC,
  webhookSecret: process.env.GMAIL_WEBHOOK_SECRET,
};

// Supported attachment types for automatic processing
const SUPPORTED_ATTACHMENTS = [
  "application/pdf",
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  "application/msword",
  "image/png",
  "image/jpeg",
  "image/jpg",
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  "application/vnd.ms-excel",
  "text/plain",
  "text/rtf",
];

// Gmail webhook endpoint
export const gmailWebhook = httpAction(async (ctx, request) => {
  // Verify webhook signature
  const signature = request.headers.get("x-goog-signature");
  if (!verifyWebhookSignature(await request.text(), signature)) {
    return new Response("Unauthorized", { status: 401 });
  }

  const body = await request.json();
  
  // Handle Gmail push notification
  if (body.message) {
    const messageData = JSON.parse(
      Buffer.from(body.message.data, "base64").toString()
    );
    
    if (messageData.emailAddress && messageData.historyId) {
      // Process new email
      await processEmailUpdate(ctx, messageData);
    }
  }
  
  return new Response("OK", { status: 200 });
});

// Process email update from Gmail
async function processEmailUpdate(ctx: any, messageData: any) {
  try {
    // Initialize Gmail API client
    const gmail = await getGmailClient(messageData.emailAddress);
    
    // Get message details
    const response = await gmail.users.messages.list({
      userId: "me",
      q: "is:unread has:attachment",
      maxResults: 10,
    });
    
    if (!response.data.messages) {
      return;
    }
    
    // Process each message
    for (const message of response.data.messages) {
      if (!message.id) continue;
      
      const fullMessage = await gmail.users.messages.get({
        userId: "me",
        id: message.id,
      });
      
      if (!fullMessage.data.payload) continue;
      
      // Extract email metadata
      const emailMetadata = extractEmailMetadata(fullMessage.data);
      
      // Check if it's a tender-related email
      if (isTenderEmail(emailMetadata)) {
        // Process attachments
        await processEmailAttachments(ctx, gmail, message.id, fullMessage.data.payload, emailMetadata);
        
        // Mark as read
        await gmail.users.messages.modify({
          userId: "me",
          id: message.id,
          requestBody: {
            removeLabelIds: ["UNREAD"],
          },
        });
      }
    }
  } catch (error) {
    console.error("Error processing email update:", error);
  }
}

// Extract email metadata
function extractEmailMetadata(message: any): any {
  const headers = message.payload?.headers || [];
  const metadata: any = {};
  
  for (const header of headers) {
    switch (header.name.toLowerCase()) {
      case "from":
        metadata.from = header.value;
        break;
      case "to":
        metadata.to = header.value;
        break;
      case "subject":
        metadata.subject = header.value;
        break;
      case "date":
        metadata.date = header.value;
        break;
    }
  }
  
  // Extract email body
  metadata.body = extractEmailBody(message.payload);
  
  return metadata;
}

// Extract email body text
function extractEmailBody(payload: any): string {
  let body = "";
  
  if (payload.body?.data) {
    body = Buffer.from(payload.body.data, "base64").toString();
  } else if (payload.parts) {
    for (const part of payload.parts) {
      if (part.mimeType === "text/plain" && part.body?.data) {
        body += Buffer.from(part.body.data, "base64").toString();
      } else if (part.parts) {
        body += extractEmailBody(part);
      }
    }
  }
  
  return body;
}

// Check if email is tender-related
function isTenderEmail(metadata: any): boolean {
  const tenderKeywords = [
    "tender",
    "rfp",
    "rfq",
    "request for proposal",
    "request for quote",
    "bid",
    "proposal",
    "quotation",
    "cleaning services",
    "facilities management",
    "ara property services",
  ];
  
  const searchText = `${metadata.subject} ${metadata.body}`.toLowerCase();
  
  return tenderKeywords.some(keyword => searchText.includes(keyword));
}

// Process email attachments
async function processEmailAttachments(
  ctx: any,
  gmail: any,
  messageId: string,
  payload: any,
  emailMetadata: any
) {
  const attachments = findAttachments(payload);
  
  for (const attachment of attachments) {
    if (!SUPPORTED_ATTACHMENTS.includes(attachment.mimeType)) {
      continue;
    }
    
    try {
      // Get attachment data
      const attachmentData = await gmail.users.messages.attachments.get({
        userId: "me",
        messageId,
        id: attachment.attachmentId,
      });
      
      if (!attachmentData.data.data) continue;
      
      // Process attachment with document parser
      const result = await ctx.runAction(api.documentParser.processEmailAttachment, {
        emailId: messageId,
        attachmentId: attachment.attachmentId,
        fileName: attachment.filename,
        mimeType: attachment.mimeType,
        size: attachment.size,
        base64Data: attachmentData.data.data,
      });
      
      // Create notification for processed document
      if (result.success && result.extractedData) {
        await createProcessingNotification(ctx, {
          emailMetadata,
          attachment,
          result,
        });
      }
    } catch (error) {
      console.error(`Error processing attachment ${attachment.filename}:`, error);
    }
  }
}

// Find attachments in email payload
function findAttachments(payload: any, attachments: any[] = []): any[] {
  if (payload.parts) {
    for (const part of payload.parts) {
      if (part.filename && part.body?.attachmentId) {
        attachments.push({
          attachmentId: part.body.attachmentId,
          filename: part.filename,
          mimeType: part.mimeType,
          size: part.body.size || 0,
        });
      }
      
      if (part.parts) {
        findAttachments(part, attachments);
      }
    }
  }
  
  return attachments;
}

// Create processing notification
async function createProcessingNotification(ctx: any, data: any) {
  const { emailMetadata, attachment, result } = data;
  
  // Create a notification or chat message about the processed document
  const notificationMessage = `
📧 **New Tender Document Processed**

**From:** ${emailMetadata.from}
**Subject:** ${emailMetadata.subject}
**File:** ${attachment.filename}
**Confidence:** ${(result.confidence.overall * 100).toFixed(1)}%

**Extracted Information:**
${result.extractedData ? formatExtractedData(result.extractedData) : "No data extracted"}

**Analysis ID:** ${result.analysisId}
**File ID:** ${result.fileId}
`;

  // You can send this to a specific chat thread or create a notification
  console.log(notificationMessage);
}

// Format extracted data for display
function formatExtractedData(data: any): string {
  const tender = data.tender_data || data;
  const lines: string[] = [];
  
  if (tender.tender_name) {
    lines.push(`• **Tender:** ${tender.tender_name}`);
  }
  if (tender.client_name) {
    lines.push(`• **Client:** ${tender.client_name}`);
  }
  if (tender.due_date) {
    lines.push(`• **Due Date:** ${tender.due_date}`);
  }
  if (tender.estimated_value) {
    lines.push(`• **Value:** $${tender.estimated_value.toLocaleString()}`);
  }
  if (tender.locations?.sites?.length > 0) {
    lines.push(`• **Locations:** ${tender.locations.sites.join(", ")}`);
  }
  
  return lines.join("\n");
}

// Verify webhook signature
function verifyWebhookSignature(body: string, signature: string | null): boolean {
  if (!signature || !GMAIL_CONFIG.webhookSecret) {
    return false;
  }
  
  const expectedSignature = crypto
    .createHmac("sha256", GMAIL_CONFIG.webhookSecret)
    .update(body)
    .digest("base64");
  
  return signature === expectedSignature;
}

// Get Gmail API client
async function getGmailClient(email: string) {
  const oauth2Client = new google.auth.OAuth2(
    GMAIL_CONFIG.clientId,
    GMAIL_CONFIG.clientSecret,
    GMAIL_CONFIG.redirectUri
  );
  
  // In production, retrieve stored tokens for the user
  // For now, we'll assume tokens are stored elsewhere
  const tokens = await getStoredTokens(email);
  oauth2Client.setCredentials(tokens);
  
  return google.gmail({ version: "v1", auth: oauth2Client });
}

// Placeholder for token retrieval
async function getStoredTokens(email: string): Promise<any> {
  // In production, retrieve from secure storage
  return {
    access_token: process.env.GMAIL_ACCESS_TOKEN,
    refresh_token: process.env.GMAIL_REFRESH_TOKEN,
    scope: "https://www.googleapis.com/auth/gmail.readonly https://www.googleapis.com/auth/gmail.modify",
    token_type: "Bearer",
  };
}

// OAuth2 callback handler
export const gmailOAuthCallback = httpAction(async (ctx, request) => {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");
  
  if (!code) {
    return new Response("Missing authorization code", { status: 400 });
  }
  
  try {
    const oauth2Client = new google.auth.OAuth2(
      GMAIL_CONFIG.clientId,
      GMAIL_CONFIG.clientSecret,
      GMAIL_CONFIG.redirectUri
    );
    
    const { tokens } = await oauth2Client.getToken(code);
    
    // Store tokens securely
    // await storeTokens(state, tokens);
    
    // Set up Gmail watch for new emails
    oauth2Client.setCredentials(tokens);
    const gmail = google.gmail({ version: "v1", auth: oauth2Client });
    
    await gmail.users.watch({
      userId: "me",
      requestBody: {
        topicName: GMAIL_CONFIG.pubsubTopic,
        labelIds: ["INBOX"],
      },
    });
    
    return new Response("Gmail integration successful! You can close this window.", {
      status: 200,
      headers: { "Content-Type": "text/html" },
    });
  } catch (error) {
    console.error("OAuth callback error:", error);
    return new Response("Authorization failed", { status: 500 });
  }
});

// Generate OAuth URL for Gmail integration
export const getGmailAuthUrl = httpAction(async () => {
  const oauth2Client = new google.auth.OAuth2(
    GMAIL_CONFIG.clientId,
    GMAIL_CONFIG.clientSecret,
    GMAIL_CONFIG.redirectUri
  );
  
  const authUrl = oauth2Client.generateAuthUrl({
    access_type: "offline",
    scope: [
      "https://www.googleapis.com/auth/gmail.readonly",
      "https://www.googleapis.com/auth/gmail.modify",
    ],
    state: crypto.randomBytes(16).toString("hex"),
  });
  
  return new Response(JSON.stringify({ authUrl }), {
    status: 200,
    headers: { "Content-Type": "application/json" },
  });
});

// Manual email processing endpoint
export const processEmailManually = httpAction(async (ctx, request) => {
  const { messageId } = await request.json();
  
  if (!messageId) {
    return new Response("Missing message ID", { status: 400 });
  }
  
  try {
    const gmail = await getGmailClient("<EMAIL>");
    
    const message = await gmail.users.messages.get({
      userId: "me",
      id: messageId,
    });
    
    const emailMetadata = extractEmailMetadata(message.data);
    
    if (message.data.payload) {
      await processEmailAttachments(ctx, gmail, messageId, message.data.payload, emailMetadata);
    }
    
    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    console.error("Manual email processing error:", error);
    return new Response(JSON.stringify({ error: "Processing failed" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
});