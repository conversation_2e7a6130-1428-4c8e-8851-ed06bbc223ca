"use node";
import { action } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";
import OpenAI from "openai";
import PDFParser from "pdf2json";

// OpenAI client will be instantiated inside functions
const getOpenAI = () => new OpenAI({
  baseURL: process.env.CONVEX_OPENAI_BASE_URL,
  apiKey: process.env.CONVEX_OPENAI_API_KEY || process.env.OPENAI_API_KEY || "",
});

export const processDocument = action({
  args: {
    storageId: v.id("_storage"),
  },
  handler: async (ctx, args) => {
    const blob = await ctx.storage.get(args.storageId);
    if (!blob) {
      throw new Error("File not found in storage.");
    }
    const buffer = await blob.arrayBuffer();

    const pdfParser = new PDFParser();

    const documentText = await new Promise<string>((resolve, reject) => {
      pdfParser.on("pdfParser_dataError", (errData) =>
        reject(errData.parserError)
      );
      pdfParser.on("pdfParser_dataReady", () => {
        resolve(pdfParser.getRawTextContent());
      });
      pdfParser.parseBuffer(Buffer.from(buffer));
    });

    const prompt = `
You are an expert tender analysis system for ARA Property Services.
Based on the following tender document text, extract the key information in JSON format.

The JSON object should have the following structure:
{
  "tenderName": "...",
  "clientName": "...",
  "dueDate": "YYYY-MM-DD",
  "estimatedValue": 123456,
  "sections": [
    { "title": "Section 1 Title", "wordLimit": 500, "scoreWeight": 10 },
    { "title": "Section 2 Title", "wordLimit": 1000, "scoreWeight": 20 }
  ]
}

- "tenderName": The official name or title of the tender.
- "clientName": The name of the organization requesting the tender.
- "dueDate": The submission deadline. Extract the date and format it as YYYY-MM-DD. If no year is specified, assume the current year.
- "estimatedValue": The estimated contract value. Extract only the numerical value.
- "sections": A list of all the required sections for the bid response. For each section, extract its title, word limit, and score weighting. If a value is not present, use a reasonable default or null.

Tender Document Text:
---
${documentText.substring(0, 30000)}
---

Extract the information and provide only the JSON object.
`;

    const openai = getOpenAI();
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [{ role: "user", content: prompt }],
      response_format: { type: "json_object" },
    });

    const jsonString = response.choices[0].message.content;
    if (!jsonString) {
      throw new Error("Failed to extract tender information from document.");
    }

    const tenderData = JSON.parse(jsonString);

    await ctx.runMutation(internal.tenders.createFromDocument, {
      name: tenderData.tenderName,
      clientName: tenderData.clientName,
      dueDate: tenderData.dueDate,
      estimatedValue: tenderData.estimatedValue,
      sections: tenderData.sections,
    });
  },
});
