import { query, mutation, internalMutation } from "./_generated/server";
import { v } from "convex/values";

export const list = query({
  handler: async (ctx) => {
    return await ctx.db.query("tenders").order("desc").collect();
  },
});

export const get = query({
  args: {
    id: v.id("tenders"),
  },
  handler: async (ctx, args) => {
    const tender = await ctx.db.get(args.id);
    if (!tender) {
      return null;
    }
    const bidSections = await ctx.db
      .query("bidSections")
      .withIndex("by_tender", (q) => q.eq("tenderId", args.id))
      .collect();
    return { ...tender, bidSections };
  },
});

export const generateUploadUrl = mutation(async (ctx) => {
  return await ctx.storage.generateUploadUrl();
});

export const createFromDocument = internalMutation({
  args: {
    name: v.string(),
    clientName: v.string(),
    dueDate: v.string(),
    estimatedValue: v.number(),
    sections: v.array(
      v.object({
        title: v.string(),
        wordLimit: v.number(),
        scoreWeight: v.number(),
      })
    ),
  },
  handler: async (ctx, args) => {
    const tenderId = await ctx.db.insert("tenders", {
      name: args.name,
      clientName: args.clientName,
      status: "bid_writing",
      dueDate: args.dueDate,
      estimatedValue: args.estimatedValue,
    });

    for (const section of args.sections) {
      await ctx.db.insert("bidSections", {
        tenderId,
        title: section.title,
        content: "",
        wordCount: 0,
        wordLimit: section.wordLimit,
        scoreWeight: section.scoreWeight,
        status: "draft",
      });
    }
    return tenderId;
  },
});
