import { v } from "convex/values";
import { mutation, query, action } from "../_generated/server";
import { api } from "../_generated/api";
import { Doc, Id } from "../_generated/dataModel";

// Content types for meeting materials
export const contentTypes = v.union(
  v.literal("executive_summary"),
  v.literal("site_heatmap"),
  v.literal("compliance_dashboard"),
  v.literal("timeline_visualization"),
  v.literal("team_introduction"),
  v.literal("qa_preparation"),
  v.literal("meeting_agenda"),
  v.literal("voice_script"),
  v.literal("presentation_deck"),
  v.literal("data_visualization")
);

export const templateThemes = v.union(
  v.literal("professional"),
  v.literal("modern"),
  v.literal("minimal"),
  v.literal("corporate"),
  v.literal("creative")
);

export const exportFormats = v.union(
  v.literal("pptx"),
  v.literal("pdf"),
  v.literal("html"),
  v.literal("google_slides"),
  v.literal("markdown"),
  v.literal("slidev")
);

// Content generation request
export const createContentRequest = mutation({
  args: {
    tenderId: v.id("tenders"),
    contentType: contentTypes,
    theme: templateThemes,
    title: v.string(),
    description: v.string(),
    targetAudience: v.string(),
    meetingType: v.string(),
    duration: v.number(), // in minutes
    parameters: v.object({
      includeVoiceScript: v.optional(v.boolean()),
      includeBranding: v.optional(v.boolean()),
      dataPoints: v.optional(v.array(v.object({
        label: v.string(),
        value: v.union(v.string(), v.number()),
        type: v.optional(v.string()),
      }))),
      siteLocations: v.optional(v.array(v.object({
        name: v.string(),
        address: v.string(),
        latitude: v.number(),
        longitude: v.number(),
        status: v.optional(v.string()),
      }))),
      complianceItems: v.optional(v.array(v.object({
        category: v.string(),
        requirement: v.string(),
        status: v.string(),
        priority: v.string(),
        dueDate: v.optional(v.number()),
      }))),
      timelineEvents: v.optional(v.array(v.object({
        date: v.number(),
        title: v.string(),
        description: v.string(),
        milestone: v.boolean(),
      }))),
      teamMembers: v.optional(v.array(v.object({
        name: v.string(),
        role: v.string(),
        bio: v.string(),
        imageUrl: v.optional(v.string()),
      }))),
      qaTopics: v.optional(v.array(v.object({
        question: v.string(),
        answer: v.string(),
        category: v.string(),
        difficulty: v.string(),
      }))),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Create content generation request
    const requestId = await ctx.db.insert("content_requests", {
      tenderId: args.tenderId,
      contentType: args.contentType,
      theme: args.theme,
      title: args.title,
      description: args.description,
      targetAudience: args.targetAudience,
      meetingType: args.meetingType,
      duration: args.duration,
      parameters: args.parameters,
      status: "pending",
      createdBy: identity.subject,
      createdAt: Date.now(),
    });

    // Find available content builder agent
    const contentAgent = await ctx.db
      .query("agents")
      .filter(q => 
        q.and(
          q.eq(q.field("type"), "specialist"),
          q.eq(q.field("isActive"), true)
        )
      )
      .first();

    if (contentAgent) {
      // Create task for agent
      await ctx.db.insert("agent_tasks", {
        agentId: contentAgent._id,
        type: "generate_content",
        status: "assigned",
        priority: "high",
        tenderId: args.tenderId,
        assignedAt: Date.now(),
        input: {
          instructions: `Generate ${args.contentType} content for ${args.title}`,
          context: {
            tenderName: args.title,
            clientName: args.targetAudience,
            sectionTitle: args.contentType,
            wordLimit: args.contentType === "voice_script" ? 500 : undefined,
          },
          parameters: {
            tone: "professional",
            style: args.contentType === "executive_summary" ? "concise" : "detailed",
            focus: [args.meetingType, args.targetAudience],
          },
        },
        progress: 0,
        retryCount: 0,
        inputTokens: 0,
        outputTokens: 0,
        totalTokens: 0,
      });

      // Schedule content generation
      await ctx.scheduler.runAfter(0, api.agents.contentBuilder.generateContent, { 
        requestId,
      });
    }

    return requestId;
  },
});

// Generate content based on type
export const generateContent = action({
  args: { requestId: v.id("content_requests") },
  handler: async (ctx, args) => {
    const request = await ctx.runQuery(api.agents.contentBuilder.getContentRequest, { 
      requestId: args.requestId 
    });
    
    if (!request) {
      throw new Error("Content request not found");
    }

    // Update status to processing
    await ctx.runMutation(api.agents.contentBuilder.updateContentStatus, {
      requestId: args.requestId,
      status: "processing",
      progress: 10,
    });

    try {
      let generatedContent;
      
      switch (request.contentType) {
        case "executive_summary":
          generatedContent = await generateExecutiveSummary(ctx, request);
          break;
        case "site_heatmap":
          generatedContent = await generateSiteHeatmap(ctx, request);
          break;
        case "compliance_dashboard":
          generatedContent = await generateComplianceDashboard(ctx, request);
          break;
        case "timeline_visualization":
          generatedContent = await generateTimelineVisualization(ctx, request);
          break;
        case "team_introduction":
          generatedContent = await generateTeamIntroduction(ctx, request);
          break;
        case "qa_preparation":
          generatedContent = await generateQAPreparation(ctx, request);
          break;
        case "meeting_agenda":
          generatedContent = await generateMeetingAgenda(ctx, request);
          break;
        case "voice_script":
          generatedContent = await generateVoiceScript(ctx, request);
          break;
        case "presentation_deck":
          generatedContent = await generatePresentationDeck(ctx, request);
          break;
        default:
          throw new Error(`Unsupported content type: ${request.contentType}`);
      }

      // Store generated content
      await ctx.runMutation(api.agents.contentBuilder.saveGeneratedContent, {
        requestId: args.requestId,
        content: generatedContent,
      });

      // Update status to completed
      await ctx.runMutation(api.agents.contentBuilder.updateContentStatus, {
        requestId: args.requestId,
        status: "completed",
        progress: 100,
      });

    } catch (error) {
      await ctx.runMutation(api.agents.contentBuilder.updateContentStatus, {
        requestId: args.requestId,
        status: "failed",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

// Content generation functions
async function generateExecutiveSummary(ctx: any, request: any) {
  const tender = await ctx.runQuery(api.tenders.get, { 
    id: request.tenderId 
  });

  const prompt = `
    Generate an executive summary slide content for:
    Title: ${request.title}
    Description: ${request.description}
    Target Audience: ${request.targetAudience}
    Meeting Type: ${request.meetingType}
    Duration: ${request.duration} minutes
    
    Include:
    - Key value propositions
    - Company credentials
    - Unique differentiators
    - Success metrics
    
    Format as structured content with:
    - Title
    - 3-5 key points
    - Call to action
  `;

  // Simulate AI generation (replace with actual AI integration)
  return {
    type: "executive_summary",
    slides: [{
      title: request.title,
      subtitle: "Executive Summary",
      content: {
        keyPoints: [
          {
            icon: "check-circle",
            title: "Proven Track Record",
            description: "25+ years of excellence in commercial cleaning services"
          },
          {
            icon: "users",
            title: "Expert Team",
            description: "150+ certified cleaning professionals"
          },
          {
            icon: "shield",
            title: "Full Compliance",
            description: "ISO 9001, 14001, and 45001 certified"
          },
          {
            icon: "trending-up",
            title: "Cost Efficiency",
            description: "20% average cost reduction for clients"
          }
        ],
        callToAction: "Partner with us for exceptional cleaning services",
        metrics: {
          clientRetention: "98%",
          serviceLocations: "500+",
          monthlyCleans: "10,000+",
        }
      },
      notes: "Emphasize our competitive advantages and proven results",
      animations: ["fade-in", "slide-up"],
    }],
    metadata: {
      wordCount: 85,
      readingTime: 45,
      confidence: 0.92,
    }
  };
}

async function generateSiteHeatmap(ctx: any, request: any) {
  const locations = request.parameters.siteLocations || [];
  
  return {
    type: "site_heatmap",
    slides: [{
      title: "Service Location Coverage",
      subtitle: "Melbourne CBD & Surrounding Areas",
      content: {
        mapConfig: {
          center: { lat: -37.8136, lng: 144.9631 },
          zoom: 12,
          style: "light",
        },
        locations: locations.map((loc: any) => ({
          ...loc,
          heat: Math.random() * 100, // Simulate heat value
          popup: {
            title: loc.name,
            details: [
              `Address: ${loc.address}`,
              `Status: ${loc.status || 'Active'}`,
              `Coverage: 24/7`,
            ]
          }
        })),
        legend: {
          title: "Service Intensity",
          ranges: [
            { min: 0, max: 25, color: "#90EE90", label: "Light" },
            { min: 26, max: 50, color: "#FFD700", label: "Moderate" },
            { min: 51, max: 75, color: "#FFA500", label: "High" },
            { min: 76, max: 100, color: "#FF4500", label: "Critical" },
          ]
        }
      },
      notes: "Interactive map showing service coverage density",
    }],
    metadata: {
      locationCount: locations.length,
      coverageArea: "25 km²",
      confidence: 0.88,
    }
  };
}

async function generateComplianceDashboard(ctx: any, request: any) {
  const complianceItems = request.parameters.complianceItems || [];
  
  const categorizedItems = complianceItems.reduce((acc: any, item: any) => {
    if (!acc[item.category]) acc[item.category] = [];
    acc[item.category].push(item);
    return acc;
  }, {});

  return {
    type: "compliance_dashboard",
    slides: [{
      title: "Compliance Status Dashboard",
      subtitle: "Real-time Compliance Tracking",
      content: {
        overview: {
          totalRequirements: complianceItems.length,
          compliant: complianceItems.filter((i: any) => i.status === "compliant").length,
          inProgress: complianceItems.filter((i: any) => i.status === "in_progress").length,
          pending: complianceItems.filter((i: any) => i.status === "pending").length,
        },
        charts: {
          statusPie: {
            type: "pie",
            data: [
              { label: "Compliant", value: 75, color: "#10B981" },
              { label: "In Progress", value: 20, color: "#F59E0B" },
              { label: "Pending", value: 5, color: "#EF4444" },
            ]
          },
          categoryBar: {
            type: "bar",
            data: Object.entries(categorizedItems).map(([category, items]: any) => ({
              category,
              compliant: items.filter((i: any) => i.status === "compliant").length,
              total: items.length,
            }))
          }
        },
        criticalItems: complianceItems
          .filter((i: any) => i.priority === "critical")
          .slice(0, 5),
      },
      notes: "Focus on critical compliance items and overall progress",
    }],
    metadata: {
      lastUpdated: Date.now(),
      complianceScore: 0.95,
      confidence: 0.91,
    }
  };
}

async function generateTimelineVisualization(ctx: any, request: any) {
  const events = request.parameters.timelineEvents || [];
  
  return {
    type: "timeline_visualization",
    slides: [{
      title: "Project Timeline",
      subtitle: "Key Milestones & Deliverables",
      content: {
        timeline: {
          startDate: Math.min(...events.map((e: any) => e.date)),
          endDate: Math.max(...events.map((e: any) => e.date)),
          events: events.map((event: any) => ({
            ...event,
            position: event.milestone ? "top" : "bottom",
            color: event.milestone ? "#3B82F6" : "#6B7280",
            icon: event.milestone ? "flag" : "circle",
          })),
        },
        summary: {
          totalDuration: `${Math.ceil((Math.max(...events.map((e: any) => e.date)) - Math.min(...events.map((e: any) => e.date))) / (1000 * 60 * 60 * 24))} days`,
          milestones: events.filter((e: any) => e.milestone).length,
          phases: 4,
        },
        phases: [
          { name: "Mobilization", duration: "2 weeks", status: "completed" },
          { name: "Implementation", duration: "4 weeks", status: "in_progress" },
          { name: "Optimization", duration: "2 weeks", status: "upcoming" },
          { name: "Steady State", duration: "Ongoing", status: "future" },
        ]
      },
      notes: "Highlight critical path and key dependencies",
    }],
    metadata: {
      eventCount: events.length,
      criticalPath: true,
      confidence: 0.87,
    }
  };
}

async function generateTeamIntroduction(ctx: any, request: any) {
  const teamMembers = request.parameters.teamMembers || [];
  
  return {
    type: "team_introduction",
    slides: teamMembers.map((member: any, index: number) => ({
      title: index === 0 ? "Meet Your Dedicated Team" : member.name,
      subtitle: member.role,
      content: {
        profile: {
          name: member.name,
          role: member.role,
          bio: member.bio,
          imageUrl: member.imageUrl || "/placeholder-avatar.jpg",
          qualifications: [
            "Industry Certified",
            "10+ Years Experience",
            "Client-Focused Approach",
          ],
          contact: {
            email: `${member.name.toLowerCase().replace(/\s+/g, '.')}@company.com`,
            availability: "24/7 Support",
          }
        },
        teamStructure: index === 0 ? {
          hierarchy: "Flat organizational structure for quick decision making",
          reportingLine: "Direct access to management team",
          coverage: "Round-the-clock support team",
        } : undefined,
      },
      layout: index === 0 ? "team-overview" : "individual-profile",
      notes: `Emphasize ${member.name}'s expertise and client success stories`,
    })),
    metadata: {
      teamSize: teamMembers.length,
      averageExperience: "12 years",
      confidence: 0.94,
    }
  };
}

async function generateQAPreparation(ctx: any, request: any) {
  const qaTopics = request.parameters.qaTopics || [];
  
  const categorizedQA = qaTopics.reduce((acc: any, qa: any) => {
    if (!acc[qa.category]) acc[qa.category] = [];
    acc[qa.category].push(qa);
    return acc;
  }, {});

  return {
    type: "qa_preparation",
    content: {
      overview: {
        totalQuestions: qaTopics.length,
        categories: Object.keys(categorizedQA),
        difficultyBreakdown: {
          easy: qaTopics.filter((q: any) => q.difficulty === "easy").length,
          medium: qaTopics.filter((q: any) => q.difficulty === "medium").length,
          hard: qaTopics.filter((q: any) => q.difficulty === "hard").length,
        }
      },
      sections: Object.entries(categorizedQA).map(([category, questions]: any) => ({
        category,
        questions: questions.map((q: any) => ({
          ...q,
          talkingPoints: [
            "Key message to emphasize",
            "Supporting evidence/example",
            "Potential follow-up",
          ],
          suggestedDuration: q.difficulty === "hard" ? 3 : q.difficulty === "medium" ? 2 : 1,
        }))
      })),
      generalGuidelines: [
        "Keep answers concise and focused",
        "Use specific examples from our experience",
        "Redirect to our strengths when possible",
        "Always end with a positive note",
      ],
      difficultQuestionStrategies: [
        "Acknowledge the concern",
        "Provide context and perspective",
        "Share relevant success story",
        "Offer concrete solutions",
      ]
    },
    metadata: {
      prepTime: `${Math.ceil(qaTopics.length * 2)} minutes`,
      confidence: 0.89,
    }
  };
}

async function generateMeetingAgenda(ctx: any, request: any) {
  const duration = request.duration;
  const sections = [
    { name: "Welcome & Introductions", duration: 5, presenter: "Account Director" },
    { name: "Company Overview", duration: 10, presenter: "Managing Director" },
    { name: "Service Capability Presentation", duration: 20, presenter: "Operations Manager" },
    { name: "Case Studies & Success Stories", duration: 15, presenter: "Client Success Manager" },
    { name: "Pricing & Commercial Terms", duration: 15, presenter: "Commercial Manager" },
    { name: "Implementation Plan", duration: 10, presenter: "Project Manager" },
    { name: "Q&A Session", duration: 20, presenter: "All" },
    { name: "Next Steps & Closing", duration: 5, presenter: "Account Director" },
  ];

  // Adjust durations proportionally to fit meeting duration
  const totalDefaultDuration = sections.reduce((sum, s) => sum + s.duration, 0);
  const scaleFactor = duration / totalDefaultDuration;

  return {
    type: "meeting_agenda",
    content: {
      meetingDetails: {
        title: request.title,
        date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        duration: `${duration} minutes`,
        location: "Client Office / Virtual",
        attendees: {
          client: ["Procurement Manager", "Facilities Manager", "Operations Director"],
          ourTeam: ["Account Director", "Operations Manager", "Commercial Manager"],
        }
      },
      agenda: sections.map((section, index) => ({
        ...section,
        duration: Math.round(section.duration * scaleFactor),
        startTime: sections.slice(0, index).reduce((sum, s) => sum + Math.round(s.duration * scaleFactor), 0),
        objectives: [
          "Primary objective for this section",
          "Key points to cover",
          "Desired outcome",
        ],
        materials: ["Presentation slides", "Handouts", "Demo materials"],
      })),
      preparation: {
        clientResearch: ["Recent news", "Key challenges", "Decision criteria"],
        materials: ["Presentation deck", "Contracts", "References"],
        teamBriefing: "30 minutes before meeting",
      },
      followUp: {
        immediate: ["Thank you email", "Meeting summary", "Action items"],
        timeline: [
          { day: 1, action: "Send proposal adjustments" },
          { day: 3, action: "Follow-up call" },
          { day: 7, action: "Final decision check-in" },
        ]
      }
    },
    metadata: {
      totalDuration: duration,
      breakIncluded: duration > 90,
      confidence: 0.93,
    }
  };
}

async function generateVoiceScript(ctx: any, request: any) {
  const includeVoiceScript = request.parameters.includeVoiceScript;
  const duration = request.duration;
  const wordsPerMinute = 150; // Average speaking pace
  const targetWordCount = Math.floor((duration * wordsPerMinute) / 1.2); // Allow for pauses

  return {
    type: "voice_script",
    content: {
      introduction: {
        duration: "30 seconds",
        script: `Good [morning/afternoon], and thank you for joining us today. I'm [Name], [Title] at [Company], and I'm delighted to present our proposal for ${request.title}. Over the next ${duration} minutes, we'll demonstrate how our proven expertise and innovative approach make us the ideal partner for your cleaning services needs.`,
        notes: "Warm, confident tone. Make eye contact. Smile.",
      },
      sections: [
        {
          title: "Company Overview",
          duration: "2 minutes",
          script: "Let me start by sharing who we are and what sets us apart. [Company] has been a leader in commercial cleaning services for over 25 years. We currently service more than 500 locations across Melbourne, with a team of 150+ certified professionals. What truly distinguishes us is our commitment to sustainability and innovation - we were the first in the industry to achieve carbon neutrality and continue to lead in green cleaning practices.",
          transitions: "Now, let me show you how this translates into value for your organization...",
          emphasis: ["25 years", "500 locations", "carbon neutrality"],
        },
        {
          title: "Service Capabilities",
          duration: "3 minutes",
          script: "Our comprehensive service offering covers all aspects of commercial cleaning, from daily maintenance to specialized deep cleaning. We utilize state-of-the-art equipment and eco-friendly products that not only ensure the highest standards of cleanliness but also protect the health of your staff and visitors. Our proprietary quality assurance system includes regular audits, real-time reporting, and immediate issue resolution.",
          visualCues: ["Point to service matrix", "Show equipment photos", "Display dashboard demo"],
          pacing: "Slow down for technical details",
        },
        {
          title: "Value Proposition",
          duration: "2 minutes",
          script: "When you partner with us, you're not just getting a cleaning service - you're gaining a strategic partner committed to enhancing your workplace environment. Our clients typically see a 20% reduction in cleaning costs, 35% improvement in audit scores, and 95% satisfaction rates from building occupants. We achieve this through our efficient processes, preventive maintenance approach, and dedicated account management.",
          keyPoints: ["20% cost reduction", "35% quality improvement", "95% satisfaction"],
          tone: "Confident and assured",
        }
      ],
      closing: {
        duration: "30 seconds",
        script: `Thank you for your time and attention today. We're confident that our experience, capabilities, and commitment to excellence make us the right choice for ${request.targetAudience}. We look forward to answering your questions and discussing how we can tailor our services to meet your specific needs. Together, we can create a cleaner, healthier, and more productive environment for your organization.`,
        callToAction: "Let's open the floor for questions and discussion.",
        finalNote: "Maintain energy and enthusiasm through to the end",
      },
      pronunciationGuide: {
        technicalTerms: {
          "LEED": "leed (Leadership in Energy and Environmental Design)",
          "IoT": "I-O-T (Internet of Things)",
          "KPIs": "K-P-Is (Key Performance Indicators)",
        },
        commonMistakes: [
          "Don't rush through numbers",
          "Pause after key points",
          "Emphasize differentiators",
        ]
      },
      timingMarkers: [
        { time: "0:00", section: "Introduction", cue: "Welcome and establish rapport" },
        { time: "0:30", section: "Company Overview", cue: "Build credibility" },
        { time: "2:30", section: "Service Capabilities", cue: "Demonstrate expertise" },
        { time: "5:30", section: "Value Proposition", cue: "Focus on benefits" },
        { time: "7:30", section: "Closing", cue: "Strong call to action" },
      ]
    },
    metadata: {
      totalWords: targetWordCount,
      estimatedDuration: `${duration} minutes`,
      speakingPace: `${wordsPerMinute} words per minute`,
      confidence: 0.90,
    }
  };
}

async function generatePresentationDeck(ctx: any, request: any) {
  // Generate comprehensive presentation deck
  return {
    type: "presentation_deck",
    slides: [
      {
        slideNumber: 1,
        layout: "title",
        title: request.title,
        subtitle: `Prepared for ${request.targetAudience}`,
        backgroundImage: "/templates/professional-cover.jpg",
        logo: true,
        notes: "Wait for everyone to settle before starting",
      },
      {
        slideNumber: 2,
        layout: "agenda",
        title: "Agenda",
        content: {
          items: [
            "Company Overview",
            "Our Understanding of Your Needs",
            "Proposed Solution",
            "Service Delivery Model",
            "Case Studies",
            "Pricing & Value",
            "Implementation Timeline",
            "Why Choose Us",
            "Next Steps",
          ]
        },
        transitions: "fade",
      },
      {
        slideNumber: 3,
        layout: "two-column",
        title: "About Us",
        content: {
          left: {
            type: "bullets",
            items: [
              "25+ years of industry leadership",
              "500+ active client sites",
              "150+ certified professionals",
              "24/7 service availability",
              "ISO 9001, 14001, 45001 certified",
            ]
          },
          right: {
            type: "image",
            src: "/images/company-overview.jpg",
            caption: "Excellence in commercial cleaning",
          }
        }
      },
      {
        slideNumber: 4,
        layout: "stats",
        title: "Our Track Record",
        content: {
          stats: [
            { value: "98%", label: "Client Retention Rate" },
            { value: "10,000+", label: "Monthly Services" },
            { value: "20%", label: "Average Cost Savings" },
            { value: "4.9/5", label: "Customer Satisfaction" },
          ],
          chart: {
            type: "growth",
            data: "year-over-year growth trends",
          }
        }
      },
      // Additional slides would be generated based on content type
    ],
    design: {
      theme: request.theme,
      colorScheme: {
        primary: "#1E40AF",
        secondary: "#64748B",
        accent: "#10B981",
        background: "#FFFFFF",
        text: "#1F2937",
      },
      fonts: {
        heading: "Montserrat",
        body: "Open Sans",
        size: {
          title: 48,
          heading: 32,
          body: 18,
          caption: 14,
        }
      },
      animations: {
        slideTransition: "slide",
        elementAnimation: "fade-up",
        duration: 500,
      }
    },
    metadata: {
      slideCount: 15,
      estimatedDuration: `${request.duration} minutes`,
      lastModified: Date.now(),
      version: "1.0",
      confidence: 0.91,
    }
  };
}

// Helper functions
export const getContentRequest = query({
  args: { requestId: v.id("content_requests") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.requestId);
  },
});

export const updateContentStatus = mutation({
  args: {
    requestId: v.id("content_requests"),
    status: v.string(),
    progress: v.optional(v.number()),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.requestId, {
      status: args.status,
      progress: args.progress,
      error: args.error,
      updatedAt: Date.now(),
    });
  },
});

export const saveGeneratedContent = mutation({
  args: {
    requestId: v.id("content_requests"),
    content: v.any(),
  },
  handler: async (ctx, args) => {
    const contentId = await ctx.db.insert("generated_content", {
      requestId: args.requestId,
      content: args.content,
      generatedAt: Date.now(),
      version: 1,
    });

    await ctx.db.patch(args.requestId, {
      contentId,
      status: "completed",
      completedAt: Date.now(),
    });

    return contentId;
  },
});

// Export content to various formats
export const exportContent = action({
  args: {
    contentId: v.id("generated_content"),
    format: exportFormats,
    options: v.optional(v.object({
      includeBranding: v.optional(v.boolean()),
      includeNotes: v.optional(v.boolean()),
      quality: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const content = await ctx.runQuery(api.agents.contentBuilder.getGeneratedContent, {
      contentId: args.contentId,
    });

    if (!content) {
      throw new Error("Content not found");
    }

    let exportedFile;

    switch (args.format) {
      case "pptx":
        exportedFile = await exportToPowerPoint(content, args.options);
        break;
      case "pdf":
        exportedFile = await exportToPDF(content, args.options);
        break;
      case "html":
        exportedFile = await exportToHTML(content, args.options);
        break;
      case "google_slides":
        exportedFile = await exportToGoogleSlides(content, args.options);
        break;
      case "markdown":
        exportedFile = await exportToMarkdown(content, args.options);
        break;
      case "slidev":
        exportedFile = await exportToSlidev(content, args.options);
        break;
      default:
        throw new Error(`Unsupported export format: ${args.format}`);
    }

    // Save export record
    await ctx.runMutation(api.agents.contentBuilder.saveExportRecord, {
      contentId: args.contentId,
      format: args.format,
      fileUrl: exportedFile.url,
      fileName: exportedFile.name,
      fileSize: exportedFile.size,
    });

    return exportedFile;
  },
});

// Export helper functions (these would integrate with actual libraries)
async function exportToPowerPoint(content: any, options: any) {
  // Integration with pptxgenjs or similar library
  return {
    url: "/exports/presentation.pptx",
    name: "presentation.pptx",
    size: 2048000,
    mimeType: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  };
}

async function exportToPDF(content: any, options: any) {
  // Integration with PDF generation library
  return {
    url: "/exports/presentation.pdf",
    name: "presentation.pdf",
    size: 1536000,
    mimeType: "application/pdf",
  };
}

async function exportToHTML(content: any, options: any) {
  // Generate HTML presentation
  return {
    url: "/exports/presentation.html",
    name: "presentation.html",
    size: 512000,
    mimeType: "text/html",
  };
}

async function exportToGoogleSlides(content: any, options: any) {
  // Integration with Google Slides API
  return {
    url: "https://docs.google.com/presentation/d/xxxxx",
    name: "Google Slides Presentation",
    size: 0,
    mimeType: "application/vnd.google-apps.presentation",
  };
}

async function exportToMarkdown(content: any, options: any) {
  // Convert to Markdown format
  return {
    url: "/exports/presentation.md",
    name: "presentation.md",
    size: 102400,
    mimeType: "text/markdown",
  };
}

// Mock SlidevTenderGenerator for contentBuilder
class MockSlidevTenderGenerator {
  constructor(options: any = {}) {}

  async generatePresentation(tenderData: any, options: any = {}) {
    return {
      slidesPath: `/tmp/slides-${Date.now()}.md`,
      exports: { pdf: `/tmp/presentation-${Date.now()}.pdf` },
      voiceScript: "Mock voice script"
    };
  }

  async startPresentationServer(slidesPath: string, port: number = 3030) {
    return `http://localhost:${port}`;
  }
}

async function exportToSlidev(content: any, options: any) {
  const generator = new MockSlidevTenderGenerator({
    theme: "default",
    outputDir: "./generated-presentations",
    exportFormats: ["pdf", "pptx"],
    includePresenterNotes: true,
    includeVoiceScript: true,
  });

  // Extract tender data from content
  const tenderData = {
    name: content.content?.meetingDetails?.title || "Tender Presentation",
    clientName: content.content?.targetAudience || "Valued Client",
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    sites: content.content?.siteLocations || [
      {
        name: "Main Facility",
        location: "CBD Location",
        services: ["Daily Cleaning", "Maintenance", "Security"]
      }
    ],
    requirements: content.content?.agenda?.agenda?.map((item: any) => ({
      section: item.name,
      description: item.objectives?.[0] || "Standard requirement",
      wordLimit: 500
    })) || [
      {
        section: "Service Delivery",
        description: "Comprehensive cleaning services",
        wordLimit: 500
      }
    ],
    complianceRequirements: content.content?.complianceItems?.map((item: any) => item.requirement) || [
      "ISO 9001 Quality Management",
      "OH&S Compliance",
      "Environmental Standards"
    ],
    estimatedValue: 250000,
    projectDuration: "12 months",
    keyContacts: content.content?.teamMembers || [
      {
        name: "Project Manager",
        role: "Account Manager",
        email: "<EMAIL>"
      }
    ]
  };

  try {
    const result = await generator.generatePresentation(tenderData, {
      exportFormats: ["pdf", "pptx"],
      includeVoiceScript: true,
    });

    // Start live presentation server
    const presentationUrl = await generator.startPresentationServer(result.slidesPath);

    return {
      url: presentationUrl,
      slidesPath: result.slidesPath,
      exports: result.exports,
      voiceScript: result.voiceScript,
      name: "slidev-presentation.md",
      size: 0,
      mimeType: "text/markdown",
      liveUrl: presentationUrl,
      presenterUrl: `${presentationUrl}/presenter`,
      overviewUrl: `${presentationUrl}/overview`,
    };
  } catch (error) {
    console.error("Slidev generation failed:", error);
    // Fallback to basic markdown
    return {
      url: "/exports/slidev-presentation.md",
      name: "slidev-presentation.md",
      size: 102400,
      mimeType: "text/markdown",
      error: "Slidev generation failed, using fallback"
    };
  }
}

export const getGeneratedContent = query({
  args: { contentId: v.id("generated_content") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.contentId);
  },
});

export const saveExportRecord = mutation({
  args: {
    contentId: v.id("generated_content"),
    format: exportFormats,
    fileUrl: v.string(),
    fileName: v.string(),
    fileSize: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("content_exports", {
      contentId: args.contentId,
      format: args.format,
      fileUrl: args.fileUrl,
      fileName: args.fileName,
      fileSize: args.fileSize,
      exportedAt: Date.now(),
    });
  },
});

// Quality assessment for generated content
export const assessContentQuality = action({
  args: {
    contentId: v.id("generated_content"),
  },
  handler: async (ctx, args) => {
    const content = await ctx.runQuery(api.agents.contentBuilder.getGeneratedContent, {
      contentId: args.contentId,
    });

    if (!content) {
      throw new Error("Content not found");
    }

    // Perform quality assessment
    const assessment = {
      overallScore: 0.88,
      dimensions: {
        clarity: { score: 0.92, feedback: "Clear and well-structured content" },
        relevance: { score: 0.85, feedback: "Highly relevant to target audience" },
        completeness: { score: 0.87, feedback: "Covers all key points" },
        visual_appeal: { score: 0.90, feedback: "Professional and engaging design" },
        accuracy: { score: 0.88, feedback: "Information is accurate and up-to-date" },
      },
      suggestions: [
        "Consider adding more specific client examples",
        "Include more visual elements in data-heavy sections",
        "Strengthen the call-to-action in closing slides",
      ],
      strengths: [
        "Excellent executive summary",
        "Strong visual hierarchy",
        "Clear value proposition",
      ],
    };

    // Save assessment
    await ctx.runMutation(api.agents.contentBuilder.saveQualityAssessment, {
      contentId: args.contentId,
      assessment,
    });

    return assessment;
  },
});

export const saveQualityAssessment = mutation({
  args: {
    contentId: v.id("generated_content"),
    assessment: v.any(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("content_quality_assessments", {
      contentId: args.contentId,
      assessment: args.assessment,
      assessedAt: Date.now(),
    });
  },
});