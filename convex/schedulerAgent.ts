import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Scheduler Agent Creation
export const createSchedulerAgent = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if scheduler agent already exists
    const existingAgent = await ctx.db
      .query("agents")
      .filter(q => q.eq(q.field("type"), "coordinator"))
      .filter(q => q.eq(q.field("specializations"), ["meeting_scheduler"]))
      .first();

    if (existingAgent) {
      return existingAgent._id;
    }

    // Create the scheduler agent
    const agentId = await ctx.db.insert("agents", {
      name: "Meeting Scheduler Assistant",
      description: "Intelligent meeting scheduling agent with multi-channel notifications and calendar integration",
      type: "coordinator",
      status: "active",
      capabilities: [
        "meeting_scheduling",
        "participant_selection",
        "conflict_detection",
        "calendar_integration",
        "notification_management",
        "rsvp_tracking",
        "resource_booking",
        "timezone_handling"
      ],
      specializations: ["meeting_scheduler"],
      model: "gpt-4o-mini",
      temperature: 0.3,
      maxTokens: 2000,
      systemPrompt: `You are an intelligent meeting scheduler assistant. Your responsibilities include:

1. Analyzing meeting requirements and suggesting optimal participants
2. Detecting and resolving scheduling conflicts
3. Managing multi-channel notifications (email, SMS, in-app)
4. Tracking RSVPs and sending automated reminders
5. Integrating with calendar systems and meeting platforms
6. Optimizing meeting times based on participant availability
7. Booking required resources and meeting rooms
8. Handling timezone conversions and international scheduling

When scheduling meetings:
- Prioritize required participants' availability
- Consider role-based requirements (e.g., Ops Mgr for state projects, ESG Mgr for ESG initiatives)
- Minimize conflicts and maximize attendance
- Provide clear meeting details and joining instructions
- Send timely reminders through preferred channels
- Track responses and follow up with non-responders

Be proactive in identifying potential issues and suggesting solutions.`,
      isActive: true,
      createdAt: Date.now(),
      version: "1.0.0",
      tasksCompleted: 0,
      averageQualityScore: 0,
      averageResponseTime: 0,
      successRate: 0,
      totalWords: 0,
      currentLoad: 0,
      maxConcurrentTasks: 10,
      autoAssign: true,
      priority: "high",
      qualityThreshold: 0.8,
    });

    return agentId;
  },
});

// Schedule meeting with AI assistance
export const scheduleWithAI = action({
  args: {
    request: v.string(),
    tenderId: v.optional(v.id("tenders")),
    context: v.optional(v.object({
      projectName: v.string(),
      projectType: v.string(),
      priority: v.string(),
      requirements: v.array(v.string()),
    })),
  },
  handler: async (ctx, args): Promise<any> => {
    // Get or create scheduler agent
    let agent = await ctx.runQuery(api.schedulerAgent.getSchedulerAgent);
    if (!agent) {
      const agentId = await ctx.runMutation(api.schedulerAgent.createSchedulerAgent);
      agent = await ctx.runQuery(api.agents.getAgent, { agentId });
    }

    // Parse the meeting request using AI
    const parsedRequest = await parseMeetingRequest(args.request, args.context);

    // Get suggested participants
    const suggestions: any = await ctx.runQuery(api.scheduler.suggestParticipants, {
      tenderId: args.tenderId,
      projectType: parsedRequest.projectType || args.context?.projectType || 'general',
      requirements: parsedRequest.requirements || args.context?.requirements || [],
      meetingType: parsedRequest.type,
      preferredTime: parsedRequest.preferredTime,
      duration: parsedRequest.duration,
    });

    // Select participants based on AI recommendations
    const selectedParticipants: any = suggestions
      .filter((s: any) => s.isRequired || (s.isRecommended && s.availability.isAvailable))
      .slice(0, parsedRequest.maxParticipants || 10)
      .map((s: any) => ({
        contactId: s.contact.id,
        role: s.contact.role,
        isRequired: s.isRequired,
        responseStatus: 'pending' as const,
      }));

    // Find optimal time slot if needed
    let finalStartTime = parsedRequest.preferredTime;
    if (parsedRequest.findOptimalTime) {
      const optimalSlot = await findOptimalTimeSlot(
        selectedParticipants,
        parsedRequest.duration,
        parsedRequest.constraints
      );
      if (optimalSlot) {
        finalStartTime = optimalSlot.start;
      }
    }

    // Create the meeting
    const meetingId: any = await ctx.runMutation(api.scheduler.createMeeting, {
      title: parsedRequest.title,
      description: parsedRequest.description,
      type: parsedRequest.type,
      tenderId: args.tenderId,
      projectContext: args.context,
      startTime: finalStartTime,
      endTime: finalStartTime + (parsedRequest.duration * 60 * 1000),
      timezone: parsedRequest.timezone,
      location: parsedRequest.location,
      participants: selectedParticipants,
      reminders: parsedRequest.reminders,
      agenda: parsedRequest.agenda,
    });

    // Generate meeting links if online
    if (parsedRequest.location.type === 'online') {
      await ctx.runAction(api.scheduler.generateMeetingLinks, {
        meetingId,
        platform: parsedRequest.platform || 'teams',
      });
    }

    // Return meeting details
    return {
      meetingId,
      title: parsedRequest.title,
      startTime: finalStartTime,
      participants: selectedParticipants.length,
      platform: parsedRequest.platform,
      status: 'scheduled',
    };
  },
});

// Get scheduler agent
export const getSchedulerAgent = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db
      .query("agents")
      .filter(q => q.eq(q.field("type"), "coordinator"))
      .filter(q => q.eq(q.field("specializations"), ["meeting_scheduler"]))
      .first();
  },
});

// Automated meeting optimization
export const optimizeMeetingSchedule = action({
  args: {
    meetingIds: v.array(v.id("scheduler_meetings")),
    constraints: v.object({
      dateRange: v.object({
        start: v.number(),
        end: v.number(),
      }),
      minimizeConflicts: v.boolean(),
      maximizeAttendance: v.boolean(),
      preferredTimes: v.optional(v.array(v.object({
        dayOfWeek: v.number(),
        startHour: v.number(),
        endHour: v.number(),
      }))),
    }),
  },
  handler: async (ctx, args): Promise<any> => {
    const meetings: any = await Promise.all(
      args.meetingIds.map((id: any) =>
        ctx.runQuery(api.scheduler.getMeeting, { meetingId: id })
      )
    );

    // Analyze current schedule
    const analysis = analyzeSchedule(meetings, args.constraints);

    // Generate optimization recommendations
    const recommendations = [];
    
    for (const meeting: any of meetings) {
      if (!meeting) continue;

      const conflicts = analysis.conflicts.filter((c: any) =>
        c.meetings.includes(meeting._id)
      );

      if (conflicts.length > 0) {
        // Find alternative time slots
        const alternatives = await findAlternativeSlots(
          meeting,
          args.constraints
        );

        recommendations.push({
          meetingId: meeting._id,
          currentTime: meeting.startTime,
          proposedTimes: alternatives,
          conflictCount: conflicts.length,
          attendanceImprovement: calculateAttendanceImprovement(
            meeting,
            alternatives[0]
          ),
        });
      }
    }

    return {
      analysis,
      recommendations,
      potentialImprovement: {
        conflictReduction: analysis.conflicts.length - recommendations.length,
        attendanceIncrease: recommendations.reduce((sum, r) => 
          sum + r.attendanceImprovement, 0
        ),
      },
    };
  },
});

// Smart RSVP follow-up
export const automatedRSVPFollowUp = action({
  args: {
    meetingId: v.id("scheduler_meetings"),
    strategy: v.string(), // 'gentle', 'urgent', 'final'
  },
  handler: async (ctx, args): Promise<any> => {
    const meeting: any = await ctx.runQuery(api.scheduler.getMeeting, {
      meetingId: args.meetingId 
    });

    if (!meeting) {
      throw new Error("Meeting not found");
    }

    const hoursUntilMeeting = (meeting.startTime - Date.now()) / (1000 * 60 * 60);
    const nonResponders: any = meeting.participants.filter((p: any) =>
      p.responseStatus === 'pending'
    );

    if (nonResponders.length === 0) {
      return { message: "All participants have responded" };
    }

    // Determine follow-up approach based on strategy and timing
    let messageTemplate;
    let channels: string[] = [];

    switch (args.strategy) {
      case 'gentle':
        if (hoursUntilMeeting > 48) {
          messageTemplate = "friendly_reminder";
          channels = ['email'];
        }
        break;
      
      case 'urgent':
        if (hoursUntilMeeting < 48 && hoursUntilMeeting > 24) {
          messageTemplate = "urgent_reminder";
          channels = ['email', 'sms'];
        }
        break;
      
      case 'final':
        if (hoursUntilMeeting < 24) {
          messageTemplate = "final_reminder";
          channels = ['email', 'sms', 'push'];
        }
        break;
    }

    // Send targeted follow-ups
    for (const participant of nonResponders) {
      const contact = await ctx.runQuery(api.scheduler.getContact, { 
        contactId: participant.contactId 
      });

      if (!contact) continue;

      // Personalize message based on participant importance
      const isRequired = participant.isRequired;
      const personalizedChannels = isRequired ? 
        [...channels, 'phone'] : channels;

      await ctx.runAction(api.scheduler.sendNudge, {
        meetingId: args.meetingId,
        targetContacts: [participant.contactId],
      });
    }

    return {
      nonResponders: nonResponders.length,
      strategy: args.strategy,
      channelsUsed: channels,
      nextFollowUp: calculateNextFollowUp(hoursUntilMeeting),
    };
  },
});

// Meeting analytics and insights
export const generateMeetingInsights = action({
  args: {
    periodStart: v.number(),
    periodEnd: v.number(),
    tenderId: v.optional(v.id("tenders")),
  },
  handler: async (ctx, args): Promise<any> => {
    // Get meetings in period
    const meetings: any = await ctx.runQuery(api.scheduler.getMeetingsInPeriod, {
      periodStart: args.periodStart,
      periodEnd: args.periodEnd,
      tenderId: args.tenderId,
    });

    // Analyze patterns
    const insights: any = {
      summary: {
        totalMeetings: meetings.length,
        totalHours: meetings.reduce((sum: any, m: any) =>
          sum + (m.endTime - m.startTime) / (1000 * 60 * 60), 0
        ),
        averageDuration: meetings.length > 0 ?
          meetings.reduce((sum: any, m: any) =>
            sum + (m.endTime - m.startTime), 0
          ) / meetings.length / (1000 * 60) : 0,
        averageParticipants: meetings.length > 0 ?
          meetings.reduce((sum: any, m: any) =>
            sum + m.participants.length, 0
          ) / meetings.length : 0,
      },
      patterns: {
        busiestDays: analyzeBusiestDays(meetings),
        peakHours: analyzePeakHours(meetings),
        commonConflicts: analyzeCommonConflicts(meetings),
        participantEngagement: analyzeParticipantEngagement(meetings),
      },
      recommendations: generateSchedulingRecommendations(meetings),
      tenderSpecific: args.tenderId ? {
        meetingFrequency: calculateMeetingFrequency(meetings),
        keyParticipants: identifyKeyParticipants(meetings),
        criticalMeetings: identifyCriticalMeetings(meetings),
      } : undefined,
    };

    // Store analytics
    await ctx.runMutation(api.scheduler.storeAnalytics, {
      periodStart: args.periodStart,
      periodEnd: args.periodEnd,
      metrics: {
        totalMeetings: insights.summary.totalMeetings,
        averageAttendance: calculateAverageAttendance(meetings),
        rsvpResponseRate: calculateRSVPRate(meetings),
        averageLeadTime: calculateAverageLeadTime(meetings),
        conflictRate: calculateConflictRate(meetings),
        cancellationRate: calculateCancellationRate(meetings),
        platformUsage: calculatePlatformUsage(meetings),
        departmentBreakdown: calculateDepartmentBreakdown(meetings),
        meetingTypeBreakdown: calculateTypeBreakdown(meetings),
        peakTimes: insights.patterns.peakHours,
        topOrganizers: calculateTopOrganizers(meetings),
        resourceUtilization: {},
      },
    });

    return insights;
  },
});

// Helper functions
async function parseMeetingRequest(request: string, context: any): Promise<any> {
  // Mock AI parsing - in production, use actual AI service
  const keywords = request.toLowerCase();
  
  return {
    title: extractTitle(request),
    description: request,
    type: detectMeetingType(keywords),
    preferredTime: detectPreferredTime(keywords),
    duration: detectDuration(keywords) || 60,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    location: detectLocation(keywords),
    platform: detectPlatform(keywords),
    maxParticipants: 10,
    findOptimalTime: keywords.includes('find best time'),
    requirements: extractRequirements(request),
    projectType: context?.projectType || detectProjectType(keywords),
    reminders: [
      { type: 'notification', minutes: 15, channel: 'email' },
      { type: 'notification', minutes: 1440, channel: 'email' }
    ],
    agenda: extractAgenda(request),
    constraints: {
      excludeWeekends: true,
      excludeHolidays: true,
      minNoticeHours: 24,
    },
  };
}

function extractTitle(request: string): string {
  // Simple extraction - enhance with AI
  const match = request.match(/meeting about (.+?)(?:\.|,|$)/i);
  return match ? match[1] : "Meeting";
}

function detectMeetingType(keywords: string): any {
  if (keywords.includes('kickoff')) return 'tender_kickoff';
  if (keywords.includes('technical') || keywords.includes('review')) return 'technical_review';
  if (keywords.includes('bid')) return 'bid_review';
  if (keywords.includes('client')) return 'client_meeting';
  if (keywords.includes('training')) return 'training';
  if (keywords.includes('workshop')) return 'workshop';
  return 'internal_sync';
}

function detectPreferredTime(keywords: string): number {
  const now = Date.now();
  if (keywords.includes('tomorrow')) return now + 24 * 60 * 60 * 1000;
  if (keywords.includes('next week')) return now + 7 * 24 * 60 * 60 * 1000;
  if (keywords.includes('urgent') || keywords.includes('asap')) return now + 4 * 60 * 60 * 1000;
  return now + 24 * 60 * 60 * 1000; // Default to tomorrow
}

function detectDuration(keywords: string): number | null {
  const match = keywords.match(/(\d+)\s*(hour|minute|min)/);
  if (match) {
    const value = parseInt(match[1]);
    const unit = match[2];
    return unit.includes('hour') ? value * 60 : value;
  }
  return null;
}

function detectLocation(keywords: string): any {
  if (keywords.includes('online') || keywords.includes('virtual')) {
    return { type: 'online', value: 'Virtual Meeting' };
  }
  if (keywords.includes('office') || keywords.includes('room')) {
    return { type: 'physical', value: 'Main Office' };
  }
  return { type: 'online', value: 'Virtual Meeting' };
}

function detectPlatform(keywords: string): string {
  if (keywords.includes('teams')) return 'teams';
  if (keywords.includes('zoom')) return 'zoom';
  if (keywords.includes('meet')) return 'meet';
  return 'teams'; // Default
}

function extractRequirements(request: string): string[] {
  const requirements = [];
  if (request.includes('ESG')) requirements.push('ESG expertise');
  if (request.includes('state')) requirements.push('state-specific knowledge');
  if (request.includes('technical')) requirements.push('technical expertise');
  if (request.includes('compliance')) requirements.push('compliance knowledge');
  return requirements;
}

function detectProjectType(keywords: string): string {
  if (keywords.includes('esg')) return 'ESG';
  if (keywords.includes('operations')) return 'Operations';
  if (keywords.includes('technical')) return 'Technical';
  return 'General';
}

function extractAgenda(request: string): any[] {
  // Simple agenda extraction - enhance with AI
  const agendaMatch = request.match(/agenda:(.+?)(?:\.|$)/i);
  if (agendaMatch) {
    const items = agendaMatch[1].split(',').map(item => ({
      item: item.trim(),
      duration: 15, // Default duration
    }));
    return items;
  }
  return [];
}

async function findOptimalTimeSlot(participants: any[], duration: number, constraints: any) {
  // Mock optimization - in production, use proper algorithm
  const baseTime = Date.now() + 24 * 60 * 60 * 1000;
  return {
    start: baseTime,
    end: baseTime + duration * 60 * 1000,
    score: 0.9,
    availableParticipants: participants.map(p => p.contactId),
    conflicts: [],
    recommendation: "Best available slot with minimal conflicts",
  };
}

function analyzeSchedule(meetings: any[], constraints: any) {
  return {
    totalMeetings: meetings.length,
    conflicts: [],
    overlaps: [],
    gaps: [],
    utilization: 0.7,
  };
}

async function findAlternativeSlots(meeting: any, constraints: any) {
  // Mock alternative slots
  return [
    {
      start: meeting.startTime + 60 * 60 * 1000,
      end: meeting.endTime + 60 * 60 * 1000,
      score: 0.95,
    },
    {
      start: meeting.startTime + 24 * 60 * 60 * 1000,
      end: meeting.endTime + 24 * 60 * 60 * 1000,
      score: 0.85,
    },
  ];
}

function calculateAttendanceImprovement(meeting: any, newSlot: any): number {
  // Mock calculation
  return Math.random() * 20;
}

function calculateNextFollowUp(hoursUntilMeeting: number): string {
  if (hoursUntilMeeting > 72) return "48 hours";
  if (hoursUntilMeeting > 48) return "24 hours";
  if (hoursUntilMeeting > 24) return "12 hours";
  return "No further follow-up";
}

// Analytics helper functions
function analyzeBusiestDays(meetings: any[]) {
  const dayCount: Record<string, number> = {};
  meetings.forEach(m => {
    const day = new Date(m.startTime).toLocaleDateString('en-US', { weekday: 'long' });
    dayCount[day] = (dayCount[day] || 0) + 1;
  });
  return Object.entries(dayCount).sort((a, b) => b[1] - a[1]);
}

function analyzePeakHours(meetings: any[]) {
  const hourCount: Record<number, number> = {};
  meetings.forEach(m => {
    const hour = new Date(m.startTime).getHours();
    hourCount[hour] = (hourCount[hour] || 0) + 1;
  });
  return Object.entries(hourCount)
    .map(([hour, count]) => ({
      hour: parseInt(hour),
      day: 'All',
      count,
    }))
    .sort((a, b) => b.count - a.count);
}

function analyzeCommonConflicts(meetings: any[]) {
  // Analyze common conflict patterns
  return [];
}

function analyzeParticipantEngagement(meetings: any[]) {
  // Analyze participant engagement patterns
  return {
    averageResponseTime: 24, // hours
    responseRate: 0.92,
    attendanceRate: 0.87,
  };
}

function generateSchedulingRecommendations(meetings: any[]) {
  return [
    "Schedule important meetings on Tuesday-Thursday mornings for best attendance",
    "Allow at least 15 minute buffers between back-to-back meetings",
    "Send reminders 24 hours and 1 hour before meetings for optimal response",
  ];
}

function calculateMeetingFrequency(meetings: any[]) {
  return meetings.length > 0 ? 
    meetings.length / ((meetings[meetings.length - 1].startTime - meetings[0].startTime) / (1000 * 60 * 60 * 24 * 7)) : 0;
}

function identifyKeyParticipants(meetings: any[]) {
  const participantCount: Record<string, number> = {};
  meetings.forEach(m => {
    m.participants.forEach((p: any) => {
      participantCount[p.contactId] = (participantCount[p.contactId] || 0) + 1;
    });
  });
  return Object.entries(participantCount)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5);
}

function identifyCriticalMeetings(meetings: any[]) {
  return meetings.filter(m => 
    m.type === 'tender_kickoff' || 
    m.type === 'bid_review' ||
    m.participants.length > 5
  );
}

// Calculation functions for analytics
function calculateAverageAttendance(meetings: any[]): number {
  if (meetings.length === 0) return 0;
  const total = meetings.reduce((sum, m) => 
    sum + (m.rsvpStats.accepted / m.participants.length), 0
  );
  return total / meetings.length;
}

function calculateRSVPRate(meetings: any[]): number {
  if (meetings.length === 0) return 0;
  const total = meetings.reduce((sum, m) => 
    sum + ((m.participants.length - m.rsvpStats.pending) / m.participants.length), 0
  );
  return total / meetings.length;
}

function calculateAverageLeadTime(meetings: any[]): number {
  if (meetings.length === 0) return 0;
  const total = meetings.reduce((sum, m) => 
    sum + ((m.startTime - m.createdAt) / (1000 * 60 * 60)), 0
  );
  return total / meetings.length;
}

function calculateConflictRate(meetings: any[]): number {
  const withConflicts = meetings.filter(m => m.conflicts && m.conflicts.length > 0);
  return meetings.length > 0 ? withConflicts.length / meetings.length : 0;
}

function calculateCancellationRate(meetings: any[]): number {
  const cancelled = meetings.filter(m => m.status === 'cancelled');
  return meetings.length > 0 ? cancelled.length / meetings.length : 0;
}

function calculatePlatformUsage(meetings: any[]): Record<string, number> {
  const usage: Record<string, number> = {};
  meetings.forEach(m => {
    if (m.location.type === 'online') {
      const platform = m.meetingLinks?.teams ? 'teams' : 
                      m.meetingLinks?.zoom ? 'zoom' : 
                      m.meetingLinks?.meet ? 'meet' : 'other';
      usage[platform] = (usage[platform] || 0) + 1;
    }
  });
  return usage;
}

function calculateDepartmentBreakdown(meetings: any[]): Record<string, number> {
  // Would need to look up participant departments
  return {};
}

function calculateTypeBreakdown(meetings: any[]): Record<string, number> {
  const breakdown: Record<string, number> = {};
  meetings.forEach(m => {
    breakdown[m.type] = (breakdown[m.type] || 0) + 1;
  });
  return breakdown;
}

function calculateTopOrganizers(meetings: any[]): any[] {
  const organizers: Record<string, number> = {};
  meetings.forEach(m => {
    organizers[m.organizer] = (organizers[m.organizer] || 0) + 1;
  });
  return Object.entries(organizers)
    .map(([userId, count]) => ({ userId, meetingCount: count }))
    .sort((a, b) => b.meetingCount - a.meetingCount)
    .slice(0, 5);
}

// Additional queries
export const getMeetingsInPeriod = query({
  args: {
    periodStart: v.number(),
    periodEnd: v.number(),
    tenderId: v.optional(v.id("tenders")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db
      .query("scheduler_meetings")
      .filter(q => 
        q.and(
          q.gte(q.field("startTime"), args.periodStart),
          q.lte(q.field("startTime"), args.periodEnd)
        )
      );

    const filteredQuery = args.tenderId
      ? baseQuery.filter(q => q.eq(q.field("tenderId"), args.tenderId))
      : baseQuery;

    return await filteredQuery.collect();
  },
});

export const storeAnalytics = mutation({
  args: {
    periodStart: v.number(),
    periodEnd: v.number(),
    metrics: v.object({
      totalMeetings: v.number(),
      averageAttendance: v.number(),
      rsvpResponseRate: v.number(),
      averageLeadTime: v.number(),
      conflictRate: v.number(),
      cancellationRate: v.number(),
      platformUsage: v.object({}),
      departmentBreakdown: v.object({}),
      meetingTypeBreakdown: v.object({}),
      peakTimes: v.array(v.object({
        hour: v.number(),
        day: v.string(),
        count: v.number(),
      })),
      topOrganizers: v.array(v.object({
        userId: v.string(),
        meetingCount: v.number(),
      })),
      resourceUtilization: v.object({}),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("scheduler_analytics", {
      periodStart: args.periodStart,
      periodEnd: args.periodEnd,
      metrics: args.metrics,
      generatedAt: Date.now(),
    });
  },
});