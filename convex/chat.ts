import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";

// Chat thread management
export const createChatThread = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    type: v.string(),
    isPrivate: v.boolean(),
    allowFileUploads: v.boolean(),
    allowAgentAccess: v.boolean(),
    tenderId: v.optional(v.id("tenders")),
    sectionId: v.optional(v.id("bidSections")),
    tags: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const threadId = await ctx.db.insert("chat_threads", {
      name: args.name,
      description: args.description,
      type: args.type,
      status: "active",
      createdBy: identity.subject,
      createdAt: Date.now(),
      lastActivity: Date.now(),
      messageCount: 0,
      participantCount: 1,
      isPrivate: args.isPrivate,
      allowFileUploads: args.allowFileUploads,
      allowAgentAccess: args.allowAgentAccess,
      retentionDays: 365,
      moderationLevel: "basic",
      tenderId: args.tenderId,
      sectionId: args.sectionId,
      projectId: null,
      tags: args.tags,
      metadata: {
        unreadCount: 0,
        isNotificationEnabled: true,
        pinnedMessageIds: [],
      },
    });

    // Add creator as participant
    await ctx.db.insert("chat_participants", {
      threadId,
      userId: identity.subject,
      type: "user",
      name: identity.name || "Unknown User",
      avatar: identity.pictureUrl,
      role: "owner",
      permissions: ["read_messages", "send_messages", "send_files", "manage_participants", "manage_settings"],
      joinedAt: Date.now(),
      isOnline: true,
      status: "active",
      messageCount: 0,
      unreadCount: 0,
      notifications: true,
      soundEnabled: true,
      mentionOnly: false,
    });

    return threadId;
  },
});

export const getChatThreads = query({
  args: {
    tenderId: v.optional(v.id("tenders")),
    sectionId: v.optional(v.id("bidSections")),
    type: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("chat_threads");
    
    const filteredQuery = args.tenderId
      ? baseQuery.filter(q => q.eq(q.field("tenderId"), args.tenderId))
      : baseQuery;
    
    const sectionFilteredQuery = args.sectionId
      ? filteredQuery.filter(q => q.eq(q.field("sectionId"), args.sectionId))
      : filteredQuery;
    
    const typeFilteredQuery = args.type
      ? sectionFilteredQuery.filter(q => q.eq(q.field("type"), args.type))
      : sectionFilteredQuery;

    const threads = await typeFilteredQuery
      .order("desc")
      .take(args.limit || 50);

    // Get participant info for each thread
    const threadsWithParticipants = await Promise.all(
      threads.map(async (thread) => {
        const participants = await ctx.db
          .query("chat_participants")
          .withIndex("by_thread", (q) => q.eq("threadId", thread._id))
          .collect();
        
        const userParticipant = participants.find(p => p.userId === identity.subject);
        const hasAccess = !thread.isPrivate || userParticipant;
        
        return hasAccess ? {
          ...thread,
          participants: participants.length,
          userRole: userParticipant?.role || "guest",
          unreadCount: userParticipant?.unreadCount || 0,
        } : null;
      })
    );

    return threadsWithParticipants.filter(Boolean);
  },
});

export const getChatThread = query({
  args: { threadId: v.id("chat_threads") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    // Check access permissions
    const participant = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .filter(q => q.eq(q.field("userId"), identity.subject))
      .first();

    if (thread.isPrivate && !participant) {
      throw new Error("Access denied");
    }

    const participants = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .collect();

    return {
      ...thread,
      participants,
      userRole: participant?.role || "guest",
      userPermissions: participant?.permissions || [],
    };
  },
});

// Chat message management
export const sendMessage = mutation({
  args: {
    threadId: v.id("chat_threads"),
    content: v.string(),
    type: v.string(),
    replyToId: v.optional(v.id("chat_messages")),
    mentions: v.array(v.string()),
    attachments: v.array(v.object({
      id: v.string(),
      type: v.string(),
      name: v.string(),
      url: v.string(),
      size: v.number(),
      mimeType: v.string(),
      thumbnail: v.optional(v.string()),
    })),
    metadata: v.optional(v.object({
      tenderId: v.optional(v.id("tenders")),
      sectionId: v.optional(v.id("bidSections")),
      agentTaskId: v.optional(v.string()),
      workflowId: v.optional(v.string()),
      priority: v.optional(v.string()),
      tags: v.array(v.string()),
      context: v.optional(v.object({})),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify thread access
    const thread = await ctx.db.get(args.threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    const participant = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .filter(q => q.eq(q.field("userId"), identity.subject))
      .first();

    if (thread.isPrivate && !participant) {
      throw new Error("Access denied");
    }

    if (participant && !participant.permissions.includes("send_messages")) {
      throw new Error("Permission denied");
    }

    const messageId = await ctx.db.insert("chat_messages", {
      threadId: args.threadId,
      senderId: identity.subject,
      senderType: "user",
      type: args.type,
      content: args.content,
      timestamp: Date.now(),
      status: "sent",
      replyToId: args.replyToId,
      mentions: args.mentions,
      attachments: args.attachments,
      reactions: [],
      metadata: args.metadata || {
        tags: [],
      },
    });

    // Update thread activity
    await ctx.db.patch(args.threadId, {
      lastActivity: Date.now(),
      lastMessageId: messageId,
      messageCount: thread.messageCount + 1,
    });

    // Update participant message count
    if (participant) {
      await ctx.db.patch(participant._id, {
        messageCount: participant.messageCount + 1,
        lastSeen: Date.now(),
      });
    }

    // Update unread counts for other participants
    const otherParticipants = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .filter(q => q.neq(q.field("userId"), identity.subject))
      .collect();

    await Promise.all(
      otherParticipants.map(p => 
        ctx.db.patch(p._id, {
          unreadCount: p.unreadCount + 1,
        })
      )
    );

    return messageId;
  },
});

export const getChatMessages = query({
  args: {
    threadId: v.id("chat_threads"),
    limit: v.optional(v.number()),
    cursor: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Verify thread access
    const thread = await ctx.db.get(args.threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    const participant = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .filter(q => q.eq(q.field("userId"), identity.subject))
      .first();

    if (thread.isPrivate && !participant) {
      throw new Error("Access denied");
    }

    if (participant && !participant.permissions.includes("read_messages")) {
      throw new Error("Permission denied");
    }

    const messages = await ctx.db
      .query("chat_messages")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .order("desc")
      .take(args.limit || 50);

    return messages.reverse();
  },
});

export const markMessagesAsRead = mutation({
  args: {
    threadId: v.id("chat_threads"),
    messageIds: v.array(v.id("chat_messages")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const participant = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", args.threadId))
      .filter(q => q.eq(q.field("userId"), identity.subject))
      .first();

    if (participant) {
      await ctx.db.patch(participant._id, {
        unreadCount: 0,
        lastSeen: Date.now(),
      });
    }

    // Mark messages as read
    await Promise.all(
      args.messageIds.map(messageId => 
        ctx.db.patch(messageId, {
          status: "read",
        })
      )
    );
  },
});

export const addReaction = mutation({
  args: {
    messageId: v.id("chat_messages"),
    emoji: v.string(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const message = await ctx.db.get(args.messageId);
    if (!message) {
      throw new Error("Message not found");
    }

    // Verify thread access
    const thread = await ctx.db.get(message.threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    const participant = await ctx.db
      .query("chat_participants")
      .withIndex("by_thread", (q) => q.eq("threadId", message.threadId))
      .filter(q => q.eq(q.field("userId"), identity.subject))
      .first();

    if (thread.isPrivate && !participant) {
      throw new Error("Access denied");
    }

    const reactions = [...message.reactions];
    const existingReaction = reactions.find(r => r.emoji === args.emoji);

    if (existingReaction) {
      if (existingReaction.users.includes(identity.subject)) {
        // Remove reaction
        existingReaction.users = existingReaction.users.filter((u: string) => u !== identity.subject);
        existingReaction.count--;
        if (existingReaction.count === 0) {
          const index = reactions.indexOf(existingReaction);
          reactions.splice(index, 1);
        }
      } else {
        // Add reaction
        existingReaction.users.push(identity.subject);
        existingReaction.count++;
      }
    } else {
      // New reaction
      reactions.push({
        emoji: args.emoji,
        count: 1,
        users: [identity.subject],
      });
    }

    await ctx.db.patch(args.messageId, {
      reactions,
    });
  },
});

// Chat search
export const searchMessages = query({
  args: {
    query: v.string(),
    threadId: v.optional(v.id("chat_threads")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let searchResults = await ctx.db
      .query("chat_messages")
      .withSearchIndex("search_messages", (q) => {
        const searchQuery = q.search("content", args.query);
        return args.threadId
          ? searchQuery.eq("threadId", args.threadId)
          : searchQuery;
      })
      .take(args.limit || 25);

    // Filter by thread access permissions
    const accessibleMessages = await Promise.all(
      searchResults.map(async (message) => {
        const thread = await ctx.db.get(message.threadId);
        if (!thread) return null;

        if (thread.isPrivate) {
          const participant = await ctx.db
            .query("chat_participants")
            .withIndex("by_thread", (q) => q.eq("threadId", message.threadId))
            .filter(q => q.eq(q.field("userId"), identity.subject))
            .first();
          
          if (!participant) return null;
        }

        return {
          ...message,
          threadName: thread.name,
        };
      })
    );

    return accessibleMessages.filter(Boolean);
  },
});