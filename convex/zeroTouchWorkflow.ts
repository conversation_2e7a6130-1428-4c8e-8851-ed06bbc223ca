"use node";

import { action, internalMutation, internalQuery, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { internal, api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Main Zero-Touch Tender Workflow Orchestrator
export const initiateZeroTouchWorkflow = action({
  args: {
    sourceType: v.string(), // "email", "portal", "manual"
    sourceData: v.any(), // Email data, file data, etc.
    fileId: v.optional(v.id("files")),
    priority: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<{ success: boolean; workflowId?: Id<"zero_touch_workflows">; error?: string }> => {
    console.log("🚀 Initiating Zero-Touch Tender Workflow");
    
    // Create workflow instance
    const workflowId: Id<"zero_touch_workflows"> = await ctx.runMutation(internal.zeroTouchWorkflow.createWorkflowInstance, {
      sourceType: args.sourceType,
      sourceData: args.sourceData,
      priority: args.priority || "normal",
    });

    // Log workflow initiation
    await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
      workflowId,
      stage: "INITIATED",
      event: "workflow_started",
      data: { sourceType: args.sourceType },
    });

    try {
      // STEP 1: Document Parsing and Ingestion
      console.log("📄 Starting document parsing...");
      const parseResult = await runDocumentParsing(ctx, workflowId, args.fileId);
      
      if (!parseResult.success) {
        throw new Error(`Document parsing failed: ${parseResult.error}`);
      }

      // Create tender record from parsed data
      const tenderId = await ctx.runMutation(internal.tenders.createFromDocument, {
        name: parseResult.data.tenderName,
        clientName: parseResult.data.clientName,
        dueDate: parseResult.data.dueDate,
        estimatedValue: parseResult.data.estimatedValue,
        sections: parseResult.data.sections || [],
      });

      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId,
        tenderId,
        stage: "INGESTED",
        status: "processing",
      });

      // STEP 2: Parallel Agent Execution
      console.log("🤖 Starting parallel agent execution...");
      const agentResults = await executeParallelAgents(ctx, workflowId, tenderId, parseResult.data);

      // STEP 3: Meeting Scheduling
      const meetingId = agentResults.scheduler.meetingId;
      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId,
        meetingId,
        stage: "SCHEDULED",
        status: "scheduled",
      });

      // STEP 4: Content Generation Complete
      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId,
        stage: "CONTENT_READY",
        status: "ready_for_meeting",
        contentData: agentResults.contentBuilder,
      });

      console.log("✅ Zero-Touch Workflow initiated successfully");
      return {
        success: true,
        workflowId,
        // tenderId, // Remove this property as it's not in the return type
        meetingId,
        data: {
          parseResult: parseResult.data,
          agentResults,
        },
      };

    } catch (error) {
      console.error("❌ Zero-Touch Workflow failed:", error);
      
      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId,
        stage: "FAILED",
        status: "error",
        error: error instanceof Error ? error.message : String(error),
      });

      return {
        success: false,
        workflowId,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Document Parsing Execution
async function runDocumentParsing(ctx: any, workflowId: string, fileId?: string) {
  await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
    workflowId,
    stage: "PARSING",
    event: "document_parsing_started",
    data: { fileId },
  });

  try {
    if (!fileId) {
      throw new Error("No file provided for parsing");
    }

    // Execute document parser agent
    const parseResult = await ctx.runAction(api.documentParser.processDocumentWithOCR, {
      fileId,
      processingOptions: {
        enableOCR: true,
        extractStructure: true,
        extractRequirements: true,
        extractEntities: true,
        confidenceThreshold: 0.7,
      },
    });

    await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
      workflowId,
      stage: "PARSING",
      event: "document_parsing_completed",
      data: { confidence: parseResult.confidence, wordCount: parseResult.wordCount },
    });

    return { success: true, data: parseResult };

  } catch (error) {
    await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
      workflowId,
      stage: "PARSING",
      event: "document_parsing_failed",
      data: { error: error instanceof Error ? error.message : String(error) },
    });

    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

// Parallel Agent Execution
async function executeParallelAgents(ctx: any, workflowId: string, tenderId: string, tenderData: any) {
  await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
    workflowId,
    stage: "AGENT_EXECUTION",
    event: "parallel_agents_started",
    data: { tenderId },
  });

  // Execute agents in parallel
  const [schedulerResult, contentBuilderResult] = await Promise.all([
    executeSchedulerAgent(ctx, workflowId, tenderId, tenderData),
    executeContentBuilderAgent(ctx, workflowId, tenderId, tenderData),
  ]);

  await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
    workflowId,
    stage: "AGENT_EXECUTION",
    event: "parallel_agents_completed",
    data: {
      scheduler: schedulerResult.success,
      contentBuilder: contentBuilderResult.success,
    },
  });

  return {
    scheduler: schedulerResult,
    contentBuilder: contentBuilderResult,
  };
}

// Scheduler Agent Execution
async function executeSchedulerAgent(ctx: any, workflowId: string, tenderId: string, tenderData: any) {
  try {
    const meetingRequest = {
      tenderId,
      title: `Tender Kick-off: ${tenderData.tenderName}`,
      duration: 60,
      urgency: "normal",
      requiredRoles: determineRequiredRoles(tenderData),
      description: `Automated kick-off meeting for ${tenderData.tenderName} - ${tenderData.clientName}`,
      deadline: tenderData.dueDate,
    };

    const result = await ctx.runAction(internal.schedulerAgent.scheduleIntelligentMeeting, {
      request: meetingRequest,
    });

    return { success: true, meetingId: result.meetingId, data: result };

  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

// Content Builder Agent Execution
async function executeContentBuilderAgent(ctx: any, workflowId: string, tenderId: string, tenderData: any) {
  try {
    const contentRequest = {
      tenderId,
      contentType: "executive_summary",
      title: `${tenderData.tenderName} - Tender Kick-off Presentation`,
      targetAudience: "stakeholder_kickoff",
      duration: 60,
      parameters: {
        clientName: tenderData.clientName,
        tenderValue: tenderData.estimatedValue,
        dueDate: tenderData.dueDate,
        siteCount: tenderData.siteCount,
        states: tenderData.states || [],
        keyRequirements: tenderData.requirements || [],
        complianceFlags: tenderData.complianceFlags || [],
      },
    };

    const result = await ctx.runAction(internal.agents.contentBuilder.createContentRequest, contentRequest);

    // Also generate Slidev presentation
    const slidevResult = await ctx.runAction(api.slidevIntegration.generateSlidevPresentation, {
      tenderId,
      options: {
        theme: "default",
        includeVoiceScript: true,
        exportFormats: ["pdf", "pptx"],
        autoStart: true,
      }
    });

    return { 
      success: true, 
      contentId: result.id, 
      slidevId: slidevResult.presentationId,
      data: result,
      slidev: {
        presentationUrl: slidevResult.urls.presentation,
        presenterUrl: slidevResult.urls.presenter,
        exports: slidevResult.exports,
      }
    };

  } catch (error) {
    return { success: false, error: error instanceof Error ? error.message : String(error) };
  }
}

// Determine required meeting roles based on tender data
function determineRequiredRoles(tenderData: any): string[] {
  const roles = ["project_manager", "proposal_manager"];

  // Add state-specific operations managers
  if (tenderData.states && tenderData.states.length > 0) {
    tenderData.states.forEach((state: string) => {
      roles.push(`ops_manager_${state.toLowerCase()}`);
    });
  }

  // Add ESG manager if ESG weight > 0
  if (tenderData.esgWeight && tenderData.esgWeight > 0) {
    roles.push("esg_manager");
  }

  // Add technical lead for complex projects
  if (tenderData.estimatedValue && tenderData.estimatedValue > 1000000) {
    roles.push("technical_lead");
  }

  // Add compliance manager for compliance-heavy tenders
  if (tenderData.complianceFlags && tenderData.complianceFlags.length > 3) {
    roles.push("compliance_manager");
  }

  return roles;
}

// Meeting Execution Handler
export const handleMeetingExecution = action({
  args: {
    workflowId: v.string(),
    meetingId: v.string(),
    meetingData: v.any(),
  },
  handler: async (ctx, args) => {
    console.log("🎤 Starting meeting execution phase");

    await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
      workflowId: args.workflowId,
      stage: "MEETING_EXECUTION",
      event: "meeting_started",
      data: { meetingId: args.meetingId },
    });

    try {
      // Initialize voice bot for meeting
      const voiceBotResult = await ctx.runAction(internal.voiceBot.initiateMeetingBot, {
        meetingId: args.meetingId,
        workflowId: args.workflowId,
        meetingData: args.meetingData,
      });

      if (!voiceBotResult.success) {
        throw new Error(`Voice bot initialization failed: ${voiceBotResult.error}`);
      }

      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId: args.workflowId,
        stage: "MEETING_ACTIVE",
        status: "meeting_in_progress",
        voiceBotId: voiceBotResult.botId,
      });

      return {
        success: true,
        botId: voiceBotResult.botId,
        meetingUrl: voiceBotResult.meetingUrl,
      };

    } catch (error) {
      await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
        workflowId: args.workflowId,
        stage: "MEETING_EXECUTION",
        event: "meeting_failed",
        data: { error: error instanceof Error ? error.message : String(error) },
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Post-Meeting Processing Handler
export const handlePostMeetingProcessing = action({
  args: {
    workflowId: v.string(),
    meetingId: v.string(),
    transcriptData: v.any(),
    pollResults: v.any(),
  },
  handler: async (ctx, args) => {
    console.log("📝 Starting post-meeting processing");

    await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
      workflowId: args.workflowId,
      stage: "POST_MEETING",
      event: "processing_started",
      data: { meetingId: args.meetingId },
    });

    try {
      // Execute summarizer agent
      const summaryResult = await ctx.runAction(internal.meetingSummarizer.processTranscript, {
        transcriptData: args.transcriptData,
        meetingId: args.meetingId,
        workflowId: args.workflowId,
      });

      // Execute task manager agent
      const taskResult = await ctx.runAction(internal.meetingTaskManager.createTasksFromSummary, {
        summaryId: summaryResult.summaryId,
        actionItems: summaryResult.actionItems,
        workflowId: args.workflowId,
      });

      // Determine workflow outcome
      const outcome = determineWorkflowOutcome(args.pollResults, summaryResult);

      await ctx.runMutation(internal.zeroTouchWorkflow.updateWorkflowInstance, {
        workflowId: args.workflowId,
        stage: outcome === "GO" ? "BID_DEVELOP" : "ARCHIVE",
        status: "completed",
        outcome,
        summaryId: summaryResult.summaryId,
        taskIds: taskResult.taskIds,
      });

      // Send wrap-up notifications
      await ctx.runAction(api.zeroTouchWorkflow.sendWrapUpNotifications, {
        workflowId: args.workflowId,
        outcome,
        summaryResult,
        taskResult,
      });

      await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
        workflowId: args.workflowId,
        stage: "POST_MEETING",
        event: "processing_completed",
        data: { outcome, taskCount: taskResult.taskIds.length },
      });

      return {
        success: true,
        outcome,
        summaryId: summaryResult.summaryId,
        taskIds: taskResult.taskIds,
      };

    } catch (error) {
      await ctx.runMutation(internal.zeroTouchWorkflow.logWorkflowEvent, {
        workflowId: args.workflowId,
        stage: "POST_MEETING",
        event: "processing_failed",
        data: { error: error instanceof Error ? error.message : String(error) },
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  },
});

// Determine workflow outcome from poll results and meeting summary
function determineWorkflowOutcome(pollResults: any, summaryResult: any): "GO" | "NO_GO" | "REVIEW" {
  // Simple majority vote logic - can be made more sophisticated
  if (pollResults?.goNoGoVote) {
    const goVotes = pollResults.goNoGoVote.filter((vote: any) => vote.response === "GO").length;
    const totalVotes = pollResults.goNoGoVote.length;
    
    if (goVotes / totalVotes >= 0.6) {
      return "GO";
    } else if (goVotes / totalVotes <= 0.3) {
      return "NO_GO";
    }
  }

  // Check for explicit decisions in summary
  if (summaryResult.decisions) {
    const decisions = summaryResult.decisions.filter((d: any) => 
      d.type === "go_no_go" || d.topic.toLowerCase().includes("proceed")
    );
    
    if (decisions.length > 0) {
      const latestDecision = decisions[decisions.length - 1];
      if (latestDecision.outcome?.toLowerCase().includes("go")) {
        return "GO";
      } else if (latestDecision.outcome?.toLowerCase().includes("no")) {
        return "NO_GO";
      }
    }
  }

  // Default to review if uncertain
  return "REVIEW";
}

// Internal mutations and queries
export const createWorkflowInstance = internalMutation({
  args: {
    sourceType: v.string(),
    sourceData: v.any(),
    priority: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("workflow_instances", {
      sourceType: args.sourceType,
      sourceData: args.sourceData,
      priority: args.priority,
      stage: "INITIATED",
      status: "starting",
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });
  },
});

export const updateWorkflowInstance = internalMutation({
  args: {
    workflowId: v.string(),
    tenderId: v.optional(v.id("tenders")),
    meetingId: v.optional(v.string()),
    stage: v.optional(v.string()),
    status: v.optional(v.string()),
    outcome: v.optional(v.string()),
    error: v.optional(v.string()),
    contentData: v.optional(v.any()),
    summaryId: v.optional(v.string()),
    taskIds: v.optional(v.array(v.string())),
    voiceBotId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = { updatedAt: Date.now() };
    
    if (args.tenderId) updates.tenderId = args.tenderId;
    if (args.meetingId) updates.meetingId = args.meetingId;
    if (args.stage) updates.stage = args.stage;
    if (args.status) updates.status = args.status;
    if (args.outcome) updates.outcome = args.outcome;
    if (args.error) updates.error = args.error;
    if (args.contentData) updates.contentData = args.contentData;
    if (args.summaryId) updates.summaryId = args.summaryId;
    if (args.taskIds) updates.taskIds = args.taskIds;
    if (args.voiceBotId) updates.voiceBotId = args.voiceBotId;

    await ctx.db.patch(args.workflowId as any, updates);
  },
});

export const logWorkflowEvent = internalMutation({
  args: {
    workflowId: v.string(),
    stage: v.string(),
    event: v.string(),
    data: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("workflow_events", {
      workflowId: args.workflowId,
      stage: args.stage,
      event: args.event,
      data: args.data,
      timestamp: Date.now(),
    });
  },
});

export const sendWrapUpNotifications = action({
  args: {
    workflowId: v.string(),
    outcome: v.string(),
    summaryResult: v.any(),
    taskResult: v.any(),
  },
  handler: async (ctx, args) => {
    // Get workflow instance to get participant list
    const workflow = await ctx.runQuery(internal.zeroTouchWorkflow.getWorkflowInstance, {
      workflowId: args.workflowId,
    });

    if (!workflow) return;

    // Send email notifications to all participants
    // This would integrate with your email service
    console.log("📧 Sending wrap-up notifications", {
      outcome: args.outcome,
      participants: workflow.participants || [],
      taskCount: args.taskResult.taskIds?.length || 0,
    });

    // TODO: Implement actual email/SMS notifications
    return { success: true };
  },
});

export const getWorkflowInstance = internalQuery({
  args: { workflowId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.workflowId as any);
  },
});

// Public queries for monitoring
export const getWorkflowStatus = query({
  args: { workflowId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.workflowId as any);
  },
});

export const listActiveWorkflows = query({
  handler: async (ctx) => {
    return await ctx.db
      .query("workflow_instances")
      .filter((q) => q.neq(q.field("status"), "completed"))
      .order("desc")
      .take(50);
  },
});

export const getWorkflowEvents = query({
  args: { workflowId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("workflow_events")
      .filter((q) => q.eq(q.field("workflowId"), args.workflowId))
      .order("desc")
      .take(100);
  },
});