"use node";

import { v } from "convex/values";
import { action, mutation, query } from "./_generated/server";
import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";

// Define SlidevTenderGenerator interface for type safety
interface SlidevTenderGenerator {
  generatePresentation(tenderData: any, options?: any): Promise<{
    slidesPath: string;
    exports: Record<string, string>;
    voiceScript?: string;
  }>;
  startPresentationServer(slidesPath: string, port?: number): Promise<string>;
}

// Mock SlidevTenderGenerator class for now
class SlidevTenderGenerator {
  constructor(options: any = {}) {
    // Mock implementation
  }

  async generatePresentation(tenderData: any, options: any = {}) {
    // Mock implementation - in production this would generate actual slides
    return {
      slidesPath: `/tmp/slides-${Date.now()}.md`,
      exports: {
        pdf: `/tmp/presentation-${Date.now()}.pdf`,
        pptx: `/tmp/presentation-${Date.now()}.pptx`,
      },
      voiceScript: "Mock voice script content"
    };
  }

  async startPresentationServer(slidesPath: string, port: number = 3030) {
    // Mock implementation - in production this would start actual Slidev server
    return `http://localhost:${port}`;
  }
}

/**
 * Slidev Integration for Zero-Touch Tender System
 * Generates live presentations from tender data
 */

// Generate Slidev presentation from tender data
export const generateSlidevPresentation = action({
  args: {
    tenderId: v.id("tenders"),
    options: v.optional(v.object({
      theme: v.optional(v.string()),
      includeVoiceScript: v.optional(v.boolean()),
      exportFormats: v.optional(v.array(v.string())),
      autoStart: v.optional(v.boolean()),
    }))
  },
  handler: async (ctx, args) => {
    // Get tender data
    const tender = await ctx.runQuery(api.tenders.getTender, { 
      tenderId: args.tenderId 
    });

    if (!tender) {
      throw new Error("Tender not found");
    }

    // Get related data
    const [sections, files] = await Promise.all([
      ctx.runQuery(api.bidSections.getSectionsByTender, { tenderId: args.tenderId }),
      ctx.runQuery(api.files.getFilesByTender, { tenderId: args.tenderId })
    ]);

    // Transform data for Slidev generator
    const tenderData = {
      name: tender.name,
      clientName: tender.clientName,
      dueDate: new Date(tender.dueDate).toISOString(),
      sites: tender.sites || [{
        name: "Primary Location",
        location: tender.location || "To be confirmed",
        services: ["Commercial Cleaning", "Maintenance Services"]
      }],
      requirements: sections.map((section: any) => ({
        section: section.title,
        description: section.content?.substring(0, 100) + "..." || "Requirements to be detailed",
        wordLimit: section.wordLimit || 500
      })),
      complianceRequirements: [
        "ISO 9001:2015 Quality Management System",
        "ISO 14001:2015 Environmental Management",
        "ISO 45001:2018 Occupational Health & Safety",
        "Public Liability Insurance $20M",
        "Workers Compensation Coverage",
        "All staff police checks and training",
      ],
      estimatedValue: tender.estimatedValue,
      projectDuration: tender.projectDuration || "12 months",
      keyContacts: [
        {
          name: "Account Manager",
          role: "Primary Contact",
          email: "<EMAIL>"
        },
        {
          name: "Operations Manager", 
          role: "Service Delivery",
          email: "<EMAIL>"
        },
        {
          name: "Project Manager",
          role: "Implementation",
          email: "<EMAIL>"
        }
      ]
    };

    const generator = new SlidevTenderGenerator({
      theme: args.options?.theme || "default",
      outputDir: "./generated-presentations",
      exportFormats: args.options?.exportFormats as any || ["pdf", "pptx"],
      includePresenterNotes: true,
      includeVoiceScript: args.options?.includeVoiceScript ?? true,
    });

    try {
      // Generate presentation
      const result = await generator.generatePresentation(tenderData, {
        exportFormats: args.options?.exportFormats as any || ["pdf", "pptx"],
        includeVoiceScript: args.options?.includeVoiceScript ?? true,
      });

      // Start presentation server if requested
      let presentationUrl: string | undefined;
      if (args.options?.autoStart !== false) {
        presentationUrl = await generator.startPresentationServer(result.slidesPath);
      }

      // Save presentation record
      const presentationId = await ctx.runMutation(api.slidevIntegration.savePresentationRecord, {
        tenderId: args.tenderId,
        slidesPath: result.slidesPath,
        exports: result.exports,
        voiceScript: result.voiceScript,
        presentationUrl: presentationUrl,
      });

      // Update tender workflow status
      await ctx.runMutation(api.tenders.updateTenderStatus, {
        tenderId: args.tenderId,
        status: "presentation_ready",
        notes: "Slidev presentation generated and ready for meeting"
      });

      return {
        presentationId,
        slidesPath: result.slidesPath,
        exports: result.exports,
        voiceScript: result.voiceScript,
        urls: {
          presentation: presentationUrl,
          presenter: presentationUrl ? `${presentationUrl}/presenter` : undefined,
          overview: presentationUrl ? `${presentationUrl}/overview` : undefined,
          export: presentationUrl ? `${presentationUrl}/export` : undefined,
        }
      };

    } catch (error) {
      console.error("Slidev generation failed:", error);
      
      // Create fallback presentation record
      const presentationId = await ctx.runMutation(api.slidevIntegration.savePresentationRecord, {
        tenderId: args.tenderId,
        slidesPath: "",
        exports: {},
        error: error instanceof Error ? error.message : "Unknown error",
      });

      throw new Error(`Failed to generate Slidev presentation: ${error}`);
    }
  },
});

// Save presentation record to database
export const savePresentationRecord = mutation({
  args: {
    tenderId: v.id("tenders"),
    slidesPath: v.string(),
    exports: v.any(),
    voiceScript: v.optional(v.string()),
    presentationUrl: v.optional(v.string()),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("slidev_presentations", {
      tenderId: args.tenderId,
      slidesPath: args.slidesPath,
      exports: args.exports,
      voiceScript: args.voiceScript,
      presentationUrl: args.presentationUrl,
      error: args.error,
      status: args.error ? "failed" : "ready",
      createdAt: Date.now(),
    });
  },
});

// Get presentation by tender ID
export const getPresentationByTender = query({
  args: { tenderId: v.id("tenders") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("slidev_presentations")
      .filter(q => q.eq(q.field("tenderId"), args.tenderId))
      .order("desc")
      .first();
  },
});

// Export presentation to additional formats
export const exportPresentationFormat = action({
  args: {
    presentationId: v.id("slidev_presentations"),
    format: v.union(v.literal("pdf"), v.literal("pptx"), v.literal("png"), v.literal("html")),
    options: v.optional(v.object({
      quality: v.optional(v.string()),
      includeNotes: v.optional(v.boolean()),
    }))
  },
  handler: async (ctx, args) => {
    const presentation = await ctx.runQuery(api.slidevIntegration.getPresentation, {
      presentationId: args.presentationId
    });

    if (!presentation) {
      throw new Error("Presentation not found");
    }

    // Use slidev export command
    const { exec } = await import('child_process');
    const { promisify } = await import('util');
    const execAsync = promisify(exec);

    try {
      const outputPath = presentation.slidesPath.replace('.md', `.${args.format}`);
      let command: string;

      switch (args.format) {
        case 'pdf':
          command = `slidev export ${presentation.slidesPath} --format pdf --output ${outputPath}`;
          break;
        case 'pptx':
          command = `slidev export ${presentation.slidesPath} --format pptx --output ${outputPath}`;
          break;
        case 'png':
          command = `slidev export ${presentation.slidesPath} --format png --output ${outputPath.replace('.png', '')}`;
          break;
        case 'html':
          command = `slidev build ${presentation.slidesPath} --out ${outputPath.replace('.html', '-dist')}`;
          break;
        default:
          throw new Error(`Unsupported format: ${args.format}`);
      }

      await execAsync(command);

      // Update presentation record with new export
      const updatedExports = {
        ...presentation.exports,
        [args.format]: outputPath
      };

      await ctx.runMutation(api.slidevIntegration.updatePresentationExports, {
        presentationId: args.presentationId,
        exports: updatedExports
      });

      return {
        format: args.format,
        path: outputPath,
        url: `/api/presentations/export/${args.presentationId}/${args.format}`
      };

    } catch (error) {
      console.error(`Export to ${args.format} failed:`, error);
      throw new Error(`Failed to export to ${args.format}: ${error}`);
    }
  },
});

// Update presentation exports
export const updatePresentationExports = mutation({
  args: {
    presentationId: v.id("slidev_presentations"),
    exports: v.any(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.presentationId, {
      exports: args.exports,
      updatedAt: Date.now(),
    });
  },
});

// Get presentation by ID
export const getPresentation = query({
  args: { presentationId: v.id("slidev_presentations") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.presentationId);
  },
});

// List all presentations
export const listPresentations = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("slidev_presentations")
      .order("desc")
      .take(args.limit || 50);
  },
});

// Start presentation server
export const startPresentationServer = action({
  args: {
    presentationId: v.id("slidev_presentations"),
    port: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const presentation = await ctx.runQuery(api.slidevIntegration.getPresentation, {
      presentationId: args.presentationId
    });

    if (!presentation) {
      throw new Error("Presentation not found");
    }

    const generator = new SlidevTenderGenerator();

    try {
      const presentationUrl = await generator.startPresentationServer(
        presentation.slidesPath, 
        args.port || 3030
      );

      // Update presentation record with server URL
      await ctx.runMutation(api.slidevIntegration.updatePresentationUrl, {
        presentationId: args.presentationId,
        presentationUrl: presentationUrl
      });

      return {
        url: presentationUrl,
        presenter: `${presentationUrl}/presenter`,
        overview: `${presentationUrl}/overview`,
        export: `${presentationUrl}/export`,
      };

    } catch (error) {
      console.error("Failed to start presentation server:", error);
      throw new Error(`Failed to start presentation server: ${error}`);
    }
  },
});

// Update presentation URL
export const updatePresentationUrl = mutation({
  args: {
    presentationId: v.id("slidev_presentations"),
    presentationUrl: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.presentationId, {
      presentationUrl: args.presentationUrl,
      updatedAt: Date.now(),
    });
  },
});

// Cleanup old presentations
export const cleanupPresentations = action({
  args: {
    olderThanDays: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const cutoffDate = Date.now() - (args.olderThanDays || 7) * 24 * 60 * 60 * 1000;
    
    const oldPresentations = await ctx.runQuery(api.slidevIntegration.getOldPresentations, {
      cutoffDate,
    });

    let cleanedCount = 0;
    
    for (const presentation of oldPresentations) {
      try {
        // Remove files from filesystem
        const { unlink } = await import('fs/promises');
        await unlink(presentation.slidesPath);
        
        // Remove exports
        for (const exportPath of Object.values(presentation.exports)) {
          if (typeof exportPath === 'string') {
            await unlink(exportPath);
          }
        }
        
        // Remove database record
        await ctx.runMutation(api.slidevIntegration.deletePresentation, {
          presentationId: presentation._id,
        });
        cleanedCount++;
        
      } catch (error) {
        console.error(`Failed to cleanup presentation ${presentation._id}:`, error);
      }
    }

    return {
      cleanedCount,
      totalFound: oldPresentations.length,
    };
  },
});

// Helper query to get old presentations
export const getOldPresentations = query({
  args: {
    cutoffDate: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("slidev_presentations")
      .filter(q => q.lt(q.field("createdAt"), args.cutoffDate))
      .collect();
  },
});

// Helper mutation to delete a presentation
export const deletePresentation = mutation({
  args: {
    presentationId: v.id("slidev_presentations"),
  },
  handler: async (ctx, args) => {
    await ctx.db.delete(args.presentationId);
  },
});