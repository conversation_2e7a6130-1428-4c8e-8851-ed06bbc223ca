import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Task management for meeting action items
export const createTaskFromActionItem = mutation({
  args: {
    actionItemId: v.id("meeting_action_items"),
    additionalDetails: v.optional(v.object({
      description: v.optional(v.string()),
      priority: v.optional(v.string()),
      assigneeId: v.optional(v.id("scheduler_contacts")),
      tags: v.optional(v.array(v.string())),
      linkedDocuments: v.optional(v.array(v.id("files"))),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const actionItem = await ctx.db.get(args.actionItemId);
    if (!actionItem) {
      throw new Error("Action item not found");
    }

    // Find task manager agent
    const agent = await ctx.db
      .query("agents")
      .filter(q => 
        q.and(
          q.eq(q.field("type"), "task_manager"),
          q.eq(q.field("status"), "active")
        )
      )
      .first();

    if (!agent) {
      throw new Error("Task manager agent not available");
    }

    // Create SMART goal from action item
    const smartGoal = await generateSMARTGoal(actionItem, args.additionalDetails);

    // Create task
    const taskId = await ctx.db.insert("meeting_tasks", {
      actionItemId: args.actionItemId,
      meetingId: actionItem.meetingId,
      tenderId: actionItem.tenderId,
      title: smartGoal.title,
      description: smartGoal.description,
      assignee: actionItem.assignee,
      assigneeContactId: args.additionalDetails?.assigneeId,
      priority: args.additionalDetails?.priority || actionItem.priority,
      category: actionItem.category,
      status: "not_started",
      progress: 0,
      createdAt: Date.now(),
      createdBy: identity.email || identity.name || "system",
      dueDate: actionItem.dueDate,
      smartGoal: smartGoal,
      tags: [...(actionItem.tags || []), ...(args.additionalDetails?.tags || [])],
      linkedDocuments: args.additionalDetails?.linkedDocuments || [],
      notifications: {
        frequency: determineNotificationFrequency(actionItem.priority),
        lastSent: null,
        nextScheduled: null,
        escalationLevel: 0,
      },
      metrics: {
        estimatedHours: smartGoal.estimatedHours,
        actualHours: 0,
        blockers: [],
        dependencies: [],
      },
    });

    // Create agent task for monitoring
    await ctx.db.insert("agent_tasks", {
      agentId: agent._id,
      type: "task_monitoring",
      status: "assigned",
      priority: actionItem.priority,
      tenderId: actionItem.tenderId || "temp_tender" as Id<"tenders">,
      assignedAt: Date.now(),
      progress: 0,
      retryCount: 0,
      input: {
        instructions: "Monitor and manage task progress, send reminders, and escalate if needed",
        context: {
          tenderName: "Task Management",
          clientName: "Internal",
          sectionTitle: smartGoal.title,
          requirements: [
            "Track progress",
            "Send timely reminders",
            "Escalate overdue items",
            "Update stakeholders",
          ],
        },
        parameters: {
          taskId,
          monitoringFrequency: "daily",
          escalationThreshold: 3,
        },
      },
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
    });

    // Schedule first reminder
    await scheduleTaskReminder(ctx, taskId, "initial");

    return taskId;
  },
});

// Generate SMART goal from action item
async function generateSMARTGoal(actionItem: any, additionalDetails?: any) {
  const baseDescription = additionalDetails?.description || actionItem.description;
  
  return {
    title: generateTaskTitle(baseDescription),
    description: baseDescription,
    specific: `Complete: ${baseDescription}`,
    measurable: determineMeasurableCriteria(baseDescription),
    achievable: {
      resources: extractRequiredResources(baseDescription),
      skills: extractRequiredSkills(baseDescription),
      dependencies: [],
    },
    relevant: {
      businessValue: determineBusinessValue(actionItem.category, actionItem.priority),
      alignment: "Directly supports tender preparation and team coordination",
    },
    timeBound: {
      dueDate: actionItem.dueDate,
      milestones: generateMilestones(actionItem.dueDate, baseDescription),
    },
    estimatedHours: estimateTaskHours(baseDescription, actionItem.category),
  };
}

function generateTaskTitle(description: string): string {
  // Extract key action from description
  const words = description.split(' ');
  if (words.length <= 5) return description;
  
  // Find action verb and object
  const actionWords = ['create', 'update', 'review', 'prepare', 'complete', 'develop', 'analyze', 'implement'];
  const action = words.find(w => actionWords.includes(w.toLowerCase())) || words[0];
  
  return `${action} ${words.slice(1, 5).join(' ')}...`;
}

function determineMeasurableCriteria(description: string): string[] {
  const criteria = [];
  
  if (description.includes('document') || description.includes('report')) {
    criteria.push("Document completed and reviewed");
    criteria.push("All sections filled with required information");
  }
  
  if (description.includes('review') || description.includes('analyze')) {
    criteria.push("Review completed with findings documented");
    criteria.push("Recommendations provided");
  }
  
  if (description.includes('meet') || description.includes('discuss')) {
    criteria.push("Meeting held with all stakeholders");
    criteria.push("Meeting minutes documented");
  }
  
  if (criteria.length === 0) {
    criteria.push("Task completed as specified");
    criteria.push("Deliverables produced and approved");
  }
  
  return criteria;
}

function extractRequiredResources(description: string): string[] {
  const resources = [];
  
  if (description.includes('data') || description.includes('information')) {
    resources.push("Access to relevant data sources");
  }
  
  if (description.includes('team') || description.includes('collaborate')) {
    resources.push("Team member availability");
  }
  
  if (description.includes('budget') || description.includes('cost')) {
    resources.push("Budget information and approval");
  }
  
  if (description.includes('document') || description.includes('template')) {
    resources.push("Document templates and examples");
  }
  
  return resources.length > 0 ? resources : ["General project resources"];
}

function extractRequiredSkills(description: string): string[] {
  const skills = [];
  
  if (description.includes('technical') || description.includes('develop')) {
    skills.push("Technical expertise");
  }
  
  if (description.includes('write') || description.includes('document')) {
    skills.push("Technical writing");
  }
  
  if (description.includes('analyze') || description.includes('review')) {
    skills.push("Analytical skills");
  }
  
  if (description.includes('present') || description.includes('communicate')) {
    skills.push("Communication skills");
  }
  
  return skills.length > 0 ? skills : ["General project management"];
}

function determineBusinessValue(category: string, priority: string): string {
  const valueMap = {
    technical: "Ensures technical accuracy and feasibility",
    documentation: "Provides essential project documentation",
    communication: "Facilitates stakeholder alignment",
    review: "Ensures quality and compliance",
    general: "Supports overall project success",
  };
  
  const priorityPrefix = priority === "urgent" ? "Critical - " : 
                        priority === "high" ? "Important - " : "";
  
  return priorityPrefix + (valueMap[category] || valueMap.general);
}

function generateMilestones(dueDate: number | null, description: string): any[] {
  if (!dueDate) return [];
  
  const now = Date.now();
  const duration = dueDate - now;
  const milestones = [];
  
  if (duration > 7 * 24 * 60 * 60 * 1000) { // More than a week
    milestones.push({
      name: "Initial planning complete",
      dueDate: now + (duration * 0.2),
      status: "pending",
    });
    
    milestones.push({
      name: "50% progress checkpoint",
      dueDate: now + (duration * 0.5),
      status: "pending",
    });
    
    milestones.push({
      name: "Draft ready for review",
      dueDate: now + (duration * 0.8),
      status: "pending",
    });
  }
  
  return milestones;
}

function estimateTaskHours(description: string, category: string): number {
  // Base estimates by category
  const categoryHours = {
    technical: 8,
    documentation: 6,
    communication: 2,
    review: 4,
    general: 4,
  };
  
  let hours = categoryHours[category] || 4;
  
  // Adjust based on complexity indicators
  if (description.includes('complex') || description.includes('detailed')) {
    hours *= 1.5;
  }
  
  if (description.includes('urgent') || description.includes('asap')) {
    hours *= 0.8; // Compressed timeline
  }
  
  if (description.includes('multiple') || description.includes('all')) {
    hours *= 1.3;
  }
  
  return Math.round(hours);
}

function determineNotificationFrequency(priority: string): string {
  switch (priority) {
    case "urgent": return "twice_daily";
    case "high": return "daily";
    case "normal": return "every_two_days";
    default: return "weekly";
  }
}

async function scheduleTaskReminder(ctx: any, taskId: Id<"meeting_tasks">, type: string) {
  const task = await ctx.db.get(taskId);
  if (!task) return;
  
  const delayMap = {
    initial: 24 * 60 * 60 * 1000, // 1 day
    daily: 24 * 60 * 60 * 1000,
    twice_daily: 12 * 60 * 60 * 1000,
    every_two_days: 48 * 60 * 60 * 1000,
    weekly: 7 * 24 * 60 * 60 * 1000,
  };
  
  const delay = delayMap[task.notifications.frequency] || delayMap.daily;
  
  await ctx.scheduler.runAfter(delay, api.meetingTaskManager.sendTaskReminder, {
    taskId,
    reminderType: type,
  });
}

// Send task reminder
export const sendTaskReminder = action({
  args: {
    taskId: v.id("meeting_tasks"),
    reminderType: v.string(),
  },
  handler: async (ctx, args) => {
    const task = await ctx.runQuery(api.meetingTaskManager.getTask, { taskId: args.taskId });
    if (!task || task.status === "completed" || task.status === "cancelled") {
      return;
    }

    // Check if overdue
    const now = Date.now();
    const isOverdue = task.dueDate && now > task.dueDate;
    const daysOverdue = isOverdue ? Math.floor((now - task.dueDate) / (24 * 60 * 60 * 1000)) : 0;

    // Prepare notification content
    const notification = {
      type: isOverdue ? "overdue" : "reminder",
      taskId: args.taskId,
      assignee: task.assignee,
      assigneeContactId: task.assigneeContactId,
      subject: generateNotificationSubject(task, isOverdue, daysOverdue),
      content: generateNotificationContent(task, isOverdue, daysOverdue),
      priority: isOverdue ? "high" : task.priority,
      channels: determineNotificationChannels(task, isOverdue),
    };

    // Send notification through configured channels
    await ctx.runAction(api.meetingTaskManager.sendNotification, notification);

    // Update task notification status
    await ctx.runMutation(api.meetingTaskManager.updateTaskNotification, {
      taskId: args.taskId,
      notificationSent: true,
      escalationLevel: isOverdue ? task.notifications.escalationLevel + 1 : task.notifications.escalationLevel,
    });

    // Handle escalation if needed
    if (isOverdue && daysOverdue > 3 && task.notifications.escalationLevel < 3) {
      await ctx.runAction(api.meetingTaskManager.escalateTask, {
        taskId: args.taskId,
        reason: `Task overdue by ${daysOverdue} days`,
      });
    }

    // Schedule next reminder
    if (task.status !== "completed") {
      await ctx.runMutation(api.meetingTaskManager.scheduleNextReminder, {
        taskId: args.taskId,
      });
    }
  },
});

function generateNotificationSubject(task: any, isOverdue: boolean, daysOverdue: number): string {
  if (isOverdue) {
    return `⚠️ OVERDUE: ${task.title} (${daysOverdue} days)`;
  }
  
  const daysUntilDue = task.dueDate ? 
    Math.floor((task.dueDate - Date.now()) / (24 * 60 * 60 * 1000)) : null;
  
  if (daysUntilDue !== null && daysUntilDue <= 1) {
    return `🔔 DUE TOMORROW: ${task.title}`;
  }
  
  return `📋 Task Reminder: ${task.title}`;
}

function generateNotificationContent(task: any, isOverdue: boolean, daysOverdue: number): string {
  let content = `Task: ${task.title}\n\n`;
  content += `Description: ${task.description}\n`;
  content += `Priority: ${task.priority.toUpperCase()}\n`;
  content += `Progress: ${task.progress}%\n`;
  
  if (isOverdue) {
    content += `\n⚠️ This task is ${daysOverdue} days overdue!\n`;
    content += `Original Due Date: ${new Date(task.dueDate).toLocaleDateString()}\n`;
  } else if (task.dueDate) {
    content += `\nDue Date: ${new Date(task.dueDate).toLocaleDateString()}\n`;
    const daysRemaining = Math.floor((task.dueDate - Date.now()) / (24 * 60 * 60 * 1000));
    content += `Days Remaining: ${daysRemaining}\n`;
  }
  
  if (task.metrics.blockers.length > 0) {
    content += `\n🚧 Current Blockers:\n`;
    task.metrics.blockers.forEach((blocker: any) => {
      content += `- ${blocker.description}\n`;
    });
  }
  
  content += `\nPlease update the task status in the system.`;
  
  return content;
}

function determineNotificationChannels(task: any, isOverdue: boolean): string[] {
  const channels = ["email", "in_app"];
  
  if (task.priority === "urgent" || isOverdue) {
    channels.push("sms");
  }
  
  if (task.notifications.escalationLevel > 1) {
    channels.push("slack");
  }
  
  return channels;
}

// Send notification through multiple channels
export const sendNotification = action({
  args: {
    type: v.string(),
    taskId: v.id("meeting_tasks"),
    assignee: v.string(),
    assigneeContactId: v.optional(v.id("scheduler_contacts")),
    subject: v.string(),
    content: v.string(),
    priority: v.string(),
    channels: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    // Create notification record
    const notificationId = await ctx.runMutation(api.meetingTaskManager.createNotification, {
      taskId: args.taskId,
      type: args.type,
      subject: args.subject,
      content: args.content,
      channels: args.channels,
      status: "pending",
    });

    // Send through each channel
    for (const channel of args.channels) {
      try {
        switch (channel) {
          case "email":
            // Integration with email service
            console.log(`Sending email to ${args.assignee}: ${args.subject}`);
            break;
            
          case "sms":
            // Integration with SMS service (Twilio)
            console.log(`Sending SMS to ${args.assignee}: ${args.subject}`);
            break;
            
          case "slack":
            // Integration with Slack
            console.log(`Sending Slack message to ${args.assignee}: ${args.subject}`);
            break;
            
          case "in_app":
            // Create in-app notification
            await ctx.runMutation(api.meetingTaskManager.createInAppNotification, {
              userId: args.assignee,
              taskId: args.taskId,
              subject: args.subject,
              content: args.content,
              priority: args.priority,
            });
            break;
        }
        
        // Update notification status for channel
        await ctx.runMutation(api.meetingTaskManager.updateNotificationChannel, {
          notificationId,
          channel,
          status: "sent",
        });
      } catch (error) {
        console.error(`Failed to send notification via ${channel}:`, error);
        await ctx.runMutation(api.meetingTaskManager.updateNotificationChannel, {
          notificationId,
          channel,
          status: "failed",
          error: error.message,
        });
      }
    }
  },
});

// Escalate overdue task
export const escalateTask = action({
  args: {
    taskId: v.id("meeting_tasks"),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const task = await ctx.runQuery(api.meetingTaskManager.getTask, { taskId: args.taskId });
    if (!task) return;

    // Find manager or escalation contact
    const escalationContacts = await ctx.runQuery(api.meetingTaskManager.getEscalationContacts, {
      assignee: task.assignee,
      category: task.category,
    });

    // Create escalation record
    await ctx.runMutation(api.meetingTaskManager.createEscalation, {
      taskId: args.taskId,
      reason: args.reason,
      escalatedTo: escalationContacts,
      level: task.notifications.escalationLevel + 1,
    });

    // Send escalation notifications
    for (const contact of escalationContacts) {
      await ctx.runAction(api.meetingTaskManager.sendNotification, {
        type: "escalation",
        taskId: args.taskId,
        assignee: contact.name,
        assigneeContactId: contact.id,
        subject: `🚨 Task Escalation: ${task.title}`,
        content: `An overdue task requires your attention:\n\n${task.description}\n\nAssignee: ${task.assignee}\nReason: ${args.reason}\n\nPlease take appropriate action.`,
        priority: "urgent",
        channels: ["email", "sms", "slack"],
      });
    }
  },
});

// Database operations
export const getTask = query({
  args: { taskId: v.id("meeting_tasks") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.taskId);
  },
});

export const updateTaskNotification = mutation({
  args: {
    taskId: v.id("meeting_tasks"),
    notificationSent: v.boolean(),
    escalationLevel: v.number(),
  },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    if (!task) return;

    await ctx.db.patch(args.taskId, {
      notifications: {
        ...task.notifications,
        lastSent: Date.now(),
        escalationLevel: args.escalationLevel,
      },
    });
  },
});

export const scheduleNextReminder = mutation({
  args: { taskId: v.id("meeting_tasks") },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    if (!task) return;

    const nextReminderTime = calculateNextReminderTime(task);
    
    await ctx.db.patch(args.taskId, {
      notifications: {
        ...task.notifications,
        nextScheduled: nextReminderTime,
      },
    });

    await ctx.scheduler.runAt(nextReminderTime, api.meetingTaskManager.sendTaskReminder, {
      taskId: args.taskId,
      reminderType: "scheduled",
    });
  },
});

function calculateNextReminderTime(task: any): number {
  const now = Date.now();
  const frequencyMap = {
    twice_daily: 12 * 60 * 60 * 1000,
    daily: 24 * 60 * 60 * 1000,
    every_two_days: 48 * 60 * 60 * 1000,
    weekly: 7 * 24 * 60 * 60 * 1000,
  };

  const interval = frequencyMap[task.notifications.frequency] || frequencyMap.daily;
  return now + interval;
}

export const createNotification = mutation({
  args: {
    taskId: v.id("meeting_tasks"),
    type: v.string(),
    subject: v.string(),
    content: v.string(),
    channels: v.array(v.string()),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("task_notifications", {
      ...args,
      createdAt: Date.now(),
      channelStatus: {},
    });
  },
});

export const updateNotificationChannel = mutation({
  args: {
    notificationId: v.id("task_notifications"),
    channel: v.string(),
    status: v.string(),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const notification = await ctx.db.get(args.notificationId);
    if (!notification) return;

    await ctx.db.patch(args.notificationId, {
      channelStatus: {
        ...notification.channelStatus,
        [args.channel]: {
          status: args.status,
          sentAt: Date.now(),
          error: args.error,
        },
      },
    });
  },
});

export const createInAppNotification = mutation({
  args: {
    userId: v.string(),
    taskId: v.id("meeting_tasks"),
    subject: v.string(),
    content: v.string(),
    priority: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("in_app_notifications", {
      ...args,
      read: false,
      createdAt: Date.now(),
    });
  },
});

export const getEscalationContacts = query({
  args: {
    assignee: v.string(),
    category: v.string(),
  },
  handler: async (ctx, args) => {
    // Find managers or team leads based on category
    const contacts = await ctx.db
      .query("scheduler_contacts")
      .filter(q => 
        q.or(
          q.eq(q.field("role"), "manager"),
          q.eq(q.field("role"), "team_lead"),
          q.eq(q.field("department"), "project_management")
        )
      )
      .collect();

    return contacts.slice(0, 3); // Return top 3 escalation contacts
  },
});

export const createEscalation = mutation({
  args: {
    taskId: v.id("meeting_tasks"),
    reason: v.string(),
    escalatedTo: v.array(v.any()),
    level: v.number(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("task_escalations", {
      ...args,
      createdAt: Date.now(),
      resolved: false,
    });
  },
});

// Update task progress
export const updateTaskProgress = mutation({
  args: {
    taskId: v.id("meeting_tasks"),
    progress: v.number(),
    status: v.optional(v.string()),
    notes: v.optional(v.string()),
    blockers: v.optional(v.array(v.object({
      description: v.string(),
      severity: v.string(),
      createdAt: v.number(),
    }))),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const task = await ctx.db.get(args.taskId);
    if (!task) {
      throw new Error("Task not found");
    }

    const updates: any = {
      progress: args.progress,
      lastUpdated: Date.now(),
      lastUpdatedBy: identity.email || identity.name || "unknown",
    };

    if (args.status) {
      updates.status = args.status;
      
      if (args.status === "completed") {
        updates.completedAt = Date.now();
        updates.progress = 100;
      }
    }

    if (args.blockers) {
      updates.metrics = {
        ...task.metrics,
        blockers: args.blockers,
      };
    }

    await ctx.db.patch(args.taskId, updates);

    // Add progress note
    if (args.notes) {
      await ctx.db.insert("task_progress_notes", {
        taskId: args.taskId,
        note: args.notes,
        progress: args.progress,
        createdBy: identity.email || identity.name || "unknown",
        createdAt: Date.now(),
      });
    }

    return args.taskId;
  },
});

// Get tasks by various filters
export const getTasks = query({
  args: {
    meetingId: v.optional(v.id("scheduler_meetings")),
    assignee: v.optional(v.string()),
    status: v.optional(v.string()),
    priority: v.optional(v.string()),
    overdue: v.optional(v.boolean()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("meeting_tasks");

    const meetingFilteredQuery = args.meetingId
      ? baseQuery.filter(q => q.eq(q.field("meetingId"), args.meetingId))
      : baseQuery;

    const assigneeFilteredQuery = args.assignee
      ? meetingFilteredQuery.filter(q => q.eq(q.field("assignee"), args.assignee))
      : meetingFilteredQuery;

    const statusFilteredQuery = args.status
      ? assigneeFilteredQuery.filter(q => q.eq(q.field("status"), args.status))
      : assigneeFilteredQuery;

    const priorityFilteredQuery = args.priority
      ? statusFilteredQuery.filter(q => q.eq(q.field("priority"), args.priority))
      : statusFilteredQuery;

    const tasks = await priorityFilteredQuery
      .order("desc")
      .take(args.limit || 50);

    // Filter overdue if requested
    const now = Date.now();
    let filteredTasks = tasks;
    
    if (args.overdue === true) {
      filteredTasks = tasks.filter(t => t.dueDate && t.dueDate < now && t.status !== "completed");
    } else if (args.overdue === false) {
      filteredTasks = tasks.filter(t => !t.dueDate || t.dueDate >= now || t.status === "completed");
    }

    // Enhance with additional data
    const enhancedTasks = await Promise.all(
      filteredTasks.map(async (task) => {
        const actionItem = await ctx.db.get(task.actionItemId);
        const meeting = await ctx.db.get(task.meetingId);
        const progressNotes = await ctx.db
          .query("task_progress_notes")
          .filter(q => q.eq(q.field("taskId"), task._id))
          .order("desc")
          .take(5);

        return {
          ...task,
          actionItemText: actionItem?.description,
          meetingTitle: meeting?.title,
          progressNotes,
          isOverdue: task.dueDate && task.dueDate < now && task.status !== "completed",
          daysUntilDue: task.dueDate ? Math.floor((task.dueDate - now) / (24 * 60 * 60 * 1000)) : null,
        };
      })
    );

    return enhancedTasks;
  },
});

// Generate task analytics
export const getTaskAnalytics = query({
  args: {
    timeframe: v.optional(v.string()), // "week", "month", "quarter"
    assignee: v.optional(v.string()),
    meetingId: v.optional(v.id("scheduler_meetings")),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Calculate timeframe
    const now = Date.now();
    const timeframeMap = {
      week: 7 * 24 * 60 * 60 * 1000,
      month: 30 * 24 * 60 * 60 * 1000,
      quarter: 90 * 24 * 60 * 60 * 1000,
    };
    const startTime = now - (timeframeMap[args.timeframe || "month"] || timeframeMap.month);

    // Get tasks in timeframe
    const baseQuery = ctx.db
      .query("meeting_tasks")
      .filter(q => q.gte(q.field("createdAt"), startTime));

    const assigneeFilteredQuery = args.assignee
      ? baseQuery.filter(q => q.eq(q.field("assignee"), args.assignee))
      : baseQuery;

    const meetingFilteredQuery = args.meetingId
      ? assigneeFilteredQuery.filter(q => q.eq(q.field("meetingId"), args.meetingId))
      : assigneeFilteredQuery;

    const tasks = await meetingFilteredQuery.collect();

    // Calculate analytics
    const analytics = {
      totalTasks: tasks.length,
      completedTasks: tasks.filter(t => t.status === "completed").length,
      overdueTasks: tasks.filter(t => t.dueDate && t.dueDate < now && t.status !== "completed").length,
      inProgressTasks: tasks.filter(t => t.status === "in_progress").length,
      notStartedTasks: tasks.filter(t => t.status === "not_started").length,
      
      completionRate: tasks.length > 0 ? 
        (tasks.filter(t => t.status === "completed").length / tasks.length) * 100 : 0,
      
      averageCompletionTime: calculateAverageCompletionTime(tasks),
      
      byPriority: {
        urgent: tasks.filter(t => t.priority === "urgent").length,
        high: tasks.filter(t => t.priority === "high").length,
        normal: tasks.filter(t => t.priority === "normal").length,
        low: tasks.filter(t => t.priority === "low").length,
      },
      
      byCategory: tasks.reduce((acc, task) => {
        acc[task.category] = (acc[task.category] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      
      topAssignees: getTopAssignees(tasks),
      
      escalationRate: calculateEscalationRate(tasks),
      
      trendsOverTime: calculateTrends(tasks, startTime, now),
    };

    return analytics;
  },
});

function calculateAverageCompletionTime(tasks: any[]): number {
  const completedTasks = tasks.filter(t => t.status === "completed" && t.completedAt);
  if (completedTasks.length === 0) return 0;

  const totalTime = completedTasks.reduce((sum, task) => {
    return sum + (task.completedAt - task.createdAt);
  }, 0);

  return totalTime / completedTasks.length / (24 * 60 * 60 * 1000); // Convert to days
}

function getTopAssignees(tasks: any[]): any[] {
  const assigneeCounts = tasks.reduce((acc, task) => {
    acc[task.assignee] = (acc[task.assignee] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(assigneeCounts)
    .map(([assignee, count]) => ({ assignee, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);
}

function calculateEscalationRate(tasks: any[]): number {
  const escalatedTasks = tasks.filter(t => t.notifications.escalationLevel > 0);
  return tasks.length > 0 ? (escalatedTasks.length / tasks.length) * 100 : 0;
}

function calculateTrends(tasks: any[], startTime: number, endTime: number): any[] {
  const duration = endTime - startTime;
  const intervals = 7; // Show 7 data points
  const intervalSize = duration / intervals;
  
  const trends = [];
  
  for (let i = 0; i < intervals; i++) {
    const intervalStart = startTime + (i * intervalSize);
    const intervalEnd = intervalStart + intervalSize;
    
    const intervalTasks = tasks.filter(t => 
      t.createdAt >= intervalStart && t.createdAt < intervalEnd
    );
    
    trends.push({
      period: new Date(intervalStart).toLocaleDateString(),
      created: intervalTasks.length,
      completed: intervalTasks.filter(t => 
        t.status === "completed" && t.completedAt && t.completedAt < intervalEnd
      ).length,
    });
  }
  
  return trends;
}

// Send action item notification (called from summarizer)
export const sendActionItemNotification = action({
  args: {
    actionItemId: v.id("meeting_action_items"),
    notificationType: v.string(),
  },
  handler: async (ctx, args) => {
    const actionItem = await ctx.runQuery(api.meetingTaskManager.getActionItem, {
      actionItemId: args.actionItemId,
    });
    
    if (!actionItem) return;

    // Create task from action item if not already created
    const existingTask = await ctx.runQuery(api.meetingTaskManager.getTaskByActionItem, {
      actionItemId: args.actionItemId,
    });

    if (!existingTask) {
      await ctx.runMutation(api.meetingTaskManager.createTaskFromActionItem, {
        actionItemId: args.actionItemId,
      });
    }
  },
});

export const getActionItem = query({
  args: { actionItemId: v.id("meeting_action_items") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.actionItemId);
  },
});

export const getTaskByActionItem = query({
  args: { actionItemId: v.id("meeting_action_items") },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("meeting_tasks")
      .filter(q => q.eq(q.field("actionItemId"), args.actionItemId))
      .first();
  },
});