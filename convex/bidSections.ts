import { internalMutation, mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

export const updateInternal = internalMutation({
  args: {
    id: v.id("bidSections"),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    const wordCount = args.content.split(/\s+/).filter(Boolean).length;
    await ctx.db.patch(args.id, { 
      content: args.content, 
      wordCount,
      lastModified: Date.now(),
    });
  },
});

export const update = mutation({
  args: {
    id: v.id("bidSections"),
    content: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.runMutation(internal.bidSections.updateInternal, {
      id: args.id,
      content: args.content,
    });
  },
});

export const updateStatus = mutation({
  args: {
    id: v.id("bidSections"),
    status: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.id, { 
      status: args.status,
      lastModified: Date.now(),
    });
  },
});

export const updateMetadata = mutation({
  args: {
    id: v.id("bidSections"),
    wordLimit: v.optional(v.number()),
    scoreWeight: v.optional(v.number()),
    priority: v.optional(v.string()),
    assignedAgent: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const updates: any = { lastModified: Date.now() };
    
    if (args.wordLimit !== undefined) updates.wordLimit = args.wordLimit;
    if (args.scoreWeight !== undefined) updates.scoreWeight = args.scoreWeight;
    if (args.priority !== undefined) updates.priority = args.priority;
    if (args.assignedAgent !== undefined) updates.assignedAgent = args.assignedAgent;
    
    await ctx.db.patch(args.id, updates);
  },
});

export const getProgress = query({
  args: {
    tenderId: v.id("tenders"),
  },
  handler: async (ctx, args) => {
    const sections = await ctx.db
      .query("bidSections")
      .withIndex("by_tender", (q) => q.eq("tenderId", args.tenderId))
      .collect();
    
    const totalSections = sections.length;
    const completedSections = sections.filter(s => s.status === "approved").length;
    const inProgressSections = sections.filter(s => s.status === "review").length;
    const totalWords = sections.reduce((sum, s) => sum + s.wordCount, 0);
    const totalWeightedScore = sections.reduce((sum, s) => sum + s.scoreWeight, 0);
    
    return {
      totalSections,
      completedSections,
      inProgressSections,
      totalWords,
      totalWeightedScore,
      completionPercentage: totalSections > 0 ? (completedSections / totalSections) * 100 : 0,
    };
  },
});
