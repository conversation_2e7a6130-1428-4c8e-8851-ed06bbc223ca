import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Integration configurations
export const configurePlatformIntegration = mutation({
  args: {
    platform: v.string(), // "planner", "trello", "asana", "slack", "teams"
    credentials: v.object({
      apiKey: v.optional(v.string()),
      apiSecret: v.optional(v.string()),
      accessToken: v.optional(v.string()),
      refreshToken: v.optional(v.string()),
      webhookUrl: v.optional(v.string()),
      workspaceId: v.optional(v.string()),
      organizationId: v.optional(v.string()),
    }),
    settings: v.object({
      syncEnabled: v.boolean(),
      autoCreateTasks: v.boolean(),
      defaultProject: v.optional(v.string()),
      defaultList: v.optional(v.string()),
      notificationChannels: v.array(v.string()),
      syncFrequency: v.string(), // "realtime", "hourly", "daily"
      taskMapping: v.object({
        priorityField: v.optional(v.string()),
        dueDateField: v.optional(v.string()),
        assigneeField: v.optional(v.string()),
        customFields: v.optional(v.object({})),
      }),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if integration already exists
    const existing = await ctx.db
      .query("platform_integrations")
      .filter(q => q.eq(q.field("platform"), args.platform))
      .first();

    if (existing) {
      // Update existing integration
      await ctx.db.patch(existing._id, {
        credentials: args.credentials,
        settings: args.settings,
        lastUpdated: Date.now(),
        updatedBy: identity.email || identity.name || "system",
      });
      return existing._id;
    }

    // Create new integration
    const integrationId = await ctx.db.insert("platform_integrations", {
      platform: args.platform,
      credentials: args.credentials,
      settings: args.settings,
      status: "active",
      createdAt: Date.now(),
      createdBy: identity.email || identity.name || "system",
      lastUpdated: Date.now(),
      updatedBy: identity.email || identity.name || "system",
      lastSync: null,
      syncErrors: [],
    });

    // Schedule initial sync
    await ctx.scheduler.runAfter(0, api.meetingIntegrations.syncPlatform, {
      integrationId,
      fullSync: true,
    });

    return integrationId;
  },
});

// Sync tasks with external platforms
export const syncTaskToPlatform = action({
  args: {
    taskId: v.id("meeting_tasks"),
    platform: v.string(),
    operation: v.string(), // "create", "update", "delete"
  },
  handler: async (ctx, args) => {
    const task = await ctx.runQuery(api.meetingTaskManager.getTask, { taskId: args.taskId });
    if (!task) {
      throw new Error("Task not found");
    }

    const integration = await ctx.runQuery(api.meetingIntegrations.getIntegration, {
      platform: args.platform,
    });
    if (!integration || integration.status !== "active") {
      throw new Error("Platform integration not configured");
    }

    try {
      let externalId: string | null = null;

      switch (args.platform) {
        case "planner":
          externalId = await syncToMicrosoftPlanner(task, integration, args.operation);
          break;
        
        case "trello":
          externalId = await syncToTrello(task, integration, args.operation);
          break;
        
        case "asana":
          externalId = await syncToAsana(task, integration, args.operation);
          break;
        
        default:
          throw new Error(`Unsupported platform: ${args.platform}`);
      }

      // Update task with external reference
      if (externalId && args.operation !== "delete") {
        await ctx.runMutation(api.meetingIntegrations.updateTaskExternalId, {
          taskId: args.taskId,
          platform: args.platform,
          externalId,
        });
      }

      // Log sync activity
      await ctx.runMutation(api.meetingIntegrations.logSyncActivity, {
        integrationId: integration._id,
        taskId: args.taskId,
        operation: args.operation,
        status: "success",
        externalId,
      });

    } catch (error) {
      // Log sync error
      await ctx.runMutation(api.meetingIntegrations.logSyncActivity, {
        integrationId: integration._id,
        taskId: args.taskId,
        operation: args.operation,
        status: "error",
        error: error.message,
      });
      
      throw error;
    }
  },
});

// Microsoft Planner integration
async function syncToMicrosoftPlanner(task: any, integration: any, operation: string): Promise<string | null> {
  const { credentials, settings } = integration;
  
  // Mock implementation - replace with actual Microsoft Graph API calls
  console.log(`Syncing task to Microsoft Planner: ${operation}`, task.title);
  
  const plannerTask = {
    title: task.title,
    dueDateTime: task.dueDate ? new Date(task.dueDate).toISOString() : null,
    percentComplete: task.progress,
    priority: mapPriorityToPlanner(task.priority),
    assignments: {
      [task.assignee]: {
        "@odata.type": "#microsoft.graph.plannerAssignment",
        "orderHint": " !",
      },
    },
    details: {
      description: task.description,
      checklist: task.smartGoal.measurable.reduce((acc: any, item: string, index: number) => {
        acc[`checklist${index}`] = {
          "@odata.type": "#microsoft.graph.plannerChecklistItem",
          title: item,
          isChecked: false,
        };
        return acc;
      }, {}),
    },
  };

  // Simulate API response
  return `planner-task-${Date.now()}`;
}

// Trello integration
async function syncToTrello(task: any, integration: any, operation: string): Promise<string | null> {
  const { credentials, settings } = integration;
  
  // Mock implementation - replace with actual Trello API calls
  console.log(`Syncing task to Trello: ${operation}`, task.title);
  
  const card = {
    name: task.title,
    desc: formatTaskDescription(task),
    due: task.dueDate ? new Date(task.dueDate).toISOString() : null,
    idList: settings.defaultList,
    idMembers: [], // Would map assignee to Trello member ID
    labels: [mapPriorityToTrelloLabel(task.priority)],
    customFieldItems: [
      {
        idCustomField: settings.taskMapping.customFields?.progressField,
        value: { number: task.progress.toString() },
      },
    ],
  };

  // Simulate API response
  return `trello-card-${Date.now()}`;
}

// Asana integration
async function syncToAsana(task: any, integration: any, operation: string): Promise<string | null> {
  const { credentials, settings } = integration;
  
  // Mock implementation - replace with actual Asana API calls
  console.log(`Syncing task to Asana: ${operation}`, task.title);
  
  const asanaTask = {
    name: task.title,
    notes: formatTaskDescription(task),
    due_on: task.dueDate ? new Date(task.dueDate).toISOString().split('T')[0] : null,
    projects: [settings.defaultProject],
    assignee: task.assigneeContactId, // Would map to Asana user ID
    custom_fields: {
      [settings.taskMapping.customFields?.priorityField]: mapPriorityToAsana(task.priority),
      [settings.taskMapping.customFields?.progressField]: task.progress,
      [settings.taskMapping.customFields?.categoryField]: task.category,
    },
    tags: task.tags,
  };

  // Simulate API response
  return `asana-task-${Date.now()}`;
}

// Helper functions
function mapPriorityToPlanner(priority: string): number {
  const priorityMap: Record<string, number> = {
    urgent: 1,
    high: 3,
    normal: 5,
    low: 9,
  };
  return priorityMap[priority] || 5;
}

function mapPriorityToTrelloLabel(priority: string): string {
  const labelMap: Record<string, string> = {
    urgent: "red",
    high: "orange",
    normal: "yellow",
    low: "green",
  };
  return labelMap[priority] || "blue";
}

function mapPriorityToAsana(priority: string): string {
  return priority.charAt(0).toUpperCase() + priority.slice(1);
}

function formatTaskDescription(task: any): string {
  let description = task.description + "\n\n";
  
  description += "**SMART Goal Details:**\n";
  description += `- Specific: ${task.smartGoal.specific}\n`;
  description += `- Measurable:\n${task.smartGoal.measurable.map((m: string) => `  • ${m}`).join('\n')}\n`;
  description += `- Business Value: ${task.smartGoal.relevant.businessValue}\n`;
  description += `- Estimated Hours: ${task.smartGoal.estimatedHours}\n\n`;
  
  if (task.metrics.blockers.length > 0) {
    description += "**Current Blockers:**\n";
    task.metrics.blockers.forEach((blocker: any) => {
      description += `- ${blocker.description} (${blocker.severity})\n`;
    });
  }
  
  return description;
}

// Send notifications through integrated platforms
export const sendPlatformNotification = action({
  args: {
    platform: v.string(),
    recipients: v.array(v.string()),
    subject: v.string(),
    content: v.string(),
    priority: v.string(),
    taskId: v.optional(v.id("meeting_tasks")),
    actionItemId: v.optional(v.id("meeting_action_items")),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.runQuery(api.meetingIntegrations.getIntegration, {
      platform: args.platform,
    });
    
    if (!integration || integration.status !== "active") {
      throw new Error("Platform integration not configured");
    }

    try {
      switch (args.platform) {
        case "slack":
          await sendSlackNotification(integration, args);
          break;
          
        case "teams":
          await sendTeamsNotification(integration, args);
          break;
          
        case "email":
          await sendEmailNotification(args);
          break;
          
        default:
          throw new Error(`Unsupported notification platform: ${args.platform}`);
      }

      // Log notification
      await ctx.runMutation(api.meetingIntegrations.logNotification, {
        platform: args.platform,
        recipients: args.recipients,
        subject: args.subject,
        taskId: args.taskId,
        status: "sent",
      });

    } catch (error) {
      // Log error
      await ctx.runMutation(api.meetingIntegrations.logNotification, {
        platform: args.platform,
        recipients: args.recipients,
        subject: args.subject,
        taskId: args.taskId,
        status: "failed",
        error: error.message,
      });
      
      throw error;
    }
  },
});

// Slack notification
async function sendSlackNotification(integration: any, args: any) {
  const { credentials, settings } = integration;
  
  // Mock implementation - replace with actual Slack API calls
  console.log("Sending Slack notification:", args.subject);
  
  const blocks = [
    {
      type: "header",
      text: {
        type: "plain_text",
        text: args.subject,
      },
    },
    {
      type: "section",
      text: {
        type: "mrkdwn",
        text: args.content,
      },
    },
  ];
  
  if (args.taskId) {
    blocks.push({
      type: "actions",
      elements: [
        {
          type: "button",
          text: {
            type: "plain_text",
            text: "View Task",
          },
          url: `${process.env.APP_URL}/tasks/${args.taskId}`,
        },
      ],
    });
  }
  
  // Would send to Slack webhook URL
  return true;
}

// Teams notification
async function sendTeamsNotification(integration: any, args: any) {
  const { credentials, settings } = integration;
  
  // Mock implementation - replace with actual Teams API calls
  console.log("Sending Teams notification:", args.subject);
  
  const card = {
    "@type": "MessageCard",
    "@context": "https://schema.org/extensions",
    "summary": args.subject,
    "themeColor": getPriorityColor(args.priority),
    "sections": [
      {
        "activityTitle": args.subject,
        "text": args.content,
        "facts": args.taskId ? [
          {
            "name": "Priority",
            "value": args.priority,
          },
        ] : [],
      },
    ],
    "potentialAction": args.taskId ? [
      {
        "@type": "OpenUri",
        "name": "View Task",
        "targets": [
          {
            "os": "default",
            "uri": `${process.env.APP_URL}/tasks/${args.taskId}`,
          },
        ],
      },
    ] : [],
  };
  
  // Would send to Teams webhook URL
  return true;
}

// Email notification
async function sendEmailNotification(args: any) {
  // Mock implementation - replace with actual email service
  console.log("Sending email notification:", args.subject);
  
  const emailContent = {
    to: args.recipients,
    subject: args.subject,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #333;">${args.subject}</h2>
        <div style="color: #666; line-height: 1.6;">
          ${args.content.replace(/\n/g, '<br>')}
        </div>
        ${args.taskId ? `
          <div style="margin-top: 20px;">
            <a href="${process.env.APP_URL}/tasks/${args.taskId}" 
               style="background-color: #007bff; color: white; padding: 10px 20px; 
                      text-decoration: none; border-radius: 5px; display: inline-block;">
              View Task
            </a>
          </div>
        ` : ''}
      </div>
    `,
  };
  
  // Would send via email service
  return true;
}

function getPriorityColor(priority: string): string {
  const colorMap: Record<string, string> = {
    urgent: "#dc3545",
    high: "#fd7e14",
    normal: "#007bff",
    low: "#28a745",
  };
  return colorMap[priority] || "#6c757d";
}

// Database operations
export const getIntegration = query({
  args: { platform: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("platform_integrations")
      .filter(q => q.eq(q.field("platform"), args.platform))
      .first();
  },
});

export const updateTaskExternalId = mutation({
  args: {
    taskId: v.id("meeting_tasks"),
    platform: v.string(),
    externalId: v.string(),
  },
  handler: async (ctx, args) => {
    const task = await ctx.db.get(args.taskId);
    if (!task) return;

    const externalIds = task.externalIds || {};
    externalIds[args.platform] = args.externalId;

    await ctx.db.patch(args.taskId, {
      externalIds,
      lastSynced: Date.now(),
    });
  },
});

export const logSyncActivity = mutation({
  args: {
    integrationId: v.id("platform_integrations"),
    taskId: v.id("meeting_tasks"),
    operation: v.string(),
    status: v.string(),
    externalId: v.optional(v.string()),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("sync_logs", {
      integrationId: args.integrationId,
      taskId: args.taskId,
      operation: args.operation,
      status: args.status,
      externalId: args.externalId,
      error: args.error,
      timestamp: Date.now(),
    });

    // Update integration last sync
    if (args.status === "success") {
      await ctx.db.patch(args.integrationId, {
        lastSync: Date.now(),
      });
    }
  },
});

export const logNotification = mutation({
  args: {
    platform: v.string(),
    recipients: v.array(v.string()),
    subject: v.string(),
    taskId: v.optional(v.id("meeting_tasks")),
    status: v.string(),
    error: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.insert("notification_logs", {
      ...args,
      timestamp: Date.now(),
    });
  },
});

// Sync all tasks with a platform
export const syncPlatform = action({
  args: {
    integrationId: v.id("platform_integrations"),
    fullSync: v.boolean(),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.runQuery(api.meetingIntegrations.getIntegrationById, {
      integrationId: args.integrationId,
    });
    
    if (!integration || integration.status !== "active") {
      return;
    }

    // Get tasks to sync
    const tasks = await ctx.runQuery(api.meetingTaskManager.getTasks, {
      status: args.fullSync ? undefined : "in_progress",
      limit: 100,
    });

    let successCount = 0;
    let errorCount = 0;

    for (const task of tasks) {
      try {
        const shouldSync = args.fullSync || 
          !task.lastSynced || 
          task.lastUpdated > task.lastSynced;

        if (shouldSync) {
          await ctx.runAction(api.meetingIntegrations.syncTaskToPlatform, {
            taskId: task._id,
            platform: integration.platform,
            operation: task.externalIds?.[integration.platform] ? "update" : "create",
          });
          successCount++;
        }
      } catch (error) {
        console.error(`Failed to sync task ${task._id}:`, error);
        errorCount++;
      }
    }

    // Update sync statistics
    await ctx.runMutation(api.meetingIntegrations.updateSyncStats, {
      integrationId: args.integrationId,
      successCount,
      errorCount,
    });

    // Schedule next sync
    if (integration.settings.syncFrequency !== "realtime") {
      const delayMap = {
        hourly: 60 * 60 * 1000,
        daily: 24 * 60 * 60 * 1000,
      };
      const delay = delayMap[integration.settings.syncFrequency] || delayMap.daily;
      
      await ctx.scheduler.runAfter(delay, api.meetingIntegrations.syncPlatform, {
        integrationId: args.integrationId,
        fullSync: false,
      });
    }
  },
});

export const getIntegrationById = query({
  args: { integrationId: v.id("platform_integrations") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.integrationId);
  },
});

export const updateSyncStats = mutation({
  args: {
    integrationId: v.id("platform_integrations"),
    successCount: v.number(),
    errorCount: v.number(),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.db.get(args.integrationId);
    if (!integration) return;

    const stats = integration.syncStats || { totalSuccess: 0, totalErrors: 0 };
    stats.totalSuccess += args.successCount;
    stats.totalErrors += args.errorCount;
    stats.lastSyncSuccess = args.successCount;
    stats.lastSyncErrors = args.errorCount;

    await ctx.db.patch(args.integrationId, {
      syncStats: stats,
      lastSync: Date.now(),
    });
  },
});

// Get platform integrations
export const getIntegrations = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.query("platform_integrations").collect();
  },
});

// webhook handler for external updates
export const handleWebhook = action({
  args: {
    platform: v.string(),
    event: v.string(),
    data: v.any(),
  },
  handler: async (ctx, args) => {
    const integration = await ctx.runQuery(api.meetingIntegrations.getIntegration, {
      platform: args.platform,
    });
    
    if (!integration || integration.status !== "active") {
      throw new Error("Platform integration not configured");
    }

    // Process webhook based on platform and event
    switch (args.platform) {
      case "trello":
        await handleTrelloWebhook(ctx, args.event, args.data);
        break;
        
      case "asana":
        await handleAsanaWebhook(ctx, args.event, args.data);
        break;
        
      case "planner":
        await handlePlannerWebhook(ctx, args.event, args.data);
        break;
        
      default:
        console.log(`Unhandled webhook for platform: ${args.platform}`);
    }
  },
});

async function handleTrelloWebhook(ctx: any, event: string, data: any) {
  // Handle Trello webhook events
  console.log("Trello webhook:", event, data);
}

async function handleAsanaWebhook(ctx: any, event: string, data: any) {
  // Handle Asana webhook events
  console.log("Asana webhook:", event, data);
}

async function handlePlannerWebhook(ctx: any, event: string, data: any) {
  // Handle Planner webhook events
  console.log("Planner webhook:", event, data);
}