import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Agent management
export const createAgent = mutation({
  args: {
    name: v.string(),
    description: v.string(),
    type: v.string(),
    capabilities: v.array(v.string()),
    specializations: v.array(v.string()),
    model: v.string(),
    temperature: v.number(),
    maxTokens: v.number(),
    systemPrompt: v.string(),
    maxConcurrentTasks: v.number(),
    qualityThreshold: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const agentId = await ctx.db.insert("agents", {
      name: args.name,
      description: args.description,
      type: args.type,
      status: "active",
      capabilities: args.capabilities,
      specializations: args.specializations,
      model: args.model,
      temperature: args.temperature,
      maxTokens: args.maxTokens,
      systemPrompt: args.systemPrompt,
      isActive: true,
      createdAt: Date.now(),
      version: "1.0.0",
      tasksCompleted: 0,
      averageQualityScore: 0,
      averageResponseTime: 0,
      successRate: 0,
      totalWords: 0,
      currentLoad: 0,
      maxConcurrentTasks: args.maxConcurrentTasks,
      autoAssign: true,
      priority: "medium",
      qualityThreshold: args.qualityThreshold,
    });

    return agentId;
  },
});

export const getAgents = query({
  args: {
    type: v.optional(v.string()),
    status: v.optional(v.string()),
    specialization: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let agentsQuery = ctx.db.query("agents");
    
    if (args.type) {
      agentsQuery = agentsQuery.filter(q => q.eq(q.field("type"), args.type));
    }
    
    if (args.status) {
      agentsQuery = agentsQuery.filter(q => q.eq(q.field("status"), args.status));
    }
    
    if (args.specialization) {
      agentsQuery = agentsQuery.filter(q => q.eq(q.field("specializations"), args.specialization));
    }

    const agents = await agentsQuery
      .order("desc")
      .take(args.limit || 50);

    // Get current tasks for each agent
    const agentsWithTasks = await Promise.all(
      agents.map(async (agent) => {
        const activeTasks = await ctx.db
          .query("agent_tasks")
          .withIndex("by_agent", (q) => q.eq("agentId", agent._id))
          .filter(q => q.eq(q.field("status"), "in_progress"))
          .collect();

        return {
          ...agent,
          currentTasks: activeTasks.length,
          availability: agent.maxConcurrentTasks - activeTasks.length,
        };
      })
    );

    return agentsWithTasks;
  },
});

export const getAgent = query({
  args: { agentId: v.id("agents") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const agent = await ctx.db.get(args.agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    // Get recent tasks
    const recentTasks = await ctx.db
      .query("agent_tasks")
      .withIndex("by_agent", (q) => q.eq("agentId", args.agentId))
      .order("desc")
      .take(10);

    // Get performance data
    const performance = await ctx.db
      .query("agent_performance")
      .withIndex("by_agent", (q) => q.eq("agentId", args.agentId))
      .order("desc")
      .first();

    return {
      ...agent,
      recentTasks,
      performance,
    };
  },
});

export const updateAgent = mutation({
  args: {
    agentId: v.id("agents"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    status: v.optional(v.string()),
    systemPrompt: v.optional(v.string()),
    temperature: v.optional(v.number()),
    maxTokens: v.optional(v.number()),
    maxConcurrentTasks: v.optional(v.number()),
    qualityThreshold: v.optional(v.number()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const { agentId, ...updates } = args;
    const agent = await ctx.db.get(agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    await ctx.db.patch(agentId, {
      ...updates,
      lastActiveAt: Date.now(),
    });

    return agentId;
  },
});

// Agent task management
export const createAgentTask = mutation({
  args: {
    agentId: v.id("agents"),
    type: v.string(),
    priority: v.string(),
    tenderId: v.id("tenders"),
    sectionId: v.optional(v.id("bidSections")),
    dueDate: v.optional(v.number()),
    estimatedDuration: v.optional(v.number()),
    input: v.object({
      content: v.optional(v.string()),
      instructions: v.string(),
      context: v.object({
        tenderName: v.string(),
        clientName: v.string(),
        sectionTitle: v.string(),
        wordLimit: v.optional(v.number()),
        scoreWeight: v.optional(v.number()),
        requirements: v.optional(v.array(v.string())),
        existingContent: v.optional(v.string()),
      }),
      parameters: v.optional(v.object({
        tone: v.optional(v.string()),
        style: v.optional(v.string()),
        focus: v.optional(v.array(v.string())),
        keywords: v.optional(v.array(v.string())),
      })),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const agent = await ctx.db.get(args.agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    // Check agent availability
    const activeTasks = await ctx.db
      .query("agent_tasks")
      .withIndex("by_agent", (q) => q.eq("agentId", args.agentId))
      .filter(q => q.eq(q.field("status"), "in_progress"))
      .collect();

    if (activeTasks.length >= agent.maxConcurrentTasks) {
      throw new Error("Agent at maximum capacity");
    }

    const taskId = await ctx.db.insert("agent_tasks", {
      agentId: args.agentId,
      type: args.type,
      status: "assigned",
      priority: args.priority,
      tenderId: args.tenderId,
      sectionId: args.sectionId,
      assignedAt: Date.now(),
      dueDate: args.dueDate,
      estimatedDuration: args.estimatedDuration,
      progress: 0,
      retryCount: 0,
      input: args.input,
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0,
    });

    // Update agent load
    await ctx.db.patch(args.agentId, {
      currentLoad: agent.currentLoad + 1,
      lastActiveAt: Date.now(),
    });

    // Schedule task processing
    await ctx.scheduler.runAfter(0, api.agents.processTask, { taskId });

    return taskId;
  },
});

export const getAgentTasks = query({
  args: {
    agentId: v.optional(v.id("agents")),
    tenderId: v.optional(v.id("tenders")),
    sectionId: v.optional(v.id("bidSections")),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("agent_tasks");
    
    const indexedQuery = args.agentId
      ? baseQuery.withIndex("by_agent", (q) => q.eq("agentId", args.agentId))
      : baseQuery;
    
    const tenderFilteredQuery = args.tenderId
      ? indexedQuery.filter(q => q.eq(q.field("tenderId"), args.tenderId))
      : indexedQuery;
    
    const sectionFilteredQuery = args.sectionId
      ? tenderFilteredQuery.filter(q => q.eq(q.field("sectionId"), args.sectionId))
      : tenderFilteredQuery;
    
    const statusFilteredQuery = args.status
      ? sectionFilteredQuery.filter(q => q.eq(q.field("status"), args.status))
      : sectionFilteredQuery;

    const tasks = await statusFilteredQuery
      .order("desc")
      .take(args.limit || 50);

    // Get agent info for each task
    const tasksWithAgents = await Promise.all(
      tasks.map(async (task) => {
        const agent = await ctx.db.get(task.agentId);
        const tender = await ctx.db.get(task.tenderId);
        const section = task.sectionId ? await ctx.db.get(task.sectionId) : null;

        return {
          ...task,
          agentName: agent?.name,
          tenderName: tender?.name,
          sectionTitle: section?.title,
        };
      })
    );

    return tasksWithAgents;
  },
});

export const updateTaskStatus = mutation({
  args: {
    taskId: v.id("agent_tasks"),
    status: v.string(),
    progress: v.optional(v.number()),
    errorMessage: v.optional(v.string()),
    output: v.optional(v.object({
      content: v.string(),
      wordCount: v.number(),
      confidence: v.number(),
      suggestions: v.optional(v.array(v.string())),
      warnings: v.optional(v.array(v.string())),
      processingTime: v.number(),
      revisionsNeeded: v.optional(v.boolean()),
      clarity: v.number(),
      relevance: v.number(),
      completeness: v.number(),
      persuasiveness: v.number(),
    })),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const task = await ctx.db.get(args.taskId);
    if (!task) {
      throw new Error("Task not found");
    }

    const updates: any = {
      status: args.status,
      progress: args.progress ?? task.progress,
      errorMessage: args.errorMessage,
    };

    if (args.status === "in_progress" && !task.startedAt) {
      updates.startedAt = Date.now();
    }

    if (args.status === "completed" || args.status === "failed") {
      updates.completedAt = Date.now();
      updates.actualDuration = task.startedAt ? Date.now() - task.startedAt : 0;
      
      // Update agent load
      const agent = await ctx.db.get(task.agentId);
      if (agent) {
        await ctx.db.patch(task.agentId, {
          currentLoad: Math.max(0, agent.currentLoad - 1),
          lastActiveAt: Date.now(),
        });
      }
    }

    if (args.output) {
      updates.output = args.output;
    }

    await ctx.db.patch(args.taskId, updates);

    return args.taskId;
  },
});

// Agent task processing (AI integration)
export const processTask = action({
  args: { taskId: v.id("agent_tasks") },
  handler: async (ctx, args) => {
    const task = await ctx.runQuery(api.agents.getTaskById, { taskId: args.taskId });
    if (!task || task.status !== "assigned") {
      return;
    }

    const agent = await ctx.runQuery(api.agents.getAgent, { agentId: task.agentId });
    if (!agent) {
      return;
    }

    // Mark task as in progress
    await ctx.runMutation(api.agents.updateTaskStatus, {
      taskId: args.taskId,
      status: "in_progress",
      progress: 0,
    });

    try {
      // Simulate AI processing with OpenAI
      const prompt = `${agent.systemPrompt}\n\nTask: ${task.input.instructions}\n\nContext: ${JSON.stringify(task.input.context)}\n\nParameters: ${JSON.stringify(task.input.parameters || {})}`;
      
      const startTime = Date.now();
      
      // Mock AI response (in real implementation, use OpenAI API)
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate processing time
      
      const mockResponse = {
        content: `Generated content for ${task.input.context.sectionTitle}. This is a mock response that would normally be generated by the AI agent based on the specific requirements and context provided.`,
        wordCount: 150,
        confidence: 0.85,
        suggestions: ["Consider adding more specific examples", "Review compliance requirements"],
        warnings: [],
        processingTime: Date.now() - startTime,
        revisionsNeeded: false,
        clarity: 0.9,
        relevance: 0.85,
        completeness: 0.8,
        persuasiveness: 0.75,
      };

      // Update task with results
      await ctx.runMutation(api.agents.updateTaskStatus, {
        taskId: args.taskId,
        status: "completed",
        progress: 100,
        output: mockResponse,
      });

      // Update agent statistics
      await ctx.runMutation(api.agents.updateAgentStats, {
        agentId: task.agentId,
        taskCompleted: true,
        qualityScore: mockResponse.clarity * 0.25 + mockResponse.relevance * 0.25 + mockResponse.completeness * 0.25 + mockResponse.persuasiveness * 0.25,
        responseTime: mockResponse.processingTime,
        wordCount: mockResponse.wordCount,
      });

    } catch (error) {
      await ctx.runMutation(api.agents.updateTaskStatus, {
        taskId: args.taskId,
        status: "failed",
        progress: 0,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
    }
  },
});

export const getTaskById = query({
  args: { taskId: v.id("agent_tasks") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.get(args.taskId);
  },
});

export const updateAgentStats = mutation({
  args: {
    agentId: v.id("agents"),
    taskCompleted: v.boolean(),
    qualityScore: v.number(),
    responseTime: v.number(),
    wordCount: v.number(),
  },
  handler: async (ctx, args) => {
    const agent = await ctx.db.get(args.agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    if (args.taskCompleted) {
      const newTasksCompleted = agent.tasksCompleted + 1;
      const newAverageQualityScore = (agent.averageQualityScore * agent.tasksCompleted + args.qualityScore) / newTasksCompleted;
      const newAverageResponseTime = (agent.averageResponseTime * agent.tasksCompleted + args.responseTime) / newTasksCompleted;
      
      await ctx.db.patch(args.agentId, {
        tasksCompleted: newTasksCompleted,
        averageQualityScore: newAverageQualityScore,
        averageResponseTime: newAverageResponseTime,
        totalWords: agent.totalWords + args.wordCount,
        lastActiveAt: Date.now(),
      });
    }

    return args.agentId;
  },
});

// Agent performance tracking
export const generateAgentPerformance = mutation({
  args: {
    agentId: v.id("agents"),
    periodStart: v.number(),
    periodEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const agent = await ctx.db.get(args.agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    // Get tasks in the period
    const tasks = await ctx.db
      .query("agent_tasks")
      .withIndex("by_agent", (q) => q.eq("agentId", args.agentId))
      .filter(q => 
        q.and(
          q.gte(q.field("assignedAt"), args.periodStart),
          q.lte(q.field("assignedAt"), args.periodEnd)
        )
      )
      .collect();

    const completedTasks = tasks.filter(t => t.status === "completed");
    const failedTasks = tasks.filter(t => t.status === "failed");

    const metrics = {
      tasksCompleted: completedTasks.length,
      averageQualityScore: completedTasks.reduce((sum, t) => sum + (t.qualityScore || 0), 0) / completedTasks.length || 0,
      averageResponseTime: completedTasks.reduce((sum, t) => sum + (t.actualDuration || 0), 0) / completedTasks.length || 0,
      successRate: tasks.length > 0 ? completedTasks.length / tasks.length : 0,
      totalWords: completedTasks.reduce((sum, t) => sum + (t.output?.wordCount || 0), 0),
      errorRate: tasks.length > 0 ? failedTasks.length / tasks.length : 0,
    };

    const performanceId = await ctx.db.insert("agent_performance", {
      agentId: args.agentId,
      periodStart: args.periodStart,
      periodEnd: args.periodEnd,
      ...metrics,
      breakdown: {
        byTaskType: {},
        bySectionType: {},
        byPriority: {},
      },
      generatedAt: Date.now(),
    });

    return performanceId;
  },
});

// Agent chat integration
export const sendAgentMessage = mutation({
  args: {
    threadId: v.id("chat_threads"),
    agentId: v.id("agents"),
    content: v.string(),
    replyToId: v.optional(v.id("chat_messages")),
    metadata: v.optional(v.object({
      confidence: v.number(),
      model: v.string(),
      tokens: v.number(),
      taskId: v.optional(v.id("agent_tasks")),
    })),
  },
  handler: async (ctx, args) => {
    const agent = await ctx.db.get(args.agentId);
    if (!agent) {
      throw new Error("Agent not found");
    }

    const thread = await ctx.db.get(args.threadId);
    if (!thread) {
      throw new Error("Thread not found");
    }

    if (!thread.allowAgentAccess) {
      throw new Error("Agent access not allowed in this thread");
    }

    const messageId = await ctx.db.insert("chat_messages", {
      threadId: args.threadId,
      senderId: args.agentId,
      senderType: "agent",
      type: "text",
      content: args.content,
      timestamp: Date.now(),
      status: "sent",
      replyToId: args.replyToId,
      mentions: [],
      attachments: [],
      reactions: [],
      metadata: {
        tags: [],
        agentTaskId: args.metadata?.taskId,
      },
      ai: {
        generated: true,
        model: args.metadata?.model || agent.model,
        confidence: args.metadata?.confidence || 0.8,
        tokens: args.metadata?.tokens || 0,
      },
    });

    // Update thread activity
    await ctx.db.patch(args.threadId, {
      lastActivity: Date.now(),
      lastMessageId: messageId,
      messageCount: thread.messageCount + 1,
    });

    return messageId;
  },
});

export const getAvailableAgents = query({
  args: {
    taskType: v.optional(v.string()),
    specialization: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    let agentsQuery = ctx.db.query("agents").filter(q => 
      q.and(
        q.eq(q.field("isActive"), true),
        q.eq(q.field("status"), "active")
      )
    );

    if (args.specialization) {
      agentsQuery = agentsQuery.filter(q => q.eq(q.field("specializations"), args.specialization));
    }

    const agents = await agentsQuery.collect();

    // Filter by availability
    const availableAgents = await Promise.all(
      agents.map(async (agent) => {
        const activeTasks = await ctx.db
          .query("agent_tasks")
          .withIndex("by_agent", (q) => q.eq("agentId", agent._id))
          .filter(q => q.eq(q.field("status"), "in_progress"))
          .collect();

        const availability = agent.maxConcurrentTasks - activeTasks.length;
        
        return availability > 0 ? {
          ...agent,
          availability,
          currentLoad: activeTasks.length,
          efficiency: agent.averageQualityScore * (1 - agent.averageResponseTime / 60000), // Factor in response time
        } : null;
      })
    );

    return availableAgents
      .filter(Boolean)
      .sort((a, b) => b!.efficiency - a!.efficiency);
  },
});