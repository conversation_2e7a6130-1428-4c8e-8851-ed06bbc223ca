import { v } from "convex/values";
import { mutation, query, action } from "./_generated/server";
import { api } from "./_generated/api";
import { Doc, Id } from "./_generated/dataModel";

// Meeting transcript processing
export const processMeetingTranscript = mutation({
  args: {
    meetingId: v.id("scheduler_meetings"),
    transcript: v.object({
      rawText: v.string(),
      speakers: v.array(v.object({
        id: v.string(),
        name: v.string(),
        role: v.optional(v.string()),
      })),
      segments: v.array(v.object({
        speakerId: v.string(),
        text: v.string(),
        startTime: v.number(),
        endTime: v.number(),
        confidence: v.optional(v.number()),
      })),
      duration: v.number(),
      language: v.string(),
    }),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const meeting = await ctx.db.get(args.meetingId);
    if (!meeting) {
      throw new Error("Meeting not found");
    }

    // Create meeting transcript record
    const transcriptId = await ctx.db.insert("meeting_transcripts", {
      meetingId: args.meetingId,
      transcript: args.transcript,
      status: "processing",
      processedAt: Date.now(),
      wordCount: args.transcript.rawText.split(/\s+/).length,
      speakerCount: args.transcript.speakers.length,
      duration: args.transcript.duration,
      language: args.transcript.language,
    });

    // Schedule AI summarization
    await ctx.scheduler.runAfter(0, api.meetingSummarizer.generateSummary, {
      transcriptId,
      meetingId: args.meetingId,
    });

    return transcriptId;
  },
});

// AI-powered meeting summarization
export const generateSummary = action({
  args: {
    transcriptId: v.id("meeting_transcripts"),
    meetingId: v.id("scheduler_meetings"),
  },
  handler: async (ctx, args): Promise<Id<"agent_tasks">> => {
    const transcript = await ctx.runQuery(api.meetingSummarizer.getTranscript, {
      transcriptId: args.transcriptId,
    });
    
    if (!transcript) {
      throw new Error("Transcript not found");
    }

    const meeting: Doc<"scheduler_meetings"> | null = await ctx.runQuery(api.meetingSummarizer.getMeetingDetails, {
      meetingId: args.meetingId,
    });

    // Create summarizer agent task
    let agentId: Id<"agents"> | null = await ctx.runQuery(api.meetingSummarizer.getSummarizerAgent);
    if (!agentId) {
      // Create a new summarizer agent if none exists
      agentId = await ctx.runMutation(api.meetingSummarizer.createSummarizerAgent);
    }

    const summaryTask: Id<"agent_tasks"> = await ctx.runMutation(api.agents.createAgentTask, {
      agentId,
      type: "meeting_summary",
      priority: "high",
      tenderId: meeting?.tenderId || "temp_tender" as Id<"tenders">,
      input: {
        instructions: "Analyze the meeting transcript and generate a comprehensive summary with key decisions, action items, and insights.",
        context: {
          tenderName: meeting?.projectContext?.projectName || "General Meeting",
          clientName: "Internal",
          sectionTitle: "Meeting Summary",
          requirements: [
            "Executive summary (2-3 paragraphs)",
            "Key decisions and outcomes",
            "Action items with assignees",
            "Risks and concerns raised",
            "Next steps and follow-up items",
            "Participant engagement analysis",
          ],
        },
        parameters: {
          tone: "professional",
          style: "structured",
          focus: ["decisions", "actions", "risks", "commitments"],
        },
      },
    });

    // Update transcript with task reference
    await ctx.runMutation(api.meetingSummarizer.updateTranscriptStatus, {
      transcriptId: args.transcriptId,
      status: "summarizing",
      summaryTaskId: summaryTask,
    });

    // Process individual segments for detailed analysis
    await ctx.runAction(api.meetingSummarizer.analyzeSegments, {
      transcriptId: args.transcriptId,
      meetingId: args.meetingId,
    });

    return summaryTask;
  },
});

// Analyze meeting segments for insights
export const analyzeSegments = action({
  args: {
    transcriptId: v.id("meeting_transcripts"),
    meetingId: v.id("scheduler_meetings"),
  },
  handler: async (ctx, args) => {
    const transcript = await ctx.runQuery(api.meetingSummarizer.getTranscript, {
      transcriptId: args.transcriptId,
    });

    if (!transcript) return;

    const analysisResults = {
      keyTopics: [] as string[],
      decisions: [] as any[],
      actionItems: [] as any[],
      risks: [] as any[],
      participantMetrics: {} as Record<string, any>,
      sentimentAnalysis: {
        overall: "neutral" as string,
        byTopic: {} as Record<string, string>,
        byParticipant: {} as Record<string, string>,
      },
      engagementScore: 0,
    };

    // Analyze each segment
    for (const segment of transcript.transcript.segments) {
      // Extract decisions (segments containing decision keywords)
      if (/\b(decided|agreed|approved|confirmed|resolved)\b/i.test(segment.text)) {
        analysisResults.decisions.push({
          text: segment.text,
          speaker: segment.speakerId,
          timestamp: segment.startTime,
          confidence: 0.8,
        });
      }

      // Extract action items (segments with action keywords)
      if (/\b(will|shall|need to|must|action|follow up|responsible for|by next)\b/i.test(segment.text)) {
        const actionItem = extractActionItem(segment, transcript.transcript.speakers);
        if (actionItem) {
          analysisResults.actionItems.push(actionItem);
        }
      }

      // Extract risks and concerns
      if (/\b(risk|concern|issue|problem|challenge|worried|careful)\b/i.test(segment.text)) {
        analysisResults.risks.push({
          text: segment.text,
          speaker: segment.speakerId,
          timestamp: segment.startTime,
          severity: determineSeverity(segment.text),
        });
      }

      // Update participant metrics
      if (!analysisResults.participantMetrics[segment.speakerId]) {
        analysisResults.participantMetrics[segment.speakerId] = {
          speakingTime: 0,
          segmentCount: 0,
          averageSegmentLength: 0,
          questionsAsked: 0,
          decisionsContributed: 0,
        };
      }

      const metrics = analysisResults.participantMetrics[segment.speakerId];
      metrics.speakingTime += segment.endTime - segment.startTime;
      metrics.segmentCount += 1;
      metrics.averageSegmentLength = metrics.speakingTime / metrics.segmentCount;
      
      if (segment.text.includes("?")) {
        metrics.questionsAsked += 1;
      }
    }

    // Calculate engagement score
    const totalParticipants = Object.keys(analysisResults.participantMetrics).length;
    const activeParticipants = Object.values(analysisResults.participantMetrics)
      .filter((m: any) => m.segmentCount > 5).length;
    analysisResults.engagementScore = (activeParticipants / totalParticipants) * 100;

    // Save analysis results
    await ctx.runMutation(api.meetingSummarizer.saveAnalysisResults, {
      transcriptId: args.transcriptId,
      analysis: analysisResults,
    });

    // Create action items in task management system
    for (const actionItem of analysisResults.actionItems) {
      await ctx.runMutation(api.meetingSummarizer.createActionItem, {
        meetingId: args.meetingId,
        actionItem,
      });
    }

    return analysisResults;
  },
});

// Helper function to extract action items
function extractActionItem(segment: any, speakers: any[]) {
  const text = segment.text;
  const speakerName = speakers.find(s => s.id === segment.speakerId)?.name || "Unknown";
  
  // Pattern matching for action items
  const actionPatterns = [
    /(?:I|we|you|they)\s+(?:will|shall|need to|must)\s+(.+?)(?:\.|$)/i,
    /action:\s*(.+?)(?:\.|$)/i,
    /(?:responsible for|assigned to)\s+(.+?)(?:\.|$)/i,
  ];

  for (const pattern of actionPatterns) {
    const match = text.match(pattern);
    if (match) {
      // Try to extract assignee
      let assignee = speakerName;
      const assigneeMatch = text.match(/(?:assigned to|responsible:|owner:)\s*(\w+)/i);
      if (assigneeMatch) {
        assignee = assigneeMatch[1];
      }

      // Try to extract deadline
      let deadline = null;
      const deadlineMatch = text.match(/(?:by|before|until|deadline:)\s*([^,.]+?)(?:\.|,|$)/i);
      if (deadlineMatch) {
        deadline = deadlineMatch[1].trim();
      }

      return {
        description: match[1].trim(),
        assignee,
        speaker: speakerName,
        deadline,
        timestamp: segment.startTime,
        originalText: text,
        priority: determinePriority(text),
        category: determineCategory(text),
      };
    }
  }

  return null;
}

// Helper function to determine severity
function determineSeverity(text: string): string {
  if (/\b(critical|urgent|severe|major)\b/i.test(text)) return "high";
  if (/\b(moderate|medium|some)\b/i.test(text)) return "medium";
  return "low";
}

// Helper function to determine priority
function determinePriority(text: string): string {
  if (/\b(urgent|asap|immediately|critical|high priority)\b/i.test(text)) return "urgent";
  if (/\b(important|priority|soon)\b/i.test(text)) return "high";
  return "normal";
}

// Helper function to determine category
function determineCategory(text: string): string {
  if (/\b(technical|development|code|system)\b/i.test(text)) return "technical";
  if (/\b(document|write|prepare|draft)\b/i.test(text)) return "documentation";
  if (/\b(meet|call|schedule|discuss)\b/i.test(text)) return "communication";
  if (/\b(review|approve|check|validate)\b/i.test(text)) return "review";
  return "general";
}

// Database queries and mutations
export const getTranscript = query({
  args: { transcriptId: v.id("meeting_transcripts") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.get(args.transcriptId);
  },
});

export const getMeetingDetails = query({
  args: { meetingId: v.id("scheduler_meetings") },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    return await ctx.db.get(args.meetingId);
  },
});

export const getSummarizerAgent = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Find existing meeting summarizer agent
    const agent = await ctx.db
      .query("agents")
      .filter(q =>
        q.and(
          q.eq(q.field("type"), "summarizer"),
          q.eq(q.field("status"), "active")
        )
      )
      .first();

    return agent?._id || null;
  },
});

export const createSummarizerAgent = mutation({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Create a new summarizer agent
    const agentId = await ctx.db.insert("agents", {
      name: "Meeting Summarizer Pro",
      description: "AI-powered meeting transcript analysis and summarization specialist",
      type: "summarizer",
      status: "active",
      capabilities: [
        "transcript_analysis",
        "summary_generation",
        "action_extraction",
        "decision_tracking",
        "sentiment_analysis",
        "engagement_scoring",
      ],
      specializations: [
        "meeting_minutes",
        "action_items",
        "decision_documentation",
        "risk_identification",
      ],
      model: "gpt-4",
      temperature: 0.3,
      maxTokens: 4000,
      systemPrompt: `You are an expert meeting analyst and summarizer. Your role is to:
1. Generate comprehensive meeting summaries with clear structure
2. Extract and categorize all action items with assignees and deadlines
3. Identify key decisions and outcomes
4. Highlight risks and concerns raised
5. Analyze participant engagement and contribution
6. Provide insights on meeting effectiveness
7. Format outputs for easy consumption and follow-up

Focus on clarity, completeness, and actionability.`,
      isActive: true,
      createdAt: Date.now(),
      version: "1.0.0",
      tasksCompleted: 0,
      averageQualityScore: 0,
      averageResponseTime: 0,
      successRate: 0,
      totalWords: 0,
      currentLoad: 0,
      maxConcurrentTasks: 5,
      autoAssign: true,
      priority: "high",
      qualityThreshold: 0.8,
    });

    return agentId;
  },
});

export const updateTranscriptStatus = mutation({
  args: {
    transcriptId: v.id("meeting_transcripts"),
    status: v.string(),
    summaryTaskId: v.optional(v.id("agent_tasks")),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.transcriptId, {
      status: args.status,
      summaryTaskId: args.summaryTaskId,
      lastUpdated: Date.now(),
    });
  },
});

export const saveAnalysisResults = mutation({
  args: {
    transcriptId: v.id("meeting_transcripts"),
    analysis: v.object({
      keyTopics: v.array(v.string()),
      decisions: v.array(v.any()),
      actionItems: v.array(v.any()),
      risks: v.array(v.any()),
      participantMetrics: v.any(),
      sentimentAnalysis: v.any(),
      engagementScore: v.number(),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.transcriptId, {
      analysisResults: args.analysis,
      analyzedAt: Date.now(),
      status: "analyzed",
    });
  },
});

export const createActionItem = mutation({
  args: {
    meetingId: v.id("scheduler_meetings"),
    actionItem: v.object({
      description: v.string(),
      assignee: v.string(),
      speaker: v.string(),
      deadline: v.optional(v.string()),
      timestamp: v.number(),
      originalText: v.string(),
      priority: v.string(),
      category: v.string(),
    }),
  },
  handler: async (ctx, args) => {
    const meeting = await ctx.db.get(args.meetingId);
    if (!meeting) {
      throw new Error("Meeting not found");
    }

    // Create action item record
    const actionItemId = await ctx.db.insert("meeting_action_items", {
      meetingId: args.meetingId,
      tenderId: meeting.tenderId,
      description: args.actionItem.description,
      assignee: args.actionItem.assignee,
      createdBy: args.actionItem.speaker,
      priority: args.actionItem.priority,
      category: args.actionItem.category,
      status: "pending",
      createdAt: Date.now(),
      dueDate: parseDueDate(args.actionItem.deadline),
      source: {
        type: "meeting_transcript",
        timestamp: args.actionItem.timestamp,
        originalText: args.actionItem.originalText,
      },
      tags: extractTags(args.actionItem.description),
      notificationsSent: 0,
      lastNotificationAt: null,
    });

    // Schedule initial notification
    await ctx.scheduler.runAfter(1000, api.meetingTaskManager.sendActionItemNotification, {
      actionItemId,
      notificationType: "created",
    });

    return actionItemId;
  },
});

// Helper function to parse deadline strings
function parseDueDate(deadline: string | null | undefined): number | null {
  if (!deadline) return null;

  const now = new Date();
  const deadlineLower = deadline.toLowerCase();

  // Common patterns
  if (deadlineLower.includes("tomorrow")) {
    return new Date(now.getTime() + 24 * 60 * 60 * 1000).getTime();
  }
  if (deadlineLower.includes("next week")) {
    return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).getTime();
  }
  if (deadlineLower.includes("end of week")) {
    const daysUntilFriday = (5 - now.getDay() + 7) % 7 || 7;
    return new Date(now.getTime() + daysUntilFriday * 24 * 60 * 60 * 1000).getTime();
  }
  if (deadlineLower.includes("next month")) {
    return new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000).getTime();
  }

  // Try to parse as date
  const parsed = Date.parse(deadline);
  if (!isNaN(parsed)) {
    return parsed;
  }

  // Default to 1 week from now if unclear
  return new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000).getTime();
}

// Helper function to extract tags
function extractTags(text: string): string[] {
  const tags = [];
  
  // Extract hashtags
  const hashtagMatches = text.match(/#\w+/g);
  if (hashtagMatches) {
    tags.push(...hashtagMatches.map(tag => tag.substring(1)));
  }

  // Add common keywords as tags
  const keywords = ["tender", "proposal", "compliance", "budget", "timeline", "resource"];
  for (const keyword of keywords) {
    if (text.toLowerCase().includes(keyword)) {
      tags.push(keyword);
    }
  }

  return [...new Set(tags)]; // Remove duplicates
}

// Get meeting summaries
export const getMeetingSummaries = query({
  args: {
    meetingId: v.optional(v.id("scheduler_meetings")),
    status: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const baseQuery = ctx.db.query("meeting_transcripts");
    
    const meetingFilteredQuery = args.meetingId
      ? baseQuery.filter(q => q.eq(q.field("meetingId"), args.meetingId))
      : baseQuery;
    
    const statusFilteredQuery = args.status
      ? meetingFilteredQuery.filter(q => q.eq(q.field("status"), args.status))
      : meetingFilteredQuery;

    const transcripts = await statusFilteredQuery
      .order("desc")
      .take(args.limit || 20);

    // Get associated meetings and summaries
    const results = await Promise.all(
      transcripts.map(async (transcript) => {
        const meeting = await ctx.db.get(transcript.meetingId);
        let summary = null;
        
        if (transcript.summaryTaskId) {
          const task = await ctx.db.get(transcript.summaryTaskId);
          if (task?.output) {
            summary = task.output;
          }
        }

        return {
          ...transcript,
          meetingTitle: meeting?.title,
          meetingDate: meeting?.startTime,
          summary,
        };
      })
    );

    return results;
  },
});