import { defineTable } from "convex/server";
import { v } from "convex/values";

// Zero-Touch System Tables
export const zeroTouchTables = {
  // Workflow Orchestration Tables
  workflows: defineTable({
    name: v.string(),
    description: v.string(),
    type: v.string(), // "tender_kickoff", "document_analysis", "team_coordination", etc.
    status: v.string(), // "active", "paused", "completed", "failed"
    version: v.string(),
    config: v.object({
      triggers: v.array(v.object({
        type: v.string(), // "email", "calendar", "webhook", "manual"
        conditions: v.object({}),
        parameters: v.object({}),
      })),
      steps: v.array(v.object({
        id: v.string(),
        name: v.string(),
        type: v.string(),
        agentType: v.optional(v.string()),
        parameters: v.object({}),
        dependencies: v.array(v.string()),
        timeout: v.optional(v.number()),
        retryPolicy: v.object({
          maxRetries: v.number(),
          backoffType: v.string(),
          initialDelay: v.number(),
        }),
      })),
      notifications: v.array(v.object({
        channel: v.string(), // "email", "sms", "slack", "teams"
        recipients: v.array(v.string()),
        events: v.array(v.string()),
        template: v.string(),
      })),
    }),
    createdBy: v.string(),
    createdAt: v.number(),
    updatedAt: v.number(),
    lastExecutedAt: v.optional(v.number()),
    executionCount: v.number(),
    successCount: v.number(),
    failureCount: v.number(),
    averageDuration: v.optional(v.number()),
    isActive: v.boolean(),
    tags: v.array(v.string()),
  })
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_creator", ["createdBy"]),

  workflow_instances: defineTable({
    workflowId: v.id("workflows"),
    status: v.string(), // "pending", "running", "completed", "failed", "cancelled"
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    tenderId: v.optional(v.id("tenders")),
    triggeredBy: v.string(), // "system", "user", "schedule", "webhook"
    triggerData: v.object({}),
    currentStep: v.optional(v.string()),
    progress: v.number(),
    context: v.object({
      variables: v.object({}),
      artifacts: v.array(v.object({
        name: v.string(),
        type: v.string(),
        url: v.optional(v.string()),
        data: v.optional(v.any()),
      })),
    }),
    steps: v.array(v.object({
      stepId: v.string(),
      status: v.string(),
      startedAt: v.optional(v.number()),
      completedAt: v.optional(v.number()),
      agentId: v.optional(v.id("agents")),
      input: v.object({}),
      output: v.optional(v.object({})),
      error: v.optional(v.string()),
      retryCount: v.number(),
    })),
    error: v.optional(v.object({
      message: v.string(),
      step: v.string(),
      code: v.string(),
      details: v.object({}),
    })),
    metadata: v.object({
      priority: v.string(),
      dueDate: v.optional(v.number()),
      tags: v.array(v.string()),
      customFields: v.object({}),
    }),
  })
    .index("by_workflow", ["workflowId"])
    .index("by_status", ["status"])
    .index("by_tender", ["tenderId"])
    .index("by_triggered_by", ["triggeredBy"]),

  // Event System Tables
  events: defineTable({
    type: v.string(), // "tender_created", "email_received", "deadline_approaching", etc.
    source: v.string(), // "system", "email", "calendar", "webhook", "user"
    status: v.string(), // "pending", "processing", "processed", "failed"
    priority: v.string(), // "low", "medium", "high", "critical"
    payload: v.object({}),
    correlationId: v.optional(v.string()),
    parentEventId: v.optional(v.id("events")),
    createdAt: v.number(),
    processedAt: v.optional(v.number()),
    retryCount: v.number(),
    maxRetries: v.number(),
    error: v.optional(v.object({
      message: v.string(),
      code: v.string(),
      details: v.object({}),
    })),
    handlers: v.array(v.object({
      name: v.string(),
      status: v.string(),
      startedAt: v.optional(v.number()),
      completedAt: v.optional(v.number()),
      error: v.optional(v.string()),
    })),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      userId: v.optional(v.string()),
      tags: v.array(v.string()),
    }),
  })
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_source", ["source"])
    .index("by_priority", ["priority"])
    .index("by_correlation", ["correlationId"]),

  // Message Queue Tables
  message_queue: defineTable({
    queue: v.string(), // Queue name/topic
    status: v.string(), // "pending", "processing", "completed", "failed", "dead_letter"
    priority: v.number(), // 0-10, higher is more urgent
    payload: v.object({}),
    headers: v.object({}),
    createdAt: v.number(),
    scheduledFor: v.optional(v.number()),
    processingStartedAt: v.optional(v.number()),
    completedAt: v.optional(v.number()),
    retryCount: v.number(),
    maxRetries: v.number(),
    visibilityTimeout: v.optional(v.number()),
    error: v.optional(v.object({
      message: v.string(),
      code: v.string(),
      stack: v.optional(v.string()),
    })),
    result: v.optional(v.object({})),
    metadata: v.object({
      correlationId: v.optional(v.string()),
      causationId: v.optional(v.string()),
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      tags: v.array(v.string()),
    }),
  })
    .index("by_queue", ["queue"])
    .index("by_status", ["status"])
    .index("by_priority", ["priority"])
    .index("by_scheduled", ["scheduledFor"]),

  // Audit Log Tables
  audit_logs: defineTable({
    action: v.string(),
    entityType: v.string(),
    entityId: v.string(),
    userId: v.optional(v.string()),
    agentId: v.optional(v.id("agents")),
    timestamp: v.number(),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    success: v.boolean(),
    changes: v.optional(v.object({
      before: v.optional(v.object({})),
      after: v.optional(v.object({})),
      diff: v.optional(v.array(v.object({
        field: v.string(),
        oldValue: v.any(),
        newValue: v.any(),
      }))),
    })),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      eventId: v.optional(v.id("events")),
      requestId: v.optional(v.string()),
      sessionId: v.optional(v.string()),
      tags: v.array(v.string()),
      context: v.object({}),
    }),
    error: v.optional(v.object({
      message: v.string(),
      code: v.string(),
      details: v.object({}),
    })),
  })
    .index("by_action", ["action"])
    .index("by_entity", ["entityType", "entityId"])
    .index("by_user", ["userId"])
    .index("by_agent", ["agentId"])
    .index("by_timestamp", ["timestamp"]),

  // Integration Configuration Tables
  integration_configs: defineTable({
    name: v.string(),
    type: v.string(), // "gmail", "calendar", "twilio", "slack", "teams", "github"
    status: v.string(), // "active", "inactive", "error"
    credentials: v.object({
      encrypted: v.boolean(),
      data: v.string(), // Encrypted credentials
      keyId: v.string(),
    }),
    config: v.object({
      endpoint: v.optional(v.string()),
      apiVersion: v.optional(v.string()),
      rateLimit: v.optional(v.object({
        requests: v.number(),
        period: v.string(),
      })),
      retryPolicy: v.object({
        maxRetries: v.number(),
        backoffType: v.string(),
        initialDelay: v.number(),
      }),
      customSettings: v.object({}),
    }),
    permissions: v.array(v.string()),
    webhooks: v.array(v.object({
      url: v.string(),
      events: v.array(v.string()),
      secret: v.string(),
      isActive: v.boolean(),
    })),
    lastSyncAt: v.optional(v.number()),
    lastErrorAt: v.optional(v.number()),
    lastError: v.optional(v.string()),
    createdAt: v.number(),
    updatedAt: v.number(),
    createdBy: v.string(),
  })
    .index("by_type", ["type"])
    .index("by_status", ["status"]),

  // Notification Tables
  notifications: defineTable({
    type: v.string(), // "tender_assigned", "deadline_reminder", "status_update", etc.
    channel: v.string(), // "email", "sms", "push", "in_app", "slack", "teams"
    status: v.string(), // "pending", "sent", "delivered", "failed", "cancelled"
    priority: v.string(), // "low", "medium", "high", "critical"
    recipient: v.object({
      type: v.string(), // "user", "email", "phone", "channel"
      id: v.string(),
      name: v.optional(v.string()),
      metadata: v.object({}),
    }),
    subject: v.string(),
    content: v.object({
      text: v.optional(v.string()),
      html: v.optional(v.string()),
      template: v.optional(v.string()),
      variables: v.object({}),
    }),
    scheduledFor: v.optional(v.number()),
    sentAt: v.optional(v.number()),
    deliveredAt: v.optional(v.number()),
    readAt: v.optional(v.number()),
    retryCount: v.number(),
    maxRetries: v.number(),
    error: v.optional(v.object({
      message: v.string(),
      code: v.string(),
      provider: v.optional(v.string()),
    })),
    metadata: v.object({
      tenderId: v.optional(v.id("tenders")),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      eventId: v.optional(v.id("events")),
      batchId: v.optional(v.string()),
      tags: v.array(v.string()),
      tracking: v.object({
        opens: v.number(),
        clicks: v.number(),
        lastOpenedAt: v.optional(v.number()),
      }),
    }),
    createdAt: v.number(),
    expiresAt: v.optional(v.number()),
  })
    .index("by_type", ["type"])
    .index("by_channel", ["channel"])
    .index("by_status", ["status"])
    .index("by_recipient", ["recipient.id"])
    .index("by_scheduled", ["scheduledFor"]),

  // Agent Coordination Tables
  agent_coordination: defineTable({
    type: v.string(), // "task_assignment", "collaboration", "handoff"
    status: v.string(), // "pending", "active", "completed", "failed"
    coordinatorId: v.id("agents"),
    participants: v.array(v.object({
      agentId: v.id("agents"),
      role: v.string(),
      status: v.string(),
      joinedAt: v.number(),
      lastActiveAt: v.optional(v.number()),
    })),
    objective: v.string(),
    context: v.object({
      tenderId: v.id("tenders"),
      sectionIds: v.optional(v.array(v.id("bidSections"))),
      workflowInstanceId: v.optional(v.id("workflow_instances")),
      sharedData: v.object({}),
    }),
    plan: v.object({
      tasks: v.array(v.object({
        id: v.string(),
        description: v.string(),
        assignedTo: v.optional(v.id("agents")),
        dependencies: v.array(v.string()),
        status: v.string(),
        priority: v.string(),
        deadline: v.optional(v.number()),
      })),
      milestones: v.array(v.object({
        name: v.string(),
        criteria: v.string(),
        targetDate: v.number(),
        achieved: v.boolean(),
      })),
    }),
    communication: v.array(v.object({
      fromAgent: v.id("agents"),
      toAgent: v.optional(v.id("agents")),
      type: v.string(), // "broadcast", "direct", "request", "response"
      content: v.string(),
      timestamp: v.number(),
    })),
    results: v.optional(v.object({
      summary: v.string(),
      artifacts: v.array(v.object({
        name: v.string(),
        type: v.string(),
        url: v.string(),
      })),
      metrics: v.object({}),
    })),
    startedAt: v.number(),
    completedAt: v.optional(v.number()),
    duration: v.optional(v.number()),
  })
    .index("by_type", ["type"])
    .index("by_status", ["status"])
    .index("by_coordinator", ["coordinatorId"])
    .index("by_tender", ["context.tenderId"]),

  // System Health and Monitoring
  system_health: defineTable({
    component: v.string(), // "orchestrator", "queue", "integrations", etc.
    status: v.string(), // "healthy", "degraded", "unhealthy", "critical"
    timestamp: v.number(),
    metrics: v.object({
      cpu: v.optional(v.number()),
      memory: v.optional(v.number()),
      diskUsage: v.optional(v.number()),
      queueDepth: v.optional(v.number()),
      errorRate: v.optional(v.number()),
      responseTime: v.optional(v.number()),
      throughput: v.optional(v.number()),
    }),
    checks: v.array(v.object({
      name: v.string(),
      status: v.string(),
      message: v.optional(v.string()),
      lastChecked: v.number(),
    })),
    alerts: v.array(v.object({
      level: v.string(),
      message: v.string(),
      timestamp: v.number(),
      acknowledged: v.boolean(),
    })),
    dependencies: v.array(v.object({
      name: v.string(),
      status: v.string(),
      latency: v.optional(v.number()),
    })),
  })
    .index("by_component", ["component"])
    .index("by_status", ["status"])
    .index("by_timestamp", ["timestamp"]),
};