# 🎭 ProHeadshots Manual Installation Guide

## Step-by-Step Installation via Proxmox Console

Since SSH is currently down, follow these steps in the **Proxmox console**:

### 1. Access Proxmox Console
1. Open browser: `https://*************:8006`
2. Login with root credentials
3. Find your machine in left sidebar
4. Click "Console"

### 2. Fix SSH First (Optional but Recommended)
```bash
systemctl restart ssh
systemctl enable ssh
systemctl status ssh
```

### 3. Clone ProHeadshots Repository
```bash
# Navigate to root directory
cd /root

# Clone the repository
git clone https://github.com/thaohienhomes/ProHeadshots.git

# Enter project directory
cd ProHeadshots

# Check what we have
ls -la
```

### 4. Install System Dependencies
```bash
# Update system
apt update

# Install Python and development tools
apt install -y python3 python3-pip python3-venv python3-dev git curl wget

# Install image processing libraries
apt install -y libgl1-mesa-glx libglib2.0-0 libsm6 libxext6 libxrender-dev libgomp1

# Install additional dependencies
apt install -y ffmpeg libmagic1 libmagic-dev build-essential
```

### 5. Create Python Virtual Environment
```bash
# Create virtual environment
python3 -m venv /root/proheadshots-env

# Activate environment
source /root/proheadshots-env/bin/activate

# Upgrade pip
pip install --upgrade pip
```

### 6. Install Python Dependencies
```bash
# If requirements.txt exists
if [ -f "requirements.txt" ]; then
    pip install -r requirements.txt
else
    # Install common dependencies manually
    pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121
    pip install transformers diffusers accelerate
    pip install pillow opencv-python-headless numpy scipy
    pip install fastapi uvicorn gradio streamlit
    pip install huggingface-hub safetensors
fi
```

### 7. Test GPU Access
```bash
# Test PyTorch CUDA
python3 -c "
import torch
print(f'PyTorch: {torch.__version__}')
print(f'CUDA Available: {torch.cuda.is_available()}')
if torch.cuda.is_available():
    print(f'GPU: {torch.cuda.get_device_name(0)}')
    print(f'Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB')
    # Test GPU operations
    x = torch.randn(100, 100).cuda()
    y = torch.matmul(x, x)
    print('GPU Test: SUCCESS')
else:
    print('WARNING: CUDA not available')
"
```

### 8. Examine Project Structure
```bash
# Look for main application files
ls -la *.py

# Check for README
cat README.md | head -20

# Look for configuration files
ls -la config* .env* docker-compose*

# Check for models directory
ls -la models/ 2>/dev/null || mkdir -p models
```

### 9. Find and Run the Application
```bash
# Common application entry points to try:
if [ -f "app.py" ]; then
    echo "Found app.py - try: python app.py"
fi

if [ -f "main.py" ]; then
    echo "Found main.py - try: python main.py"
fi

if [ -f "gradio_app.py" ]; then
    echo "Found gradio_app.py - try: python gradio_app.py"
fi

if [ -f "streamlit_app.py" ]; then
    echo "Found streamlit_app.py - try: streamlit run streamlit_app.py"
fi

# Or look for any Python files
echo "All Python files:"
find . -name "*.py" -type f | head -10
```

### 10. Start the Application
```bash
# Most likely commands (try these):
python app.py
# OR
python main.py
# OR
python gradio_app.py
# OR
streamlit run streamlit_app.py --server.port 7860 --server.address 0.0.0.0
```

## 🌐 Access URLs After Installation

Once running, access via:
- **Local Network**: `http://*************:PORT`
- **Tailscale**: `http://*************:PORT`

Common ports:
- Gradio: 7860
- Streamlit: 8501
- FastAPI: 8000
- Custom: Check console output

## 🔧 Troubleshooting

### If Installation Fails:
```bash
# Check Python version
python3 --version

# Check GPU drivers
nvidia-smi

# Check disk space
df -h

# Check memory
free -h
```

### If Dependencies Fail:
```bash
# Try with system packages first
apt install -y python3-torch python3-torchvision

# Or use conda instead
wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh
bash Miniconda3-latest-Linux-x86_64.sh -b
~/miniconda3/bin/conda create -n proheadshots python=3.10
~/miniconda3/bin/conda activate proheadshots
```

### If GPU Not Working:
```bash
# Check CUDA installation
nvcc --version

# Install CUDA toolkit if needed
apt install -y nvidia-cuda-toolkit

# Restart after CUDA installation
reboot
```

## 📱 Monitor Installation

You can monitor GPU usage during installation:
```bash
# In another terminal (if SSH works)
watch -n 1 nvidia-smi

# Or check via web API
curl http://localhost:9092/api/gpu/status
```

## 🎯 Quick Start After Installation

1. **Activate environment**: `source /root/proheadshots-env/bin/activate`
2. **Navigate to project**: `cd /root/ProHeadshots`
3. **Run application**: `python app.py` (or whatever the main file is)
4. **Access via browser**: `http://*************:PORT`

## ✅ Success Indicators

You'll know it's working when you see:
- ✅ Python dependencies installed successfully
- ✅ PyTorch detects CUDA and RTX 4090
- ✅ Application starts without errors
- ✅ Web interface accessible via Tailscale
- ✅ GPU utilization shows in monitoring dashboard

## 📋 Copy-Paste Commands for Console

Here are the essential commands you can copy-paste into the Proxmox console:

```bash
# Fix SSH
systemctl restart ssh

# Install ProHeadshots
cd /root
git clone https://github.com/thaohienhomes/ProHeadshots.git
cd ProHeadshots
apt update && apt install -y python3 python3-pip python3-venv git
python3 -m venv /root/proheadshots-env
source /root/proheadshots-env/bin/activate
pip install --upgrade pip

# Install dependencies (if requirements.txt exists)
[ -f requirements.txt ] && pip install -r requirements.txt

# Test GPU
python3 -c "import torch; print(f'CUDA: {torch.cuda.is_available()}')"

# Run application (check what files exist first)
ls -la *.py
```

That's it! The installation should take 5-10 minutes depending on download speeds.