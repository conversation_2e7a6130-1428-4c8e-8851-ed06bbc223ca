# GPU Configuration for RTX 4090 (24GB VRAM)
# Adjust these based on your GPU memory
SURYA_BATCH_SIZE=4
QWEN_GPU_UTIL=0.4      # ~10GB for Qwen2.5-VL
LAYOUTLM_BATCH=8
MINIMAX_GPU_UTIL=0.9   # ~22GB for MiniMax (most demanding)
PHI4_GPU_UTIL=0.3      # ~7GB for Phi-4

# Model Configuration
ENABLE_FP16=true
ENABLE_TENSORRT=false  # Set to true if TensorRT is installed
MODEL_CACHE_DIR=./models

# API Configuration
API_KEY=your-secure-api-key-here
RATE_LIMIT_PER_MINUTE=60
MAX_UPLOAD_SIZE_MB=100

# Database
POSTGRES_USER=docuser
POSTGRES_PASSWORD=docpass
POSTGRES_DB=docdb

# Redis
REDIS_PASSWORD=
REDIS_MAX_MEMORY=2gb

# Monitoring
ENABLE_MONITORING=false  # Set to true to enable Prometheus/Grafana

# Cloud Configuration (for cloud deployment)
# AWS
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-west-2
S3_BUCKET_NAME=

# GCP
GOOGLE_APPLICATION_CREDENTIALS=
GCS_BUCKET_NAME=

# Azure
AZURE_STORAGE_CONNECTION_STRING=
AZURE_CONTAINER_NAME=

# RunPod/Vast.ai/Lambda Labs Configuration
RUNPOD_API_KEY=
VASTAI_API_KEY=
LAMBDA_API_KEY=

# Model Download Tokens (if needed)
HF_TOKEN=  # Hugging Face token for gated models