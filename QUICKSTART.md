# 🚀 Zero-Touch Tender System - Quick Start Guide

## 🎯 Three Ways to Run the System

### Option 1: Icon Shortcuts (Easiest) 🎯
Use desktop app bundles with beautiful icons:

```bash
# Create icon shortcuts (first time only)
npm run shortcuts

# Open shortcuts folder
./scripts/open-shortcuts.sh
```

**Then double-click:**
- 🚀 **Zero-Touch Dev Mode.app** - Start development
- 🖥️ **Zero-Touch Desktop Builder.app** - Build desktop app  
- 📊 **Zero-Touch Presentation.app** - Launch slides

### Option 2: Development Mode (Web-based)
Run the full system in your browser with hot reloading:

```bash
# Start development environment
npm run dev:app
# OR
./scripts/dev-runner.sh
```

**Access Points:**
- 🌐 **Web App**: http://localhost:3000
- 📊 **Zero-Touch Dashboard**: http://localhost:3000/zero-touch
- 🗄️ **Convex Dashboard**: Shown in terminal

### Option 3: Desktop App (Standalone)
Build and run as a native desktop application:

```bash
# Build desktop app (first time setup)
npm run desktop
# OR
./scripts/build-tauri.sh

# For development with hot reload
npm run tauri:dev
```

**App Features:**
- 🖥️ **Native desktop app** (no browser needed)
- 🎯 **System tray integration**
- 📁 **File system access**
- 🔔 **Native notifications**
- 💾 **Offline capability** (once data is loaded)

## 🛠️ Prerequisites

### Required Tools
- **Node.js** 18+ (or Bun/pnpm)
- **Rust** (for desktop app only)
- **Convex CLI** (installed automatically)

### API Keys Setup
Create `.env.local` with your API keys:

```bash
# Core Configuration
CONVEX_DEPLOYMENT=dev:your-deployment
NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud

# AI Integration
CONVEX_OPENAI_API_KEY=your_openai_key

# Gmail Integration (for email processing)
GMAIL_CLIENT_ID=your_gmail_client_id
GMAIL_CLIENT_SECRET=your_gmail_secret

# Calendar Integration
GOOGLE_CALENDAR_API_KEY=your_calendar_key

# SMS Notifications
TWILIO_ACCOUNT_SID=your_twilio_sid
TWILIO_AUTH_TOKEN=your_twilio_token

# Meeting Platforms
TEAMS_CLIENT_ID=your_teams_client_id
ZOOM_API_KEY=your_zoom_key
```

## 🎯 Zero-Touch Features Available

### ✅ Core System Components
- **📄 Document Parser Agent** - OCR + AI extraction
- **📅 Scheduler Agent** - Meeting coordination
- **📊 Content Builder** - Presentation generation
- **🤖 Voice Bot** - Meeting facilitation
- **📝 Summarizer** - Post-meeting processing
- **✅ Task Manager** - Automated assignment

### ✅ User Interfaces
- **Zero-Touch Dashboard** - Workflow monitoring
- **Agent Status Panel** - Real-time agent health
- **Event Timeline** - Complete activity log
- **File Upload System** - Drag & drop processing

### ✅ Integrations Ready
- Gmail (email processing)
- Google Calendar (scheduling)
- Twilio (SMS notifications)
- Teams/Zoom (meeting platforms)
- Task management platforms

## 🚀 Getting Started Steps

### 1. Web Development Mode
```bash
# Install dependencies
npm install

# Configure environment
cp .env.example .env.local
# Edit .env.local with your API keys

# Start development
npm run dev:app
```

### 2. Desktop App Mode
```bash
# Install Rust (if not installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Build desktop app
npm run desktop

# Launch app (after build completes)
open src-tauri/target/release/bundle/dmg/Zero-Touch\ Tender\ System_1.0.0_aarch64.dmg
```

## 📱 Usage Examples

### Trigger Zero-Touch Workflow
1. **Via UI**: Upload tender PDF to `/zero-touch` page
2. **Via Email**: Forward tender emails to configured Gmail
3. **Via API**: POST to `/api/zero-touch/initiate`

### Monitor Progress
- **Real-time Dashboard**: Live updates of all workflows
- **Agent Performance**: Individual agent success rates
- **Event Timeline**: Complete audit trail

### Meeting Facilitation
- Voice bot automatically joins scheduled meetings
- Presents generated slides with AI narration
- Conducts interactive polls and Q&A
- Records and processes meeting outcomes

## 🎯 Key Directories

```
├── scripts/           # Automation scripts
├── src-tauri/         # Desktop app configuration
├── components/        # React UI components
├── convex/           # Backend functions & schema
├── orchestrator/     # Workflow orchestration
├── voice-bot-system/ # Meeting facilitation
├── zero-touch-system/ # Core automation logic
└── types/            # TypeScript definitions
```

## 🆘 Troubleshooting

### Common Issues
- **Missing API keys**: Check `.env.local` configuration
- **Rust not found**: Install via `curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh`
- **Convex connection**: Run `npx convex dev` separately if needed
- **Port conflicts**: Change ports in `next.config.js` if 3000 is busy
- **Icon errors in presentation**: Fixed! Now uses emoji instead of external icon libraries

### Get Help
- 📖 **Documentation**: Check individual module READMEs
- 🐛 **Issues**: Report problems in project issues
- 💬 **Support**: Contact development team

## 🎉 Ready to Go!

Your Zero-Touch Tender System is now ready for:
- ✅ **Automated document processing**
- ✅ **AI-powered meeting scheduling**
- ✅ **Professional content generation**
- ✅ **Voice bot meeting facilitation**
- ✅ **Intelligent task management**

Choose your preferred mode and start transforming your tender process! 🚀