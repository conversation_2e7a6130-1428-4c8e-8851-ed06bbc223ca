[{"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/layout.tsx": "1", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/page.tsx": "2", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/BidWritingStudio.tsx": "3", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/SignInForm.tsx": "4", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/SignOutButton.tsx": "5", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/TenderUpload.tsx": "6", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/lib/utils.ts": "7", "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/src/lib/utils.ts": "8"}, {"size": 1394, "mtime": 1751829255733, "results": "9", "hashOfConfig": "10"}, {"size": 1349, "mtime": 1751829273978, "results": "11", "hashOfConfig": "10"}, {"size": 8941, "mtime": 1751829499066, "results": "12", "hashOfConfig": "10"}, {"size": 2615, "mtime": 1751829286181, "results": "13", "hashOfConfig": "10"}, {"size": 572, "mtime": 1751829286184, "results": "14", "hashOfConfig": "10"}, {"size": 3653, "mtime": 1751829504649, "results": "15", "hashOfConfig": "10"}, {"size": 169, "mtime": 1751829291330, "results": "16", "hashOfConfig": "10"}, {"size": 169, "mtime": 1751389430000, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rf7it5", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/layout.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/page.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/BidWritingStudio.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/SignInForm.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/SignOutButton.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/TenderUpload.tsx", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/lib/utils.ts", [], [], "/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/src/lib/utils.ts", [], []]