{"c": ["webpack", "vendors"], "r": [], "m": ["(app-pages-browser)/./node_modules/convex/dist/esm/browser/http_client.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/logging.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/long.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/simple_client.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/authentication_manager.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/client.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/local_state.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/metrics.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/optimistic_updates_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/protocol.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/remote_query_set.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/request_manager.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/session.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/udf_path_utils.js", "(app-pages-browser)/./node_modules/convex/dist/esm/browser/sync/web_socket_manager.js", "(app-pages-browser)/./node_modules/convex/dist/esm/common/index.js", "(app-pages-browser)/./node_modules/convex/dist/esm/index.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/ConvexAuthState.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/auth_helpers.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/client.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/hydration.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/queries_observer.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/use_paginated_query.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/use_queries.js", "(app-pages-browser)/./node_modules/convex/dist/esm/react/use_subscription.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/api.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/components/index.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/components/paths.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/cron.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/database.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/filter_builder.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/functionName.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/actions_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/authentication_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/database_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/filter_builder_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/index_range_builder_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/query_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/registration_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/scheduler_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/search_filter_builder_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/storage_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/syscall.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/validate.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/impl/vector_search_impl.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/index_range_builder.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/pagination.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/router.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/schema.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/search_filter_builder.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/storage.js", "(app-pages-browser)/./node_modules/convex/dist/esm/server/vector_search.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/base64.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/compare.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/compare_utf8.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/errors.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/index.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/validator.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/validators.js", "(app-pages-browser)/./node_modules/convex/dist/esm/values/value.js", "(app-pages-browser)/./node_modules/jwt-decode/build/esm/index.js"]}