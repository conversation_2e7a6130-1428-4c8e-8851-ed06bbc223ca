/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/@convex-dev+auth@0.0.80_@auth+core@0.37.4_convex@1.25.2_react@19.1.0__react@19.1.0/node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ConvexClientProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Check if Convex URL is properly configured\nconst convexUrl = \"https://modest-viper-346.convex.cloud\";\nconst isPlaceholderUrl = !convexUrl || convexUrl === \"https://your-convex-deployment.convex.cloud\";\nif (isPlaceholderUrl) {\n    console.error(\"⚠️ Convex URL not configured. Please run 'npx convex dev' to set up your Convex deployment.\");\n}\n// Initialize Convex client\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(convexUrl || \"\");\nfunction ConvexClientProvider(param) {\n    let { children } = param;\n    _s();\n    const [showSetupError, setShowSetupError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ConvexClientProvider.useEffect\": ()=>{\n            if (isPlaceholderUrl) {\n                setShowSetupError(true);\n            }\n        }\n    }[\"ConvexClientProvider.useEffect\"], []);\n    if (showSetupError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-lg w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-500 mr-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                children: \"Convex Setup Required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: \"The application needs to be connected to a Convex deployment. Please follow these steps:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"1. Run in your terminal:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npx convex dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"2. Update your .env file with the deployment URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"3. Restart your development server:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npm run dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-700 dark:text-blue-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Need help?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                \" Check the\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://docs.convex.dev/quickstart\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"underline hover:text-blue-800 dark:hover:text-blue-200\",\n                                    children: \"Convex documentation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \"for detailed setup instructions.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthProvider, {\n        client: convex,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexProvider, {\n            client: convex,\n            children: [\n                children,\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(ConvexClientProvider, \"IhZ0vL2yqVhGgsBipN5FH/gaqbw=\");\n_c = ConvexClientProvider;\nvar _c;\n$RefreshReg$(_c, \"ConvexClientProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ConvexClientProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(app-pages-browser)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-mono\",\"display\":\"swap\"}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/index.css */ \"(app-pages-browser)/./src/index.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGlhcy1kYW4lMkZEb2N1bWVudHMlMkZDbGllbnRzJTJGQVJBUFMlMkZCaWRXcml0aW5nVGVuZGVyU3R1ZGlvJTJGQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28lMkZjb21wb25lbnRzJTJGQ29udmV4Q2xpZW50UHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyQ29udmV4Q2xpZW50UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGlhcy1kYW4lMkZEb2N1bWVudHMlMkZDbGllbnRzJTJGQVJBUFMlMkZCaWRXcml0aW5nVGVuZGVyU3R1ZGlvJTJGQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28lMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS4zLjVfcmVhY3QtZG9tJTQwMTkuMS4wX3JlYWN0JTQwMTkuMS4wX19yZWFjdCU0MDE5LjEuMCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlMkMlNUMlMjJ2YXJpYWJsZSU1QyUyMiUzQSU1QyUyMi0tZm9udC1pbnRlciU1QyUyMiUyQyU1QyUyMmRpc3BsYXklNUMlMjIlM0ElNUMlMjJzd2FwJTVDJTIyJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGlhcy1kYW4lMkZEb2N1bWVudHMlMkZDbGllbnRzJTJGQVJBUFMlMkZCaWRXcml0aW5nVGVuZGVyU3R1ZGlvJTJGQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28lMkZub2RlX21vZHVsZXMlMkYucG5wbSUyRm5leHQlNDAxNS4zLjVfcmVhY3QtZG9tJTQwMTkuMS4wX3JlYWN0JTQwMTkuMS4wX19yZWFjdCU0MDE5LjEuMCUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZmb250JTJGZ29vZ2xlJTJGdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlMkZsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIyR2Vpc3RfTW9ubyU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCUyQyU1QyUyMnZhcmlhYmxlJTVDJTIyJTNBJTVDJTIyLS1mb250LWdlaXN0LW1vbm8lNUMlMjIlMkMlNUMlMjJkaXNwbGF5JTVDJTIyJTNBJTVDJTIyc3dhcCU1QyUyMiU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmdlaXN0TW9ubyU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFsaWFzLWRhbiUyRkRvY3VtZW50cyUyRkNsaWVudHMlMkZBUkFQUyUyRkJpZFdyaXRpbmdUZW5kZXJTdHVkaW8lMkZCaWRUZW5kZXJXcml0aW5nU3R1ZGlvbyUyRnNyYyUyRmluZGV4LmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9ZmFsc2UhIiwibWFwcGluZ3MiOiJBQUFBLG9NQUEwTTtBQUMxTTtBQUNBLHNxQkFBMlo7QUFDM1o7QUFDQSxrc0JBQXlhO0FBQ3phO0FBQ0Esd0pBQTBJIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJDb252ZXhDbGllbnRQcm92aWRlclwiXSAqLyBcIi9Vc2Vycy9hbGlhcy1kYW4vRG9jdW1lbnRzL0NsaWVudHMvQVJBUFMvQmlkV3JpdGluZ1RlbmRlclN0dWRpby9CaWRUZW5kZXJXcml0aW5nU3R1ZGlvby9jb21wb25lbnRzL0NvbnZleENsaWVudFByb3ZpZGVyLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsaWFzLWRhbi9Eb2N1bWVudHMvQ2xpZW50cy9BUkFQUy9CaWRXcml0aW5nVGVuZGVyU3R1ZGlvL0JpZFRlbmRlcldyaXRpbmdTdHVkaW9vL25vZGVfbW9kdWxlcy8ucG5wbS9uZXh0QDE1LjMuNV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF9fcmVhY3RAMTkuMS4wL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1xcXCJwYXRoXFxcIjpcXFwiYXBwL2xheW91dC50c3hcXFwiLFxcXCJpbXBvcnRcXFwiOlxcXCJJbnRlclxcXCIsXFxcImFyZ3VtZW50c1xcXCI6W3tcXFwic3Vic2V0c1xcXCI6W1xcXCJsYXRpblxcXCJdLFxcXCJ2YXJpYWJsZVxcXCI6XFxcIi0tZm9udC1pbnRlclxcXCIsXFxcImRpc3BsYXlcXFwiOlxcXCJzd2FwXFxcIn1dLFxcXCJ2YXJpYWJsZU5hbWVcXFwiOlxcXCJpbnRlclxcXCJ9XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTUuMy41X3JlYWN0LWRvbUAxOS4xLjBfcmVhY3RAMTkuMS4wX19yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz97XFxcInBhdGhcXFwiOlxcXCJhcHAvbGF5b3V0LnRzeFxcXCIsXFxcImltcG9ydFxcXCI6XFxcIkdlaXN0X01vbm9cXFwiLFxcXCJhcmd1bWVudHNcXFwiOlt7XFxcInN1YnNldHNcXFwiOltcXFwibGF0aW5cXFwiXSxcXFwidmFyaWFibGVcXFwiOlxcXCItLWZvbnQtZ2Vpc3QtbW9ub1xcXCIsXFxcImRpc3BsYXlcXFwiOlxcXCJzd2FwXFxcIn1dLFxcXCJ2YXJpYWJsZU5hbWVcXFwiOlxcXCJnZWlzdE1vbm9cXFwifVwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsaWFzLWRhbi9Eb2N1bWVudHMvQ2xpZW50cy9BUkFQUy9CaWRXcml0aW5nVGVuZGVyU3R1ZGlvL0JpZFRlbmRlcldyaXRpbmdTdHVkaW9vL3NyYy9pbmRleC5jc3NcIik7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"8c85d41dd3cf\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vc3JjL2luZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjhjODVkNDFkZDNjZlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/index.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);