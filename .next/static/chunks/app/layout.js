/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(app-pages-browser)/./node_modules/.pnpm/@convex-dev+auth@0.0.80_@auth+core@0.37.4_convex@1.25.2_react@19.1.0__react@19.1.0/node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(app-pages-browser)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ConvexClientProvider auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n// Check if Convex URL is properly configured\nconst convexUrl = \"https://modest-viper-346.convex.cloud\";\nconst isPlaceholderUrl = !convexUrl || convexUrl === \"https://your-convex-deployment.convex.cloud\";\nif (isPlaceholderUrl) {\n    console.error(\"⚠️ Convex URL not configured. Please run 'npx convex dev' to set up your Convex deployment.\");\n}\n// Initialize Convex client\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(convexUrl || \"\");\nfunction ConvexClientProvider(param) {\n    let { children } = param;\n    _s();\n    const [showSetupError, setShowSetupError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ConvexClientProvider.useEffect\": ()=>{\n            if (isPlaceholderUrl) {\n                setShowSetupError(true);\n            }\n        }\n    }[\"ConvexClientProvider.useEffect\"], []);\n    if (showSetupError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-lg w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-500 mr-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                children: \"Convex Setup Required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: \"The application needs to be connected to a Convex deployment. Please follow these steps:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"1. Run in your terminal:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npx convex dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"2. Update your .env file with the deployment URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"3. Restart your development server:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npm run dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-700 dark:text-blue-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Need help?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                \" Check the\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://docs.convex.dev/quickstart\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"underline hover:text-blue-800 dark:hover:text-blue-200\",\n                                    children: \"Convex documentation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \"for detailed setup instructions.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthProvider, {\n        client: convex,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(ConvexClientProvider, \"IhZ0vL2yqVhGgsBipN5FH/gaqbw=\");\n_c = ConvexClientProvider;\nvar _c;\n$RefreshReg$(_c, \"ConvexClientProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ConvexClientProvider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(app-pages-browser)/./components/ConvexClientProvider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-mono\",\"display\":\"swap\"}],\"variableName\":\"geistMono\"} */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/index.css */ \"(app-pages-browser)/./src/index.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"39182ba2d490\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9pbmRleC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vc3JjL2luZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM5MTgyYmEyZDQ5MFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/index.css\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["vendors","main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/.pnpm/next@15.3.5_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2F.pnpm%2Fnext%4015.3.5_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);