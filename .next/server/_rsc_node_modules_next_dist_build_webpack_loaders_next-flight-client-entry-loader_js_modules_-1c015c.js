/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_node_modules_next_dist_build_webpack_loaders_next-flight-client-entry-loader_js_modules_-1c015c";
exports.ids = ["_rsc_node_modules_next_dist_build_webpack_loaders_next-flight-client-entry-loader_js_modules_-1c015c"];
exports.modules = {

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\",\"display\":\"swap\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_display_swap_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app/layout.tsx\",\"import\":\"Geist_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-geist-mono\",\"display\":\"swap\"}],\"variableName\":\"geistMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app/layout.tsx\\\",\\\"import\\\":\\\"Geist_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-geist-mono\\\",\\\"display\\\":\\\"swap\\\"}],\\\"variableName\\\":\\\"geistMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_display_swap_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_display_swap_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/ConvexClientProvider */ \"(rsc)/./components/ConvexClientProvider.tsx\");\n/* harmony import */ var _src_index_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../src/index.css */ \"(rsc)/./src/index.css\");\n\n\n\n\n\nconst metadata = {\n    title: 'ARA Property Services - Bid Writing Studio',\n    description: 'Multi-Agent Tender & Bid Writing Platform',\n    keywords: [\n        'bid writing',\n        'tender',\n        'property services',\n        'ARA'\n    ],\n    authors: [\n        {\n            name: 'ARA Property Services'\n        }\n    ],\n    robots: 'index, follow',\n    icons: {\n        icon: '/favicon.ico'\n    }\n};\nconst viewport = {\n    width: 'device-width',\n    initialScale: 1,\n    themeColor: '#0a0a0a'\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_display_swap_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_Geist_Mono_arguments_subsets_latin_variable_font_geist_mono_display_swap_variableName_geistMono___WEBPACK_IMPORTED_MODULE_4___default().variable)}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"min-h-screen bg-black text-white font-mono antialiased\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ConvexClientProvider__WEBPACK_IMPORTED_MODULE_1__.ConvexClientProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/layout.tsx\",\n                lineNumber: 43,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/layout.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/app/layout.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__);

const ConvexClientProvider = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call ConvexClientProvider() from the server but ConvexClientProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx",
"ConvexClientProvider",
);

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(rsc)/./components/ConvexClientProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(rsc)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(rsc)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(rsc)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(rsc)/./src/index.css":
/*!***********************!*\
  !*** ./src/index.css ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"d7f84a8bc2ed\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvaW5kZXguY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vc3JjL2luZGV4LmNzcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImQ3Zjg0YThiYzJlZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/index.css\n");

/***/ }),

/***/ "(ssr)/./components/ConvexClientProvider.tsx":
/*!*********************************************!*\
  !*** ./components/ConvexClientProvider.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConvexClientProvider: () => (/* binding */ ConvexClientProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @convex-dev/auth/react */ \"(ssr)/./node_modules/@convex-dev/auth/dist/react/index.js\");\n/* harmony import */ var convex_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! convex/react */ \"(ssr)/./node_modules/convex/dist/esm/react/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* __next_internal_client_entry_do_not_use__ ConvexClientProvider auto */ \n\n\n\n\n// Check if Convex URL is properly configured\nconst convexUrl = \"https://modest-viper-346.convex.cloud\";\nconst isPlaceholderUrl = !convexUrl || convexUrl === \"https://your-convex-deployment.convex.cloud\";\nif (isPlaceholderUrl) {\n    console.error(\"⚠️ Convex URL not configured. Please run 'npx convex dev' to set up your Convex deployment.\");\n}\n// Initialize Convex client\nconst convex = new convex_react__WEBPACK_IMPORTED_MODULE_1__.ConvexReactClient(convexUrl || \"\");\nfunction ConvexClientProvider({ children }) {\n    const [showSetupError, setShowSetupError] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)({\n        \"ConvexClientProvider.useEffect\": ()=>{\n            if (isPlaceholderUrl) {\n                setShowSetupError(true);\n            }\n        }\n    }[\"ConvexClientProvider.useEffect\"], []);\n    if (showSetupError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen p-4 bg-gray-50 dark:bg-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-lg w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-8 h-8 text-red-500 mr-3\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                viewBox: \"0 0 24 24\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    strokeWidth: 2,\n                                    d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 37,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-bold text-gray-900 dark:text-white\",\n                                children: \"Convex Setup Required\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 40,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-300\",\n                                children: \"The application needs to be connected to a Convex deployment. Please follow these steps:\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"1. Run in your terminal:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npx convex dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"2. Update your .env file with the deployment URL:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"NEXT_PUBLIC_CONVEX_URL=https://your-deployment.convex.cloud\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 63,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-gray-100 dark:bg-gray-700 rounded-lg p-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm font-medium text-gray-700 dark:text-gray-200 mb-2\",\n                                        children: \"3. Restart your development server:\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                        className: \"bg-gray-900 text-green-400 p-3 rounded text-sm font-mono overflow-x-auto\",\n                                        children: \"npm run dev\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 45,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-blue-700 dark:text-blue-300\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Need help?\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 15\n                                }, this),\n                                \" Check the\",\n                                \" \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                    href: \"https://docs.convex.dev/quickstart\",\n                                    target: \"_blank\",\n                                    rel: \"noopener noreferrer\",\n                                    className: \"underline hover:text-blue-800 dark:hover:text-blue-200\",\n                                    children: \"Convex documentation\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 15\n                                }, this),\n                                \" \",\n                                \"for detailed setup instructions.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                            lineNumber: 79,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                lineNumber: 35,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_convex_dev_auth_react__WEBPACK_IMPORTED_MODULE_4__.ConvexAuthProvider, {\n        client: convex,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {}, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Clients/ARAPS/BidWritingTenderStudio/BidTenderWritingStudioo/components/ConvexClientProvider.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ConvexClientProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/ConvexClientProvider.tsx */ \"(ssr)/./components/ConvexClientProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fcomponents%2FConvexClientProvider.tsx%22%2C%22ids%22%3A%5B%22ConvexClientProvider%22%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Ffont%2Fgoogle%2Ftarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%2Flayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Geist_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22variable%5C%22%3A%5C%22--font-geist-mono%5C%22%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22geistMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fsrc%2Findex.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-segment.js */ \"(ssr)/./node_modules/next/dist/client/components/client-segment.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/async-metadata.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/metadata/metadata-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxpYXMtZGFuJTJGRG9jdW1lbnRzJTJGQ2xpZW50cyUyRkFSQVBTJTJGQmlkV3JpdGluZ1RlbmRlclN0dWRpbyUyRkJpZFRlbmRlcldyaXRpbmdTdHVkaW9vJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGY2xpZW50LXBhZ2UuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGlhcy1kYW4lMkZEb2N1bWVudHMlMkZDbGllbnRzJTJGQVJBUFMlMkZCaWRXcml0aW5nVGVuZGVyU3R1ZGlvJTJGQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZjbGllbnQtc2VnbWVudC5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFsaWFzLWRhbiUyRkRvY3VtZW50cyUyRkNsaWVudHMlMkZBUkFQUyUyRkJpZFdyaXRpbmdUZW5kZXJTdHVkaW8lMkZCaWRUZW5kZXJXcml0aW5nU3R1ZGlvbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmVycm9yLWJvdW5kYXJ5LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxpYXMtZGFuJTJGRG9jdW1lbnRzJTJGQ2xpZW50cyUyRkFSQVBTJTJGQmlkV3JpdGluZ1RlbmRlclN0dWRpbyUyRkJpZFRlbmRlcldyaXRpbmdTdHVkaW9vJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGaHR0cC1hY2Nlc3MtZmFsbGJhY2slMkZlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFsaWFzLWRhbiUyRkRvY3VtZW50cyUyRkNsaWVudHMlMkZBUkFQUyUyRkJpZFdyaXRpbmdUZW5kZXJTdHVkaW8lMkZCaWRUZW5kZXJXcml0aW5nU3R1ZGlvbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRmxheW91dC1yb3V0ZXIuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyJTJGVXNlcnMlMkZhbGlhcy1kYW4lMkZEb2N1bWVudHMlMkZDbGllbnRzJTJGQVJBUFMlMkZCaWRXcml0aW5nVGVuZGVyU3R1ZGlvJTJGQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28lMkZub2RlX21vZHVsZXMlMkZuZXh0JTJGZGlzdCUyRmNsaWVudCUyRmNvbXBvbmVudHMlMkZtZXRhZGF0YSUyRmFzeW5jLW1ldGFkYXRhLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYWxpYXMtZGFuJTJGRG9jdW1lbnRzJTJGQ2xpZW50cyUyRkFSQVBTJTJGQmlkV3JpdGluZ1RlbmRlclN0dWRpbyUyRkJpZFRlbmRlcldyaXRpbmdTdHVkaW9vJTJGbm9kZV9tb2R1bGVzJTJGbmV4dCUyRmRpc3QlMkZjbGllbnQlMkZjb21wb25lbnRzJTJGbWV0YWRhdGElMkZtZXRhZGF0YS1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjIlMkZVc2VycyUyRmFsaWFzLWRhbiUyRkRvY3VtZW50cyUyRkNsaWVudHMlMkZBUkFQUyUyRkJpZFdyaXRpbmdUZW5kZXJTdHVkaW8lMkZCaWRUZW5kZXJXcml0aW5nU3R1ZGlvbyUyRm5vZGVfbW9kdWxlcyUyRm5leHQlMkZkaXN0JTJGY2xpZW50JTJGY29tcG9uZW50cyUyRnJlbmRlci1mcm9tLXRlbXBsYXRlLWNvbnRleHQuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLG9PQUFvTDtBQUNwTDtBQUNBLDBPQUF1TDtBQUN2TDtBQUNBLDBPQUF1TDtBQUN2TDtBQUNBLG9SQUE0TTtBQUM1TTtBQUNBLHdPQUFzTDtBQUN0TDtBQUNBLDRQQUFnTTtBQUNoTTtBQUNBLGtRQUFtTTtBQUNuTTtBQUNBLHNRQUFxTSIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsaWFzLWRhbi9Eb2N1bWVudHMvQ2xpZW50cy9BUkFQUy9CaWRXcml0aW5nVGVuZGVyU3R1ZGlvL0JpZFRlbmRlcldyaXRpbmdTdHVkaW9vL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvY2xpZW50LXBhZ2UuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGlhcy1kYW4vRG9jdW1lbnRzL0NsaWVudHMvQVJBUFMvQmlkV3JpdGluZ1RlbmRlclN0dWRpby9CaWRUZW5kZXJXcml0aW5nU3R1ZGlvby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2NsaWVudC1zZWdtZW50LmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9lcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsaWFzLWRhbi9Eb2N1bWVudHMvQ2xpZW50cy9BUkFQUy9CaWRXcml0aW5nVGVuZGVyU3R1ZGlvL0JpZFRlbmRlcldyaXRpbmdTdHVkaW9vL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvaHR0cC1hY2Nlc3MtZmFsbGJhY2svZXJyb3ItYm91bmRhcnkuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGlhcy1kYW4vRG9jdW1lbnRzL0NsaWVudHMvQVJBUFMvQmlkV3JpdGluZ1RlbmRlclN0dWRpby9CaWRUZW5kZXJXcml0aW5nU3R1ZGlvby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL2xheW91dC1yb3V0ZXIuanNcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIi9Vc2Vycy9hbGlhcy1kYW4vRG9jdW1lbnRzL0NsaWVudHMvQVJBUFMvQmlkV3JpdGluZ1RlbmRlclN0dWRpby9CaWRUZW5kZXJXcml0aW5nU3R1ZGlvby9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NsaWVudC9jb21wb25lbnRzL21ldGFkYXRhL2FzeW5jLW1ldGFkYXRhLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYWxpYXMtZGFuL0RvY3VtZW50cy9DbGllbnRzL0FSQVBTL0JpZFdyaXRpbmdUZW5kZXJTdHVkaW8vQmlkVGVuZGVyV3JpdGluZ1N0dWRpb28vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jbGllbnQvY29tcG9uZW50cy9tZXRhZGF0YS9tZXRhZGF0YS1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiL1VzZXJzL2FsaWFzLWRhbi9Eb2N1bWVudHMvQ2xpZW50cy9BUkFQUy9CaWRXcml0aW5nVGVuZGVyU3R1ZGlvL0JpZFRlbmRlcldyaXRpbmdTdHVkaW9vL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvcmVuZGVyLWZyb20tdGVtcGxhdGUtY29udGV4dC5qc1wiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-segment.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fhttp-access-fallback%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fasync-metadata.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fmetadata%2Fmetadata-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Falias-dan%2FDocuments%2FClients%2FARAPS%2FBidWritingTenderStudio%2FBidTenderWritingStudioo%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ })

};
;