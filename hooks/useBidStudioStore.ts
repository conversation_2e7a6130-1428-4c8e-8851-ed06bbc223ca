import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Version {
  id: string;
  content: string;
  timestamp: Date;
  sectionId: string;
  autoSaved?: boolean;
  wordCount?: number;
}

interface Template {
  id: string;
  name: string;
  content: string;
  category: string;
}

interface Notification {
  id: string;
  type: 'deadline' | 'reminder' | 'collaboration' | 'system';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  urgent?: boolean;
  actionUrl?: string;
}

interface UserPreferences {
  autoSaveEnabled: boolean;
  autoSaveInterval: number;
  showDeadlineWarnings: boolean;
  emailNotifications: boolean;
  theme: 'dark' | 'light';
  defaultExportFormat: 'pdf' | 'word' | 'html';
  collaborationMode: 'real-time' | 'manual';
}

interface BidStudioState {
  // Editor state
  editorContent: string;
  setEditorContent: (content: string) => void;
  
  // Auto-save and versions
  autoSaveEnabled: boolean;
  setAutoSaveEnabled: (enabled: boolean) => void;
  recentVersions: Version[];
  addVersion: (version: Version) => void;
  restoreVersion: (versionId: string) => void;
  
  // Templates
  selectedTemplate: Template | null;
  setSelectedTemplate: (template: Template | null) => void;
  customTemplates: Template[];
  addCustomTemplate: (template: Template) => void;
  
  // Notifications and reminders
  notifications: Notification[];
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp'>) => void;
  markNotificationRead: (id: string) => void;
  clearNotifications: () => void;
  
  // User preferences
  preferences: UserPreferences;
  updatePreferences: (preferences: Partial<UserPreferences>) => void;
  
  // Deadline tracking
  deadlineWarnings: Record<string, Date>; // sectionId -> warning timestamp
  setDeadlineWarning: (sectionId: string, timestamp: Date) => void;
  
  // Collaboration state
  activeCollaborators: Record<string, string[]>; // sectionId -> userIds
  setActiveCollaborators: (sectionId: string, userIds: string[]) => void;
  
  // UI state
  sidebarCollapsed: boolean;
  setSidebarCollapsed: (collapsed: boolean) => void;
  activePanels: string[];
  togglePanel: (panelId: string) => void;
  
  // Performance tracking
  sessionStartTime: Date;
  wordsWrittenThisSession: number;
  addWordsWritten: (count: number) => void;
  resetSession: () => void;
}

export const useBidStudioStore = create<BidStudioState>()(
  persist(
    (set, get) => ({
      // Editor state
      editorContent: '',
      setEditorContent: (content) => set({ editorContent: content }),
      
      // Auto-save and versions
      autoSaveEnabled: true,
      setAutoSaveEnabled: (enabled) => set({ autoSaveEnabled: enabled }),
      recentVersions: [],
      addVersion: (version) => 
        set((state) => ({
          recentVersions: [version, ...state.recentVersions].slice(0, 50) // Keep last 50 versions
        })),
      restoreVersion: (versionId) => {
        const version = get().recentVersions.find(v => v.id === versionId);
        if (version) {
          set({ editorContent: version.content });
        }
      },
      
      // Templates
      selectedTemplate: null,
      setSelectedTemplate: (template) => set({ selectedTemplate: template }),
      customTemplates: [],
      addCustomTemplate: (template) =>
        set((state) => ({
          customTemplates: [...state.customTemplates, template]
        })),
      
      // Notifications and reminders
      notifications: [],
      addNotification: (notification) =>
        set((state) => ({
          notifications: [
            {
              ...notification,
              id: Date.now().toString(),
              timestamp: new Date(),
            },
            ...state.notifications
          ].slice(0, 100) // Keep last 100 notifications
        })),
      markNotificationRead: (id) =>
        set((state) => ({
          notifications: state.notifications.map(n => 
            n.id === id ? { ...n, read: true } : n
          )
        })),
      clearNotifications: () => set({ notifications: [] }),
      
      // User preferences
      preferences: {
        autoSaveEnabled: true,
        autoSaveInterval: 2000,
        showDeadlineWarnings: true,
        emailNotifications: true,
        theme: 'dark',
        defaultExportFormat: 'pdf',
        collaborationMode: 'real-time',
      },
      updatePreferences: (newPreferences) =>
        set((state) => ({
          preferences: { ...state.preferences, ...newPreferences }
        })),
      
      // Deadline tracking
      deadlineWarnings: {},
      setDeadlineWarning: (sectionId, timestamp) =>
        set((state) => ({
          deadlineWarnings: { ...state.deadlineWarnings, [sectionId]: timestamp }
        })),
      
      // Collaboration state
      activeCollaborators: {},
      setActiveCollaborators: (sectionId, userIds) =>
        set((state) => ({
          activeCollaborators: { ...state.activeCollaborators, [sectionId]: userIds }
        })),
      
      // UI state
      sidebarCollapsed: false,
      setSidebarCollapsed: (collapsed) => set({ sidebarCollapsed: collapsed }),
      activePanels: [],
      togglePanel: (panelId) =>
        set((state) => ({
          activePanels: state.activePanels.includes(panelId)
            ? state.activePanels.filter(id => id !== panelId)
            : [...state.activePanels, panelId]
        })),
      
      // Performance tracking
      sessionStartTime: new Date(),
      wordsWrittenThisSession: 0,
      addWordsWritten: (count) =>
        set((state) => ({
          wordsWrittenThisSession: state.wordsWrittenThisSession + count
        })),
      resetSession: () =>
        set({
          sessionStartTime: new Date(),
          wordsWrittenThisSession: 0,
        }),
    }),
    {
      name: 'bid-studio-storage',
      partialize: (state) => ({
        // Only persist certain parts of the state
        autoSaveEnabled: state.autoSaveEnabled,
        recentVersions: state.recentVersions.slice(0, 20), // Limit persisted versions
        customTemplates: state.customTemplates,
        preferences: state.preferences,
        sidebarCollapsed: state.sidebarCollapsed,
        activePanels: state.activePanels,
      }),
    }
  )
);

// Selectors for computed values
export const useBidStudioSelectors = () => {
  const store = useBidStudioStore();
  
  return {
    unreadNotifications: store.notifications.filter(n => !n.read),
    urgentNotifications: store.notifications.filter(n => n.urgent && !n.read),
    deadlineNotifications: store.notifications.filter(n => n.type === 'deadline' && !n.read),
    totalWordsThisSession: store.wordsWrittenThisSession,
    sessionDuration: Date.now() - store.sessionStartTime.getTime(),
    hasActiveCollaborators: (sectionId: string) => 
      (store.activeCollaborators[sectionId] || []).length > 0,
  };
};

// Hook for deadline management
export const useDeadlineManager = () => {
  const store = useBidStudioStore();
  
  const checkDeadlines = (tenderDueDate: string, sections: any[]) => {
    const deadline = new Date(tenderDueDate);
    const now = new Date();
    const daysLeft = Math.ceil((deadline.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    // Add deadline warnings
    if (daysLeft <= 3 && daysLeft > 0) {
      store.addNotification({
        type: 'deadline',
        title: 'Deadline Approaching',
        message: `Tender deadline is in ${daysLeft} day${daysLeft === 1 ? '' : 's'}`,
        read: false,
        urgent: daysLeft <= 1,
      });
    } else if (daysLeft <= 0) {
      store.addNotification({
        type: 'deadline',
        title: 'Deadline Overdue',
        message: 'Tender deadline has passed',
        read: false,
        urgent: true,
      });
    }
    
    // Check incomplete sections
    const incompleteSections = sections.filter(s => 
      s.status !== 'approved' && s.status !== 'completed'
    );
    
    if (incompleteSections.length > 0 && daysLeft <= 7) {
      store.addNotification({
        type: 'reminder',
        title: 'Incomplete Sections',
        message: `${incompleteSections.length} section${incompleteSections.length === 1 ? '' : 's'} still need attention`,
        read: false,
        urgent: daysLeft <= 3,
      });
    }
  };
  
  return { checkDeadlines };
};

// Hook for performance analytics
export const usePerformanceAnalytics = () => {
  const store = useBidStudioStore();
  
  const getProductivityMetrics = () => {
    const sessionHours = (Date.now() - store.sessionStartTime.getTime()) / (1000 * 60 * 60);
    const wordsPerHour = sessionHours > 0 ? store.wordsWrittenThisSession / sessionHours : 0;
    
    return {
      sessionDuration: sessionHours,
      wordsWritten: store.wordsWrittenThisSession,
      wordsPerHour: Math.round(wordsPerHour),
      versionsCreated: store.recentVersions.length,
      autoSaveEnabled: store.autoSaveEnabled,
    };
  };
  
  return { getProductivityMetrics };
};