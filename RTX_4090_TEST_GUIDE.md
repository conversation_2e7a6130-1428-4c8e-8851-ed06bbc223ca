# 🎮 RTX 4090 Local Testing Guide

## 🖥️ Testing Options

Since you're on macOS but have an RTX 4090, here are your testing options:

### **Option 1: Direct Linux Machine with RTX 4090** (Recommended)

If your RTX 4090 is in a Linux machine:

```bash
# SSH into your Linux machine with the GPU
ssh user@your-linux-machine

# Clone the repository
git clone [your-repo] doc-processor
cd doc-processor

# Install Docker and NVIDIA Container Toolkit
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install NVIDIA Container Toolkit
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

sudo apt-get update && sudo apt-get install -y nvidia-container-toolkit
sudo systemctl restart docker

# Test GPU access
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi

# Run the full stack
docker-compose up -d
```

### **Option 2: Remote Docker on GPU Machine**

Configure Docker on your Mac to use the remote Linux machine:

```bash
# On your Mac
export DOCKER_HOST="ssh://user@your-linux-machine"

# Test connection
docker info

# Run compose
docker-compose up -d
```

### **Option 3: Windows with WSL2** 

If your RTX 4090 is in a Windows machine:

1. Install WSL2 with Ubuntu
2. Install Docker Desktop for Windows with WSL2 backend
3. Enable GPU support in Docker Desktop
4. Run the test from WSL2 terminal

### **Option 4: Cloud GPU for Testing**

Quick test on a cloud GPU:

```bash
# Use vast.ai for cheap testing ($0.20-0.40/hour)
# 1. Create account at vast.ai
# 2. Rent RTX 4090 instance
# 3. SSH and run:

git clone [your-repo] doc-processor
cd doc-processor
./runpod-startup.sh  # Works on any GPU cloud
```

## 🧪 Test Procedure for RTX 4090

### **1. Basic GPU Test**
```bash
# Verify GPU is accessible
nvidia-smi

# Test Docker GPU access
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi
```

### **2. Start Services**
```bash
# Start all services
docker-compose up -d

# Monitor GPU usage
watch -n 1 nvidia-smi
```

### **3. Test Document Processing**

```bash
# Upload a test document
curl -X POST -F "file=@/path/to/tender.pdf" http://localhost:8000/api/upload

# Process with all models
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "document_id": "your-doc-id",
    "models": ["surya", "layoutlm", "qwen", "minimax", "phi4"],
    "pipeline": "tender"
  }' \
  http://localhost:8000/api/process

# Check job status
curl http://localhost:8000/api/job/{job-id}
```

### **4. Monitor Performance**

```bash
# GPU Memory Usage per Model
docker exec surya nvidia-smi
docker exec qwen nvidia-smi
docker exec minimax nvidia-smi

# Processing times
docker-compose logs -f gateway | grep "Processing time"
```

## 📊 Expected RTX 4090 Performance

| Model | VRAM Usage | Processing Speed | Batch Size |
|-------|------------|------------------|------------|
| Surya OCR | ~3-4 GB | 100+ pages/min | 8-16 |
| Qwen2.5-VL | ~9-10 GB | 50 tokens/sec | 1-2 |
| LayoutLMv3 | ~3-4 GB | 200+ docs/hour | 16-32 |
| MiniMax (AWQ) | ~20-22 GB | 30 tokens/sec | 1 |
| Phi-4 | ~6-7 GB | 100+ tokens/sec | 2-4 |

**Total VRAM**: ~23GB (fits perfectly in 24GB RTX 4090)

## 🔧 Performance Tuning for RTX 4090

### **Optimize Batch Sizes**
```bash
# Edit .env.docker
SURYA_BATCH_SIZE=16      # Increase from 4
LAYOUTLM_BATCH=32        # Increase from 8
QWEN_GPU_UTIL=0.35       # Reduce if needed
MINIMAX_GPU_UTIL=0.85    # Optimal for AWQ
```

### **Enable TensorRT** (Optional)
```bash
# For 20-30% speed boost
ENABLE_TENSORRT=true

# Rebuild with TensorRT
docker-compose build --build-arg ENABLE_TENSORRT=true
```

### **Memory Optimization**
```python
# In model services, add:
torch.backends.cudnn.benchmark = True
torch.backends.cuda.matmul.allow_tf32 = True
```

## 🧪 Test with Real Tender

### **1. Copy BKI Tender to Test**
```bash
# Copy from your Desktop
cp ~/Desktop/BKI/BKI001194*.pdf ./test-docs/
```

### **2. Process Full Tender**
```bash
# Upload main tender document
RESPONSE=$(curl -s -X POST \
  -F "file=@test-docs/BKI001194 - ICN Tender Information Pack.pdf" \
  http://localhost:8000/api/upload)

DOC_ID=$(echo $RESPONSE | jq -r '.document_id')

# Process with full pipeline
JOB_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d "{
    \"document_id\": \"$DOC_ID\",
    \"models\": [\"surya\", \"layoutlm\", \"qwen\", \"minimax\", \"phi4\"],
    \"pipeline\": \"tender\"
  }" \
  http://localhost:8000/api/process)

JOB_ID=$(echo $JOB_RESPONSE | jq -r '.job_id')

# Poll for results
while true; do
  STATUS=$(curl -s http://localhost:8000/api/job/$JOB_ID | jq -r '.status')
  echo "Status: $STATUS"
  if [ "$STATUS" = "completed" ] || [ "$STATUS" = "failed" ]; then
    break
  fi
  sleep 5
done

# Get final results
curl -s http://localhost:8000/api/job/$JOB_ID | jq '.results'
```

## 📈 Benchmarking Script

```bash
#!/bin/bash
# benchmark.sh - Test processing speed

echo "🏁 Starting RTX 4090 Benchmark"

# Test different batch sizes
for batch in 4 8 16 32; do
  export SURYA_BATCH_SIZE=$batch
  docker-compose restart surya
  sleep 10
  
  # Time the processing
  START=$(date +%s)
  ./test-document.sh
  END=$(date +%s)
  
  echo "Batch size $batch: $((END-START))s"
done
```

## 🚀 Quick Test Commands

```bash
# Test basic setup
make test-local

# Full GPU test
make test-gpu

# Benchmark performance
make benchmark

# Monitor GPU
make gpu-monitor
```

## 🛠️ Troubleshooting RTX 4090 Issues

### **CUDA Out of Memory**
- Reduce batch sizes
- Lower GPU utilization percentages
- Use more aggressive quantization

### **Slow Processing**
- Check GPU clock speeds: `nvidia-smi -q -d CLOCK`
- Ensure GPU not thermal throttling
- Verify PCIe bandwidth: `nvidia-smi -q -d PCIE`

### **Docker GPU Not Found**
- Verify NVIDIA Container Toolkit: `docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi`
- Check Docker daemon.json has nvidia runtime

Ready to test your RTX 4090? The setup will maximize your GPU's potential! 🎮