// Core Voice Bot Class

import { EventEmitter } from 'events';
import {
  VoiceBotConfig,
  MeetingSession,
  VoiceCommand,
  BotResponse,
  MeetingContext,
  TranscriptEntry,
  Participant
} from '../types';
import { SpeechService } from '../services/SpeechService';
import { MeetingPlatformService } from '../services/MeetingPlatformService';
import { NLPService } from '../services/NLPService';
import { TranscriptionService } from '../services/TranscriptionService';
import { PresentationService } from '../services/PresentationService';
import { PollingService } from '../services/PollingService';
import { SentimentAnalysisService } from '../services/SentimentAnalysisService';
import { ActionItemExtractor } from '../services/ActionItemExtractor';
import { CommandProcessor } from '../handlers/CommandProcessor';
import { MeetingRecorder } from '../services/MeetingRecorder';

export class VoiceBot extends EventEmitter {
  private config: VoiceBotConfig;
  private currentSession?: MeetingSession;
  private context?: MeetingContext;
  private isActive: boolean = false;

  // Services
  private speechService: SpeechService;
  private platformService: MeetingPlatformService;
  private nlpService: NLPService;
  private transcriptionService: TranscriptionService;
  private presentationService: PresentationService;
  private pollingService: PollingService;
  private sentimentService: SentimentAnalysisService;
  private actionItemExtractor: ActionItemExtractor;
  private commandProcessor: CommandProcessor;
  private recorder: MeetingRecorder;

  constructor(config: VoiceBotConfig) {
    super();
    this.config = config;
    this.initializeServices();
  }

  private initializeServices(): void {
    // Initialize all services with configuration
    this.speechService = new SpeechService(this.config.voice, this.config.credentials.speechServices);
    this.platformService = new MeetingPlatformService(this.config.meetingPlatform, this.config.credentials);
    this.nlpService = new NLPService(this.config.credentials.openai);
    this.transcriptionService = new TranscriptionService(this.config.credentials.speechServices);
    this.presentationService = new PresentationService();
    this.pollingService = new PollingService();
    this.sentimentService = new SentimentAnalysisService();
    this.actionItemExtractor = new ActionItemExtractor(this.nlpService);
    this.commandProcessor = new CommandProcessor(this);
    this.recorder = new MeetingRecorder(this.config.meetingPlatform);

    // Set up event listeners
    this.setupEventListeners();
  }

  private setupEventListeners(): void {
    // Speech recognition events
    this.speechService.on('speech-detected', (text: string) => {
      this.handleSpeechInput(text);
    });

    // Platform events
    this.platformService.on('participant-joined', (participant: Participant) => {
      this.handleParticipantJoined(participant);
    });

    this.platformService.on('participant-left', (participant: Participant) => {
      this.handleParticipantLeft(participant);
    });

    // Transcription events
    this.transcriptionService.on('transcript-ready', (entry: TranscriptEntry) => {
      this.handleTranscriptEntry(entry);
    });

    // Command processing events
    this.commandProcessor.on('command-executed', (response: BotResponse) => {
      this.handleBotResponse(response);
    });
  }

  public async joinMeeting(meetingId: string, context: MeetingContext): Promise<void> {
    try {
      // Set meeting context
      this.context = context;

      // Create new session
      this.currentSession = {
        id: this.generateSessionId(),
        meetingId,
        platform: this.config.meetingPlatform.type,
        startTime: new Date(),
        participants: [],
        transcript: [],
        polls: [],
        actionItems: [],
        sentiment: {
          overall: { positive: 0, neutral: 1, negative: 0, dominant: 'neutral' },
          timeline: [],
          byParticipant: {}
        }
      };

      // Connect to meeting platform
      await this.platformService.joinMeeting(meetingId);

      // Start services
      if (this.config.features.speechRecognition) {
        await this.speechService.startListening();
      }

      if (this.config.features.realTimeTranscription) {
        await this.transcriptionService.startTranscription();
      }

      if (this.config.meetingPlatform.recordingEnabled) {
        await this.recorder.startRecording(meetingId);
      }

      this.isActive = true;

      // Introduce the bot
      await this.introduce();

      this.emit('meeting-joined', { meetingId, session: this.currentSession });
    } catch (error) {
      this.emit('error', { type: 'join-meeting', error });
      throw error;
    }
  }

  private async introduce(): Promise<void> {
    const introMessage = `Hello everyone, I'm ${this.config.name}, your AI meeting assistant. 
    I'll be helping facilitate today's tender kick-off meeting for ${this.context?.tenderDetails.title}. 
    I can answer questions, manage polls, control presentations, and help keep us on track. 
    Just say my name followed by your command or question.`;

    await this.speak(introMessage);
  }

  private async handleSpeechInput(text: string): Promise<void> {
    if (!this.isActive || !this.currentSession) return;

    // Check if the bot is being addressed
    const botName = this.config.name.toLowerCase();
    const lowerText = text.toLowerCase();

    if (lowerText.includes(botName) || this.isWaitingForResponse()) {
      // Process as command or question
      const command = await this.nlpService.parseCommand(text);
      
      if (command) {
        await this.commandProcessor.processCommand(command);
      } else {
        // Treat as a question
        await this.handleQuestion(text);
      }
    }

    // Always add to transcript
    const entry: TranscriptEntry = {
      timestamp: new Date(),
      speaker: 'Unknown', // Will be identified by platform service
      text,
      confidence: 0.95,
      keywords: await this.nlpService.extractKeywords(text)
    };

    this.currentSession.transcript.push(entry);

    // Analyze sentiment
    if (this.config.features.sentimentAnalysis) {
      const sentiment = await this.sentimentService.analyzeSentiment(text);
      entry.sentiment = sentiment;
      this.updateSentimentData(sentiment);
    }
  }

  private async handleQuestion(question: string): Promise<void> {
    if (!this.config.features.naturalLanguageQA) return;

    try {
      // Generate context-aware response
      const response = await this.nlpService.generateResponse(question, {
        tenderDetails: this.context?.tenderDetails,
        meetingContext: this.getCurrentMeetingContext(),
        recentTranscript: this.getRecentTranscript(5)
      });

      await this.speak(response);
    } catch (error) {
      await this.speak("I'm sorry, I couldn't process that question. Could you please rephrase?");
      this.emit('error', { type: 'question-handling', error });
    }
  }

  public async speak(text: string, options?: { priority?: 'high' | 'normal'; waitForCompletion?: boolean }): Promise<void> {
    try {
      const audioUrl = await this.speechService.synthesizeSpeech(text);
      await this.platformService.playAudio(audioUrl);

      // Add bot's speech to transcript
      if (this.currentSession) {
        this.currentSession.transcript.push({
          timestamp: new Date(),
          speaker: this.config.name,
          text,
          confidence: 1.0
        });
      }

      this.emit('bot-spoke', { text, audioUrl });
    } catch (error) {
      this.emit('error', { type: 'speech-synthesis', error });
    }
  }

  public async startPoll(question: string, options?: string[], type: 'yes-no' | 'multiple-choice' = 'yes-no'): Promise<void> {
    if (!this.config.features.pollingEnabled || !this.currentSession) return;

    const poll = await this.pollingService.createPoll({
      question,
      type,
      options: options || (type === 'yes-no' ? ['Yes', 'No'] : undefined)
    });

    this.currentSession.polls.push(poll);

    // Announce poll
    await this.speak(`I'm starting a poll: ${question}. Please respond with your answer.`);

    // Start collecting responses
    this.pollingService.startCollecting(poll.id);

    // Set a timer to close the poll
    setTimeout(async () => {
      await this.endPoll(poll.id);
    }, 60000); // 1 minute default
  }

  public async endPoll(pollId: string): Promise<void> {
    const results = await this.pollingService.endPoll(pollId);
    
    if (results) {
      const summary = this.pollingService.generateSummary(results);
      await this.speak(`Poll results: ${summary}`);
      this.emit('poll-completed', { pollId, results });
    }
  }

  public async controlPresentation(action: 'next' | 'previous' | 'goto', slideNumber?: number): Promise<void> {
    if (!this.config.features.presentationControl) return;

    try {
      await this.presentationService.controlSlide(action, slideNumber);
      
      // Get current slide info
      const currentSlide = this.presentationService.getCurrentSlide();
      
      if (currentSlide?.narration) {
        await this.speak(currentSlide.narration.text);
      }

      this.emit('slide-changed', { slide: currentSlide });
    } catch (error) {
      this.emit('error', { type: 'presentation-control', error });
    }
  }

  public async extractActionItems(): Promise<void> {
    if (!this.config.features.actionItemExtraction || !this.currentSession) return;

    const actionItems = await this.actionItemExtractor.extractFromTranscript(
      this.currentSession.transcript
    );

    this.currentSession.actionItems = actionItems;

    if (actionItems.length > 0) {
      await this.speak(`I've identified ${actionItems.length} action items from our discussion. I'll include these in the meeting summary.`);
    }
  }

  public async generateMeetingSummary(): Promise<string> {
    if (!this.currentSession) return '';

    const summary = await this.nlpService.generateMeetingSummary({
      transcript: this.currentSession.transcript,
      actionItems: this.currentSession.actionItems,
      polls: this.currentSession.polls,
      sentiment: this.currentSession.sentiment,
      context: this.context
    });

    return summary;
  }

  public async leaveMeeting(): Promise<void> {
    if (!this.isActive || !this.currentSession) return;

    try {
      // Stop all services
      await this.speechService.stopListening();
      await this.transcriptionService.stopTranscription();
      
      if (this.recorder.isRecording()) {
        const recordingData = await this.recorder.stopRecording();
        this.currentSession.recording = recordingData;
      }

      // Extract final action items
      await this.extractActionItems();

      // Generate and announce summary
      const summary = await this.generateMeetingSummary();
      await this.speak("Thank you all for your participation. I'll send the meeting summary and action items shortly. Goodbye!");

      // Disconnect from platform
      await this.platformService.leaveMeeting();

      this.currentSession.endTime = new Date();
      this.isActive = false;

      this.emit('meeting-left', { 
        session: this.currentSession, 
        summary 
      });
    } catch (error) {
      this.emit('error', { type: 'leave-meeting', error });
    }
  }

  private handleParticipantJoined(participant: Participant): void {
    if (!this.currentSession) return;

    this.currentSession.participants.push(participant);
    
    // Greet new participant if meeting has started
    if (this.isActive && Date.now() - this.currentSession.startTime.getTime() > 30000) {
      this.speak(`Welcome ${participant.name} to the meeting.`, { priority: 'normal' });
    }
  }

  private handleParticipantLeft(participant: Participant): void {
    if (!this.currentSession) return;

    const index = this.currentSession.participants.findIndex(p => p.id === participant.id);
    if (index !== -1) {
      this.currentSession.participants[index].leaveTime = new Date();
    }
  }

  private handleTranscriptEntry(entry: TranscriptEntry): void {
    if (!this.currentSession) return;

    this.currentSession.transcript.push(entry);

    // Check for action items in real-time
    if (this.config.features.actionItemExtraction) {
      this.actionItemExtractor.checkForActionItem(entry.text).then(actionItem => {
        if (actionItem && this.currentSession) {
          this.currentSession.actionItems.push(actionItem);
          this.emit('action-item-detected', actionItem);
        }
      });
    }
  }

  private handleBotResponse(response: BotResponse): void {
    this.emit('bot-response', response);
  }

  private updateSentimentData(sentiment: any): void {
    if (!this.currentSession) return;

    // Update overall sentiment
    const { sentiment: sessionSentiment } = this.currentSession;
    
    // Add to timeline
    sessionSentiment.timeline.push({
      timestamp: new Date(),
      score: sentiment
    });

    // Recalculate overall
    const recentScores = sessionSentiment.timeline.slice(-10);
    const avgPositive = recentScores.reduce((sum, s) => sum + s.score.positive, 0) / recentScores.length;
    const avgNeutral = recentScores.reduce((sum, s) => sum + s.score.neutral, 0) / recentScores.length;
    const avgNegative = recentScores.reduce((sum, s) => sum + s.score.negative, 0) / recentScores.length;

    sessionSentiment.overall = {
      positive: avgPositive,
      neutral: avgNeutral,
      negative: avgNegative,
      dominant: avgPositive > avgNegative ? (avgPositive > avgNeutral ? 'positive' : 'neutral') : 
                (avgNegative > avgNeutral ? 'negative' : 'neutral')
    };
  }

  private getCurrentMeetingContext(): any {
    if (!this.currentSession || !this.context) return {};

    return {
      duration: Date.now() - this.currentSession.startTime.getTime(),
      participantCount: this.currentSession.participants.filter(p => !p.leaveTime).length,
      currentAgenda: this.context.agenda[this.context.currentAgendaItem],
      timeRemaining: (this.context.scheduledDuration * 60000) - (Date.now() - this.currentSession.startTime.getTime())
    };
  }

  private getRecentTranscript(minutes: number): TranscriptEntry[] {
    if (!this.currentSession) return [];

    const cutoffTime = Date.now() - (minutes * 60000);
    return this.currentSession.transcript.filter(entry => 
      entry.timestamp.getTime() > cutoffTime
    );
  }

  private isWaitingForResponse(): boolean {
    // Check if bot is waiting for a response (e.g., during a poll)
    return this.pollingService.hasActivePoll();
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public getters
  public getSession(): MeetingSession | undefined {
    return this.currentSession;
  }

  public getContext(): MeetingContext | undefined {
    return this.context;
  }

  public isInMeeting(): boolean {
    return this.isActive;
  }
}