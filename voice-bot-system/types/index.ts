// Voice Bot System Types

export interface VoiceBotConfig {
  name: string;
  voice: VoiceSettings;
  meetingPlatform: MeetingPlatform;
  features: BotFeatures;
  credentials: PlatformCredentials;
}

export interface VoiceSettings {
  language: string;
  voiceId: string;
  speed: number;
  pitch: number;
  volume: number;
  provider: 'azure' | 'google' | 'amazon' | 'elevenlabs';
}

export interface MeetingPlatform {
  type: 'teams' | 'zoom' | 'meet' | 'webrtc';
  autoJoin: boolean;
  coHostEnabled: boolean;
  recordingEnabled: boolean;
}

export interface BotFeatures {
  speechRecognition: boolean;
  naturalLanguageQA: boolean;
  presentationControl: boolean;
  pollingEnabled: boolean;
  sentimentAnalysis: boolean;
  actionItemExtraction: boolean;
  realTimeTranscription: boolean;
}

export interface PlatformCredentials {
  teams?: {
    tenantId: string;
    clientId: string;
    clientSecret: string;
  };
  zoom?: {
    apiKey: string;
    apiSecret: string;
    sdkKey: string;
    sdkSecret: string;
  };
  googleMeet?: {
    clientId: string;
    clientSecret: string;
    apiKey: string;
  };
  speechServices: {
    azure?: {
      subscriptionKey: string;
      region: string;
    };
    google?: {
      apiKey: string;
      projectId: string;
    };
  };
  openai: {
    apiKey: string;
    model: string;
  };
}

export interface MeetingSession {
  id: string;
  meetingId: string;
  platform: MeetingPlatform['type'];
  startTime: Date;
  endTime?: Date;
  participants: Participant[];
  transcript: TranscriptEntry[];
  polls: Poll[];
  actionItems: ActionItem[];
  sentiment: SentimentData;
  recording?: RecordingData;
  presentation?: PresentationData;
}

export interface Participant {
  id: string;
  name: string;
  email?: string;
  role: 'host' | 'co-host' | 'participant';
  joinTime: Date;
  leaveTime?: Date;
  speakingTime: number;
  sentimentScore?: number;
}

export interface TranscriptEntry {
  timestamp: Date;
  speaker: string;
  text: string;
  confidence: number;
  sentiment?: SentimentScore;
  keywords?: string[];
}

export interface Poll {
  id: string;
  question: string;
  type: 'yes-no' | 'multiple-choice' | 'scale' | 'open-ended';
  options?: string[];
  responses: PollResponse[];
  startTime: Date;
  endTime?: Date;
  results?: PollResults;
}

export interface PollResponse {
  participantId: string;
  response: string | number;
  timestamp: Date;
  voiceConfidence?: number;
}

export interface PollResults {
  totalResponses: number;
  distribution: Record<string, number>;
  summary: string;
}

export interface ActionItem {
  id: string;
  description: string;
  assignee?: string;
  dueDate?: Date;
  priority: 'high' | 'medium' | 'low';
  status: 'pending' | 'in-progress' | 'completed';
  extractedFrom: string;
  confidence: number;
}

export interface SentimentData {
  overall: SentimentScore;
  timeline: SentimentTimeline[];
  byParticipant: Record<string, SentimentScore>;
}

export interface SentimentScore {
  positive: number;
  neutral: number;
  negative: number;
  dominant: 'positive' | 'neutral' | 'negative';
}

export interface SentimentTimeline {
  timestamp: Date;
  score: SentimentScore;
  triggerText?: string;
}

export interface RecordingData {
  url: string;
  duration: number;
  format: 'mp4' | 'webm' | 'mp3';
  size: number;
  cloudStorage?: {
    provider: 'azure' | 'aws' | 'gcp';
    path: string;
  };
}

export interface PresentationData {
  id: string;
  title: string;
  slides: Slide[];
  currentSlide: number;
  shareStatus: 'not-shared' | 'sharing' | 'paused';
}

export interface Slide {
  number: number;
  title?: string;
  content?: string;
  notes?: string;
  media?: {
    type: 'image' | 'video' | 'chart';
    url: string;
  };
  narration?: {
    text: string;
    audioUrl?: string;
    duration?: number;
  };
}

export interface VoiceCommand {
  intent: CommandIntent;
  parameters?: Record<string, any>;
  confidence: number;
  rawText: string;
  timestamp: Date;
}

export type CommandIntent = 
  | 'next-slide'
  | 'previous-slide'
  | 'go-to-slide'
  | 'start-poll'
  | 'end-poll'
  | 'mute-all'
  | 'unmute-all'
  | 'start-recording'
  | 'stop-recording'
  | 'show-participants'
  | 'answer-question'
  | 'summarize-discussion'
  | 'extract-action-items';

export interface MeetingContext {
  tenderDetails: {
    title: string;
    client: string;
    deadline: Date;
    requirements: string[];
    budget?: number;
  };
  agenda: AgendaItem[];
  currentAgendaItem: number;
  timeElapsed: number;
  scheduledDuration: number;
}

export interface AgendaItem {
  title: string;
  duration: number;
  presenter?: string;
  completed: boolean;
  notes?: string;
}

export interface BotResponse {
  type: 'speech' | 'action' | 'poll' | 'presentation';
  content: string;
  audioUrl?: string;
  action?: BotAction;
  metadata?: Record<string, any>;
}

export interface BotAction {
  type: 'slide-change' | 'poll-start' | 'recording-toggle' | 'participant-action';
  parameters: Record<string, any>;
}