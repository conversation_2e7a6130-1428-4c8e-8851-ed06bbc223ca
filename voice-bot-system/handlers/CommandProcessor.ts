// Voice Command Processing Handler

import { EventEmitter } from 'events';
import { VoiceCommand, BotResponse, BotAction } from '../types';
import { VoiceBot } from '../core/VoiceBot';

export class CommandProcessor extends EventEmitter {
  private bot: VoiceBot;
  private commandQueue: VoiceCommand[] = [];
  private isProcessing: boolean = false;

  constructor(bot: VoiceBot) {
    super();
    this.bot = bot;
  }

  public async processCommand(command: VoiceCommand): Promise<void> {
    // Add to queue
    this.commandQueue.push(command);
    
    // Process queue if not already processing
    if (!this.isProcessing) {
      await this.processQueue();
    }
  }

  private async processQueue(): Promise<void> {
    if (this.commandQueue.length === 0) {
      this.isProcessing = false;
      return;
    }

    this.isProcessing = true;
    const command = this.commandQueue.shift()!;

    try {
      const response = await this.executeCommand(command);
      this.emit('command-executed', response);
    } catch (error) {
      this.emit('command-error', { command, error });
      
      // Notify user of error
      await this.bot.speak('I encountered an error processing that command. Please try again.');
    }

    // Continue processing queue
    await this.processQueue();
  }

  private async executeCommand(command: VoiceCommand): Promise<BotResponse> {
    switch (command.intent) {
      case 'next-slide':
        return await this.handleNextSlide();
      
      case 'previous-slide':
        return await this.handlePreviousSlide();
      
      case 'go-to-slide':
        return await this.handleGoToSlide(command.parameters?.slideNumber);
      
      case 'start-poll':
        return await this.handleStartPoll(command.parameters);
      
      case 'end-poll':
        return await this.handleEndPoll();
      
      case 'mute-all':
        return await this.handleMuteAll();
      
      case 'unmute-all':
        return await this.handleUnmuteAll();
      
      case 'start-recording':
        return await this.handleStartRecording();
      
      case 'stop-recording':
        return await this.handleStopRecording();
      
      case 'show-participants':
        return await this.handleShowParticipants();
      
      case 'answer-question':
        return await this.handleAnswerQuestion(command.rawText);
      
      case 'summarize-discussion':
        return await this.handleSummarizeDiscussion();
      
      case 'extract-action-items':
        return await this.handleExtractActionItems();
      
      default:
        throw new Error(`Unknown command intent: ${command.intent}`);
    }
  }

  private async handleNextSlide(): Promise<BotResponse> {
    await this.bot.controlPresentation('next');
    
    return {
      type: 'action',
      content: 'Moving to the next slide.',
      action: {
        type: 'slide-change',
        parameters: { direction: 'next' }
      }
    };
  }

  private async handlePreviousSlide(): Promise<BotResponse> {
    await this.bot.controlPresentation('previous');
    
    return {
      type: 'action',
      content: 'Going back to the previous slide.',
      action: {
        type: 'slide-change',
        parameters: { direction: 'previous' }
      }
    };
  }

  private async handleGoToSlide(slideNumber?: number): Promise<BotResponse> {
    if (!slideNumber) {
      await this.bot.speak('Please specify which slide number you\'d like to go to.');
      return {
        type: 'speech',
        content: 'Slide number not specified.'
      };
    }

    await this.bot.controlPresentation('goto', slideNumber);
    
    return {
      type: 'action',
      content: `Navigating to slide ${slideNumber}.`,
      action: {
        type: 'slide-change',
        parameters: { slideNumber }
      }
    };
  }

  private async handleStartPoll(parameters?: Record<string, any>): Promise<BotResponse> {
    const question = parameters?.question || 'Should we proceed with this approach?';
    const type = parameters?.type || 'yes-no';
    const options = parameters?.options;

    await this.bot.startPoll(question, options, type);
    
    return {
      type: 'poll',
      content: `Starting poll: ${question}`,
      metadata: { question, type, options }
    };
  }

  private async handleEndPoll(): Promise<BotResponse> {
    const session = this.bot.getSession();
    if (!session) {
      return {
        type: 'speech',
        content: 'No active session found.'
      };
    }

    // Find active poll
    const activePoll = session.polls.find(p => !p.endTime);
    if (!activePoll) {
      await this.bot.speak('There is no active poll to end.');
      return {
        type: 'speech',
        content: 'No active poll found.'
      };
    }

    await this.bot.endPoll(activePoll.id);
    
    return {
      type: 'action',
      content: 'Poll has been closed.',
      action: {
        type: 'poll-end',
        parameters: { pollId: activePoll.id }
      }
    };
  }

  private async handleMuteAll(): Promise<BotResponse> {
    // This would integrate with the meeting platform
    await this.bot.speak('Muting all participants.');
    
    return {
      type: 'action',
      content: 'All participants have been muted.',
      action: {
        type: 'participant-action',
        parameters: { action: 'mute-all' }
      }
    };
  }

  private async handleUnmuteAll(): Promise<BotResponse> {
    await this.bot.speak('Unmuting all participants.');
    
    return {
      type: 'action',
      content: 'All participants have been unmuted.',
      action: {
        type: 'participant-action',
        parameters: { action: 'unmute-all' }
      }
    };
  }

  private async handleStartRecording(): Promise<BotResponse> {
    await this.bot.speak('Starting meeting recording.');
    
    return {
      type: 'action',
      content: 'Meeting recording has started.',
      action: {
        type: 'recording-toggle',
        parameters: { action: 'start' }
      }
    };
  }

  private async handleStopRecording(): Promise<BotResponse> {
    await this.bot.speak('Stopping meeting recording.');
    
    return {
      type: 'action',
      content: 'Meeting recording has stopped.',
      action: {
        type: 'recording-toggle',
        parameters: { action: 'stop' }
      }
    };
  }

  private async handleShowParticipants(): Promise<BotResponse> {
    const session = this.bot.getSession();
    if (!session) {
      return {
        type: 'speech',
        content: 'No active session found.'
      };
    }

    const activeParticipants = session.participants.filter(p => !p.leaveTime);
    const participantList = activeParticipants
      .map(p => p.name)
      .join(', ');

    await this.bot.speak(`We currently have ${activeParticipants.length} participants: ${participantList}`);
    
    return {
      type: 'speech',
      content: `${activeParticipants.length} participants in meeting.`,
      metadata: { participants: activeParticipants }
    };
  }

  private async handleAnswerQuestion(question: string): Promise<BotResponse> {
    // The bot will handle this through its Q&A system
    return {
      type: 'speech',
      content: 'Processing question...',
      metadata: { question }
    };
  }

  private async handleSummarizeDiscussion(): Promise<BotResponse> {
    const session = this.bot.getSession();
    if (!session) {
      return {
        type: 'speech',
        content: 'No active session found.'
      };
    }

    // Get last 5 minutes of discussion
    const recentTranscript = session.transcript.filter(entry => 
      Date.now() - entry.timestamp.getTime() < 5 * 60 * 1000
    );

    if (recentTranscript.length === 0) {
      await this.bot.speak('There hasn\'t been any recent discussion to summarize.');
      return {
        type: 'speech',
        content: 'No recent discussion found.'
      };
    }

    // Generate summary using NLP service
    const summary = recentTranscript
      .map(entry => `${entry.speaker}: ${entry.text}`)
      .join(' ');

    await this.bot.speak(`Here\'s a summary of the recent discussion: ${summary.substring(0, 200)}...`);
    
    return {
      type: 'speech',
      content: 'Discussion summarized.',
      metadata: { transcriptLength: recentTranscript.length }
    };
  }

  private async handleExtractActionItems(): Promise<BotResponse> {
    await this.bot.extractActionItems();
    
    const session = this.bot.getSession();
    const actionItemCount = session?.actionItems.length || 0;
    
    return {
      type: 'action',
      content: `Extracted ${actionItemCount} action items.`,
      metadata: { count: actionItemCount }
    };
  }

  // Command validation and enhancement
  public async validateCommand(command: VoiceCommand): Promise<boolean> {
    // Check confidence threshold
    if (command.confidence < 0.6) {
      return false;
    }

    // Validate required parameters
    switch (command.intent) {
      case 'go-to-slide':
        return !!command.parameters?.slideNumber;
      
      case 'start-poll':
        return !!command.parameters?.question;
      
      default:
        return true;
    }
  }

  public async enhanceCommand(command: VoiceCommand): Promise<VoiceCommand> {
    // Add context-based enhancements
    const enhanced = { ...command };

    switch (command.intent) {
      case 'start-poll':
        // If no type specified, infer from question
        if (!enhanced.parameters?.type) {
          const question = enhanced.parameters?.question || '';
          if (question.toLowerCase().includes('agree') || 
              question.toLowerCase().includes('approve')) {
            enhanced.parameters = {
              ...enhanced.parameters,
              type: 'yes-no'
            };
          }
        }
        break;
    }

    return enhanced;
  }

  // Command history and analytics
  private commandHistory: VoiceCommand[] = [];

  public getCommandHistory(): VoiceCommand[] {
    return [...this.commandHistory];
  }

  public getCommandStats(): Record<CommandIntent, number> {
    const stats: Partial<Record<CommandIntent, number>> = {};
    
    this.commandHistory.forEach(command => {
      stats[command.intent] = (stats[command.intent] || 0) + 1;
    });

    return stats as Record<CommandIntent, number>;
  }

  public getMostUsedCommands(limit: number = 5): CommandIntent[] {
    const stats = this.getCommandStats();
    
    return Object.entries(stats)
      .sort(([, a], [, b]) => b - a)
      .slice(0, limit)
      .map(([intent]) => intent as CommandIntent);
  }
}