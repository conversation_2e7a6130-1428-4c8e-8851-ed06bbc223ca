// Meeting Recording Service

import { EventEmitter } from 'events';
import { RecordingData, MeetingPlatform } from '../types';

export class MeetingRecorder extends EventEmitter {
  private platform: MeetingPlatform;
  private isRecording: boolean = false;
  private recordingData?: RecordingData;
  private mediaRecorder?: MediaRecorder;
  private recordedChunks: Blob[] = [];
  private startTime?: Date;

  constructor(platform: MeetingPlatform) {
    super();
    this.platform = platform;
  }

  public async startRecording(meetingId: string): Promise<void> {
    if (this.isRecording) {
      throw new Error('Recording already in progress');
    }

    try {
      this.startTime = new Date();
      this.recordedChunks = [];

      // Get screen and audio streams
      const screenStream = await this.getScreenStream();
      const audioStream = await this.getAudioStream();

      // Combine streams
      const combinedStream = new MediaStream([
        ...screenStream.getTracks(),
        ...audioStream.getTracks()
      ]);

      // Initialize MediaRecorder
      this.mediaRecorder = new MediaRecorder(combinedStream, {
        mimeType: this.getSupportedMimeType()
      });

      this.setupRecorderEventHandlers();

      // Start recording
      this.mediaRecorder.start(1000); // Collect data every second
      this.isRecording = true;

      this.emit('recording-started', { meetingId, startTime: this.startTime });
    } catch (error) {
      this.emit('recording-error', { type: 'start-failed', error });
      throw error;
    }
  }

  public async stopRecording(): Promise<RecordingData> {
    if (!this.isRecording || !this.mediaRecorder || !this.startTime) {
      throw new Error('No recording in progress');
    }

    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder) {
        reject(new Error('MediaRecorder not initialized'));
        return;
      }

      this.mediaRecorder.onstop = async () => {
        try {
          const recordingData = await this.processRecording();
          resolve(recordingData);
        } catch (error) {
          reject(error);
        }
      };

      this.mediaRecorder.stop();
      this.isRecording = false;
    });
  }

  private async getScreenStream(): Promise<MediaStream> {
    try {
      // Try to get screen share
      const stream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          mediaSource: 'screen',
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 }
        } as any,
        audio: true
      });

      return stream;
    } catch (error) {
      console.warn('Screen capture failed, using fallback');
      
      // Fallback to camera if screen sharing fails
      return await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      });
    }
  }

  private async getAudioStream(): Promise<MediaStream> {
    try {
      return await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 44100
        }
      });
    } catch (error) {
      console.warn('Audio capture failed');
      // Return empty stream if audio fails
      return new MediaStream();
    }
  }

  private getSupportedMimeType(): string {
    const types = [
      'video/webm;codecs=vp9,opus',
      'video/webm;codecs=vp8,opus',
      'video/webm;codecs=h264,opus',
      'video/mp4;codecs=h264,aac',
      'video/webm'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        return type;
      }
    }

    return 'video/webm'; // Fallback
  }

  private setupRecorderEventHandlers(): void {
    if (!this.mediaRecorder) return;

    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
        this.emit('recording-data', { 
          size: event.data.size,
          timestamp: new Date()
        });
      }
    };

    this.mediaRecorder.onerror = (event) => {
      this.emit('recording-error', { 
        type: 'recording-error', 
        error: event.error 
      });
    };

    this.mediaRecorder.onstart = () => {
      this.emit('recording-started', { 
        timestamp: new Date() 
      });
    };

    this.mediaRecorder.onstop = () => {
      this.emit('recording-stopped', { 
        timestamp: new Date(),
        chunks: this.recordedChunks.length
      });
    };
  }

  private async processRecording(): Promise<RecordingData> {
    if (!this.startTime) {
      throw new Error('Recording start time not set');
    }

    const endTime = new Date();
    const duration = endTime.getTime() - this.startTime.getTime();

    // Create blob from recorded chunks
    const mimeType = this.mediaRecorder?.mimeType || 'video/webm';
    const recordingBlob = new Blob(this.recordedChunks, { type: mimeType });

    // Generate URL for local access
    const url = URL.createObjectURL(recordingBlob);

    // Determine format from mime type
    const format = this.getFormatFromMimeType(mimeType);

    const recordingData: RecordingData = {
      url,
      duration,
      format,
      size: recordingBlob.size
    };

    // Upload to cloud storage if configured
    try {
      const cloudStorage = await this.uploadToCloudStorage(recordingBlob);
      if (cloudStorage) {
        recordingData.cloudStorage = cloudStorage;
      }
    } catch (error) {
      console.warn('Cloud upload failed:', error);
    }

    this.recordingData = recordingData;
    return recordingData;
  }

  private getFormatFromMimeType(mimeType: string): RecordingData['format'] {
    if (mimeType.includes('mp4')) return 'mp4';
    if (mimeType.includes('webm')) return 'webm';
    if (mimeType.includes('mp3')) return 'mp3';
    return 'webm'; // Default
  }

  private async uploadToCloudStorage(blob: Blob): Promise<RecordingData['cloudStorage']> {
    // This would integrate with your cloud storage provider
    // For now, return a mock implementation
    
    const fileName = `recording_${Date.now()}.${this.getFormatFromMimeType(blob.type)}`;
    
    // Mock upload - in production, implement actual cloud storage
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          provider: 'aws',
          path: `recordings/${fileName}`
        });
      }, 1000);
    });
  }

  public async pauseRecording(): Promise<void> {
    if (!this.isRecording || !this.mediaRecorder) {
      throw new Error('No recording in progress');
    }

    if (this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.pause();
      this.emit('recording-paused', { timestamp: new Date() });
    }
  }

  public async resumeRecording(): Promise<void> {
    if (!this.isRecording || !this.mediaRecorder) {
      throw new Error('No recording in progress');
    }

    if (this.mediaRecorder.state === 'paused') {
      this.mediaRecorder.resume();
      this.emit('recording-resumed', { timestamp: new Date() });
    }
  }

  public isRecording(): boolean {
    return this.isRecording;
  }

  public getRecordingStatus(): {
    isRecording: boolean;
    duration: number;
    size: number;
    chunks: number;
  } {
    const duration = this.startTime ? Date.now() - this.startTime.getTime() : 0;
    const size = this.recordedChunks.reduce((total, chunk) => total + chunk.size, 0);

    return {
      isRecording: this.isRecording,
      duration,
      size,
      chunks: this.recordedChunks.length
    };
  }

  public async generateThumbnail(timeOffset: number = 0): Promise<string> {
    if (!this.recordingData) {
      throw new Error('No recording data available');
    }

    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.src = this.recordingData!.url;
      video.currentTime = timeOffset;

      video.onloadedmetadata = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Canvas context not available'));
          return;
        }

        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        ctx.drawImage(video, 0, 0);
        
        const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.8);
        resolve(thumbnailUrl);
      };

      video.onerror = () => {
        reject(new Error('Failed to load video for thumbnail'));
      };
    });
  }

  public async extractAudioTrack(): Promise<string> {
    if (!this.recordingData) {
      throw new Error('No recording data available');
    }

    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.src = this.recordingData!.url;

      video.onloadedmetadata = () => {
        const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const source = audioContext.createMediaElementSource(video);
        
        // Create recorder for audio only
        const dest = audioContext.createMediaStreamDestination();
        source.connect(dest);
        
        const audioRecorder = new MediaRecorder(dest.stream, {
          mimeType: 'audio/webm'
        });
        
        const audioChunks: Blob[] = [];
        
        audioRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            audioChunks.push(event.data);
          }
        };
        
        audioRecorder.onstop = () => {
          const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
          const audioUrl = URL.createObjectURL(audioBlob);
          resolve(audioUrl);
        };
        
        audioRecorder.start();
        video.play();
        
        video.onended = () => {
          audioRecorder.stop();
        };
      };

      video.onerror = () => {
        reject(new Error('Failed to load video for audio extraction'));
      };
    });
  }

  public async getRecordingMetadata(): Promise<{
    duration: number;
    size: number;
    format: string;
    resolution?: string;
    frameRate?: number;
    audioChannels?: number;
    sampleRate?: number;
  }> {
    if (!this.recordingData) {
      throw new Error('No recording data available');
    }

    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      video.src = this.recordingData!.url;

      video.onloadedmetadata = () => {
        const metadata = {
          duration: video.duration * 1000, // Convert to milliseconds
          size: this.recordingData!.size,
          format: this.recordingData!.format,
          resolution: `${video.videoWidth}x${video.videoHeight}`,
          frameRate: 30, // Default, would need more sophisticated detection
          audioChannels: 2, // Default stereo
          sampleRate: 44100 // Default sample rate
        };

        resolve(metadata);
      };

      video.onerror = () => {
        reject(new Error('Failed to load video metadata'));
      };
    });
  }

  public async cleanup(): Promise<void> {
    if (this.isRecording) {
      await this.stopRecording();
    }

    // Clean up object URLs
    if (this.recordingData?.url) {
      URL.revokeObjectURL(this.recordingData.url);
    }

    // Stop all media tracks
    if (this.mediaRecorder && this.mediaRecorder.stream) {
      this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
    }

    // Reset state
    this.recordedChunks = [];
    this.recordingData = undefined;
    this.mediaRecorder = undefined;
    this.startTime = undefined;
  }
}