// Sentiment Analysis Service

import { EventEmitter } from 'events';
import { SentimentScore } from '../types';

export class SentimentAnalysisService extends EventEmitter {
  private sentimentHistory: Array<{ timestamp: Date; score: SentimentScore; text: string }> = [];

  public async analyzeSentiment(text: string): Promise<SentimentScore> {
    try {
      // Use multiple approaches for better accuracy
      const lexiconScore = this.lexiconBasedAnalysis(text);
      const patternScore = this.patternBasedAnalysis(text);
      const contextScore = this.contextualAnalysis(text);

      // Combine scores with weights
      const combined = this.combineScores([
        { score: lexiconScore, weight: 0.4 },
        { score: patternScore, weight: 0.3 },
        { score: contextScore, weight: 0.3 }
      ]);

      // Store in history
      this.sentimentHistory.push({
        timestamp: new Date(),
        score: combined,
        text
      });

      // Emit sentiment change if significant
      this.checkSentimentChange(combined);

      return combined;
    } catch (error) {
      console.error('Sentiment analysis failed:', error);
      return { positive: 0.33, neutral: 0.34, negative: 0.33, dominant: 'neutral' };
    }
  }

  private lexiconBasedAnalysis(text: string): SentimentScore {
    // Define sentiment lexicons
    const positiveWords = [
      'good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'perfect', 'love', 'like',
      'happy', 'pleased', 'satisfied', 'agree', 'yes', 'definitely', 'absolutely', 'brilliant',
      'outstanding', 'impressive', 'successful', 'effective', 'efficient', 'valuable', 'beneficial',
      'positive', 'optimistic', 'confident', 'excited', 'enthusiastic', 'supportive', 'approve'
    ];

    const negativeWords = [
      'bad', 'terrible', 'awful', 'horrible', 'hate', 'dislike', 'disappointed', 'frustrated',
      'angry', 'upset', 'concerned', 'worried', 'disagree', 'no', 'never', 'impossible',
      'difficult', 'challenging', 'problem', 'issue', 'risk', 'failure', 'unsuccessful',
      'ineffective', 'inefficient', 'useless', 'waste', 'expensive', 'costly', 'negative'
    ];

    const neutralWords = [
      'okay', 'fine', 'maybe', 'perhaps', 'possibly', 'consider', 'think', 'believe',
      'understand', 'information', 'data', 'fact', 'detail', 'requirement', 'specification'
    ];

    const words = text.toLowerCase().replace(/[^\w\s]/g, '').split(/\s+/);
    
    let positiveCount = 0;
    let negativeCount = 0;
    let neutralCount = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) positiveCount++;
      else if (negativeWords.includes(word)) negativeCount++;
      else if (neutralWords.includes(word)) neutralCount++;
    });

    const total = positiveCount + negativeCount + neutralCount;
    
    if (total === 0) {
      return { positive: 0.33, neutral: 0.34, negative: 0.33, dominant: 'neutral' };
    }

    const positive = positiveCount / total;
    const negative = negativeCount / total;
    const neutral = 1 - positive - negative;

    return {
      positive: Math.max(0, Math.min(1, positive)),
      neutral: Math.max(0, Math.min(1, neutral)),
      negative: Math.max(0, Math.min(1, negative)),
      dominant: positive > negative ? (positive > neutral ? 'positive' : 'neutral') : 
                (negative > neutral ? 'negative' : 'neutral')
    };
  }

  private patternBasedAnalysis(text: string): SentimentScore {
    const patterns = {
      positive: [
        /\b(very|really|extremely|highly)\s+(good|great|pleased|satisfied|happy)\b/gi,
        /\b(love|like|enjoy|appreciate|support)\b/gi,
        /\b(yes|definitely|absolutely|certainly|of course)\b/gi,
        /\b(excellent|outstanding|impressive|successful|effective)\b/gi,
        /\b(agree|approve|accept|endorse)\b/gi
      ],
      negative: [
        /\b(very|really|extremely|highly)\s+(bad|terrible|disappointed|frustrated|concerned)\b/gi,
        /\b(hate|dislike|oppose|reject|refuse)\b/gi,
        /\b(no|never|impossible|unacceptable|ridiculous)\b/gi,
        /\b(problem|issue|risk|failure|disaster)\b/gi,
        /\b(disagree|disapprove|decline|deny)\b/gi
      ],
      neutral: [
        /\b(maybe|perhaps|possibly|consider|think|believe)\b/gi,
        /\b(information|data|fact|detail|requirement)\b/gi,
        /\b(understand|clarify|explain|discuss)\b/gi
      ]
    };

    let positiveMatches = 0;
    let negativeMatches = 0;
    let neutralMatches = 0;

    patterns.positive.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) positiveMatches += matches.length;
    });

    patterns.negative.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) negativeMatches += matches.length;
    });

    patterns.neutral.forEach(pattern => {
      const matches = text.match(pattern);
      if (matches) neutralMatches += matches.length;
    });

    const total = positiveMatches + negativeMatches + neutralMatches;
    
    if (total === 0) {
      return { positive: 0.33, neutral: 0.34, negative: 0.33, dominant: 'neutral' };
    }

    const positive = positiveMatches / total;
    const negative = negativeMatches / total;
    const neutral = neutralMatches / total;

    return {
      positive,
      neutral,
      negative,
      dominant: positive > negative ? (positive > neutral ? 'positive' : 'neutral') : 
                (negative > neutral ? 'negative' : 'neutral')
    };
  }

  private contextualAnalysis(text: string): SentimentScore {
    // Context-specific analysis for business meetings
    const businessContext = {
      positive: [
        'budget', 'timeline', 'deadline', 'approved', 'signed', 'contract', 'deal',
        'partnership', 'collaboration', 'opportunity', 'growth', 'profit', 'revenue',
        'milestone', 'achievement', 'deliverable', 'completion', 'success'
      ],
      negative: [
        'delay', 'postpone', 'cancel', 'reject', 'decline', 'budget cut', 'overtime',
        'shortage', 'deficit', 'loss', 'penalty', 'breach', 'violation', 'dispute',
        'complaint', 'escalation', 'urgent', 'critical', 'emergency'
      ],
      neutral: [
        'meeting', 'discussion', 'review', 'analysis', 'proposal', 'document',
        'requirement', 'specification', 'process', 'procedure', 'policy', 'standard'
      ]
    };

    const lower = text.toLowerCase();
    let positiveScore = 0;
    let negativeScore = 0;
    let neutralScore = 0;

    businessContext.positive.forEach(term => {
      if (lower.includes(term)) positiveScore += 1;
    });

    businessContext.negative.forEach(term => {
      if (lower.includes(term)) negativeScore += 1;
    });

    businessContext.neutral.forEach(term => {
      if (lower.includes(term)) neutralScore += 1;
    });

    const total = positiveScore + negativeScore + neutralScore;
    
    if (total === 0) {
      return { positive: 0.33, neutral: 0.34, negative: 0.33, dominant: 'neutral' };
    }

    const positive = positiveScore / total;
    const negative = negativeScore / total;
    const neutral = neutralScore / total;

    return {
      positive,
      neutral,
      negative,
      dominant: positive > negative ? (positive > neutral ? 'positive' : 'neutral') : 
                (negative > neutral ? 'negative' : 'neutral')
    };
  }

  private combineScores(scores: Array<{ score: SentimentScore; weight: number }>): SentimentScore {
    const totalWeight = scores.reduce((sum, s) => sum + s.weight, 0);
    
    const combined = scores.reduce((acc, s) => ({
      positive: acc.positive + (s.score.positive * s.weight),
      neutral: acc.neutral + (s.score.neutral * s.weight),
      negative: acc.negative + (s.score.negative * s.weight)
    }), { positive: 0, neutral: 0, negative: 0 });

    // Normalize by total weight
    const positive = combined.positive / totalWeight;
    const neutral = combined.neutral / totalWeight;
    const negative = combined.negative / totalWeight;

    return {
      positive,
      neutral,
      negative,
      dominant: positive > negative ? (positive > neutral ? 'positive' : 'neutral') : 
                (negative > neutral ? 'negative' : 'neutral')
    };
  }

  private checkSentimentChange(currentScore: SentimentScore): void {
    if (this.sentimentHistory.length < 2) return;

    const previous = this.sentimentHistory[this.sentimentHistory.length - 2];
    const current = currentScore;

    // Check for significant sentiment shift
    const threshold = 0.3;
    const positiveChange = Math.abs(current.positive - previous.score.positive);
    const negativeChange = Math.abs(current.negative - previous.score.negative);

    if (positiveChange > threshold || negativeChange > threshold) {
      this.emit('sentiment-change', {
        previous: previous.score,
        current: current,
        change: {
          positive: current.positive - previous.score.positive,
          negative: current.negative - previous.score.negative
        }
      });
    }

    // Check for concerning trends
    if (current.negative > 0.7 && current.dominant === 'negative') {
      this.emit('negative-sentiment-alert', {
        score: current,
        threshold: 0.7,
        suggestion: 'Consider addressing concerns or taking a break'
      });
    }
  }

  public getOverallSentiment(timeframe?: number): SentimentScore {
    const cutoff = timeframe ? Date.now() - (timeframe * 60000) : 0;
    const relevantHistory = this.sentimentHistory.filter(h => h.timestamp.getTime() > cutoff);

    if (relevantHistory.length === 0) {
      return { positive: 0.33, neutral: 0.34, negative: 0.33, dominant: 'neutral' };
    }

    const averages = relevantHistory.reduce((acc, h) => ({
      positive: acc.positive + h.score.positive,
      neutral: acc.neutral + h.score.neutral,
      negative: acc.negative + h.score.negative
    }), { positive: 0, neutral: 0, negative: 0 });

    const count = relevantHistory.length;
    const positive = averages.positive / count;
    const neutral = averages.neutral / count;
    const negative = averages.negative / count;

    return {
      positive,
      neutral,
      negative,
      dominant: positive > negative ? (positive > neutral ? 'positive' : 'neutral') : 
                (negative > neutral ? 'negative' : 'neutral')
    };
  }

  public getSentimentTrend(timeframe: number = 10): Array<{ timestamp: Date; score: SentimentScore }> {
    const cutoff = Date.now() - (timeframe * 60000);
    return this.sentimentHistory
      .filter(h => h.timestamp.getTime() > cutoff)
      .map(h => ({ timestamp: h.timestamp, score: h.score }));
  }

  public getEngagementScore(): number {
    // Calculate engagement based on sentiment variation and frequency
    if (this.sentimentHistory.length < 5) return 0.5;

    const recentHistory = this.sentimentHistory.slice(-10);
    
    // Measure sentiment variation (more variation = more engagement)
    const variations = recentHistory.map(h => h.score.positive - h.score.negative);
    const avgVariation = variations.reduce((sum, v) => sum + Math.abs(v), 0) / variations.length;
    
    // Measure frequency of sentiment changes
    const timeSpan = recentHistory[recentHistory.length - 1].timestamp.getTime() - 
                     recentHistory[0].timestamp.getTime();
    const frequency = recentHistory.length / (timeSpan / 60000); // per minute

    // Combine factors
    const variationScore = Math.min(avgVariation * 2, 1); // Normalize to 0-1
    const frequencyScore = Math.min(frequency / 5, 1); // Normalize assuming 5 per minute is high

    return (variationScore + frequencyScore) / 2;
  }

  public generateSentimentReport(): string {
    const overall = this.getOverallSentiment();
    const engagement = this.getEngagementScore();
    const trend = this.getSentimentTrend();

    let report = `Meeting Sentiment Analysis Report\n`;
    report += `Overall Sentiment: ${overall.dominant} (${Math.round(overall[overall.dominant] * 100)}%)\n`;
    report += `Engagement Level: ${engagement > 0.7 ? 'High' : engagement > 0.4 ? 'Medium' : 'Low'}\n`;
    
    if (trend.length > 0) {
      const recent = trend.slice(-3);
      const trendDirection = recent[recent.length - 1].score.positive - recent[0].score.positive;
      report += `Recent Trend: ${trendDirection > 0.1 ? 'Improving' : trendDirection < -0.1 ? 'Declining' : 'Stable'}\n`;
    }

    // Add recommendations
    if (overall.negative > 0.6) {
      report += `Recommendation: Consider addressing concerns or taking a break to improve meeting atmosphere.\n`;
    } else if (overall.positive > 0.7) {
      report += `Recommendation: Great positive atmosphere! Consider leveraging this momentum for important decisions.\n`;
    }

    return report;
  }
}