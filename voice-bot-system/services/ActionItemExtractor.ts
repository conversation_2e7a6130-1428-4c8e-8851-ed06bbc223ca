// Action Item Extraction Service

import { ActionItem, TranscriptEntry } from '../types';
import { NLPService } from './NLPService';

export class ActionItemExtractor {
  private nlpService: NLPService;
  private actionItemPatterns: RegExp[] = [
    /\b(will|shall|should|need to|must|have to|going to|plan to)\s+[\w\s]+/gi,
    /\b(action|task|todo|assignment|responsibility)\s*:\s*[\w\s]+/gi,
    /\b(assigned to|responsible for|owner|lead)\s+[\w\s]+/gi,
    /\b(by|before|due|deadline|complete by)\s+[\w\s]+/gi,
    /\b(follow up|follow-up|next step|next steps)\s*:?\s*[\w\s]+/gi
  ];

  constructor(nlpService: NLPService) {
    this.nlpService = nlpService;
  }

  public async extractFromTranscript(transcript: TranscriptEntry[]): Promise<ActionItem[]> {
    const actionItems: ActionItem[] = [];

    for (const entry of transcript) {
      const item = await this.checkForActionItem(entry.text);
      if (item) {
        actionItems.push(item);
      }
    }

    // Deduplicate similar action items
    return this.deduplicateActionItems(actionItems);
  }

  public async checkForActionItem(text: string): Promise<ActionItem | null> {
    // First check with patterns
    const patternMatch = this.checkPatterns(text);
    if (patternMatch) {
      return patternMatch;
    }

    // Use NLP service for more sophisticated extraction
    return await this.nlpService.extractActionItem(text);
  }

  private checkPatterns(text: string): ActionItem | null {
    // Check for common action item patterns
    const actionIndicators = [
      'will', 'shall', 'should', 'need to', 'must', 'have to',
      'going to', 'plan to', 'action', 'task', 'todo', 'assignment',
      'follow up', 'next step'
    ];

    const lowerText = text.toLowerCase();
    const hasActionIndicator = actionIndicators.some(indicator => 
      lowerText.includes(indicator)
    );

    if (!hasActionIndicator) {
      return null;
    }

    // Extract components
    const description = this.extractDescription(text);
    const assignee = this.extractAssignee(text);
    const dueDate = this.extractDueDate(text);
    const priority = this.determinePriority(text);

    if (description) {
      return {
        id: this.generateActionItemId(),
        description,
        assignee,
        dueDate,
        priority,
        status: 'pending',
        extractedFrom: text,
        confidence: 0.8
      };
    }

    return null;
  }

  private extractDescription(text: string): string | null {
    // Patterns to extract action descriptions
    const patterns = [
      /(?:will|shall|should|need to|must|have to|going to|plan to)\s+([^.!?]+)/gi,
      /(?:action|task|todo|assignment):\s*([^.!?]+)/gi,
      /(?:follow up|follow-up|next step|next steps):\s*([^.!?]+)/gi
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        return match[1].trim();
      }
    }

    return null;
  }

  private extractAssignee(text: string): string | null {
    // Patterns to extract assignee
    const patterns = [
      /(?:assigned to|responsible for|owner|lead)\s+([A-Za-z\s]+)/gi,
      /([A-Za-z\s]+)\s+(?:will|shall|should|need to|must|have to)/gi
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        const assignee = match[1].trim();
        // Filter out common non-names
        if (!this.isCommonWord(assignee)) {
          return assignee;
        }
      }
    }

    return null;
  }

  private extractDueDate(text: string): Date | null {
    // Patterns to extract due dates
    const datePatterns = [
      /(?:by|before|due|deadline|complete by)\s+([A-Za-z0-9\s,]+)/gi,
      /(\d{1,2}\/\d{1,2}\/\d{2,4})/g,
      /(\d{1,2}-\d{1,2}-\d{2,4})/g,
      /(monday|tuesday|wednesday|thursday|friday|saturday|sunday)/gi,
      /(today|tomorrow|next week|next month)/gi
    ];

    for (const pattern of datePatterns) {
      const match = text.match(pattern);
      if (match) {
        const dateStr = match[1] || match[0];
        const date = this.parseDate(dateStr);
        if (date) {
          return date;
        }
      }
    }

    return null;
  }

  private parseDate(dateStr: string): Date | null {
    const lower = dateStr.toLowerCase().trim();
    const today = new Date();

    // Handle relative dates
    switch (lower) {
      case 'today':
        return today;
      case 'tomorrow':
        return new Date(today.getTime() + 24 * 60 * 60 * 1000);
      case 'next week':
        return new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
      case 'next month':
        const nextMonth = new Date(today);
        nextMonth.setMonth(today.getMonth() + 1);
        return nextMonth;
    }

    // Handle day names
    const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
    const dayIndex = dayNames.indexOf(lower);
    if (dayIndex !== -1) {
      const currentDay = today.getDay();
      const daysUntil = (dayIndex - currentDay + 7) % 7;
      return new Date(today.getTime() + daysUntil * 24 * 60 * 60 * 1000);
    }

    // Try standard date parsing
    try {
      const parsed = new Date(dateStr);
      if (!isNaN(parsed.getTime())) {
        return parsed;
      }
    } catch (error) {
      // Ignore parsing errors
    }

    return null;
  }

  private determinePriority(text: string): ActionItem['priority'] {
    const lowerText = text.toLowerCase();

    // High priority indicators
    if (lowerText.includes('urgent') || lowerText.includes('asap') || 
        lowerText.includes('immediately') || lowerText.includes('critical') ||
        lowerText.includes('must') || lowerText.includes('deadline')) {
      return 'high';
    }

    // Low priority indicators
    if (lowerText.includes('when possible') || lowerText.includes('eventually') ||
        lowerText.includes('if time permits') || lowerText.includes('nice to have')) {
      return 'low';
    }

    // Default to medium
    return 'medium';
  }

  private isCommonWord(word: string): boolean {
    const commonWords = [
      'we', 'they', 'you', 'I', 'he', 'she', 'it', 'someone', 'everyone',
      'team', 'group', 'department', 'company', 'organization'
    ];
    return commonWords.includes(word.toLowerCase());
  }

  private deduplicateActionItems(actionItems: ActionItem[]): ActionItem[] {
    const deduplicated: ActionItem[] = [];
    const seen = new Set<string>();

    for (const item of actionItems) {
      const key = this.generateDeduplicationKey(item);
      if (!seen.has(key)) {
        seen.add(key);
        deduplicated.push(item);
      }
    }

    return deduplicated;
  }

  private generateDeduplicationKey(item: ActionItem): string {
    // Create a key based on description and assignee
    const description = item.description.toLowerCase().replace(/[^\w\s]/g, '');
    const assignee = item.assignee?.toLowerCase() || '';
    return `${description}-${assignee}`;
  }

  private generateActionItemId(): string {
    return `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Advanced extraction methods
  public async extractFromContext(
    transcript: TranscriptEntry[],
    context: {
      meetingType?: string;
      participants?: string[];
      previousActionItems?: ActionItem[];
    }
  ): Promise<ActionItem[]> {
    const actionItems: ActionItem[] = [];

    // Group transcript by speaker and time segments
    const segments = this.segmentTranscript(transcript);

    for (const segment of segments) {
      const items = await this.extractFromSegment(segment, context);
      actionItems.push(...items);
    }

    // Enhance with context
    return this.enhanceWithContext(actionItems, context);
  }

  private segmentTranscript(transcript: TranscriptEntry[]): TranscriptEntry[][] {
    const segments: TranscriptEntry[][] = [];
    let currentSegment: TranscriptEntry[] = [];

    for (const entry of transcript) {
      currentSegment.push(entry);

      // Create new segment on speaker change or time gap
      if (currentSegment.length > 1) {
        const prevEntry = currentSegment[currentSegment.length - 2];
        const timeDiff = entry.timestamp.getTime() - prevEntry.timestamp.getTime();
        
        if (prevEntry.speaker !== entry.speaker || timeDiff > 30000) { // 30 seconds
          segments.push([...currentSegment]);
          currentSegment = [entry];
        }
      }
    }

    if (currentSegment.length > 0) {
      segments.push(currentSegment);
    }

    return segments;
  }

  private async extractFromSegment(
    segment: TranscriptEntry[],
    context: any
  ): Promise<ActionItem[]> {
    const combinedText = segment.map(entry => entry.text).join(' ');
    const speaker = segment[0].speaker;

    const actionItem = await this.checkForActionItem(combinedText);
    if (actionItem) {
      // Enhance with segment context
      actionItem.assignee = actionItem.assignee || speaker;
      return [actionItem];
    }

    return [];
  }

  private enhanceWithContext(
    actionItems: ActionItem[],
    context: any
  ): ActionItem[] {
    return actionItems.map(item => {
      // Enhance assignee with participant validation
      if (item.assignee && context.participants) {
        const matchedParticipant = context.participants.find(
          (p: string) => p.toLowerCase().includes(item.assignee!.toLowerCase())
        );
        if (matchedParticipant) {
          item.assignee = matchedParticipant;
        }
      }

      // Enhance priority based on meeting type
      if (context.meetingType === 'kick-off' && item.priority === 'medium') {
        item.priority = 'high';
      }

      return item;
    });
  }

  public generateActionItemSummary(actionItems: ActionItem[]): string {
    if (actionItems.length === 0) {
      return 'No action items identified in the meeting.';
    }

    const byPriority = {
      high: actionItems.filter(item => item.priority === 'high'),
      medium: actionItems.filter(item => item.priority === 'medium'),
      low: actionItems.filter(item => item.priority === 'low')
    };

    const byAssignee = actionItems.reduce((acc, item) => {
      const assignee = item.assignee || 'Unassigned';
      acc[assignee] = (acc[assignee] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    let summary = `Action Items Summary (${actionItems.length} total):\n\n`;
    
    if (byPriority.high.length > 0) {
      summary += `High Priority (${byPriority.high.length}):\n`;
      byPriority.high.forEach(item => {
        summary += `- ${item.description} (${item.assignee || 'TBD'})\n`;
      });
      summary += '\n';
    }

    if (byPriority.medium.length > 0) {
      summary += `Medium Priority (${byPriority.medium.length}):\n`;
      byPriority.medium.forEach(item => {
        summary += `- ${item.description} (${item.assignee || 'TBD'})\n`;
      });
      summary += '\n';
    }

    if (byPriority.low.length > 0) {
      summary += `Low Priority (${byPriority.low.length}):\n`;
      byPriority.low.forEach(item => {
        summary += `- ${item.description} (${item.assignee || 'TBD'})\n`;
      });
      summary += '\n';
    }

    summary += `Distribution by Assignee:\n`;
    Object.entries(byAssignee).forEach(([assignee, count]) => {
      summary += `- ${assignee}: ${count} items\n`;
    });

    return summary;
  }
}