// Real-time Transcription Service

import { EventEmitter } from 'events';
import { TranscriptEntry } from '../types';

export class TranscriptionService extends EventEmitter {
  private credentials: any;
  private isTranscribing: boolean = false;
  private transcriptionClient?: any;
  private audioStream?: MediaStream;
  private recognitionStream?: any;

  constructor(credentials: any) {
    super();
    this.credentials = credentials;
  }

  public async startTranscription(): Promise<void> {
    if (this.isTranscribing) return;

    try {
      // Get microphone access
      this.audioStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        }
      });

      // Initialize transcription based on available service
      if (this.credentials.google) {
        await this.startGoogleTranscription();
      } else if (this.credentials.azure) {
        await this.startAzureTranscription();
      } else {
        await this.startWebSpeechTranscription();
      }

      this.isTranscribing = true;
      this.emit('transcription-started');
    } catch (error) {
      this.emit('error', { type: 'transcription-start', error });
      throw error;
    }
  }

  private async startGoogleTranscription(): Promise<void> {
    const speech = await import('@google-cloud/speech');
    
    this.transcriptionClient = new speech.SpeechClient({
      keyFilename: this.credentials.google.keyFile,
      projectId: this.credentials.google.projectId
    });

    const request = {
      config: {
        encoding: 'LINEAR16',
        sampleRateHertz: 16000,
        languageCode: 'en-US',
        enableAutomaticPunctuation: true,
        enableSpeakerDiarization: true,
        diarizationSpeakerCount: 10,
        model: 'latest_long',
        useEnhanced: true
      },
      interimResults: true
    };

    this.recognitionStream = this.transcriptionClient
      .streamingRecognize(request)
      .on('error', (error: any) => {
        this.emit('error', { type: 'google-transcription', error });
      })
      .on('data', (data: any) => {
        this.processGoogleTranscription(data);
      });

    // Convert audio stream to format expected by Google
    this.pipeAudioToGoogle();
  }

  private async startAzureTranscription(): Promise<void> {
    const sdk = await import('@azure/cognitiveservices-speech-sdk');
    
    const speechConfig = sdk.SpeechConfig.fromSubscription(
      this.credentials.azure.subscriptionKey,
      this.credentials.azure.region
    );

    speechConfig.speechRecognitionLanguage = 'en-US';
    speechConfig.enableDictation();

    const audioConfig = sdk.AudioConfig.fromDefaultMicrophoneInput();
    this.transcriptionClient = new sdk.SpeechRecognizer(speechConfig, audioConfig);

    // Set up event handlers
    this.transcriptionClient.recognizing = (s: any, e: any) => {
      this.processAzureTranscription(e, false);
    };

    this.transcriptionClient.recognized = (s: any, e: any) => {
      this.processAzureTranscription(e, true);
    };

    this.transcriptionClient.canceled = (s: any, e: any) => {
      this.emit('error', { type: 'azure-transcription-canceled', error: e });
    };

    await this.transcriptionClient.startContinuousRecognitionAsync();
  }

  private async startWebSpeechTranscription(): Promise<void> {
    if (!('webkitSpeechRecognition' in window)) {
      throw new Error('Web Speech API not supported');
    }

    const SpeechRecognition = (window as any).webkitSpeechRecognition;
    this.transcriptionClient = new SpeechRecognition();

    this.transcriptionClient.continuous = true;
    this.transcriptionClient.interimResults = true;
    this.transcriptionClient.lang = 'en-US';

    this.transcriptionClient.onresult = (event: any) => {
      this.processWebSpeechTranscription(event);
    };

    this.transcriptionClient.onerror = (event: any) => {
      this.emit('error', { type: 'web-speech-error', error: event.error });
    };

    this.transcriptionClient.onend = () => {
      if (this.isTranscribing) {
        // Restart recognition
        this.transcriptionClient.start();
      }
    };

    this.transcriptionClient.start();
  }

  private processGoogleTranscription(data: any): void {
    if (data.results[0] && data.results[0].alternatives[0]) {
      const result = data.results[0];
      const transcript = result.alternatives[0].transcript;
      const confidence = result.alternatives[0].confidence || 0.9;

      // Speaker diarization
      const speakers = this.extractSpeakers(result);
      const speaker = speakers.length > 0 ? speakers[0] : 'Unknown';

      const entry: TranscriptEntry = {
        timestamp: new Date(),
        speaker,
        text: transcript,
        confidence,
        keywords: this.extractKeywords(transcript)
      };

      if (result.isFinal) {
        this.emit('transcript-ready', entry);
      } else {
        this.emit('transcript-interim', entry);
      }
    }
  }

  private processAzureTranscription(event: any, isFinal: boolean): void {
    if (event.result && event.result.text) {
      const entry: TranscriptEntry = {
        timestamp: new Date(),
        speaker: 'Unknown', // Azure doesn't provide speaker info in basic tier
        text: event.result.text,
        confidence: event.result.confidence || 0.9,
        keywords: this.extractKeywords(event.result.text)
      };

      if (isFinal) {
        this.emit('transcript-ready', entry);
      } else {
        this.emit('transcript-interim', entry);
      }
    }
  }

  private processWebSpeechTranscription(event: any): void {
    const last = event.results.length - 1;
    const result = event.results[last];
    const transcript = result[0].transcript;
    const confidence = result[0].confidence || 0.8;

    const entry: TranscriptEntry = {
      timestamp: new Date(),
      speaker: 'Unknown',
      text: transcript,
      confidence,
      keywords: this.extractKeywords(transcript)
    };

    if (result.isFinal) {
      this.emit('transcript-ready', entry);
    } else {
      this.emit('transcript-interim', entry);
    }
  }

  private extractSpeakers(result: any): string[] {
    if (result.alternatives[0].words && result.alternatives[0].words.length > 0) {
      const speakers = result.alternatives[0].words
        .filter((word: any) => word.speakerTag)
        .map((word: any) => `Speaker ${word.speakerTag}`);
      
      return [...new Set(speakers)];
    }
    return [];
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction - in production, use NLP service
    const commonWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'shall', 'must', 'a', 'an', 'this', 'that', 'these', 'those'];
    
    return text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.includes(word))
      .slice(0, 5);
  }

  private pipeAudioToGoogle(): void {
    if (!this.audioStream || !this.recognitionStream) return;

    // Create audio context for processing
    const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    const source = audioContext.createMediaStreamSource(this.audioStream);
    
    // Create script processor for audio data
    const processor = audioContext.createScriptProcessor(4096, 1, 1);
    
    processor.onaudioprocess = (event) => {
      const inputData = event.inputBuffer.getChannelData(0);
      
      // Convert float32 to int16 for Google
      const int16Array = new Int16Array(inputData.length);
      for (let i = 0; i < inputData.length; i++) {
        int16Array[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
      }
      
      // Send to Google
      if (this.recognitionStream) {
        this.recognitionStream.write(int16Array);
      }
    };

    source.connect(processor);
    processor.connect(audioContext.destination);
  }

  public async stopTranscription(): Promise<void> {
    if (!this.isTranscribing) return;

    this.isTranscribing = false;

    try {
      if (this.recognitionStream) {
        this.recognitionStream.destroy();
      }

      if (this.transcriptionClient) {
        if (this.credentials.azure) {
          await this.transcriptionClient.stopContinuousRecognitionAsync();
        } else if (this.credentials.google) {
          // Google client cleanup
        } else {
          // Web Speech cleanup
          this.transcriptionClient.stop();
        }
      }

      if (this.audioStream) {
        this.audioStream.getTracks().forEach(track => track.stop());
      }

      this.emit('transcription-stopped');
    } catch (error) {
      this.emit('error', { type: 'transcription-stop', error });
      throw error;
    }
  }

  public isActive(): boolean {
    return this.isTranscribing;
  }

  // Advanced features
  public async identifySpeaker(audioSegment: ArrayBuffer): Promise<string> {
    // In production, use speaker identification service
    return 'Unknown';
  }

  public async detectLanguage(text: string): Promise<string> {
    // Simple language detection
    const commonEnglishWords = ['the', 'and', 'is', 'to', 'of', 'in', 'it', 'you', 'that', 'he'];
    const words = text.toLowerCase().split(/\s+/);
    const englishWordCount = words.filter(word => commonEnglishWords.includes(word)).length;
    
    return (englishWordCount / words.length) > 0.1 ? 'en' : 'unknown';
  }

  public async correctTranscription(text: string): Promise<string> {
    // Simple text correction - in production, use advanced NLP
    return text
      .replace(/\bur\b/g, 'your')
      .replace(/\bu\b/g, 'you')
      .replace(/\bteh\b/g, 'the')
      .replace(/\bwith\b/g, 'with');
  }
}