// Meeting Platform Integration Service

import { EventEmitter } from 'events';
import { MeetingPlatform, Participant, PlatformCredentials } from '../types';

export class MeetingPlatformService extends EventEmitter {
  private platform: MeetingPlatform;
  private credentials: PlatformCredentials;
  private connection?: any;
  private meetingId?: string;
  private audioStream?: MediaStream;

  constructor(platform: MeetingPlatform, credentials: PlatformCredentials) {
    super();
    this.platform = platform;
    this.credentials = credentials;
  }

  public async joinMeeting(meetingId: string): Promise<void> {
    this.meetingId = meetingId;

    switch (this.platform.type) {
      case 'teams':
        await this.joinTeamsMeeting(meetingId);
        break;
      case 'zoom':
        await this.joinZoomMeeting(meetingId);
        break;
      case 'meet':
        await this.joinGoogleMeet(meetingId);
        break;
      case 'webrtc':
        await this.joinWebRTCMeeting(meetingId);
        break;
    }

    this.emit('connected', { meetingId, platform: this.platform.type });
  }

  private async joinTeamsMeeting(meetingId: string): Promise<void> {
    try {
      // Microsoft Teams SDK integration
      const { app, meeting, LiveShareClient } = await import('@microsoft/teams-js');
      
      // Initialize Teams SDK
      await app.initialize();

      // Get meeting context
      const context = await app.getContext();
      
      if (context.meeting?.id !== meetingId) {
        throw new Error('Invalid meeting ID');
      }

      // Request permissions
      await meeting.requestAppAudioHandling(true);

      // Join as bot/app
      const joinUrl = await this.generateTeamsJoinUrl(meetingId);
      
      // Initialize Graph API client for advanced features
      const graphClient = await this.initializeGraphClient();
      
      // Join meeting
      const meetingInfo = await graphClient
        .api(`/me/onlineMeetings/${meetingId}`)
        .get();

      // Set up audio handling
      this.setupTeamsAudioHandling();

      // Monitor participants
      this.monitorTeamsParticipants(meetingId);

      this.connection = { graphClient, meetingInfo };
    } catch (error) {
      console.error('Failed to join Teams meeting:', error);
      throw error;
    }
  }

  private async joinZoomMeeting(meetingId: string): Promise<void> {
    try {
      // Zoom SDK integration
      const { ZoomMtgEmbedded } = await import('@zoomus/websdk/embedded');
      
      const client = ZoomMtgEmbedded.createClient();
      
      // Generate signature for SDK
      const signature = await this.generateZoomSignature(meetingId);

      await client.init({
        zoomAppRoot: document.getElementById('zoom-app-root'),
        language: 'en-US',
        customize: {
          video: {
            isResizable: false,
            viewSizes: {
              default: { width: 0, height: 0 },
              ribbon: { width: 0, height: 0 }
            }
          },
          chat: { popper: { placement: 'top' } }
        }
      });

      await client.join({
        signature,
        sdkKey: this.credentials.zoom?.sdkKey,
        meetingNumber: meetingId,
        password: '', // Would be provided
        userName: this.platform.type,
        userEmail: '<EMAIL>',
        tk: '',
        zak: ''
      });

      // Request audio permission
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      this.audioStream = stream;

      // Set up event handlers
      this.setupZoomEventHandlers(client);

      this.connection = client;
    } catch (error) {
      console.error('Failed to join Zoom meeting:', error);
      throw error;
    }
  }

  private async joinGoogleMeet(meetingId: string): Promise<void> {
    try {
      // Google Meet API integration
      // Note: Google Meet doesn't have a direct bot API, so we use browser automation
      
      // Use Google Calendar API to get meeting details
      const { google } = await import('googleapis');
      
      const auth = new google.auth.OAuth2(
        this.credentials.googleMeet?.clientId,
        this.credentials.googleMeet?.clientSecret
      );

      auth.setCredentials({
        access_token: await this.getGoogleAccessToken()
      });

      const calendar = google.calendar({ version: 'v3', auth });
      
      // Get meeting details from calendar
      const event = await calendar.events.get({
        calendarId: 'primary',
        eventId: meetingId
      });

      if (event.data.conferenceData?.entryPoints) {
        const meetUrl = event.data.conferenceData.entryPoints.find(
          ep => ep.entryPointType === 'video'
        )?.uri;

        if (meetUrl) {
          // Use Puppeteer or similar to join as bot
          await this.joinMeetViaAutomation(meetUrl);
        }
      }
    } catch (error) {
      console.error('Failed to join Google Meet:', error);
      throw error;
    }
  }

  private async joinWebRTCMeeting(meetingId: string): Promise<void> {
    try {
      // Generic WebRTC implementation
      const configuration = {
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' },
          { urls: 'stun:stun1.l.google.com:19302' }
        ]
      };

      const pc = new RTCPeerConnection(configuration);

      // Set up local audio stream
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        } 
      });

      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });

      this.audioStream = stream;

      // Set up signaling (WebSocket connection to server)
      const ws = new WebSocket(`wss://meeting-server.com/join/${meetingId}`);
      
      ws.onmessage = async (event) => {
        const message = JSON.parse(event.data);
        
        switch (message.type) {
          case 'offer':
            await pc.setRemoteDescription(message.offer);
            const answer = await pc.createAnswer();
            await pc.setLocalDescription(answer);
            ws.send(JSON.stringify({ type: 'answer', answer }));
            break;
          case 'ice-candidate':
            await pc.addIceCandidate(message.candidate);
            break;
          case 'participant-joined':
            this.handleParticipantJoined(message.participant);
            break;
          case 'participant-left':
            this.handleParticipantLeft(message.participant);
            break;
        }
      };

      pc.onicecandidate = (event) => {
        if (event.candidate) {
          ws.send(JSON.stringify({
            type: 'ice-candidate',
            candidate: event.candidate
          }));
        }
      };

      pc.ontrack = (event) => {
        // Handle incoming audio streams from other participants
        this.emit('remote-stream', event.streams[0]);
      };

      this.connection = { pc, ws };
    } catch (error) {
      console.error('Failed to join WebRTC meeting:', error);
      throw error;
    }
  }

  public async playAudio(audioUrl: string): Promise<void> {
    try {
      // Create audio element
      const audio = new Audio(audioUrl);
      
      // Get audio context for processing
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const source = audioContext.createMediaElementSource(audio);
      
      // Apply any audio processing (e.g., gain control)
      const gainNode = audioContext.createGain();
      gainNode.gain.value = 1.0;
      
      source.connect(gainNode);

      // Route to appropriate output based on platform
      switch (this.platform.type) {
        case 'teams':
          await this.playAudioInTeams(gainNode);
          break;
        case 'zoom':
          await this.playAudioInZoom(gainNode);
          break;
        case 'webrtc':
          await this.playAudioInWebRTC(gainNode);
          break;
        default:
          // Fallback to speakers
          gainNode.connect(audioContext.destination);
      }

      // Play the audio
      await audio.play();

      this.emit('audio-played', { url: audioUrl });
    } catch (error) {
      this.emit('error', { type: 'audio-playback', error });
      throw error;
    }
  }

  private async playAudioInTeams(audioNode: GainNode): Promise<void> {
    // Teams-specific audio routing
    if (this.connection?.graphClient) {
      // Use Teams audio API
      const { meeting } = await import('@microsoft/teams-js');
      
      // Get audio context from Teams
      const teamsAudioContext = await meeting.getAppAudioHandling();
      
      // Connect audio node to Teams output
      audioNode.connect(teamsAudioContext.destination);
    }
  }

  private async playAudioInZoom(audioNode: GainNode): Promise<void> {
    // Zoom-specific audio routing
    if (this.connection) {
      // Use Zoom SDK audio methods
      const audioTrack = audioNode.context.createMediaStreamDestination();
      audioNode.connect(audioTrack);
      
      // Send to Zoom
      await this.connection.stream.startAudio({
        stream: audioTrack.stream
      });
    }
  }

  private async playAudioInWebRTC(audioNode: GainNode): Promise<void> {
    // WebRTC audio routing
    if (this.connection?.pc && this.audioStream) {
      const audioTrack = audioNode.context.createMediaStreamDestination();
      audioNode.connect(audioTrack);
      
      // Replace audio track in peer connection
      const sender = this.connection.pc.getSenders().find(
        (s: RTCRtpSender) => s.track?.kind === 'audio'
      );
      
      if (sender && audioTrack.stream.getAudioTracks()[0]) {
        await sender.replaceTrack(audioTrack.stream.getAudioTracks()[0]);
      }
    }
  }

  public async leaveMeeting(): Promise<void> {
    try {
      switch (this.platform.type) {
        case 'teams':
          await this.leaveTeamsMeeting();
          break;
        case 'zoom':
          await this.leaveZoomMeeting();
          break;
        case 'meet':
          await this.leaveGoogleMeet();
          break;
        case 'webrtc':
          await this.leaveWebRTCMeeting();
          break;
      }

      // Clean up audio streams
      if (this.audioStream) {
        this.audioStream.getTracks().forEach(track => track.stop());
        this.audioStream = undefined;
      }

      this.emit('disconnected', { meetingId: this.meetingId });
      this.meetingId = undefined;
      this.connection = undefined;
    } catch (error) {
      this.emit('error', { type: 'leave-meeting', error });
      throw error;
    }
  }

  private async leaveTeamsMeeting(): Promise<void> {
    if (this.connection?.graphClient) {
      // Leave Teams meeting
      const { meeting } = await import('@microsoft/teams-js');
      await meeting.leave();
    }
  }

  private async leaveZoomMeeting(): Promise<void> {
    if (this.connection) {
      await this.connection.leave();
    }
  }

  private async leaveGoogleMeet(): Promise<void> {
    // Close browser automation if used
  }

  private async leaveWebRTCMeeting(): Promise<void> {
    if (this.connection) {
      this.connection.pc.close();
      this.connection.ws.close();
    }
  }

  // Helper methods
  private async generateTeamsJoinUrl(meetingId: string): Promise<string> {
    // Generate Teams meeting join URL
    return `https://teams.microsoft.com/l/meetup-join/${meetingId}`;
  }

  private async generateZoomSignature(meetingId: string): Promise<string> {
    // Generate Zoom SDK signature
    const crypto = await import('crypto');
    
    const timestamp = Math.round(new Date().getTime() / 1000) - 30;
    const msg = Buffer.from(
      this.credentials.zoom!.sdkKey + meetingId + timestamp + 0
    ).toString('base64');
    
    const hash = crypto
      .createHmac('sha256', this.credentials.zoom!.sdkSecret!)
      .update(msg)
      .digest('base64');
    
    return `${this.credentials.zoom!.sdkKey}.${meetingId}.${timestamp}.0.${hash}`;
  }

  private async initializeGraphClient(): Promise<any> {
    // Initialize Microsoft Graph client
    const { Client } = await import('@microsoft/microsoft-graph-client');
    
    return Client.initWithMiddleware({
      authProvider: {
        getAccessToken: async () => {
          // Get access token using client credentials
          return this.getTeamsAccessToken();
        }
      }
    });
  }

  private async getTeamsAccessToken(): Promise<string> {
    // Get Teams access token
    const response = await fetch(
      `https://login.microsoftonline.com/${this.credentials.teams!.tenantId}/oauth2/v2.0/token`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: new URLSearchParams({
          client_id: this.credentials.teams!.clientId,
          client_secret: this.credentials.teams!.clientSecret,
          scope: 'https://graph.microsoft.com/.default',
          grant_type: 'client_credentials'
        })
      }
    );

    const data = await response.json();
    return data.access_token;
  }

  private async getGoogleAccessToken(): Promise<string> {
    // Get Google access token
    // Implementation would depend on OAuth flow
    return '';
  }

  private setupTeamsAudioHandling(): void {
    // Set up Teams-specific audio handling
  }

  private monitorTeamsParticipants(meetingId: string): void {
    // Monitor Teams participants
    setInterval(async () => {
      if (this.connection?.graphClient) {
        try {
          const participants = await this.connection.graphClient
            .api(`/communications/calls/${meetingId}/participants`)
            .get();
          
          // Process participants
          participants.value.forEach((p: any) => {
            const participant: Participant = {
              id: p.id,
              name: p.info.identity.user.displayName,
              email: p.info.identity.user.id,
              role: p.info.role,
              joinTime: new Date(p.metadata.joinedDateTime),
              speakingTime: 0
            };
            
            this.emit('participant-updated', participant);
          });
        } catch (error) {
          console.error('Failed to fetch participants:', error);
        }
      }
    }, 5000);
  }

  private setupZoomEventHandlers(client: any): void {
    // Set up Zoom SDK event handlers
    client.on('user-added', (payload: any) => {
      const participant: Participant = {
        id: payload.userId,
        name: payload.displayName,
        role: payload.isHost ? 'host' : 'participant',
        joinTime: new Date(),
        speakingTime: 0
      };
      this.emit('participant-joined', participant);
    });

    client.on('user-removed', (payload: any) => {
      this.emit('participant-left', { id: payload.userId });
    });

    client.on('active-speaker', (payload: any) => {
      this.emit('active-speaker', payload.userId);
    });
  }

  private async joinMeetViaAutomation(meetUrl: string): Promise<void> {
    // Use Puppeteer or similar to join Google Meet
    // This is a simplified example
    const puppeteer = await import('puppeteer');
    
    const browser = await puppeteer.launch({ headless: false });
    const page = await browser.newPage();
    
    // Grant permissions
    const context = browser.defaultBrowserContext();
    await context.overridePermissions(meetUrl, ['microphone']);
    
    await page.goto(meetUrl);
    
    // Wait for and click join button
    await page.waitForSelector('[data-mdc-dialog-action="join"]');
    await page.click('[data-mdc-dialog-action="join"]');
    
    this.connection = { browser, page };
  }

  private handleParticipantJoined(data: any): void {
    const participant: Participant = {
      id: data.id || data.userId,
      name: data.name || data.displayName || 'Unknown',
      email: data.email,
      role: data.role || 'participant',
      joinTime: new Date(),
      speakingTime: 0
    };
    
    this.emit('participant-joined', participant);
  }

  private handleParticipantLeft(data: any): void {
    const participant: Partial<Participant> = {
      id: data.id || data.userId,
      leaveTime: new Date()
    };
    
    this.emit('participant-left', participant);
  }

  // Public getters
  public isConnected(): boolean {
    return !!this.connection;
  }

  public getCurrentMeetingId(): string | undefined {
    return this.meetingId;
  }

  public getPlatformType(): string {
    return this.platform.type;
  }
}