// Natural Language Processing Service

import OpenAI from 'openai';
import { VoiceCommand, CommandIntent, TranscriptEntry, ActionItem, Poll } from '../types';

interface NLPContext {
  tenderDetails?: any;
  meetingContext?: any;
  recentTranscript?: TranscriptEntry[];
}

interface MeetingSummaryInput {
  transcript: TranscriptEntry[];
  actionItems: ActionItem[];
  polls: Poll[];
  sentiment: any;
  context?: any;
}

export class NLPService {
  private openai: OpenAI;
  private model: string;

  constructor(credentials: { apiKey: string; model: string }) {
    this.openai = new OpenAI({ apiKey: credentials.apiKey });
    this.model = credentials.model || 'gpt-4-turbo-preview';
  }

  public async parseCommand(text: string): Promise<VoiceCommand | null> {
    try {
      const prompt = `
        Analyze the following voice command and extract the intent and parameters.
        
        Text: "${text}"
        
        Possible intents:
        - next-slide: Move to next slide
        - previous-slide: Move to previous slide
        - go-to-slide: Navigate to specific slide (needs slide number)
        - start-poll: Begin a poll (needs question)
        - end-poll: Close current poll
        - mute-all: Mute all participants
        - unmute-all: Unmute all participants
        - start-recording: Begin meeting recording
        - stop-recording: Stop meeting recording
        - show-participants: Display participant list
        - answer-question: Provide answer to a question
        - summarize-discussion: Summarize recent discussion
        - extract-action-items: Extract action items
        
        Return JSON with:
        {
          "intent": "intent-name",
          "parameters": { ... },
          "confidence": 0.0-1.0
        }
        
        Return null if no clear command intent is found.
      `;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: 'You are a meeting command parser. Return only valid JSON or null.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      if (result && result.intent) {
        return {
          intent: result.intent as CommandIntent,
          parameters: result.parameters || {},
          confidence: result.confidence || 0.8,
          rawText: text,
          timestamp: new Date()
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to parse command:', error);
      return null;
    }
  }

  public async generateResponse(question: string, context: NLPContext): Promise<string> {
    try {
      const systemPrompt = `
        You are an AI meeting assistant helping with a tender kick-off meeting.
        You have access to:
        - Tender details: ${JSON.stringify(context.tenderDetails || {})}
        - Current meeting context: ${JSON.stringify(context.meetingContext || {})}
        - Recent discussion transcript
        
        Provide helpful, concise, and professional responses.
        Focus on the tender requirements and meeting objectives.
        Be specific and actionable in your answers.
      `;

      const recentDiscussion = context.recentTranscript
        ?.map(entry => `${entry.speaker}: ${entry.text}`)
        .join('\n') || 'No recent discussion';

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: `Recent discussion:\n${recentDiscussion}\n\nQuestion: ${question}` }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      return response.choices[0].message.content || 'I apologize, but I couldn\'t generate a response.';
    } catch (error) {
      console.error('Failed to generate response:', error);
      throw error;
    }
  }

  public async extractKeywords(text: string): Promise<string[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { 
            role: 'system', 
            content: 'Extract key business terms, technical terms, and important concepts from the text. Return as a JSON array of strings.' 
          },
          { role: 'user', content: text }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{"keywords":[]}');
      return result.keywords || [];
    } catch (error) {
      console.error('Failed to extract keywords:', error);
      return [];
    }
  }

  public async generateMeetingSummary(input: MeetingSummaryInput): Promise<string> {
    try {
      const transcriptSummary = this.summarizeTranscript(input.transcript);
      const sentimentSummary = this.summarizeSentiment(input.sentiment);
      const pollsSummary = this.summarizePolls(input.polls);

      const prompt = `
        Generate a comprehensive meeting summary for a tender kick-off meeting.
        
        Meeting Context: ${JSON.stringify(input.context || {})}
        
        Transcript Summary: ${transcriptSummary}
        
        Sentiment Analysis: ${sentimentSummary}
        
        Polls Conducted: ${pollsSummary}
        
        Action Items Identified: ${input.actionItems.map(item => 
          `- ${item.description} (Assigned to: ${item.assignee || 'TBD'}, Due: ${item.dueDate || 'TBD'})`
        ).join('\n')}
        
        Please provide:
        1. Executive Summary (2-3 sentences)
        2. Key Discussion Points
        3. Decisions Made
        4. Action Items Summary
        5. Next Steps
        6. Risks or Concerns Raised
        
        Format the summary in a clear, professional manner suitable for distribution to stakeholders.
      `;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { 
            role: 'system', 
            content: 'You are a professional meeting summarizer. Create clear, actionable summaries.' 
          },
          { role: 'user', content: prompt }
        ],
        temperature: 0.2,
        max_tokens: 1500
      });

      return response.choices[0].message.content || 'Unable to generate summary.';
    } catch (error) {
      console.error('Failed to generate meeting summary:', error);
      throw error;
    }
  }

  public async classifyIntent(text: string): Promise<{
    category: 'question' | 'statement' | 'action' | 'decision';
    confidence: number;
  }> {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { 
            role: 'system', 
            content: 'Classify the intent of the text as: question, statement, action, or decision. Return JSON with category and confidence (0-1).' 
          },
          { role: 'user', content: text }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      return {
        category: result.category || 'statement',
        confidence: result.confidence || 0.5
      };
    } catch (error) {
      console.error('Failed to classify intent:', error);
      return { category: 'statement', confidence: 0.5 };
    }
  }

  public async generatePollQuestion(topic: string, context?: string): Promise<{
    question: string;
    type: 'yes-no' | 'multiple-choice';
    options?: string[];
  }> {
    try {
      const prompt = `
        Generate a relevant poll question about: ${topic}
        Context: ${context || 'General tender discussion'}
        
        Return JSON with:
        {
          "question": "Clear poll question",
          "type": "yes-no" or "multiple-choice",
          "options": ["option1", "option2", ...] (only for multiple-choice)
        }
        
        Make the question specific, actionable, and relevant to tender decisions.
      `;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: 'Generate clear, decision-oriented poll questions for business meetings.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        response_format: { type: 'json_object' }
      });

      return JSON.parse(response.choices[0].message.content || '{}');
    } catch (error) {
      console.error('Failed to generate poll question:', error);
      return {
        question: `Should we proceed with ${topic}?`,
        type: 'yes-no'
      };
    }
  }

  public async extractActionItem(text: string): Promise<ActionItem | null> {
    try {
      const prompt = `
        Analyze this text for potential action items:
        "${text}"
        
        Extract if present:
        - Action to be taken
        - Assigned person (if mentioned)
        - Due date (if mentioned)
        - Priority (high/medium/low based on context)
        
        Return JSON or null if no action item found:
        {
          "description": "Clear action description",
          "assignee": "Person name or null",
          "dueDate": "ISO date or null",
          "priority": "high/medium/low",
          "confidence": 0.0-1.0
        }
      `;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: 'Extract action items from meeting discussions. Be precise and only extract clear action items.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.1,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{}');
      
      if (result && result.description && result.confidence > 0.7) {
        return {
          id: `action_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          description: result.description,
          assignee: result.assignee,
          dueDate: result.dueDate ? new Date(result.dueDate) : undefined,
          priority: result.priority || 'medium',
          status: 'pending',
          extractedFrom: text,
          confidence: result.confidence
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to extract action item:', error);
      return null;
    }
  }

  public async generateSlideNarration(slide: {
    title?: string;
    content?: string;
    notes?: string;
  }): Promise<string> {
    try {
      const prompt = `
        Generate professional narration for this presentation slide:
        Title: ${slide.title || 'Untitled'}
        Content: ${slide.content || 'No content'}
        Notes: ${slide.notes || 'No notes'}
        
        Create a natural, conversational narration (2-3 sentences) that:
        - Introduces the slide topic
        - Highlights key points
        - Transitions smoothly
        
        Tone: Professional but engaging, suitable for a tender kick-off meeting.
      `;

      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { role: 'system', content: 'You are a professional presenter creating slide narrations.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.4,
        max_tokens: 200
      });

      return response.choices[0].message.content || `Now let's look at ${slide.title || 'this slide'}.`;
    } catch (error) {
      console.error('Failed to generate slide narration:', error);
      return `Moving on to ${slide.title || 'the next slide'}.`;
    }
  }

  // Helper methods
  private summarizeTranscript(transcript: TranscriptEntry[]): string {
    if (transcript.length === 0) return 'No transcript available.';

    const totalMinutes = Math.round(
      (transcript[transcript.length - 1].timestamp.getTime() - transcript[0].timestamp.getTime()) / 60000
    );

    const speakers = [...new Set(transcript.map(entry => entry.speaker))];
    const totalWords = transcript.reduce((sum, entry) => sum + entry.text.split(' ').length, 0);

    return `Meeting duration: ${totalMinutes} minutes, ${speakers.length} participants, ${totalWords} words spoken.`;
  }

  private summarizeSentiment(sentiment: any): string {
    if (!sentiment) return 'No sentiment data available.';

    const overall = sentiment.overall;
    const dominantMood = overall.dominant;
    const score = Math.round(overall[dominantMood] * 100);

    return `Overall meeting sentiment was ${dominantMood} (${score}% confidence).`;
  }

  private summarizePolls(polls: Poll[]): string {
    if (polls.length === 0) return 'No polls conducted.';

    return polls.map(poll => {
      const totalResponses = poll.responses.length;
      const summary = poll.results?.summary || 'Results pending';
      return `"${poll.question}" - ${totalResponses} responses. ${summary}`;
    }).join(' ');
  }

  // Advanced NLP features
  public async detectTopic(text: string): Promise<string[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { 
            role: 'system', 
            content: 'Identify the main topics discussed in this text. Return as JSON array of topic strings.' 
          },
          { role: 'user', content: text }
        ],
        temperature: 0.2,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{"topics":[]}');
      return result.topics || [];
    } catch (error) {
      console.error('Failed to detect topics:', error);
      return [];
    }
  }

  public async generateFollowUpQuestions(context: string): Promise<string[]> {
    try {
      const response = await this.openai.chat.completions.create({
        model: this.model,
        messages: [
          { 
            role: 'system', 
            content: 'Generate 3 relevant follow-up questions for a tender meeting based on the context. Return as JSON array.' 
          },
          { role: 'user', content: context }
        ],
        temperature: 0.5,
        response_format: { type: 'json_object' }
      });

      const result = JSON.parse(response.choices[0].message.content || '{"questions":[]}');
      return result.questions || [];
    } catch (error) {
      console.error('Failed to generate follow-up questions:', error);
      return [];
    }
  }
}