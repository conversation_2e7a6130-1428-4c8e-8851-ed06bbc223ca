// Polling and Voting Service

import { EventEmitter } from 'events';
import { Poll, PollResponse, PollResults } from '../types';

export class PollingService extends EventEmitter {
  private activePolls: Map<string, Poll> = new Map();
  private pollTimers: Map<string, NodeJS.Timeout> = new Map();

  public async createPoll(params: {
    question: string;
    type: Poll['type'];
    options?: string[];
    duration?: number;
  }): Promise<Poll> {
    const poll: Poll = {
      id: this.generatePollId(),
      question: params.question,
      type: params.type,
      options: params.options,
      responses: [],
      startTime: new Date()
    };

    this.activePolls.set(poll.id, poll);

    // Set auto-close timer if duration specified
    if (params.duration) {
      const timer = setTimeout(() => {
        this.endPoll(poll.id);
      }, params.duration);
      this.pollTimers.set(poll.id, timer);
    }

    this.emit('poll-created', poll);
    return poll;
  }

  public startCollecting(pollId: string): void {
    const poll = this.activePolls.get(pollId);
    if (!poll) {
      throw new Error(`Poll ${pollId} not found`);
    }

    this.emit('poll-started', { pollId });
  }

  public async addResponse(pollId: string, response: {
    participantId: string;
    response: string | number;
    voiceConfidence?: number;
  }): Promise<void> {
    const poll = this.activePolls.get(pollId);
    if (!poll) {
      throw new Error(`Poll ${pollId} not found`);
    }

    if (poll.endTime) {
      throw new Error('Poll has already ended');
    }

    // Validate response based on poll type
    this.validateResponse(poll, response.response);

    const pollResponse: PollResponse = {
      participantId: response.participantId,
      response: response.response,
      timestamp: new Date(),
      voiceConfidence: response.voiceConfidence
    };

    // Check if participant has already responded
    const existingIndex = poll.responses.findIndex(
      r => r.participantId === response.participantId
    );

    if (existingIndex >= 0) {
      // Update existing response
      poll.responses[existingIndex] = pollResponse;
    } else {
      // Add new response
      poll.responses.push(pollResponse);
    }

    this.emit('response-added', { pollId, response: pollResponse });

    // Calculate live results
    const results = this.calculateResults(poll);
    this.emit('results-updated', { pollId, results });
  }

  public async endPoll(pollId: string): Promise<PollResults | null> {
    const poll = this.activePolls.get(pollId);
    if (!poll) {
      return null;
    }

    poll.endTime = new Date();
    
    // Clear timer if exists
    const timer = this.pollTimers.get(pollId);
    if (timer) {
      clearTimeout(timer);
      this.pollTimers.delete(pollId);
    }

    // Calculate final results
    const results = this.calculateResults(poll);
    poll.results = results;

    this.emit('poll-ended', { pollId, results });
    return results;
  }

  public hasActivePoll(): boolean {
    return Array.from(this.activePolls.values()).some(poll => !poll.endTime);
  }

  public getActivePoll(): Poll | undefined {
    return Array.from(this.activePolls.values()).find(poll => !poll.endTime);
  }

  public getPoll(pollId: string): Poll | undefined {
    return this.activePolls.get(pollId);
  }

  public generateSummary(results: PollResults): string {
    const total = results.totalResponses;
    
    if (total === 0) {
      return 'No responses received.';
    }

    const topResponse = Object.entries(results.distribution)
      .sort(([, a], [, b]) => b - a)[0];

    if (topResponse) {
      const percentage = Math.round((topResponse[1] / total) * 100);
      return `${total} responses received. ${percentage}% chose "${topResponse[0]}".`;
    }

    return `${total} responses received.`;
  }

  private validateResponse(poll: Poll, response: string | number): void {
    switch (poll.type) {
      case 'yes-no':
        if (typeof response !== 'string' || !['yes', 'no'].includes(response.toLowerCase())) {
          throw new Error('Response must be "yes" or "no"');
        }
        break;
      
      case 'multiple-choice':
        if (!poll.options || !poll.options.includes(response as string)) {
          throw new Error('Response must be one of the provided options');
        }
        break;
      
      case 'scale':
        if (typeof response !== 'number' || response < 1 || response > 10) {
          throw new Error('Response must be a number between 1 and 10');
        }
        break;
      
      case 'open-ended':
        if (typeof response !== 'string' || response.trim().length === 0) {
          throw new Error('Response cannot be empty');
        }
        break;
    }
  }

  private calculateResults(poll: Poll): PollResults {
    const distribution: Record<string, number> = {};

    // Process based on poll type
    switch (poll.type) {
      case 'yes-no':
      case 'multiple-choice':
        poll.responses.forEach(response => {
          const key = response.response.toString().toLowerCase();
          distribution[key] = (distribution[key] || 0) + 1;
        });
        break;
      
      case 'scale':
        // Group scale responses
        poll.responses.forEach(response => {
          const value = response.response as number;
          const key = value.toString();
          distribution[key] = (distribution[key] || 0) + 1;
        });
        break;
      
      case 'open-ended':
        // For open-ended, we might categorize or just count
        distribution['responses'] = poll.responses.length;
        break;
    }

    const summary = this.generateDetailedSummary(poll, distribution);

    return {
      totalResponses: poll.responses.length,
      distribution,
      summary
    };
  }

  private generateDetailedSummary(poll: Poll, distribution: Record<string, number>): string {
    const total = poll.responses.length;
    
    if (total === 0) {
      return 'No responses received for this poll.';
    }

    switch (poll.type) {
      case 'yes-no': {
        const yesCount = distribution['yes'] || 0;
        const noCount = distribution['no'] || 0;
        const yesPercentage = Math.round((yesCount / total) * 100);
        const noPercentage = Math.round((noCount / total) * 100);
        
        if (yesPercentage > noPercentage) {
          return `The majority (${yesPercentage}%) responded "Yes" to "${poll.question}"`;
        } else if (noPercentage > yesPercentage) {
          return `The majority (${noPercentage}%) responded "No" to "${poll.question}"`;
        } else {
          return `The responses were evenly split (50/50) for "${poll.question}"`;
        }
      }
      
      case 'multiple-choice': {
        const sorted = Object.entries(distribution)
          .sort(([, a], [, b]) => b - a);
        
        if (sorted.length > 0) {
          const [winner, count] = sorted[0];
          const percentage = Math.round((count / total) * 100);
          return `${percentage}% selected "${winner}" as their choice`;
        }
        break;
      }
      
      case 'scale': {
        const values = poll.responses.map(r => r.response as number);
        const average = values.reduce((sum, val) => sum + val, 0) / values.length;
        const rounded = Math.round(average * 10) / 10;
        
        return `Average rating: ${rounded}/10 based on ${total} responses`;
      }
      
      case 'open-ended': {
        return `Received ${total} text responses. Review transcript for details.`;
      }
    }

    return `Poll completed with ${total} responses.`;
  }

  private generatePollId(): string {
    return `poll_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Voice-specific methods
  public async processVoiceResponse(pollId: string, voiceText: string, participantId: string): Promise<void> {
    const poll = this.activePolls.get(pollId);
    if (!poll) {
      throw new Error(`Poll ${pollId} not found`);
    }

    // Parse voice response based on poll type
    let parsedResponse: string | number;
    let confidence = 0.8; // Default confidence

    switch (poll.type) {
      case 'yes-no':
        parsedResponse = this.parseYesNo(voiceText);
        break;
      
      case 'multiple-choice':
        const result = this.parseMultipleChoice(voiceText, poll.options || []);
        parsedResponse = result.option;
        confidence = result.confidence;
        break;
      
      case 'scale':
        parsedResponse = this.parseScale(voiceText);
        break;
      
      case 'open-ended':
        parsedResponse = voiceText;
        confidence = 1.0;
        break;
      
      default:
        throw new Error('Unknown poll type');
    }

    await this.addResponse(pollId, {
      participantId,
      response: parsedResponse,
      voiceConfidence: confidence
    });
  }

  private parseYesNo(text: string): string {
    const lower = text.toLowerCase();
    
    // Check for clear yes indicators
    if (lower.includes('yes') || lower.includes('yeah') || lower.includes('yep') || 
        lower.includes('agree') || lower.includes('correct') || lower.includes('affirmative')) {
      return 'yes';
    }
    
    // Check for clear no indicators
    if (lower.includes('no') || lower.includes('nope') || lower.includes('disagree') || 
        lower.includes('negative') || lower.includes('incorrect')) {
      return 'no';
    }
    
    // Default or ambiguous
    throw new Error('Could not determine yes/no response from voice input');
  }

  private parseMultipleChoice(text: string, options: string[]): { option: string; confidence: number } {
    const lower = text.toLowerCase();
    
    // Try exact match first
    for (const option of options) {
      if (lower.includes(option.toLowerCase())) {
        return { option, confidence: 0.9 };
      }
    }
    
    // Try fuzzy matching
    const words = lower.split(/\s+/);
    for (const option of options) {
      const optionWords = option.toLowerCase().split(/\s+/);
      const matches = optionWords.filter(word => words.includes(word));
      
      if (matches.length > 0) {
        const confidence = matches.length / optionWords.length;
        if (confidence > 0.5) {
          return { option, confidence };
        }
      }
    }
    
    throw new Error('Could not match voice input to any option');
  }

  private parseScale(text: string): number {
    // Extract numbers from text
    const numbers = text.match(/\d+/g);
    
    if (numbers && numbers.length > 0) {
      const value = parseInt(numbers[0]);
      if (value >= 1 && value <= 10) {
        return value;
      }
    }
    
    // Try word-based numbers
    const wordNumbers: Record<string, number> = {
      'one': 1, 'two': 2, 'three': 3, 'four': 4, 'five': 5,
      'six': 6, 'seven': 7, 'eight': 8, 'nine': 9, 'ten': 10
    };
    
    const lower = text.toLowerCase();
    for (const [word, value] of Object.entries(wordNumbers)) {
      if (lower.includes(word)) {
        return value;
      }
    }
    
    throw new Error('Could not extract scale value from voice input');
  }

  // Analytics methods
  public getResponseRate(pollId: string, totalParticipants: number): number {
    const poll = this.activePolls.get(pollId);
    if (!poll) return 0;
    
    return (poll.responses.length / totalParticipants) * 100;
  }

  public getResponseTime(pollId: string): { average: number; median: number } {
    const poll = this.activePolls.get(pollId);
    if (!poll || poll.responses.length === 0) {
      return { average: 0, median: 0 };
    }
    
    const responseTimes = poll.responses.map(r => 
      r.timestamp.getTime() - poll.startTime.getTime()
    ).sort((a, b) => a - b);
    
    const average = responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length;
    const median = responseTimes[Math.floor(responseTimes.length / 2)];
    
    return { 
      average: Math.round(average / 1000), // Convert to seconds
      median: Math.round(median / 1000)
    };
  }
}