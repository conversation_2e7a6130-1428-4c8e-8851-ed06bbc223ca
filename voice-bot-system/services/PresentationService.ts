// Presentation Control Service

import { EventEmitter } from 'events';
import { PresentationData, Slide } from '../types';

export class PresentationService extends EventEmitter {
  private currentPresentation?: PresentationData;
  private slideAdvanceTimer?: NodeJS.Timeout;

  public async loadPresentation(presentationData: PresentationData): Promise<void> {
    this.currentPresentation = presentationData;
    this.emit('presentation-loaded', presentationData);
  }

  public async controlSlide(
    action: 'next' | 'previous' | 'goto',
    slideNumber?: number
  ): Promise<void> {
    if (!this.currentPresentation) {
      throw new Error('No presentation loaded');
    }

    const presentation = this.currentPresentation;
    let newSlideNumber = presentation.currentSlide;

    switch (action) {
      case 'next':
        newSlideNumber = Math.min(
          presentation.currentSlide + 1,
          presentation.slides.length - 1
        );
        break;
      
      case 'previous':
        newSlideNumber = Math.max(presentation.currentSlide - 1, 0);
        break;
      
      case 'goto':
        if (slideNumber !== undefined) {
          newSlideNumber = Math.max(0, Math.min(slideNumber - 1, presentation.slides.length - 1));
        }
        break;
    }

    if (newSlideNumber !== presentation.currentSlide) {
      presentation.currentSlide = newSlideNumber;
      this.emit('slide-changed', {
        slideNumber: newSlideNumber + 1,
        slide: presentation.slides[newSlideNumber]
      });
    }
  }

  public getCurrentSlide(): Slide | undefined {
    if (!this.currentPresentation) return undefined;
    return this.currentPresentation.slides[this.currentPresentation.currentSlide];
  }

  public async startSlideshow(autoAdvance?: boolean, intervalMs?: number): Promise<void> {
    if (!this.currentPresentation) {
      throw new Error('No presentation loaded');
    }

    this.currentPresentation.shareStatus = 'sharing';
    this.emit('slideshow-started', this.currentPresentation);

    if (autoAdvance && intervalMs) {
      this.startAutoAdvance(intervalMs);
    }
  }

  public async pauseSlideshow(): Promise<void> {
    if (!this.currentPresentation) return;

    this.currentPresentation.shareStatus = 'paused';
    this.stopAutoAdvance();
    this.emit('slideshow-paused', this.currentPresentation);
  }

  public async stopSlideshow(): Promise<void> {
    if (!this.currentPresentation) return;

    this.currentPresentation.shareStatus = 'not-shared';
    this.stopAutoAdvance();
    this.emit('slideshow-stopped', this.currentPresentation);
  }

  private startAutoAdvance(intervalMs: number): void {
    this.stopAutoAdvance();
    
    this.slideAdvanceTimer = setInterval(() => {
      if (this.currentPresentation) {
        const isLastSlide = this.currentPresentation.currentSlide >= 
                           this.currentPresentation.slides.length - 1;
        
        if (!isLastSlide) {
          this.controlSlide('next');
        } else {
          this.stopAutoAdvance();
          this.emit('slideshow-completed', this.currentPresentation);
        }
      }
    }, intervalMs);
  }

  private stopAutoAdvance(): void {
    if (this.slideAdvanceTimer) {
      clearInterval(this.slideAdvanceTimer);
      this.slideAdvanceTimer = undefined;
    }
  }

  public async generateSlideNotes(slide: Slide): Promise<string> {
    if (slide.notes) {
      return slide.notes;
    }

    // Generate notes based on slide content
    let notes = '';
    
    if (slide.title) {
      notes += `This slide covers ${slide.title}. `;
    }

    if (slide.content) {
      const contentPoints = this.extractContentPoints(slide.content);
      if (contentPoints.length > 0) {
        notes += `Key points include: ${contentPoints.join(', ')}. `;
      }
    }

    if (slide.media) {
      notes += `This slide includes ${slide.media.type} content. `;
    }

    return notes || 'No specific notes for this slide.';
  }

  private extractContentPoints(content: string): string[] {
    // Extract bullet points or key phrases
    const lines = content.split('\n').filter(line => line.trim());
    const points: string[] = [];

    for (const line of lines) {
      // Remove bullet point markers
      const cleaned = line.replace(/^[-*•]\s*/, '').trim();
      if (cleaned && cleaned.length > 5) {
        points.push(cleaned);
      }
    }

    return points.slice(0, 5); // Limit to 5 key points
  }

  public async createSlideFromContent(content: {
    title?: string;
    bulletPoints?: string[];
    images?: string[];
    notes?: string;
  }): Promise<Slide> {
    const slideNumber = this.currentPresentation 
      ? this.currentPresentation.slides.length + 1 
      : 1;

    const slide: Slide = {
      number: slideNumber,
      title: content.title,
      content: content.bulletPoints?.join('\n') || '',
      notes: content.notes
    };

    if (content.images && content.images.length > 0) {
      slide.media = {
        type: 'image',
        url: content.images[0]
      };
    }

    return slide;
  }

  public async addSlideToPresentation(slide: Slide): Promise<void> {
    if (!this.currentPresentation) {
      throw new Error('No presentation loaded');
    }

    this.currentPresentation.slides.push(slide);
    this.emit('slide-added', slide);
  }

  public async updateSlideContent(
    slideNumber: number,
    updates: Partial<Slide>
  ): Promise<void> {
    if (!this.currentPresentation) {
      throw new Error('No presentation loaded');
    }

    const slideIndex = slideNumber - 1;
    if (slideIndex < 0 || slideIndex >= this.currentPresentation.slides.length) {
      throw new Error('Invalid slide number');
    }

    const slide = this.currentPresentation.slides[slideIndex];
    Object.assign(slide, updates);
    
    this.emit('slide-updated', { slideNumber, slide });
  }

  public getPresentationOverview(): {
    totalSlides: number;
    currentSlide: number;
    title: string;
    progress: number;
  } | null {
    if (!this.currentPresentation) return null;

    return {
      totalSlides: this.currentPresentation.slides.length,
      currentSlide: this.currentPresentation.currentSlide + 1,
      title: this.currentPresentation.title,
      progress: ((this.currentPresentation.currentSlide + 1) / this.currentPresentation.slides.length) * 100
    };
  }

  public async generatePresentationSummary(): Promise<string> {
    if (!this.currentPresentation) {
      return 'No presentation loaded.';
    }

    const presentation = this.currentPresentation;
    let summary = `Presentation Summary: ${presentation.title}\n\n`;
    
    summary += `Total slides: ${presentation.slides.length}\n`;
    summary += `Current slide: ${presentation.currentSlide + 1}\n`;
    summary += `Status: ${presentation.shareStatus}\n\n`;

    summary += 'Slide Overview:\n';
    presentation.slides.forEach((slide, index) => {
      summary += `${index + 1}. ${slide.title || 'Untitled Slide'}\n`;
      if (slide.content) {
        const firstLine = slide.content.split('\n')[0];
        summary += `   ${firstLine.substring(0, 50)}${firstLine.length > 50 ? '...' : ''}\n`;
      }
    });

    return summary;
  }

  // Integration with meeting platforms
  public async syncWithPlatform(platform: 'teams' | 'zoom' | 'meet'): Promise<void> {
    if (!this.currentPresentation) {
      throw new Error('No presentation loaded');
    }

    switch (platform) {
      case 'teams':
        await this.syncWithTeams();
        break;
      case 'zoom':
        await this.syncWithZoom();
        break;
      case 'meet':
        await this.syncWithGoogleMeet();
        break;
    }
  }

  private async syncWithTeams(): Promise<void> {
    // Teams screen sharing integration
    try {
      const { sharing } = await import('@microsoft/teams-js');
      
      if (this.currentPresentation?.shareStatus === 'sharing') {
        await sharing.shareAppContent({
          contentUrl: this.generateSlideUrl(),
          threadId: 'current-meeting'
        });
      }
    } catch (error) {
      console.error('Teams sync failed:', error);
    }
  }

  private async syncWithZoom(): Promise<void> {
    // Zoom screen sharing integration
    // This would typically involve the Zoom SDK
    console.log('Syncing with Zoom...');
  }

  private async syncWithGoogleMeet(): Promise<void> {
    // Google Meet screen sharing integration
    console.log('Syncing with Google Meet...');
  }

  private generateSlideUrl(): string {
    if (!this.currentPresentation) return '';
    
    const slideNumber = this.currentPresentation.currentSlide + 1;
    return `/presentation/${this.currentPresentation.id}/slide/${slideNumber}`;
  }

  // Animation and transition controls
  public async setSlideTransition(
    transitionType: 'none' | 'fade' | 'slide' | 'zoom',
    duration: number = 500
  ): Promise<void> {
    this.emit('transition-set', { transitionType, duration });
  }

  public async animateSlideElement(
    elementId: string,
    animation: 'fade-in' | 'slide-in' | 'zoom-in',
    delay: number = 0
  ): Promise<void> {
    this.emit('element-animate', { elementId, animation, delay });
  }

  // Accessibility features
  public async enableAccessibilityFeatures(): Promise<void> {
    this.emit('accessibility-enabled', {
      features: [
        'screen-reader-support',
        'high-contrast-mode',
        'large-text-mode',
        'keyboard-navigation'
      ]
    });
  }

  public async announceSlideChange(slide: Slide): Promise<string> {
    let announcement = '';
    
    if (slide.title) {
      announcement += `Now showing slide ${slide.number}: ${slide.title}. `;
    }

    if (slide.content) {
      const contentPoints = this.extractContentPoints(slide.content);
      if (contentPoints.length > 0) {
        announcement += `This slide contains ${contentPoints.length} main points. `;
      }
    }

    if (slide.media) {
      announcement += `This slide includes ${slide.media.type} content. `;
    }

    return announcement;
  }
}