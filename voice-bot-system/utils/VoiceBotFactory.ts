// Voice Bot Factory and Configuration

import { VoiceBot } from '../core/VoiceBot';
import { VoiceBotConfig, VoiceSettings, MeetingPlatform, BotFeatures, PlatformCredentials } from '../types';

export class VoiceBotFactory {
  private static instance: VoiceBotFactory;
  private botInstances: Map<string, VoiceBot> = new Map();

  public static getInstance(): VoiceBotFactory {
    if (!VoiceBotFactory.instance) {
      VoiceBotFactory.instance = new VoiceBotFactory();
    }
    return VoiceBotFactory.instance;
  }

  public createBot(config: VoiceBotConfig): VoiceBot {
    const botId = this.generateBotId(config.name);
    
    if (this.botInstances.has(botId)) {
      throw new Error(`Bot with name ${config.name} already exists`);
    }

    const bot = new VoiceBot(config);
    this.botInstances.set(botId, bot);

    // Set up cleanup when bot leaves meeting
    bot.on('meeting-left', () => {
      this.botInstances.delete(botId);
    });

    return bot;
  }

  public getBot(botName: string): VoiceBot | undefined {
    const botId = this.generateBotId(botName);
    return this.botInstances.get(botId);
  }

  public getAllBots(): VoiceBot[] {
    return Array.from(this.botInstances.values());
  }

  public async destroyBot(botName: string): Promise<void> {
    const botId = this.generateBotId(botName);
    const bot = this.botInstances.get(botId);
    
    if (bot) {
      if (bot.isInMeeting()) {
        await bot.leaveMeeting();
      }
      this.botInstances.delete(botId);
    }
  }

  private generateBotId(name: string): string {
    return name.toLowerCase().replace(/[^a-z0-9]/g, '_');
  }

  // Pre-configured bot templates
  public createTenderKickoffBot(credentials: PlatformCredentials): VoiceBot {
    const config: VoiceBotConfig = {
      name: 'TenderBot',
      voice: {
        language: 'en-US',
        voiceId: 'en-US-JennyNeural', // Azure voice
        speed: 1.0,
        pitch: 0,
        volume: 80,
        provider: 'azure'
      },
      meetingPlatform: {
        type: 'teams',
        autoJoin: true,
        coHostEnabled: true,
        recordingEnabled: true
      },
      features: {
        speechRecognition: true,
        naturalLanguageQA: true,
        presentationControl: true,
        pollingEnabled: true,
        sentimentAnalysis: true,
        actionItemExtraction: true,
        realTimeTranscription: true
      },
      credentials
    };

    return this.createBot(config);
  }

  public createBasicMeetingBot(
    platform: MeetingPlatform['type'],
    credentials: PlatformCredentials
  ): VoiceBot {
    const config: VoiceBotConfig = {
      name: 'MeetingAssistant',
      voice: {
        language: 'en-US',
        voiceId: 'en-US-AriaNeural',
        speed: 1.0,
        pitch: 0,
        volume: 75,
        provider: 'azure'
      },
      meetingPlatform: {
        type: platform,
        autoJoin: false,
        coHostEnabled: false,
        recordingEnabled: false
      },
      features: {
        speechRecognition: true,
        naturalLanguageQA: true,
        presentationControl: false,
        pollingEnabled: true,
        sentimentAnalysis: false,
        actionItemExtraction: true,
        realTimeTranscription: true
      },
      credentials
    };

    return this.createBot(config);
  }

  public createAdvancedPresentationBot(credentials: PlatformCredentials): VoiceBot {
    const config: VoiceBotConfig = {
      name: 'PresentationBot',
      voice: {
        language: 'en-US',
        voiceId: 'en-US-GuyNeural',
        speed: 0.9,
        pitch: 0,
        volume: 85,
        provider: 'azure'
      },
      meetingPlatform: {
        type: 'webrtc',
        autoJoin: true,
        coHostEnabled: true,
        recordingEnabled: true
      },
      features: {
        speechRecognition: true,
        naturalLanguageQA: true,
        presentationControl: true,
        pollingEnabled: true,
        sentimentAnalysis: true,
        actionItemExtraction: true,
        realTimeTranscription: true
      },
      credentials
    };

    return this.createBot(config);
  }
}

export class ConfigurationValidator {
  public static validateConfig(config: VoiceBotConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate basic configuration
    if (!config.name || config.name.trim().length === 0) {
      errors.push('Bot name is required');
    }

    if (!config.voice) {
      errors.push('Voice configuration is required');
    } else {
      errors.push(...this.validateVoiceSettings(config.voice));
    }

    if (!config.meetingPlatform) {
      errors.push('Meeting platform configuration is required');
    } else {
      errors.push(...this.validateMeetingPlatform(config.meetingPlatform));
    }

    if (!config.credentials) {
      errors.push('Credentials are required');
    } else {
      errors.push(...this.validateCredentials(config.credentials, config.voice.provider, config.meetingPlatform.type));
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private static validateVoiceSettings(voice: VoiceSettings): string[] {
    const errors: string[] = [];

    if (!voice.language) {
      errors.push('Voice language is required');
    }

    if (!voice.voiceId) {
      errors.push('Voice ID is required');
    }

    if (!['azure', 'google', 'amazon', 'elevenlabs'].includes(voice.provider)) {
      errors.push('Invalid voice provider');
    }

    if (voice.speed < 0.5 || voice.speed > 2.0) {
      errors.push('Voice speed must be between 0.5 and 2.0');
    }

    if (voice.volume < 0 || voice.volume > 100) {
      errors.push('Voice volume must be between 0 and 100');
    }

    return errors;
  }

  private static validateMeetingPlatform(platform: MeetingPlatform): string[] {
    const errors: string[] = [];

    if (!['teams', 'zoom', 'meet', 'webrtc'].includes(platform.type)) {
      errors.push('Invalid meeting platform type');
    }

    return errors;
  }

  private static validateCredentials(
    credentials: PlatformCredentials,
    voiceProvider: string,
    platformType: string
  ): string[] {
    const errors: string[] = [];

    // Validate OpenAI credentials (required for NLP)
    if (!credentials.openai?.apiKey) {
      errors.push('OpenAI API key is required');
    }

    // Validate speech service credentials
    if (!credentials.speechServices) {
      errors.push('Speech service credentials are required');
    } else {
      switch (voiceProvider) {
        case 'azure':
          if (!credentials.speechServices.azure?.subscriptionKey) {
            errors.push('Azure Speech subscription key is required');
          }
          if (!credentials.speechServices.azure?.region) {
            errors.push('Azure Speech region is required');
          }
          break;
        case 'google':
          if (!credentials.speechServices.google?.apiKey) {
            errors.push('Google Speech API key is required');
          }
          break;
      }
    }

    // Validate platform-specific credentials
    switch (platformType) {
      case 'teams':
        if (!credentials.teams?.tenantId || !credentials.teams?.clientId || !credentials.teams?.clientSecret) {
          errors.push('Complete Teams credentials (tenantId, clientId, clientSecret) are required');
        }
        break;
      case 'zoom':
        if (!credentials.zoom?.apiKey || !credentials.zoom?.apiSecret || !credentials.zoom?.sdkKey || !credentials.zoom?.sdkSecret) {
          errors.push('Complete Zoom credentials (apiKey, apiSecret, sdkKey, sdkSecret) are required');
        }
        break;
      case 'meet':
        if (!credentials.googleMeet?.clientId || !credentials.googleMeet?.clientSecret) {
          errors.push('Google Meet credentials (clientId, clientSecret) are required');
        }
        break;
    }

    return errors;
  }
}

export class VoiceBotMonitor {
  private static instance: VoiceBotMonitor;
  private monitoringInterval?: NodeJS.Timeout;

  public static getInstance(): VoiceBotMonitor {
    if (!VoiceBotMonitor.instance) {
      VoiceBotMonitor.instance = new VoiceBotMonitor();
    }
    return VoiceBotMonitor.instance;
  }

  public startMonitoring(intervalMs: number = 5000): void {
    if (this.monitoringInterval) {
      this.stopMonitoring();
    }

    this.monitoringInterval = setInterval(() => {
      this.checkBotHealth();
    }, intervalMs);
  }

  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = undefined;
    }
  }

  private checkBotHealth(): void {
    const factory = VoiceBotFactory.getInstance();
    const bots = factory.getAllBots();

    bots.forEach(bot => {
      try {
        const session = bot.getSession();
        if (session) {
          console.log(`Bot health check: ${bot.isInMeeting() ? 'Active' : 'Inactive'}`);
          
          // Check for potential issues
          if (bot.isInMeeting() && session.participants.length === 0) {
            console.warn('Bot is in meeting but no participants detected');
          }
        }
      } catch (error) {
        console.error('Bot health check failed:', error);
      }
    });
  }

  public getBotStatistics(): {
    totalBots: number;
    activeBots: number;
    totalMeetings: number;
    averageSessionDuration: number;
  } {
    const factory = VoiceBotFactory.getInstance();
    const bots = factory.getAllBots();

    const activeBots = bots.filter(bot => bot.isInMeeting()).length;
    const totalMeetings = bots.length;

    // Calculate average session duration
    let totalDuration = 0;
    let sessionCount = 0;

    bots.forEach(bot => {
      const session = bot.getSession();
      if (session) {
        const duration = session.endTime 
          ? session.endTime.getTime() - session.startTime.getTime()
          : Date.now() - session.startTime.getTime();
        totalDuration += duration;
        sessionCount++;
      }
    });

    const averageSessionDuration = sessionCount > 0 ? totalDuration / sessionCount : 0;

    return {
      totalBots: bots.length,
      activeBots,
      totalMeetings,
      averageSessionDuration: Math.round(averageSessionDuration / 1000) // Convert to seconds
    };
  }
}