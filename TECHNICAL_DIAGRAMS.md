# 🎯 Zero-Touch Tender System - Technical Diagrams

This document contains comprehensive technical diagrams for the Zero-Touch Tender System architecture, workflows, and integrations.

## 1. System Architecture Overview

```mermaid
graph TB
    subgraph "Input Layer"
        A1[Email/Portal Upload]
        A2[Gmail Webhook]
        A3[Manual Upload]
        A4[API Endpoint]
    end
    
    subgraph "Processing Layer"
        B1[Document Parser Agent]
        B2[LangGraph Orchestrator]
        B3[Event Bus]
        B4[Message Queue]
    end
    
    subgraph "AI Agent Layer"
        C1[Scheduler Agent]
        C2[Content Builder]
        C3[Voice Bot]
        C4[Summarizer]
        C5[Task Manager]
        C6[Agent Coordinator]
    end
    
    subgraph "Integration Layer"
        D1[Gmail API]
        D2[Google Calendar]
        D3[Twilio SMS]
        D4[Teams/Zoom]
        D5[OpenAI API]
        D6[Task Platforms]
    end
    
    subgraph "Data Layer"
        E1[Convex Database]
        E2[File Storage]
        E3[Audit Logs]
        E4[Event Store]
    end
    
    subgraph "UI Layer"
        F1[Zero-Touch Dashboard]
        F2[Workflow Monitor]
        F3[Agent Status]
        F4[Event Timeline]
    end
    
    A1 --> A2
    A2 --> B1
    A3 --> B1
    A4 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> B4
    
    B2 --> C1
    B2 --> C2
    B2 --> C3
    C1 --> C4
    C2 --> C4
    C3 --> C4
    C4 --> C5
    C6 -.-> C1
    C6 -.-> C2
    C6 -.-> C3
    C6 -.-> C4
    C6 -.-> C5
    
    C1 --> D2
    C1 --> D3
    C2 --> D5
    C3 --> D4
    C4 --> D5
    C5 --> D6
    B1 --> D5
    
    B2 --> E1
    B1 --> E2
    B3 --> E3
    B4 --> E4
    
    F1 --> E1
    F2 --> E1
    F3 --> E1
    F4 --> E3
    
    style A1 fill:#e1f5fe
    style B2 fill:#fff3e0
    style C6 fill:#f3e5f5
    style E1 fill:#e8f5e8
    style F1 fill:#fce4ec
```

## 2. Detailed Workflow Sequence

```mermaid
sequenceDiagram
    autonumber
    participant Email as Email/Portal
    participant Parser as Document Parser
    participant Orch as Orchestrator
    participant Sched as Scheduler Agent
    participant Content as Content Builder
    participant VBot as Voice Bot
    participant Meet as Meeting Platform
    participant Sum as Summarizer
    participant Task as Task Manager
    participant DB as Database
    participant Users as Participants
    
    Email->>Parser: New tender document
    Parser->>Parser: OCR + LLM extraction
    Parser-->>Orch: Structured JSON data
    Orch->>DB: Create workflow instance
    
    par Parallel Agent Execution
        Orch->>Sched: Schedule meeting
        Sched->>DB: Query contact sheet
        Sched->>Users: Send invites (email/SMS)
        Users-->>Sched: RSVP responses
        Sched-->>Orch: Meeting created
    and
        Orch->>Content: Generate materials
        Content->>Content: Create slides/agenda
        Content->>DB: Store content
        Content-->>Orch: Materials ready
    end
    
    Note over Meet: Meeting Day
    VBot->>Meet: Join as co-host
    VBot->>Users: Present materials
    VBot->>Users: Conduct Q&A
    VBot->>Users: Run Go/No-Go poll
    Users-->>VBot: Poll responses
    
    VBot->>Sum: Meeting transcript
    Sum->>Sum: Extract action items
    Sum->>Task: Action item list
    Task->>DB: Create SMART tasks
    Task->>Users: Task notifications
    
    Orch->>DB: Update workflow status
    Orch->>Users: Send wrap-up email
```

## 3. Agent Interaction Diagram

```mermaid
graph TD
    subgraph "Agent Ecosystem"
        AC[Agent Coordinator]
        
        subgraph "Processing Agents"
            DP[Document Parser]
            SA[Scheduler Agent]
            CB[Content Builder]
        end
        
        subgraph "Execution Agents"
            VB[Voice Bot]
            SU[Summarizer]
            TM[Task Manager]
        end
        
        subgraph "Support Systems"
            EB[Event Bus]
            MQ[Message Queue]
            AL[Audit Logger]
        end
    end
    
    AC -.->|Coordinates| DP
    AC -.->|Manages| SA
    AC -.->|Monitors| CB
    AC -.->|Controls| VB
    AC -.->|Supervises| SU
    AC -.->|Oversees| TM
    
    DP -->|Document Ready| EB
    SA -->|Meeting Scheduled| EB
    CB -->|Content Generated| EB
    VB -->|Meeting Complete| EB
    SU -->|Summary Ready| EB
    TM -->|Tasks Created| EB
    
    EB -->|Events| MQ
    MQ -->|Process| AC
    
    DP -.->|Log Activity| AL
    SA -.->|Log Activity| AL
    CB -.->|Log Activity| AL
    VB -.->|Log Activity| AL
    SU -.->|Log Activity| AL
    TM -.->|Log Activity| AL
    
    style AC fill:#ff9800
    style EB fill:#4caf50
    style MQ fill:#2196f3
    style AL fill:#9c27b0
```

## 4. Database Schema Relationships

```mermaid
erDiagram
    WORKFLOW_INSTANCES ||--o{ WORKFLOW_EVENTS : logs
    WORKFLOW_INSTANCES ||--o{ TENDERS : creates
    TENDERS ||--o{ BID_SECTIONS : contains
    
    WORKFLOW_INSTANCES ||--o{ SCHEDULER_MEETINGS : schedules
    SCHEDULER_MEETINGS ||--o{ SCHEDULER_RSVPS : tracks
    SCHEDULER_MEETINGS ||--o{ MEETING_TRANSCRIPTS : records
    
    MEETING_TRANSCRIPTS ||--o{ MEETING_ACTION_ITEMS : extracts
    MEETING_ACTION_ITEMS ||--o{ MEETING_TASKS : creates
    MEETING_TASKS ||--o{ TASK_NOTIFICATIONS : sends
    
    WORKFLOW_INSTANCES ||--o{ CONTENT_REQUESTS : generates
    CONTENT_REQUESTS ||--o{ GENERATED_CONTENT : produces
    GENERATED_CONTENT ||--o{ CONTENT_EXPORTS : exports
    
    AGENTS ||--o{ AGENT_TASKS : executes
    AGENT_TASKS ||--o{ AGENT_PERFORMANCE : measures
    
    FILES ||--o{ DOCUMENT_ANALYSIS : analyzes
    DOCUMENT_ANALYSIS ||--o{ DOCUMENT_REQUIREMENTS : extracts
    
    WORKFLOW_INSTANCES {
        string id PK
        string sourceType
        string stage
        string status
        string outcome
        datetime createdAt
        string tenderId FK
        string meetingId FK
    }
    
    AGENTS {
        string id PK
        string name
        string type
        string status
        array capabilities
        number successRate
        number averageResponseTime
    }
    
    TENDERS {
        string id PK
        string name
        string clientName
        string status
        datetime dueDate
        number estimatedValue
    }
    
    SCHEDULER_MEETINGS {
        string id PK
        string title
        datetime startTime
        datetime endTime
        string status
        string tenderId FK
    }
    
    MEETING_TASKS {
        string id PK
        string title
        string assignee
        string priority
        string status
        datetime dueDate
        object smartGoal
    }
```

## 5. Voice Bot Architecture

```mermaid
graph TB
    subgraph "Voice Bot Core"
        VBC[Voice Bot Controller]
        SM[State Manager]
        CM[Command Manager]
    end
    
    subgraph "Speech Services"
        STT[Speech-to-Text]
        TTS[Text-to-Speech]
        NLP[NLP Processor]
    end
    
    subgraph "Meeting Services"
        MPS[Meeting Platform Service]
        PS[Presentation Service]
        RS[Recording Service]
        TS[Transcription Service]
    end
    
    subgraph "Intelligence Services"
        QA[Q&A Handler]
        PLS[Polling Service]
        SAS[Sentiment Analysis]
        AIE[Action Item Extractor]
    end
    
    subgraph "Platform Integrations"
        TEA[Teams API]
        ZA[Zoom API]
        GMA[Google Meet API]
        WR[WebRTC]
    end
    
    VBC --> SM
    VBC --> CM
    
    CM --> STT
    CM --> TTS
    CM --> NLP
    
    VBC --> MPS
    VBC --> PS
    VBC --> RS
    VBC --> TS
    
    NLP --> QA
    NLP --> PLS
    NLP --> SAS
    TS --> AIE
    
    MPS --> TEA
    MPS --> ZA
    MPS --> GMA
    MPS --> WR
    
    style VBC fill:#ff5722
    style NLP fill:#4caf50
    style MPS fill:#2196f3
    style QA fill:#9c27b0
```

## 6. Content Generation Pipeline

```mermaid
flowchart LR
    subgraph "Input Processing"
        TD[Tender Data]
        TR[Template Request]
        CP[Content Parameters]
    end
    
    subgraph "Content Engine"
        TA[Template Analyzer]
        CG[Content Generator]
        QA[Quality Assessor]
        FO[Format Optimizer]
    end
    
    subgraph "Generation Types"
        ES[Executive Summary]
        HM[Heat Maps]
        CD[Compliance Dashboard]
        TL[Timeline]
        AG[Agenda]
        VS[Voice Script]
    end
    
    subgraph "Output Formats"
        PPT[PowerPoint]
        PDF[PDF Export]
        HTML[HTML Presentation]
        GS[Google Slides]
        MD[Markdown]
    end
    
    subgraph "Quality Control"
        BC[Brand Compliance]
        CR[Content Review]
        AO[Accessibility Check]
        PV[Preview Generation]
    end
    
    TD --> TA
    TR --> TA
    CP --> TA
    
    TA --> CG
    CG --> QA
    QA --> FO
    
    CG --> ES
    CG --> HM
    CG --> CD
    CG --> TL
    CG --> AG
    CG --> VS
    
    ES --> PPT
    HM --> PDF
    CD --> HTML
    TL --> GS
    AG --> MD
    VS --> PPT
    
    FO --> BC
    BC --> CR
    CR --> AO
    AO --> PV
    
    style CG fill:#ff9800
    style QA fill:#4caf50
    style BC fill:#e91e63
```

## 7. Task Management Flow

```mermaid
stateDiagram-v2
    [*] --> ActionItemExtracted
    
    ActionItemExtracted --> SMARTGoalGenerated : Apply SMART criteria
    SMARTGoalGenerated --> AssigneeIdentified : Determine assignee
    AssigneeIdentified --> PriorityCalculated : Calculate priority
    PriorityCalculated --> TaskCreated : Create in platforms
    
    TaskCreated --> NotificationSent : Send initial notification
    NotificationSent --> InProgress : Task started
    
    InProgress --> ProgressUpdate : Regular updates
    ProgressUpdate --> InProgress : Continue work
    ProgressUpdate --> Blocked : Obstacle encountered
    ProgressUpdate --> Completed : Task finished
    
    Blocked --> EscalationLevel1 : Auto-escalate
    EscalationLevel1 --> EscalationLevel2 : Still blocked
    EscalationLevel2 --> ManagerNotified : Critical escalation
    
    EscalationLevel1 --> InProgress : Blocker resolved
    EscalationLevel2 --> InProgress : Blocker resolved
    ManagerNotified --> InProgress : Manual intervention
    
    Completed --> Verified : Quality check
    Verified --> Closed : Task complete
    Closed --> [*]
    
    state InProgress {
        [*] --> Working
        Working --> UpdateProgress : Regular check-in
        UpdateProgress --> Working : Continue
        UpdateProgress --> NeedsHelp : Request assistance
        NeedsHelp --> Working : Help received
    }
```

## 8. Integration Architecture

```mermaid
graph TB
    subgraph "External Services"
        subgraph "Google Workspace"
            Gmail[Gmail API]
            GCal[Google Calendar]
            GDrive[Google Drive]
            GSlides[Google Slides]
        end
        
        subgraph "Microsoft 365"
            Teams[Microsoft Teams]
            Outlook[Outlook Calendar]
            OneDrive[OneDrive]
            PowerPoint[PowerPoint Online]
        end
        
        subgraph "Communication"
            Twilio[Twilio SMS]
            SendGrid[SendGrid Email]
            Slack[Slack API]
        end
        
        subgraph "Meeting Platforms"
            Zoom[Zoom API]
            WebEx[WebEx API]
            GoogleMeet[Google Meet]
        end
        
        subgraph "Task Management"
            Planner[Microsoft Planner]
            Trello[Trello API]
            Asana[Asana API]
            Monday[Monday.com]
        end
        
        subgraph "AI Services"
            OpenAI[OpenAI API]
            Azure[Azure Speech]
            Google_AI[Google AI]
        end
    end
    
    subgraph "Integration Layer"
        API_Gateway[API Gateway]
        Auth_Manager[Auth Manager]
        Rate_Limiter[Rate Limiter]
        Error_Handler[Error Handler]
        Cache_Layer[Cache Layer]
    end
    
    subgraph "Zero-Touch System"
        ZTS[Zero-Touch Core]
        Agent_Pool[Agent Pool]
        Event_Stream[Event Stream]
        Workflow_Engine[Workflow Engine]
    end
    
    Gmail --> API_Gateway
    GCal --> API_Gateway
    Teams --> API_Gateway
    Twilio --> API_Gateway
    Zoom --> API_Gateway
    OpenAI --> API_Gateway
    Planner --> API_Gateway
    
    API_Gateway --> Auth_Manager
    Auth_Manager --> Rate_Limiter
    Rate_Limiter --> Error_Handler
    Error_Handler --> Cache_Layer
    Cache_Layer --> ZTS
    
    ZTS --> Agent_Pool
    ZTS --> Event_Stream
    ZTS --> Workflow_Engine
    
    style API_Gateway fill:#ff9800
    style ZTS fill:#4caf50
    style Agent_Pool fill:#2196f3
```

## 9. Real-time Event Flow

```mermaid
sequenceDiagram
    participant UI as Dashboard UI
    participant WE as Workflow Engine
    participant EB as Event Bus
    participant AG as Agents
    participant EXT as External APIs
    participant DB as Database
    participant NOT as Notifications
    
    Note over UI,NOT: Real-time Event Processing
    
    UI->>WE: Monitor workflow
    WE->>EB: Subscribe to events
    
    loop Continuous Processing
        AG->>EB: Emit progress event
        EB->>DB: Store event
        EB->>UI: Stream update
        EB->>WE: Workflow event
        
        alt Critical Event
            EB->>NOT: Send alert
            NOT->>EXT: External notification
        end
        
        alt Error Event
            EB->>WE: Error handling
            WE->>AG: Retry instruction
        end
        
        alt Completion Event
            EB->>WE: Stage complete
            WE->>AG: Next stage
        end
    end
    
    WE-->>UI: Final status
    DB-->>UI: Historical data
```

## 10. Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        subgraph "Frontend"
            CDN[CDN/Edge Cache]
            Static[Static Assets]
            App[Next.js App]
        end
        
        subgraph "Backend Services"
            Convex[Convex Database]
            API[API Gateway]
            Queue[Message Queue]
            Storage[File Storage]
        end
        
        subgraph "AI Services"
            OpenAI_Prod[OpenAI API]
            Speech[Speech Services]
            NLP[NLP Services]
        end
        
        subgraph "External Integrations"
            Google[Google APIs]
            Microsoft[Microsoft APIs]
            Twilio_Prod[Twilio SMS]
            Meeting[Meeting Platforms]
        end
        
        subgraph "Monitoring"
            Logs[Logging Service]
            Metrics[Metrics Collection]
            Alerts[Alert Manager]
            Health[Health Checks]
        end
    end
    
    subgraph "Development Environment"
        Dev_App[Dev App]
        Dev_DB[Dev Database]
        Test_APIs[Test APIs]
    end
    
    subgraph "CI/CD Pipeline"
        GitHub[GitHub Repository]
        Actions[GitHub Actions]
        Deploy[Auto Deploy]
        Tests[Test Suite]
    end
    
    CDN --> App
    App --> API
    API --> Convex
    API --> Queue
    App --> Storage
    
    Queue --> OpenAI_Prod
    Queue --> Speech
    API --> Google
    API --> Microsoft
    Queue --> Twilio_Prod
    
    App --> Logs
    API --> Metrics
    Queue --> Alerts
    Convex --> Health
    
    GitHub --> Actions
    Actions --> Tests
    Tests --> Deploy
    Deploy --> App
    
    style App fill:#4caf50
    style Convex fill:#2196f3
    style Queue fill:#ff9800
    style Deploy fill:#9c27b0
```

## Usage Instructions

To use these diagrams:

1. **Copy any diagram code** into [Mermaid Live Editor](https://mermaid.live/)
2. **Paste into documentation** tools that support Mermaid
3. **Use in presentations** by exporting as PNG/SVG
4. **Include in technical reviews** for architecture discussions

Each diagram provides a different perspective on the Zero-Touch Tender System, from high-level architecture to detailed implementation flows.