events {
    worker_connections 1024;
}

http {
    upstream gateway {
        server gateway:8000;
    }

    upstream qwen {
        server qwen:8000;
    }

    upstream minimax {
        server minimax:8000;
    }

    upstream phi4 {
        server phi4:8000;
    }

    # Main API Gateway
    server {
        listen 80;
        server_name _;
        
        client_max_body_size 200M;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
        }
        
        # Main gateway API
        location /api/ {
            proxy_pass http://gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            
            # CORS headers
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;
            add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range' always;
        }
        
        # Direct model endpoints (optional)
        location /models/qwen/ {
            proxy_pass http://qwen/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_read_timeout 600s;
        }
        
        location /models/minimax/ {
            proxy_pass http://minimax/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_read_timeout 600s;
        }
        
        location /models/phi4/ {
            proxy_pass http://phi4/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_read_timeout 600s;
        }
        
        # Metrics endpoint
        location /metrics {
            proxy_pass http://gateway/metrics;
        }
        
        # Default route
        location / {
            return 200 'Document Processing Pipeline API\n\nEndpoints:\n- POST /api/upload\n- POST /api/process\n- GET /api/job/{job_id}\n- GET /health\n- GET /metrics\n';
            add_header Content-Type text/plain;
        }
    }
}