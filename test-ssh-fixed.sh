#!/bin/bash

echo "🧪 Testing SSH After Fix"
echo "========================"
echo ""

echo "Testing SSH connection..."
if ssh -o ConnectTimeout=10 -o BatchMode=yes root@100.93.124.60 "echo 'SSH is working!' && whoami && nvidia-smi --query-gpu=name --format=csv,noheader"; then
    echo ""
    echo "🎉 SUCCESS! SSH is working!"
    echo ""
    echo "Now you can proceed with Refact setup:"
    echo "ssh root@100.93.124.60"
    echo "cd /root/refact-setup && source venv/bin/activate"
    echo "python3 -c \"import torch; print(f'CUDA: {torch.cuda.is_available()}')\""
else
    echo ""
    echo "❌ SSH still not working. Try these steps:"
    echo "1. In Proxmox console, run: journalctl -u ssh -f"
    echo "2. Look for error messages"
    echo "3. Try: systemctl restart sshd"
    echo "4. Check firewall: ufw status"
fi