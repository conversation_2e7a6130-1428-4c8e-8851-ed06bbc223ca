/**
 * Workflow Templates
 * 
 * Pre-defined workflow templates for tender processing
 */

import { WorkflowTemplate, NodeConfig, WorkflowEdge } from '../types';

/**
 * Zero-touch tender processing workflow
 */
export const zeroTouchTenderWorkflow: WorkflowTemplate = {
  id: 'zero-touch-tender',
  name: 'Zero-Touch Tender Processing',
  description: 'Fully automated tender processing from ingestion to submission',
  type: 'tender_processing',
  nodes: [
    {
      id: 'start',
      type: 'start',
      name: 'Initialize Workflow',
      description: 'Initialize the tender processing workflow',
      requiredAgents: [],
      inputs: [
        {
          name: 'tenderId',
          type: 'string',
          required: true,
          source: 'context'
        },
        {
          name: 'documentIds',
          type: 'array',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'initialized',
          type: 'boolean',
          destination: 'variable',
          path: 'workflowInitialized'
        }
      ],
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'exponential',
        initialDelay: 1000,
        maxDelay: 10000
      }
    },
    {
      id: 'ingestion',
      type: 'ingestion',
      name: 'Document Ingestion',
      description: 'Process and extract information from tender documents',
      requiredAgents: [
        {
          type: 'analyst',
          capabilities: ['analyze_requirements', 'extract_keywords'],
          minCount: 2,
          maxCount: 4,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'documentIds',
          type: 'array',
          required: true,
          source: 'variable'
        },
        {
          name: 'extractionRules',
          type: 'object',
          required: false,
          source: 'variable',
          defaultValue: {
            sections: ['requirements', 'scope', 'evaluation_criteria', 'deadlines'],
            keywords: true,
            entities: true
          }
        }
      ],
      outputs: [
        {
          name: 'extractedData',
          type: 'object',
          destination: 'variable',
          path: 'extractedData'
        },
        {
          name: 'ingestionReport',
          type: 'object',
          destination: 'result',
          path: 'ingestion'
        }
      ],
      timeout: 300000, // 5 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'linear',
        initialDelay: 5000,
        maxDelay: 30000
      }
    },
    {
      id: 'analysis',
      type: 'content_generation',
      name: 'Requirements Analysis',
      description: 'Analyze tender requirements and create bid structure',
      requiredAgents: [
        {
          type: 'analyst',
          capabilities: ['analyze_requirements', 'generate_outline'],
          minCount: 1,
          maxCount: 2,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'extractedData',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'bidSections',
          type: 'array',
          destination: 'variable',
          path: 'bidSections'
        },
        {
          name: 'analysisReport',
          type: 'object',
          destination: 'result',
          path: 'analysis'
        }
      ],
      timeout: 180000, // 3 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'exponential',
        initialDelay: 2000,
        maxDelay: 15000
      },
      conditions: [
        {
          field: 'variables.ingestionComplete',
          operator: 'equals',
          value: true,
          onTrue: 'scheduling',
          onFalse: 'error_handler'
        }
      ]
    },
    {
      id: 'scheduling',
      type: 'scheduling',
      name: 'Meeting Scheduling',
      description: 'Schedule review meetings and allocate resources',
      requiredAgents: [
        {
          type: 'coordinator',
          capabilities: ['generate_outline'],
          minCount: 1,
          maxCount: 1,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'bidSections',
          type: 'array',
          required: true,
          source: 'variable'
        },
        {
          name: 'participants',
          type: 'array',
          required: false,
          source: 'variable',
          defaultValue: []
        }
      ],
      outputs: [
        {
          name: 'meetingSchedule',
          type: 'object',
          destination: 'variable',
          path: 'meetingSchedule'
        },
        {
          name: 'meetingId',
          type: 'string',
          destination: 'variable',
          path: 'meetingId'
        }
      ],
      timeout: 60000, // 1 minute
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'fixed',
        initialDelay: 2000,
        maxDelay: 2000
      }
    },
    {
      id: 'content_parallel',
      type: 'content_generation',
      name: 'Parallel Content Generation',
      description: 'Generate bid content for all sections in parallel',
      requiredAgents: [
        {
          type: 'writer',
          capabilities: ['content_generation', 'technical_writing'],
          minCount: 3,
          maxCount: 8,
          priority: 'required'
        },
        {
          type: 'specialist',
          capabilities: ['pricing_analysis', 'compliance_check'],
          minCount: 1,
          maxCount: 2,
          priority: 'preferred'
        }
      ],
      inputs: [
        {
          name: 'bidSections',
          type: 'array',
          required: true,
          source: 'variable'
        },
        {
          name: 'extractedData',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'generatedContent',
          type: 'object',
          destination: 'variable',
          path: 'generatedContent'
        },
        {
          name: 'contentReport',
          type: 'object',
          destination: 'result',
          path: 'contentGeneration'
        }
      ],
      timeout: 600000, // 10 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'exponential',
        initialDelay: 5000,
        maxDelay: 60000,
        retryableErrors: ['AGENT_UNAVAILABLE', 'TIMEOUT']
      }
    },
    {
      id: 'review',
      type: 'content_generation',
      name: 'Content Review',
      description: 'Review and improve generated content',
      requiredAgents: [
        {
          type: 'reviewer',
          capabilities: ['content_review', 'quality_assurance'],
          minCount: 2,
          maxCount: 3,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'generatedContent',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'reviewedContent',
          type: 'object',
          destination: 'variable',
          path: 'reviewedContent'
        },
        {
          name: 'qualityScores',
          type: 'object',
          destination: 'result',
          path: 'review'
        }
      ],
      timeout: 300000, // 5 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'linear',
        initialDelay: 3000,
        maxDelay: 20000
      }
    },
    {
      id: 'meeting_prep',
      type: 'content_generation',
      name: 'Meeting Preparation',
      description: 'Prepare presentation deck and talking points',
      requiredAgents: [
        {
          type: 'writer',
          capabilities: ['generate_summary', 'content_generation'],
          minCount: 1,
          maxCount: 2,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'reviewedContent',
          type: 'object',
          required: true,
          source: 'variable'
        },
        {
          name: 'meetingSchedule',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'presentationDeck',
          type: 'object',
          destination: 'variable',
          path: 'presentationDeck'
        },
        {
          name: 'talkingPoints',
          type: 'object',
          destination: 'variable',
          path: 'talkingPoints'
        }
      ],
      timeout: 120000, // 2 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'fixed',
        initialDelay: 2000,
        maxDelay: 2000
      }
    },
    {
      id: 'meeting',
      type: 'meeting_execution',
      name: 'Meeting Execution',
      description: 'Conduct the review meeting with voice bot',
      requiredAgents: [
        {
          type: 'coordinator',
          capabilities: ['content_review'],
          minCount: 1,
          maxCount: 1,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'meetingId',
          type: 'string',
          required: true,
          source: 'variable'
        },
        {
          name: 'presentationDeck',
          type: 'object',
          required: true,
          source: 'variable'
        },
        {
          name: 'talkingPoints',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'meetingTranscript',
          type: 'string',
          destination: 'variable',
          path: 'meetingTranscript'
        },
        {
          name: 'meetingNotes',
          type: 'object',
          destination: 'variable',
          path: 'meetingNotes'
        },
        {
          name: 'meetingReport',
          type: 'object',
          destination: 'result',
          path: 'meetingExecution'
        }
      ],
      timeout: 3600000, // 1 hour
      retryPolicy: {
        maxAttempts: 1,
        backoffStrategy: 'fixed',
        initialDelay: 0,
        maxDelay: 0
      }
    },
    {
      id: 'post_processing',
      type: 'post_processing',
      name: 'Post-Processing',
      description: 'Process meeting outcomes and generate final documents',
      requiredAgents: [
        {
          type: 'writer',
          capabilities: ['generate_summary', 'format_content'],
          minCount: 2,
          maxCount: 3,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'meetingTranscript',
          type: 'string',
          required: true,
          source: 'variable'
        },
        {
          name: 'meetingNotes',
          type: 'object',
          required: true,
          source: 'variable'
        },
        {
          name: 'reviewedContent',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'executiveSummary',
          type: 'string',
          destination: 'variable',
          path: 'executiveSummary'
        },
        {
          name: 'actionItems',
          type: 'array',
          destination: 'variable',
          path: 'actionItems'
        },
        {
          name: 'finalDocuments',
          type: 'object',
          destination: 'result',
          path: 'postProcessing'
        }
      ],
      timeout: 300000, // 5 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'exponential',
        initialDelay: 2000,
        maxDelay: 15000
      }
    },
    {
      id: 'finalization',
      type: 'content_generation',
      name: 'Document Finalization',
      description: 'Format and finalize all bid documents',
      requiredAgents: [
        {
          type: 'specialist',
          capabilities: ['format_content', 'compliance_check'],
          minCount: 1,
          maxCount: 2,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'reviewedContent',
          type: 'object',
          required: true,
          source: 'variable'
        },
        {
          name: 'executiveSummary',
          type: 'string',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'finalBidDocument',
          type: 'object',
          destination: 'result',
          path: 'finalBid'
        }
      ],
      timeout: 180000, // 3 minutes
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'linear',
        initialDelay: 2000,
        maxDelay: 10000
      }
    },
    {
      id: 'workflow_advancement',
      type: 'workflow_advancement',
      name: 'Status Update',
      description: 'Update tender status and create follow-up tasks',
      requiredAgents: [],
      inputs: [
        {
          name: 'actionItems',
          type: 'array',
          required: false,
          source: 'variable',
          defaultValue: []
        }
      ],
      outputs: [
        {
          name: 'statusUpdate',
          type: 'object',
          destination: 'result',
          path: 'workflowAdvancement'
        }
      ],
      timeout: 30000,
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'fixed',
        initialDelay: 1000,
        maxDelay: 1000
      }
    },
    {
      id: 'error_handler',
      type: 'error_handler',
      name: 'Error Recovery',
      description: 'Handle workflow errors and attempt recovery',
      requiredAgents: [],
      inputs: [],
      outputs: [
        {
          name: 'errorReport',
          type: 'object',
          destination: 'result',
          path: 'errorHandling'
        }
      ],
      timeout: 60000,
      retryPolicy: {
        maxAttempts: 1,
        backoffStrategy: 'fixed',
        initialDelay: 0,
        maxDelay: 0
      }
    },
    {
      id: 'end',
      type: 'end',
      name: 'Complete Workflow',
      description: 'Finalize and archive workflow execution',
      requiredAgents: [],
      inputs: [],
      outputs: [
        {
          name: 'completionReport',
          type: 'object',
          destination: 'result',
          path: 'completion'
        }
      ],
      timeout: 10000,
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'fixed',
        initialDelay: 1000,
        maxDelay: 1000
      }
    }
  ],
  edges: [
    {
      id: 'edge-1',
      source: 'start',
      target: 'ingestion'
    },
    {
      id: 'edge-2',
      source: 'ingestion',
      target: 'analysis'
    },
    {
      id: 'edge-3',
      source: 'analysis',
      target: 'scheduling',
      condition: {
        type: 'success',
        field: 'status',
        operator: 'equals',
        value: 'completed'
      }
    },
    {
      id: 'edge-4',
      source: 'scheduling',
      target: 'content_parallel'
    },
    {
      id: 'edge-5',
      source: 'content_parallel',
      target: 'review'
    },
    {
      id: 'edge-6',
      source: 'review',
      target: 'meeting_prep'
    },
    {
      id: 'edge-7',
      source: 'meeting_prep',
      target: 'meeting'
    },
    {
      id: 'edge-8',
      source: 'meeting',
      target: 'post_processing'
    },
    {
      id: 'edge-9',
      source: 'post_processing',
      target: 'finalization'
    },
    {
      id: 'edge-10',
      source: 'finalization',
      target: 'workflow_advancement'
    },
    {
      id: 'edge-11',
      source: 'workflow_advancement',
      target: 'end'
    },
    {
      id: 'edge-error-1',
      source: 'analysis',
      target: 'error_handler',
      condition: {
        type: 'failure',
        field: 'variables.ingestionComplete',
        operator: 'equals',
        value: false
      }
    },
    {
      id: 'edge-error-2',
      source: 'error_handler',
      target: 'end',
      condition: {
        type: 'conditional',
        field: 'status',
        operator: 'equals',
        value: 'failed'
      }
    }
  ],
  variables: [
    {
      name: 'documentIds',
      type: 'array',
      description: 'IDs of tender documents to process',
      required: true,
      validation: {
        min: 1
      }
    },
    {
      name: 'participants',
      type: 'array',
      description: 'Meeting participants',
      required: false,
      defaultValue: []
    },
    {
      name: 'meetingDuration',
      type: 'number',
      description: 'Meeting duration in minutes',
      required: false,
      defaultValue: 60,
      validation: {
        min: 15,
        max: 180
      }
    },
    {
      name: 'outputFormat',
      type: 'string',
      description: 'Final document format',
      required: false,
      defaultValue: 'pdf',
      validation: {
        enum: ['pdf', 'docx', 'html']
      }
    }
  ],
  settings: {
    timeout: 7200000, // 2 hours total
    maxRetries: 3,
    errorHandling: 'continue',
    parallelism: {
      enabled: true,
      maxConcurrent: 10
    },
    monitoring: {
      logLevel: 'info',
      metricsEnabled: true,
      checkpointFrequency: 5
    },
    notifications: [
      {
        type: 'internal',
        events: ['start', 'complete', 'error', 'milestone'],
        recipients: ['workflow-manager'],
        template: 'default'
      }
    ]
  }
};

/**
 * Quick bid review workflow
 */
export const quickBidReviewWorkflow: WorkflowTemplate = {
  id: 'quick-bid-review',
  name: 'Quick Bid Review',
  description: 'Streamlined workflow for reviewing and improving existing bid content',
  type: 'review_approval',
  nodes: [
    // Simplified node structure for review workflow
    {
      id: 'start',
      type: 'start',
      name: 'Initialize Review',
      description: 'Start the bid review process',
      requiredAgents: [],
      inputs: [],
      outputs: [],
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'fixed',
        initialDelay: 1000,
        maxDelay: 1000
      }
    },
    {
      id: 'review',
      type: 'content_generation',
      name: 'Content Review',
      description: 'Review existing bid content',
      requiredAgents: [
        {
          type: 'reviewer',
          capabilities: ['content_review', 'quality_assurance'],
          minCount: 1,
          maxCount: 2,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'bidContent',
          type: 'object',
          required: true,
          source: 'variable'
        }
      ],
      outputs: [
        {
          name: 'reviewResults',
          type: 'object',
          destination: 'result',
          path: 'review'
        }
      ],
      timeout: 180000,
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'linear',
        initialDelay: 2000,
        maxDelay: 10000
      }
    },
    {
      id: 'improvement',
      type: 'content_generation',
      name: 'Content Improvement',
      description: 'Improve bid content based on review',
      requiredAgents: [
        {
          type: 'writer',
          capabilities: ['improve_content'],
          minCount: 1,
          maxCount: 3,
          priority: 'required'
        }
      ],
      inputs: [
        {
          name: 'reviewResults',
          type: 'object',
          required: true,
          source: 'result',
          path: 'review'
        }
      ],
      outputs: [
        {
          name: 'improvedContent',
          type: 'object',
          destination: 'result',
          path: 'improvement'
        }
      ],
      timeout: 300000,
      retryPolicy: {
        maxAttempts: 2,
        backoffStrategy: 'exponential',
        initialDelay: 3000,
        maxDelay: 20000
      }
    },
    {
      id: 'end',
      type: 'end',
      name: 'Complete Review',
      description: 'Finalize the review process',
      requiredAgents: [],
      inputs: [],
      outputs: [],
      timeout: 5000,
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: 'fixed',
        initialDelay: 1000,
        maxDelay: 1000
      }
    }
  ],
  edges: [
    {
      id: 'edge-1',
      source: 'start',
      target: 'review'
    },
    {
      id: 'edge-2',
      source: 'review',
      target: 'improvement'
    },
    {
      id: 'edge-3',
      source: 'improvement',
      target: 'end'
    }
  ],
  variables: [
    {
      name: 'bidContent',
      type: 'object',
      description: 'Existing bid content to review',
      required: true
    },
    {
      name: 'reviewCriteria',
      type: 'array',
      description: 'Specific criteria to focus on during review',
      required: false,
      defaultValue: ['clarity', 'completeness', 'compliance', 'persuasiveness']
    }
  ],
  settings: {
    timeout: 900000, // 15 minutes
    maxRetries: 2,
    errorHandling: 'stop',
    parallelism: {
      enabled: false,
      maxConcurrent: 1
    },
    monitoring: {
      logLevel: 'info',
      metricsEnabled: true,
      checkpointFrequency: 3
    },
    notifications: [
      {
        type: 'internal',
        events: ['complete', 'error'],
        recipients: ['review-manager'],
        template: 'review'
      }
    ]
  }
};

/**
 * Get workflow template by ID
 */
export function getWorkflowTemplate(templateId: string): WorkflowTemplate | null {
  const templates: Record<string, WorkflowTemplate> = {
    'zero-touch-tender': zeroTouchTenderWorkflow,
    'quick-bid-review': quickBidReviewWorkflow
  };

  return templates[templateId] || null;
}

/**
 * List all available workflow templates
 */
export function listWorkflowTemplates(): Array<{
  id: string;
  name: string;
  description: string;
  type: string;
}> {
  return [
    {
      id: zeroTouchTenderWorkflow.id,
      name: zeroTouchTenderWorkflow.name,
      description: zeroTouchTenderWorkflow.description,
      type: zeroTouchTenderWorkflow.type
    },
    {
      id: quickBidReviewWorkflow.id,
      name: quickBidReviewWorkflow.name,
      description: quickBidReviewWorkflow.description,
      type: quickBidReviewWorkflow.type
    }
  ];
}