/**
 * Agent Coordinator
 * 
 * Manages agent allocation, task distribution, and coordination
 */

import { 
  AgentResource, 
  ResourcePool, 
  ResourceRequest,
  ResourceLock,
  AgentRequirement,
  AgentTaskMessage,
  AgentResponseMessage,
  WorkflowPriority,
  CoordinationMessage
} from '../types';
import { 
  BidWritingAgent, 
  AgentType, 
  AgentTaskType,
  AgentStatus 
} from '../../types/agent';
import { EventBus } from '../events/EventBus';
import { Logger } from '../utils/Logger';
import { BaseMessage } from '@langchain/core/messages';

interface TaskExecutionOptions {
  parallel?: boolean;
  timeout?: number;
  streaming?: boolean;
  onProgress?: (progress: number) => void;
}

interface TaskExecutionResult {
  taskId: string;
  agentId: string;
  status: 'success' | 'failure' | 'timeout';
  output?: any;
  error?: string;
  confidence?: number;
  metrics?: {
    tokensUsed: number;
    processingTime: number;
    qualityScore?: number;
  };
}

export class AgentCoordinator {
  private resourcePool: ResourcePool;
  private eventBus: EventBus;
  private logger: Logger;
  private taskQueue: Map<string, AgentTaskMessage> = new Map();
  private activeExecutions: Map<string, AbortController> = new Map();
  private coordinationMessages: CoordinationMessage[] = [];

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = new Logger('AgentCoordinator');
    this.resourcePool = {
      agents: new Map(),
      locks: new Map(),
      queue: []
    };

    // Subscribe to agent-related events
    this.setupEventHandlers();
  }

  /**
   * Initialize agent resources
   */
  public async initialize(agents: BidWritingAgent[]): Promise<void> {
    this.logger.info('Initializing agent coordinator', { agentCount: agents.length });

    // Create agent resources
    agents.forEach(agent => {
      const resource: AgentResource = {
        agentId: agent.id,
        type: agent.type,
        status: agent.status === 'active' ? 'available' : 'offline',
        currentTasks: [],
        capacity: agent.settings.maxConcurrentTasks || 3,
        utilization: 0,
        lastAssigned: undefined
      };

      this.resourcePool.agents.set(agent.id, resource);
    });

    this.logger.info('Agent coordinator initialized');
  }

  /**
   * Allocate agents based on requirements
   */
  public async allocateAgents(
    requirements: AgentRequirement[],
    priority: WorkflowPriority
  ): Promise<AgentResource[]> {
    const allocatedAgents: AgentResource[] = [];

    for (const requirement of requirements) {
      const availableAgents = this.findAvailableAgents(requirement);

      if (availableAgents.length < requirement.minCount) {
        if (requirement.priority === 'required') {
          throw new Error(`Not enough agents available for ${requirement.type}`);
        }
        this.logger.warn('Insufficient agents for requirement', { requirement });
      }

      // Allocate agents up to maxCount or available count
      const countToAllocate = Math.min(
        requirement.maxCount || requirement.minCount,
        availableAgents.length
      );

      for (let i = 0; i < countToAllocate; i++) {
        const agent = availableAgents[i];
        
        // Create resource lock
        const lock: ResourceLock = {
          resourceId: agent.agentId,
          ownerId: `allocation-${Date.now()}-${i}`,
          type: 'exclusive',
          acquiredAt: Date.now(),
          expiresAt: Date.now() + 3600000 // 1 hour
        };

        this.resourcePool.locks.set(lock.ownerId, lock);
        agent.status = 'reserved';
        
        allocatedAgents.push(agent);
      }
    }

    this.logger.info('Allocated agents', { 
      count: allocatedAgents.length,
      priority 
    });

    return allocatedAgents;
  }

  /**
   * Execute a single task
   */
  public async executeTask(
    task: Partial<AgentTaskMessage>,
    options: TaskExecutionOptions = {}
  ): Promise<TaskExecutionResult> {
    const taskId = `task-${Date.now()}-${Math.random()}`;
    const timeout = options.timeout || 300000; // 5 minutes default

    try {
      // Create abort controller for cancellation
      const abortController = new AbortController();
      this.activeExecutions.set(taskId, abortController);

      // Create task message
      const taskMessage: AgentTaskMessage = {
        content: JSON.stringify(task.input),
        taskId,
        agentId: task.agentId!,
        taskType: task.taskType!,
        priority: task.priority || 'medium',
        deadline: task.deadline,
        input: task.input,
        name: 'agent_task',
        additional_kwargs: {}
      };

      // Add to task queue
      this.taskQueue.set(taskId, taskMessage);

      // Execute with timeout
      const result = await Promise.race([
        this.executeTaskInternal(taskMessage, options, abortController.signal),
        this.createTimeout(timeout, taskId)
      ]);

      return result;

    } finally {
      // Cleanup
      this.taskQueue.delete(taskId);
      this.activeExecutions.delete(taskId);
    }
  }

  /**
   * Execute multiple tasks
   */
  public async executeTasks(
    tasks: Partial<AgentTaskMessage>[],
    options: TaskExecutionOptions = {}
  ): Promise<TaskExecutionResult[]> {
    if (options.parallel) {
      // Execute tasks in parallel
      const promises = tasks.map(task => this.executeTask(task, options));
      return Promise.all(promises);
    } else {
      // Execute tasks sequentially
      const results: TaskExecutionResult[] = [];
      for (const task of tasks) {
        const result = await this.executeTask(task, options);
        results.push(result);
        
        // Report progress if callback provided
        if (options.onProgress) {
          const progress = (results.length / tasks.length) * 100;
          options.onProgress(progress);
        }
      }
      return results;
    }
  }

  /**
   * Find alternative agent for failed task
   */
  public async findAlternativeAgent(
    originalAgentId: string,
    priority: WorkflowPriority
  ): Promise<AgentResource | null> {
    const originalAgent = this.resourcePool.agents.get(originalAgentId);
    if (!originalAgent) {
      return null;
    }

    // Find agents of same type that are available
    const alternatives = Array.from(this.resourcePool.agents.values())
      .filter(agent => 
        agent.agentId !== originalAgentId &&
        agent.type === originalAgent.type &&
        agent.status === 'available' &&
        agent.utilization < 0.8
      )
      .sort((a, b) => a.utilization - b.utilization);

    if (alternatives.length === 0) {
      return null;
    }

    // Allocate the best alternative
    const alternative = alternatives[0];
    alternative.status = 'reserved';

    this.logger.info('Found alternative agent', {
      originalAgentId,
      alternativeAgentId: alternative.agentId
    });

    return alternative;
  }

  /**
   * Wait for resources to become available
   */
  public async waitForResources(timeout: number): Promise<void> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      // Check if any agents have become available
      const availableCount = Array.from(this.resourcePool.agents.values())
        .filter(a => a.status === 'available').length;

      if (availableCount > 0) {
        return;
      }

      // Wait a bit before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    throw new Error('Timeout waiting for resources');
  }

  /**
   * Release allocated agents
   */
  public releaseAgents(agentIds: string[]): void {
    agentIds.forEach(agentId => {
      const agent = this.resourcePool.agents.get(agentId);
      if (agent) {
        agent.status = 'available';
        agent.currentTasks = [];
        agent.utilization = 0;
      }

      // Remove any locks for this agent
      const locksToRemove: string[] = [];
      this.resourcePool.locks.forEach((lock, ownerId) => {
        if (lock.resourceId === agentId) {
          locksToRemove.push(ownerId);
        }
      });

      locksToRemove.forEach(ownerId => {
        this.resourcePool.locks.delete(ownerId);
      });
    });

    this.logger.info('Released agents', { count: agentIds.length });
  }

  /**
   * Get agent utilization metrics
   */
  public getUtilizationMetrics(): Record<string, number> {
    const metrics: Record<string, number> = {};

    this.resourcePool.agents.forEach((agent, agentId) => {
      metrics[agentId] = agent.utilization;
    });

    return metrics;
  }

  /**
   * Send coordination message
   */
  public async sendCoordinationMessage(message: Omit<CoordinationMessage, 'id' | 'timestamp'>): Promise<void> {
    const fullMessage: CoordinationMessage = {
      ...message,
      id: `coord-${Date.now()}-${Math.random()}`,
      timestamp: Date.now(),
      ackReceived: false
    };

    this.coordinationMessages.push(fullMessage);

    // Emit coordination event
    await this.eventBus.emit({
      id: `event-${Date.now()}`,
      type: 'agent.assigned',
      workflowId: 'current',
      executionId: 'current',
      timestamp: Date.now(),
      data: fullMessage,
      source: 'AgentCoordinator'
    });

    this.logger.debug('Sent coordination message', { 
      type: message.type,
      target: message.target 
    });
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Handle agent status updates
    this.eventBus.on('agent.status_changed', (event) => {
      const { agentId, status } = event.data;
      const agent = this.resourcePool.agents.get(agentId);
      if (agent) {
        agent.status = status === 'active' ? 'available' : 'offline';
        this.logger.info('Agent status updated', { agentId, status });
      }
    });

    // Handle task completion
    this.eventBus.on('agent.completed', (event) => {
      const { agentId, taskId } = event.data;
      const agent = this.resourcePool.agents.get(agentId);
      if (agent) {
        agent.currentTasks = agent.currentTasks.filter(t => t !== taskId);
        agent.utilization = agent.currentTasks.length / agent.capacity;
        if (agent.status === 'busy' && agent.currentTasks.length === 0) {
          agent.status = 'available';
        }
      }
    });
  }

  /**
   * Find available agents matching requirements
   */
  private findAvailableAgents(requirement: AgentRequirement): AgentResource[] {
    return Array.from(this.resourcePool.agents.values())
      .filter(agent => 
        agent.type === requirement.type &&
        agent.status === 'available' &&
        agent.utilization < 0.9 &&
        this.hasRequiredCapabilities(agent, requirement.capabilities)
      )
      .sort((a, b) => {
        // Sort by utilization (prefer less utilized agents)
        if (a.utilization !== b.utilization) {
          return a.utilization - b.utilization;
        }
        // Then by last assigned time (prefer agents that haven't been used recently)
        return (a.lastAssigned || 0) - (b.lastAssigned || 0);
      });
  }

  /**
   * Check if agent has required capabilities
   */
  private hasRequiredCapabilities(agent: AgentResource, requiredCapabilities: string[]): boolean {
    // This would check against actual agent capabilities
    // For now, return true as placeholder
    return true;
  }

  /**
   * Execute task internally
   */
  private async executeTaskInternal(
    taskMessage: AgentTaskMessage,
    options: TaskExecutionOptions,
    signal: AbortSignal
  ): Promise<TaskExecutionResult> {
    const startTime = Date.now();

    try {
      // Update agent status
      const agent = this.resourcePool.agents.get(taskMessage.agentId);
      if (agent) {
        agent.status = 'busy';
        agent.currentTasks.push(taskMessage.taskId);
        agent.utilization = agent.currentTasks.length / agent.capacity;
        agent.lastAssigned = Date.now();
      }

      // Emit task started event
      await this.eventBus.emit({
        id: `event-${Date.now()}`,
        type: 'agent.assigned',
        workflowId: 'current',
        executionId: 'current',
        timestamp: Date.now(),
        data: { agentId: taskMessage.agentId, taskId: taskMessage.taskId },
        source: 'AgentCoordinator'
      });

      // Simulate task execution (in real implementation, would call actual agent)
      await this.simulateTaskExecution(taskMessage, options, signal);

      // Generate mock response
      const response: AgentResponseMessage = {
        content: JSON.stringify({ result: 'Task completed successfully' }),
        taskId: taskMessage.taskId,
        agentId: taskMessage.agentId,
        status: 'success',
        output: {
          content: `Generated content for task ${taskMessage.taskType}`,
          wordCount: 150,
          metadata: {}
        },
        confidence: 0.85,
        metrics: {
          tokensUsed: 500,
          processingTime: Date.now() - startTime,
          qualityScore: 0.9
        },
        name: 'agent_response',
        additional_kwargs: {}
      };

      // Emit task completed event
      await this.eventBus.emit({
        id: `event-${Date.now()}`,
        type: 'agent.completed',
        workflowId: 'current',
        executionId: 'current',
        timestamp: Date.now(),
        data: { 
          agentId: taskMessage.agentId, 
          taskId: taskMessage.taskId,
          metrics: response.metrics 
        },
        source: 'AgentCoordinator'
      });

      return {
        taskId: taskMessage.taskId,
        agentId: taskMessage.agentId,
        status: 'success',
        output: response.output,
        confidence: response.confidence,
        metrics: response.metrics
      };

    } catch (error) {
      // Handle task failure
      this.logger.error('Task execution failed', { 
        taskId: taskMessage.taskId,
        error 
      });

      await this.eventBus.emit({
        id: `event-${Date.now()}`,
        type: 'agent.failed',
        workflowId: 'current',
        executionId: 'current',
        timestamp: Date.now(),
        data: { 
          agentId: taskMessage.agentId, 
          taskId: taskMessage.taskId,
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        source: 'AgentCoordinator'
      });

      return {
        taskId: taskMessage.taskId,
        agentId: taskMessage.agentId,
        status: 'failure',
        error: error instanceof Error ? error.message : 'Unknown error'
      };

    } finally {
      // Update agent status
      const agent = this.resourcePool.agents.get(taskMessage.agentId);
      if (agent) {
        agent.currentTasks = agent.currentTasks.filter(t => t !== taskMessage.taskId);
        agent.utilization = agent.currentTasks.length / agent.capacity;
        if (agent.currentTasks.length === 0) {
          agent.status = 'available';
        }
      }
    }
  }

  /**
   * Simulate task execution
   */
  private async simulateTaskExecution(
    taskMessage: AgentTaskMessage,
    options: TaskExecutionOptions,
    signal: AbortSignal
  ): Promise<void> {
    // Simulate processing time based on task type
    const processingTimes: Record<AgentTaskType, number> = {
      generate_content: 5000,
      improve_content: 3000,
      review_content: 2000,
      analyze_requirements: 4000,
      generate_outline: 2000,
      fact_check: 3000,
      format_content: 1000,
      translate_content: 4000,
      extract_keywords: 1500,
      generate_summary: 2500
    };

    const baseTime = processingTimes[taskMessage.taskType] || 3000;
    const actualTime = baseTime + Math.random() * 2000; // Add some randomness

    // Simulate progress updates if streaming
    if (options.streaming && options.onProgress) {
      const intervals = 10;
      for (let i = 0; i < intervals; i++) {
        if (signal.aborted) {
          throw new Error('Task cancelled');
        }
        await new Promise(resolve => setTimeout(resolve, actualTime / intervals));
        options.onProgress((i + 1) / intervals * 100);
      }
    } else {
      // Simple delay
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(resolve, actualTime);
        signal.addEventListener('abort', () => {
          clearTimeout(timeout);
          reject(new Error('Task cancelled'));
        });
      });
    }
  }

  /**
   * Create timeout promise
   */
  private createTimeout(timeout: number, taskId: string): Promise<TaskExecutionResult> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Cancel the task
        const controller = this.activeExecutions.get(taskId);
        if (controller) {
          controller.abort();
        }

        resolve({
          taskId,
          agentId: '',
          status: 'timeout',
          error: `Task timed out after ${timeout}ms`
        });
      }, timeout);
    });
  }

  /**
   * Get coordinator status
   */
  public getStatus(): {
    totalAgents: number;
    availableAgents: number;
    busyAgents: number;
    queuedTasks: number;
    activeExecutions: number;
  } {
    const agents = Array.from(this.resourcePool.agents.values());
    
    return {
      totalAgents: agents.length,
      availableAgents: agents.filter(a => a.status === 'available').length,
      busyAgents: agents.filter(a => a.status === 'busy').length,
      queuedTasks: this.taskQueue.size,
      activeExecutions: this.activeExecutions.size
    };
  }
}