/**
 * State Manager
 * 
 * Manages workflow state persistence and retrieval
 */

import { OrchestrationState, WorkflowCheckpoint, WorkflowContext } from '../types';
import { Id } from '../../convex/_generated/dataModel';
import { Logger } from '../utils/Logger';

export class StateManager {
  private logger: Logger;
  private currentState: OrchestrationState | null = null;
  private checkpoints: Map<string, WorkflowCheckpoint> = new Map();
  private stateHistory: OrchestrationState[] = [];
  private maxHistorySize: number = 100;

  constructor() {
    this.logger = new Logger('StateManager');
  }

  /**
   * Initialize new workflow state
   */
  public initializeState(
    workflowId: string,
    executionId: string,
    context: WorkflowContext,
    initialVariables: Record<string, any> = {}
  ): OrchestrationState {
    const state: OrchestrationState = {
      workflowId,
      executionId,
      status: 'draft',
      completedSteps: [],
      blockedSteps: [],
      failedSteps: [],
      context,
      variables: initialVariables,
      results: {},
      activeAgents: [],
      agentMessages: [],
      errors: [],
      retryCount: {},
      startTime: Date.now(),
      lastUpdateTime: Date.now(),
      metrics: {
        stepsCompleted: 0,
        stepsFailed: 0,
        stepsSkipped: 0,
        totalDuration: 0,
        agentUtilization: {},
        resourceUsage: {
          tokensUsed: 0,
          apiCalls: 0,
          storageUsed: 0
        },
        qualityScores: {}
      },
      checkpoints: []
    };

    this.currentState = state;
    this.addToHistory(state);
    this.logger.info('Initialized workflow state', { workflowId, executionId });

    return state;
  }

  /**
   * Get current workflow state
   */
  public getCurrentState(): OrchestrationState | null {
    return this.currentState;
  }

  /**
   * Update workflow state
   */
  public updateState(updates: Partial<OrchestrationState>): OrchestrationState {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    // Deep merge updates into current state
    this.currentState = this.deepMerge(this.currentState, updates) as OrchestrationState;
    this.currentState.lastUpdateTime = Date.now();

    // Add to history
    this.addToHistory(this.currentState);

    this.logger.debug('Updated workflow state', { 
      workflowId: this.currentState.workflowId,
      updates 
    });

    return this.currentState;
  }

  /**
   * Create a checkpoint
   */
  public createCheckpoint(stepId: string, description: string): WorkflowCheckpoint {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    const checkpoint: WorkflowCheckpoint = {
      id: `checkpoint-${Date.now()}-${Math.random()}`,
      stepId,
      timestamp: Date.now(),
      state: this.cloneState(this.currentState),
      description
    };

    this.checkpoints.set(checkpoint.id, checkpoint);
    
    // Add checkpoint to current state
    this.currentState.checkpoints.push(checkpoint);

    this.logger.info('Created checkpoint', { 
      checkpointId: checkpoint.id,
      stepId,
      description 
    });

    return checkpoint;
  }

  /**
   * Restore from checkpoint
   */
  public restoreFromCheckpoint(checkpointId: string): OrchestrationState {
    const checkpoint = this.checkpoints.get(checkpointId);
    if (!checkpoint) {
      throw new Error(`Checkpoint not found: ${checkpointId}`);
    }

    // Restore state from checkpoint
    this.currentState = this.deepMerge({}, checkpoint.state) as OrchestrationState;
    this.currentState.lastUpdateTime = Date.now();

    // Add restoration to history
    this.addToHistory(this.currentState);

    this.logger.info('Restored from checkpoint', { 
      checkpointId,
      workflowId: this.currentState.workflowId 
    });

    return this.currentState;
  }

  /**
   * Get workflow execution history
   */
  public getHistory(limit?: number): OrchestrationState[] {
    const actualLimit = limit || this.stateHistory.length;
    return this.stateHistory.slice(-actualLimit);
  }

  /**
   * Clear workflow state
   */
  public clearState(): void {
    this.currentState = null;
    this.checkpoints.clear();
    this.stateHistory = [];
    this.logger.info('Cleared workflow state');
  }

  /**
   * Get current workflow ID
   */
  public getCurrentWorkflowId(): string {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }
    return this.currentState.workflowId;
  }

  /**
   * Get current execution ID
   */
  public getCurrentExecutionId(): string {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }
    return this.currentState.executionId;
  }

  /**
   * Get current step ID
   */
  public getCurrentStepId(): string | undefined {
    return this.currentState?.currentStepId;
  }

  /**
   * Add variable to state
   */
  public setVariable(name: string, value: any): void {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    this.currentState.variables[name] = value;
    this.currentState.lastUpdateTime = Date.now();

    this.logger.debug('Set variable', { name, value });
  }

  /**
   * Get variable from state
   */
  public getVariable(name: string): any {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    return this.currentState.variables[name];
  }

  /**
   * Add result to state
   */
  public addResult(key: string, value: any): void {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    this.currentState.results[key] = value;
    this.currentState.lastUpdateTime = Date.now();

    this.logger.debug('Added result', { key, value });
  }

  /**
   * Get result from state
   */
  public getResult(key: string): any {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    return this.currentState.results[key];
  }

  /**
   * Update metrics
   */
  public updateMetrics(updates: Partial<typeof this.currentState.metrics>): void {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    this.currentState.metrics = {
      ...this.currentState.metrics,
      ...updates
    };
    this.currentState.lastUpdateTime = Date.now();

    this.logger.debug('Updated metrics', { updates });
  }

  /**
   * Add error to state
   */
  public addError(error: typeof this.currentState.errors[0]): void {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    this.currentState.errors.push(error);
    this.currentState.lastUpdateTime = Date.now();

    this.logger.error('Added error to state', { error });
  }

  /**
   * Update tender status (external integration)
   */
  public async updateTenderStatus(tenderId: Id<'tenders'>, status: string): Promise<void> {
    // This would integrate with the Convex backend
    this.logger.info('Updating tender status', { tenderId, status });
    // Implementation would call Convex mutation
  }

  /**
   * Create follow-up tasks (external integration)
   */
  public async createFollowUpTasks(workflowId: string, tasks: any[]): Promise<void> {
    // This would integrate with the Convex backend
    this.logger.info('Creating follow-up tasks', { workflowId, taskCount: tasks.length });
    // Implementation would call Convex mutation
  }

  /**
   * Save workflow execution (external integration)
   */
  public async saveWorkflowExecution(execution: any): Promise<void> {
    // This would integrate with the Convex backend
    this.logger.info('Saving workflow execution', { 
      workflowId: execution.workflowId,
      executionId: execution.executionId 
    });
    // Implementation would call Convex mutation
  }

  /**
   * Load workflow state from storage
   */
  public async loadState(executionId: string): Promise<OrchestrationState | null> {
    // This would integrate with the Convex backend
    this.logger.info('Loading workflow state', { executionId });
    // Implementation would query Convex database
    return null;
  }

  /**
   * Save current state to storage
   */
  public async saveState(): Promise<void> {
    if (!this.currentState) {
      throw new Error('No active workflow state');
    }

    // This would integrate with the Convex backend
    this.logger.info('Saving workflow state', { 
      workflowId: this.currentState.workflowId,
      executionId: this.currentState.executionId 
    });
    // Implementation would call Convex mutation
  }

  /**
   * Deep merge objects
   */
  private deepMerge(target: any, source: any): any {
    const output = { ...target };
    
    if (this.isObject(target) && this.isObject(source)) {
      Object.keys(source).forEach(key => {
        if (this.isObject(source[key])) {
          if (!(key in target)) {
            Object.assign(output, { [key]: source[key] });
          } else {
            output[key] = this.deepMerge(target[key], source[key]);
          }
        } else if (Array.isArray(source[key])) {
          if (Array.isArray(target[key])) {
            // Merge arrays by concatenating and removing duplicates
            output[key] = [...new Set([...target[key], ...source[key]])];
          } else {
            output[key] = source[key];
          }
        } else {
          Object.assign(output, { [key]: source[key] });
        }
      });
    }
    
    return output;
  }

  /**
   * Check if value is object
   */
  private isObject(item: any): boolean {
    return item && typeof item === 'object' && !Array.isArray(item);
  }

  /**
   * Clone state object
   */
  private cloneState(state: OrchestrationState): Partial<OrchestrationState> {
    // Create a deep clone of essential state properties
    return {
      workflowId: state.workflowId,
      executionId: state.executionId,
      status: state.status,
      currentStepId: state.currentStepId,
      completedSteps: [...state.completedSteps],
      blockedSteps: [...state.blockedSteps],
      failedSteps: [...state.failedSteps],
      variables: { ...state.variables },
      results: { ...state.results },
      metrics: { ...state.metrics }
    };
  }

  /**
   * Add state to history
   */
  private addToHistory(state: OrchestrationState): void {
    // Clone state before adding to history
    const historicalState = JSON.parse(JSON.stringify(state));
    this.stateHistory.push(historicalState);

    // Maintain history size limit
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    }
  }

  /**
   * Get state summary
   */
  public getStateSummary(): any {
    if (!this.currentState) {
      return null;
    }

    return {
      workflowId: this.currentState.workflowId,
      executionId: this.currentState.executionId,
      status: this.currentState.status,
      currentStep: this.currentState.currentStepId,
      progress: {
        completed: this.currentState.completedSteps.length,
        failed: this.currentState.failedSteps.length,
        blocked: this.currentState.blockedSteps.length
      },
      duration: Date.now() - this.currentState.startTime,
      errorCount: this.currentState.errors.length,
      activeAgents: this.currentState.activeAgents.length
    };
  }
}