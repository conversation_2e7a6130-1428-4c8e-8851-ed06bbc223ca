/**
 * Orchestration Engine
 * 
 * Main entry point for the LangGraph-based workflow orchestration system
 */

import { WorkflowEngine } from './core/WorkflowEngine';
import { StateManager } from './state/StateManager';
import { AgentCoordinator } from './agents/AgentCoordinator';
import { EventBus } from './events/EventBus';
import { MonitoringService } from './monitoring/MonitoringService';
import { Logger } from './utils/Logger';
import { getWorkflowTemplate, listWorkflowTemplates } from './workflows/templates';
import {
  OrchestrationState,
  WorkflowContext,
  WorkflowTemplate,
  WorkflowEvent,
  WorkflowHealth,
  AgentResource
} from './types';
import { BidWritingAgent } from '../types/agent';
import { Id } from '../convex/_generated/dataModel';

export interface OrchestratorConfig {
  agents: BidWritingAgent[];
  enableMonitoring?: boolean;
  logLevel?: 'error' | 'warn' | 'info' | 'debug';
  metricsRetention?: number;
}

export interface WorkflowExecutionOptions {
  templateId: string;
  context: WorkflowContext;
  variables?: Record<string, any>;
  resumeFromCheckpoint?: string;
}

export class Orchestrator {
  private logger: Logger;
  private eventBus: EventBus;
  private stateManager: StateManager;
  private agentCoordinator: AgentCoordinator;
  private monitoringService: MonitoringService;
  private workflowEngines: Map<string, WorkflowEngine> = new Map();
  private config: OrchestratorConfig;
  private isInitialized: boolean = false;

  constructor(config: OrchestratorConfig) {
    this.config = config;
    this.logger = new Logger('Orchestrator');
    
    if (config.logLevel) {
      this.logger.setLevel(config.logLevel);
    }

    // Initialize core components
    this.eventBus = new EventBus();
    this.stateManager = new StateManager();
    this.agentCoordinator = new AgentCoordinator(this.eventBus);
    this.monitoringService = new MonitoringService(this.eventBus);
  }

  /**
   * Initialize the orchestrator
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    this.logger.info('Initializing orchestrator');

    try {
      // Initialize agent coordinator with available agents
      await this.agentCoordinator.initialize(this.config.agents);

      // Start monitoring if enabled
      if (this.config.enableMonitoring !== false) {
        this.monitoringService.start();
      }

      // Setup event handlers
      this.setupEventHandlers();

      this.isInitialized = true;
      this.logger.info('Orchestrator initialized successfully', {
        agentCount: this.config.agents.length,
        monitoringEnabled: this.config.enableMonitoring !== false
      });

    } catch (error) {
      this.logger.error('Failed to initialize orchestrator', error as Error);
      throw error;
    }
  }

  /**
   * Execute a workflow
   */
  public async executeWorkflow(options: WorkflowExecutionOptions): Promise<{
    executionId: string;
    status: 'success' | 'failed';
    results: Record<string, any>;
    errors?: any[];
  }> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    const executionId = `exec-${Date.now()}-${Math.random()}`;
    this.logger.info('Starting workflow execution', {
      executionId,
      templateId: options.templateId,
      workflowId: options.context.tenderId
    });

    try {
      // Get workflow template
      const template = getWorkflowTemplate(options.templateId);
      if (!template) {
        throw new Error(`Workflow template not found: ${options.templateId}`);
      }

      // Initialize or restore state
      let state: OrchestrationState;
      if (options.resumeFromCheckpoint) {
        state = this.stateManager.restoreFromCheckpoint(options.resumeFromCheckpoint);
      } else {
        state = this.stateManager.initializeState(
          options.context.tenderId,
          executionId,
          options.context,
          options.variables
        );
      }

      // Create workflow engine
      const engine = new WorkflowEngine(
        template,
        this.stateManager,
        this.agentCoordinator,
        this.eventBus,
        this.monitoringService
      );

      this.workflowEngines.set(executionId, engine);

      // Execute workflow
      const finalState = await engine.execute(state);

      // Clean up
      this.workflowEngines.delete(executionId);

      return {
        executionId,
        status: finalState.status === 'completed' ? 'success' : 'failed',
        results: finalState.results,
        errors: finalState.errors.length > 0 ? finalState.errors : undefined
      };

    } catch (error) {
      this.logger.error('Workflow execution failed', error as Error);
      
      // Clean up on error
      this.workflowEngines.delete(executionId);
      
      return {
        executionId,
        status: 'failed',
        results: {},
        errors: [error instanceof Error ? error.message : 'Unknown error']
      };
    }
  }

  /**
   * Pause a running workflow
   */
  public async pauseWorkflow(executionId: string): Promise<void> {
    const engine = this.workflowEngines.get(executionId);
    if (!engine) {
      throw new Error(`No running workflow found for execution: ${executionId}`);
    }

    await engine.pause();
    this.logger.info('Workflow paused', { executionId });
  }

  /**
   * Resume a paused workflow
   */
  public async resumeWorkflow(executionId: string): Promise<void> {
    const engine = this.workflowEngines.get(executionId);
    if (!engine) {
      throw new Error(`No paused workflow found for execution: ${executionId}`);
    }

    await engine.resume();
    this.logger.info('Workflow resumed', { executionId });
  }

  /**
   * Get workflow status
   */
  public getWorkflowStatus(executionId?: string): any {
    if (executionId) {
      const engine = this.workflowEngines.get(executionId);
      if (!engine) {
        return null;
      }
      return {
        executionId,
        ...engine.getStatus(),
        state: this.stateManager.getStateSummary()
      };
    }

    // Return all active workflows
    const activeWorkflows: any[] = [];
    this.workflowEngines.forEach((engine, execId) => {
      activeWorkflows.push({
        executionId: execId,
        ...engine.getStatus()
      });
    });

    return {
      activeWorkflows,
      totalActive: activeWorkflows.length
    };
  }

  /**
   * Get workflow health
   */
  public getWorkflowHealth(workflowId: string): WorkflowHealth | undefined {
    return this.monitoringService.getWorkflowHealth(workflowId);
  }

  /**
   * Get monitoring dashboard
   */
  public getMonitoringDashboard(): any {
    return {
      orchestrator: {
        initialized: this.isInitialized,
        activeWorkflows: this.workflowEngines.size,
        eventBusStatus: this.eventBus.getStatus(),
        agentCoordinatorStatus: this.agentCoordinator.getStatus()
      },
      monitoring: this.monitoringService.getDashboardData(),
      state: this.stateManager.getStateSummary()
    };
  }

  /**
   * Get available workflow templates
   */
  public getAvailableTemplates(): Array<{
    id: string;
    name: string;
    description: string;
    type: string;
  }> {
    return listWorkflowTemplates();
  }

  /**
   * Subscribe to workflow events
   */
  public subscribeToEvents(
    handler: (event: WorkflowEvent) => void,
    filter?: { types?: string[]; workflowId?: string }
  ): string {
    return this.eventBus.on('*', handler, (event) => {
      if (filter?.types && !filter.types.includes(event.type)) {
        return false;
      }
      if (filter?.workflowId && event.workflowId !== filter.workflowId) {
        return false;
      }
      return true;
    });
  }

  /**
   * Unsubscribe from events
   */
  public unsubscribeFromEvents(subscriptionId: string): boolean {
    return this.eventBus.off(subscriptionId);
  }

  /**
   * Create event stream
   */
  public createEventStream(filter?: {
    types?: string[];
    workflowId?: string;
  }): AsyncGenerator<WorkflowEvent> {
    return this.eventBus.createEventStream(filter);
  }

  /**
   * Get agent utilization
   */
  public getAgentUtilization(): Record<string, number> {
    return this.agentCoordinator.getUtilizationMetrics();
  }

  /**
   * Manually allocate agents
   */
  public async allocateAgents(
    requirements: any[],
    priority: 'low' | 'medium' | 'high' | 'urgent'
  ): Promise<AgentResource[]> {
    return this.agentCoordinator.allocateAgents(requirements, priority);
  }

  /**
   * Release allocated agents
   */
  public releaseAgents(agentIds: string[]): void {
    this.agentCoordinator.releaseAgents(agentIds);
  }

  /**
   * Create workflow checkpoint
   */
  public createCheckpoint(stepId: string, description: string): string {
    const checkpoint = this.stateManager.createCheckpoint(stepId, description);
    return checkpoint.id;
  }

  /**
   * Get workflow execution history
   */
  public getExecutionHistory(limit?: number): any[] {
    return this.stateManager.getHistory(limit);
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Log important workflow events
    this.eventBus.on('workflow.started', (event) => {
      this.logger.info('Workflow started', {
        workflowId: event.workflowId,
        executionId: event.executionId
      });
    });

    this.eventBus.on('workflow.completed', (event) => {
      this.logger.info('Workflow completed', {
        workflowId: event.workflowId,
        executionId: event.executionId,
        duration: event.data.duration
      });
    });

    this.eventBus.on('workflow.failed', (event) => {
      this.logger.error('Workflow failed', {
        workflowId: event.workflowId,
        executionId: event.executionId,
        error: event.data.error
      });
    });

    // Monitor critical errors
    this.eventBus.on('error.occurred', (event) => {
      if (event.data.alert?.severity === 'critical') {
        this.logger.error('Critical error detected', event.data);
      }
    });
  }

  /**
   * Shutdown the orchestrator
   */
  public async shutdown(): Promise<void> {
    this.logger.info('Shutting down orchestrator');

    // Stop all active workflows
    const activeEngines = Array.from(this.workflowEngines.values());
    await Promise.all(activeEngines.map(engine => engine.pause()));

    // Stop monitoring
    this.monitoringService.stop();

    // Clear state
    this.stateManager.clearState();

    // Clear workflow engines
    this.workflowEngines.clear();

    this.isInitialized = false;
    this.logger.info('Orchestrator shutdown complete');
  }
}

// Export types and utilities
export * from './types';
export { getWorkflowTemplate, listWorkflowTemplates } from './workflows/templates';
export { Logger } from './utils/Logger';