/**
 * Event Bus
 * 
 * Central event management system for workflow orchestration
 */

import { WorkflowEvent, WorkflowEventType } from '../types';
import { Logger } from '../utils/Logger';

type EventHandler = (event: WorkflowEvent) => void | Promise<void>;
type EventFilter = (event: WorkflowEvent) => boolean;

interface Subscription {
  id: string;
  eventType: WorkflowEventType | '*';
  handler: EventHandler;
  filter?: EventFilter;
  once: boolean;
}

interface EventMetrics {
  totalEvents: number;
  eventsByType: Record<WorkflowEventType, number>;
  processingTime: number[];
  errors: number;
}

export class EventBus {
  private subscriptions: Map<string, Subscription> = new Map();
  private eventHistory: WorkflowEvent[] = [];
  private maxHistorySize: number = 1000;
  private logger: Logger;
  private metrics: EventMetrics;
  private isPaused: boolean = false;
  private eventQueue: WorkflowEvent[] = [];
  private isProcessing: boolean = false;

  constructor() {
    this.logger = new Logger('EventBus');
    this.metrics = {
      totalEvents: 0,
      eventsByType: {} as Record<WorkflowEventType, number>,
      processingTime: [],
      errors: 0
    };
  }

  /**
   * Subscribe to events
   */
  public on(
    eventType: WorkflowEventType | '*',
    handler: EventHandler,
    filter?: EventFilter
  ): string {
    const subscriptionId = `sub-${Date.now()}-${Math.random()}`;
    
    const subscription: Subscription = {
      id: subscriptionId,
      eventType,
      handler,
      filter,
      once: false
    };

    this.subscriptions.set(subscriptionId, subscription);
    
    this.logger.debug('Added subscription', { 
      subscriptionId, 
      eventType,
      hasFilter: !!filter 
    });

    return subscriptionId;
  }

  /**
   * Subscribe to event once
   */
  public once(
    eventType: WorkflowEventType | '*',
    handler: EventHandler,
    filter?: EventFilter
  ): string {
    const subscriptionId = `sub-once-${Date.now()}-${Math.random()}`;
    
    const subscription: Subscription = {
      id: subscriptionId,
      eventType,
      handler,
      filter,
      once: true
    };

    this.subscriptions.set(subscriptionId, subscription);
    
    this.logger.debug('Added one-time subscription', { 
      subscriptionId, 
      eventType 
    });

    return subscriptionId;
  }

  /**
   * Unsubscribe from events
   */
  public off(subscriptionId: string): boolean {
    const removed = this.subscriptions.delete(subscriptionId);
    
    if (removed) {
      this.logger.debug('Removed subscription', { subscriptionId });
    }

    return removed;
  }

  /**
   * Emit an event
   */
  public async emit(event: WorkflowEvent): Promise<void> {
    if (this.isPaused) {
      this.eventQueue.push(event);
      this.logger.debug('Event queued (bus paused)', { 
        eventType: event.type,
        queueSize: this.eventQueue.length 
      });
      return;
    }

    await this.processEvent(event);
  }

  /**
   * Process a single event
   */
  private async processEvent(event: WorkflowEvent): Promise<void> {
    const startTime = Date.now();

    // Add to history
    this.addToHistory(event);

    // Update metrics
    this.metrics.totalEvents++;
    this.metrics.eventsByType[event.type] = 
      (this.metrics.eventsByType[event.type] || 0) + 1;

    this.logger.debug('Processing event', { 
      eventId: event.id,
      eventType: event.type,
      workflowId: event.workflowId 
    });

    // Get matching subscriptions
    const matchingSubscriptions = this.getMatchingSubscriptions(event);

    // Execute handlers
    const handlerPromises = matchingSubscriptions.map(async (subscription) => {
      try {
        await this.executeHandler(subscription, event);
        
        // Remove one-time subscriptions
        if (subscription.once) {
          this.subscriptions.delete(subscription.id);
        }
      } catch (error) {
        this.metrics.errors++;
        this.logger.error('Handler execution failed', {
          subscriptionId: subscription.id,
          eventType: event.type,
          error
        });
      }
    });

    // Wait for all handlers to complete
    await Promise.allSettled(handlerPromises);

    // Record processing time
    const processingTime = Date.now() - startTime;
    this.metrics.processingTime.push(processingTime);
    
    // Keep only last 100 processing times
    if (this.metrics.processingTime.length > 100) {
      this.metrics.processingTime.shift();
    }

    this.logger.debug('Event processed', { 
      eventId: event.id,
      handlerCount: matchingSubscriptions.length,
      processingTime 
    });
  }

  /**
   * Execute a handler with error handling
   */
  private async executeHandler(
    subscription: Subscription,
    event: WorkflowEvent
  ): Promise<void> {
    try {
      await subscription.handler(event);
    } catch (error) {
      // Log error but don't throw - allow other handlers to execute
      this.logger.error('Event handler error', {
        subscriptionId: subscription.id,
        eventType: event.type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      // Re-emit error as event
      const errorEvent: WorkflowEvent = {
        id: `error-${Date.now()}`,
        type: 'error.occurred',
        workflowId: event.workflowId,
        executionId: event.executionId,
        timestamp: Date.now(),
        data: {
          originalEvent: event,
          error: error instanceof Error ? {
            message: error.message,
            stack: error.stack
          } : { message: 'Unknown error' }
        },
        source: 'EventBus'
      };

      // Emit error event without waiting (to avoid recursion)
      this.processEvent(errorEvent).catch(() => {
        // Ignore errors from error event processing
      });
    }
  }

  /**
   * Get subscriptions matching an event
   */
  private getMatchingSubscriptions(event: WorkflowEvent): Subscription[] {
    return Array.from(this.subscriptions.values()).filter(subscription => {
      // Check event type match
      if (subscription.eventType !== '*' && subscription.eventType !== event.type) {
        return false;
      }

      // Apply custom filter if provided
      if (subscription.filter && !subscription.filter(event)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Pause event processing
   */
  public pause(): void {
    this.isPaused = true;
    this.logger.info('Event bus paused');
  }

  /**
   * Resume event processing
   */
  public async resume(): Promise<void> {
    this.isPaused = false;
    this.logger.info('Event bus resumed', { 
      queuedEvents: this.eventQueue.length 
    });

    // Process queued events
    if (this.eventQueue.length > 0 && !this.isProcessing) {
      await this.processQueue();
    }
  }

  /**
   * Process queued events
   */
  private async processQueue(): Promise<void> {
    if (this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.eventQueue.length > 0 && !this.isPaused) {
        const event = this.eventQueue.shift()!;
        await this.processEvent(event);
      }
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Get event history
   */
  public getHistory(
    filter?: {
      type?: WorkflowEventType;
      workflowId?: string;
      startTime?: number;
      endTime?: number;
    }
  ): WorkflowEvent[] {
    let history = [...this.eventHistory];

    if (filter) {
      if (filter.type) {
        history = history.filter(e => e.type === filter.type);
      }
      if (filter.workflowId) {
        history = history.filter(e => e.workflowId === filter.workflowId);
      }
      if (filter.startTime) {
        history = history.filter(e => e.timestamp >= filter.startTime!);
      }
      if (filter.endTime) {
        history = history.filter(e => e.timestamp <= filter.endTime!);
      }
    }

    return history;
  }

  /**
   * Clear event history
   */
  public clearHistory(): void {
    this.eventHistory = [];
    this.logger.info('Event history cleared');
  }

  /**
   * Get event metrics
   */
  public getMetrics(): EventMetrics & {
    averageProcessingTime: number;
    subscriptionCount: number;
    queueSize: number;
  } {
    const avgProcessingTime = this.metrics.processingTime.length > 0
      ? this.metrics.processingTime.reduce((a, b) => a + b, 0) / this.metrics.processingTime.length
      : 0;

    return {
      ...this.metrics,
      averageProcessingTime: avgProcessingTime,
      subscriptionCount: this.subscriptions.size,
      queueSize: this.eventQueue.length
    };
  }

  /**
   * Reset metrics
   */
  public resetMetrics(): void {
    this.metrics = {
      totalEvents: 0,
      eventsByType: {} as Record<WorkflowEventType, number>,
      processingTime: [],
      errors: 0
    };
    this.logger.info('Event metrics reset');
  }

  /**
   * Add event to history
   */
  private addToHistory(event: WorkflowEvent): void {
    this.eventHistory.push(event);

    // Maintain history size limit
    if (this.eventHistory.length > this.maxHistorySize) {
      this.eventHistory.shift();
    }
  }

  /**
   * Wait for specific event
   */
  public async waitFor(
    eventType: WorkflowEventType,
    filter?: EventFilter,
    timeout?: number
  ): Promise<WorkflowEvent> {
    return new Promise((resolve, reject) => {
      let subscriptionId: string;
      let timeoutId: NodeJS.Timeout | undefined;

      // Setup timeout if provided
      if (timeout) {
        timeoutId = setTimeout(() => {
          this.off(subscriptionId);
          reject(new Error(`Timeout waiting for event: ${eventType}`));
        }, timeout);
      }

      // Subscribe to event
      subscriptionId = this.once(eventType, (event) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        resolve(event);
      }, filter);
    });
  }

  /**
   * Create event stream
   */
  public createEventStream(
    filter?: {
      types?: WorkflowEventType[];
      workflowId?: string;
    }
  ): AsyncGenerator<WorkflowEvent> {
    const events: WorkflowEvent[] = [];
    let resolveNext: ((value: IteratorResult<WorkflowEvent>) => void) | null = null;

    // Subscribe to all events
    const subscriptionId = this.on('*', (event) => {
      // Apply filter
      if (filter) {
        if (filter.types && !filter.types.includes(event.type)) {
          return;
        }
        if (filter.workflowId && event.workflowId !== filter.workflowId) {
          return;
        }
      }

      // Add to queue or resolve waiting promise
      if (resolveNext) {
        resolveNext({ value: event, done: false });
        resolveNext = null;
      } else {
        events.push(event);
      }
    });

    // Return async generator
    return {
      async next(): Promise<IteratorResult<WorkflowEvent>> {
        // Return queued event if available
        if (events.length > 0) {
          return { value: events.shift()!, done: false };
        }

        // Wait for next event
        return new Promise((resolve) => {
          resolveNext = resolve;
        });
      },

      async return(): Promise<IteratorResult<WorkflowEvent>> {
        // Cleanup subscription
        this.off(subscriptionId);
        return { value: undefined as any, done: true };
      },

      [Symbol.asyncIterator]() {
        return this;
      }
    } as AsyncGenerator<WorkflowEvent>;
  }

  /**
   * Get event bus status
   */
  public getStatus(): {
    isPaused: boolean;
    subscriptionCount: number;
    queueSize: number;
    historySize: number;
    isProcessing: boolean;
  } {
    return {
      isPaused: this.isPaused,
      subscriptionCount: this.subscriptions.size,
      queueSize: this.eventQueue.length,
      historySize: this.eventHistory.length,
      isProcessing: this.isProcessing
    };
  }
}