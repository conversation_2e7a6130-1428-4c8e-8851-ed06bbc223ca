/**
 * Logger
 * 
 * Structured logging utility for orchestration engine
 */

import winston from 'winston';

type LogLevel = 'error' | 'warn' | 'info' | 'debug';

interface LogContext {
  [key: string]: any;
}

export class Logger {
  private winston: winston.Logger;
  private component: string;

  constructor(component: string) {
    this.component = component;

    // Create winston logger instance
    this.winston = winston.createLogger({
      level: process.env.LOG_LEVEL || 'info',
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { component },
      transports: [
        // Console transport with color
        new winston.transports.Console({
          format: winston.format.combine(
            winston.format.colorize(),
            winston.format.simple(),
            winston.format.printf(({ timestamp, level, component, message, ...meta }) => {
              const metaStr = Object.keys(meta).length > 0 ? 
                ` ${JSON.stringify(meta)}` : '';
              return `${timestamp} [${component}] ${level}: ${message}${metaStr}`;
            })
          )
        })
      ]
    });

    // Add file transport in production
    if (process.env.NODE_ENV === 'production') {
      this.winston.add(new winston.transports.File({
        filename: 'logs/error.log',
        level: 'error',
        maxsize: 5242880, // 5MB
        maxFiles: 5
      }));

      this.winston.add(new winston.transports.File({
        filename: 'logs/combined.log',
        maxsize: 5242880, // 5MB
        maxFiles: 5
      }));
    }
  }

  /**
   * Log error message
   */
  public error(message: string, context?: LogContext | Error): void {
    if (context instanceof Error) {
      this.winston.error(message, {
        error: {
          message: context.message,
          stack: context.stack,
          name: context.name
        }
      });
    } else {
      this.winston.error(message, context);
    }
  }

  /**
   * Log warning message
   */
  public warn(message: string, context?: LogContext): void {
    this.winston.warn(message, context);
  }

  /**
   * Log info message
   */
  public info(message: string, context?: LogContext): void {
    this.winston.info(message, context);
  }

  /**
   * Log debug message
   */
  public debug(message: string, context?: LogContext): void {
    this.winston.debug(message, context);
  }

  /**
   * Create child logger with additional context
   */
  public child(context: LogContext): Logger {
    const childLogger = new Logger(`${this.component}:${context.subcomponent || 'child'}`);
    childLogger.winston.defaultMeta = {
      ...childLogger.winston.defaultMeta,
      ...context
    };
    return childLogger;
  }

  /**
   * Log with custom level
   */
  public log(level: LogLevel, message: string, context?: LogContext): void {
    this.winston.log(level, message, context);
  }

  /**
   * Start timer for performance logging
   */
  public startTimer(): () => void {
    const start = Date.now();
    return () => {
      const duration = Date.now() - start;
      return duration;
    };
  }

  /**
   * Log performance metric
   */
  public performance(operation: string, duration: number, context?: LogContext): void {
    this.info(`Performance: ${operation}`, {
      ...context,
      duration,
      operation
    });
  }

  /**
   * Log audit event
   */
  public audit(action: string, userId: string, context?: LogContext): void {
    this.info(`Audit: ${action}`, {
      ...context,
      action,
      userId,
      timestamp: new Date().toISOString(),
      audit: true
    });
  }

  /**
   * Set log level
   */
  public setLevel(level: LogLevel): void {
    this.winston.level = level;
  }

  /**
   * Get current log level
   */
  public getLevel(): string {
    return this.winston.level;
  }
}