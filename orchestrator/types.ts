/**
 * Orchestration Engine Type Definitions
 * 
 * Core types for the LangGraph-based workflow orchestration system
 */

import { BaseMessage } from '@langchain/core/messages';
import { Id } from '../convex/_generated/dataModel';
import { AgentTaskType, AgentType, BidWritingAgent } from '../types/agent';
import { WorkflowStatus, WorkflowStepStatus, WorkflowPriority } from '../types/workflow';

// Orchestration state types
export interface OrchestrationState {
  // Core workflow state
  workflowId: string;
  executionId: string;
  status: WorkflowStatus;
  currentStepId?: string;
  completedSteps: string[];
  blockedSteps: string[];
  failedSteps: string[];
  
  // Context and data
  context: WorkflowContext;
  variables: Record<string, any>;
  results: Record<string, any>;
  
  // Agent management
  activeAgents: AgentAllocation[];
  agentMessages: AgentMessage[];
  
  // Error handling
  errors: WorkflowError[];
  retryCount: Record<string, number>;
  
  // Timing
  startTime: number;
  lastUpdateTime: number;
  estimatedCompletion?: number;
  
  // Monitoring
  metrics: WorkflowMetrics;
  checkpoints: WorkflowCheckpoint[];
}

export interface WorkflowContext {
  tenderId: Id<'tenders'>;
  userId: string;
  priority: WorkflowPriority;
  deadline?: number;
  metadata: {
    clientName: string;
    tenderName: string;
    submissionDeadline?: number;
    estimatedValue?: number;
    tags: string[];
  };
}

export interface AgentAllocation {
  agentId: string;
  agentType: AgentType;
  taskId: string;
  taskType: AgentTaskType;
  status: 'assigned' | 'working' | 'completed' | 'failed';
  assignedAt: number;
  startedAt?: number;
  completedAt?: number;
}

export interface AgentMessage {
  id: string;
  agentId: string;
  taskId: string;
  type: 'request' | 'response' | 'status' | 'error';
  content: string;
  timestamp: number;
  metadata?: Record<string, any>;
}

export interface WorkflowError {
  id: string;
  stepId?: string;
  agentId?: string;
  code: string;
  message: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  stack?: string;
  recoverable: boolean;
  retryable: boolean;
}

export interface WorkflowMetrics {
  stepsCompleted: number;
  stepsFailed: number;
  stepsSkipped: number;
  totalDuration: number;
  agentUtilization: Record<string, number>;
  resourceUsage: {
    tokensUsed: number;
    apiCalls: number;
    storageUsed: number;
  };
  qualityScores: Record<string, number>;
}

export interface WorkflowCheckpoint {
  id: string;
  stepId: string;
  timestamp: number;
  state: Partial<OrchestrationState>;
  description: string;
}

// Graph node types
export type WorkflowNode = 
  | 'start'
  | 'ingestion'
  | 'scheduling'
  | 'content_generation'
  | 'meeting_execution'
  | 'post_processing'
  | 'workflow_advancement'
  | 'error_handler'
  | 'end';

export interface NodeConfig {
  id: string;
  type: WorkflowNode;
  name: string;
  description: string;
  requiredAgents: AgentRequirement[];
  inputs: NodeInput[];
  outputs: NodeOutput[];
  timeout: number;
  retryPolicy: RetryPolicy;
  conditions?: NodeCondition[];
}

export interface AgentRequirement {
  type: AgentType;
  capabilities: string[];
  minCount: number;
  maxCount?: number;
  priority: 'required' | 'preferred' | 'optional';
}

export interface NodeInput {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  source?: 'context' | 'previous_step' | 'variable' | 'constant';
  path?: string;
  defaultValue?: any;
}

export interface NodeOutput {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  destination: 'result' | 'variable' | 'context';
  path: string;
}

export interface RetryPolicy {
  maxAttempts: number;
  backoffStrategy: 'linear' | 'exponential' | 'fixed';
  initialDelay: number;
  maxDelay: number;
  retryableErrors?: string[];
}

export interface NodeCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'exists';
  value: any;
  onTrue?: string; // Next node ID
  onFalse?: string; // Next node ID
}

// Message types for agent communication
export interface AgentTaskMessage extends BaseMessage {
  taskId: string;
  agentId: string;
  taskType: AgentTaskType;
  priority: WorkflowPriority;
  deadline?: number;
  input: any;
}

export interface AgentResponseMessage extends BaseMessage {
  taskId: string;
  agentId: string;
  status: 'success' | 'failure' | 'partial';
  output?: any;
  error?: string;
  confidence?: number;
  metrics?: {
    tokensUsed: number;
    processingTime: number;
    qualityScore?: number;
  };
}

// Workflow templates
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  type: 'tender_processing' | 'bid_writing' | 'review_approval' | 'custom';
  nodes: NodeConfig[];
  edges: WorkflowEdge[];
  variables: WorkflowVariable[];
  settings: WorkflowSettings;
}

export interface WorkflowEdge {
  id: string;
  source: string;
  target: string;
  condition?: EdgeCondition;
  weight?: number;
}

export interface EdgeCondition {
  type: 'success' | 'failure' | 'conditional';
  expression?: string;
  field?: string;
  operator?: string;
  value?: any;
}

export interface WorkflowVariable {
  name: string;
  type: string;
  description: string;
  defaultValue?: any;
  required: boolean;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    enum?: any[];
  };
}

export interface WorkflowSettings {
  timeout: number;
  maxRetries: number;
  errorHandling: 'stop' | 'continue' | 'rollback';
  parallelism: {
    enabled: boolean;
    maxConcurrent: number;
  };
  monitoring: {
    logLevel: 'debug' | 'info' | 'warn' | 'error';
    metricsEnabled: boolean;
    checkpointFrequency: number;
  };
  notifications: NotificationSettings[];
}

export interface NotificationSettings {
  type: 'email' | 'slack' | 'webhook' | 'internal';
  events: ('start' | 'complete' | 'error' | 'milestone')[];
  recipients: string[];
  template?: string;
  webhookUrl?: string;
}

// Event types
export interface WorkflowEvent {
  id: string;
  type: WorkflowEventType;
  workflowId: string;
  executionId: string;
  timestamp: number;
  data: any;
  source: string;
}

export type WorkflowEventType = 
  | 'workflow.started'
  | 'workflow.completed'
  | 'workflow.failed'
  | 'workflow.paused'
  | 'workflow.resumed'
  | 'step.started'
  | 'step.completed'
  | 'step.failed'
  | 'step.retrying'
  | 'agent.assigned'
  | 'agent.completed'
  | 'agent.failed'
  | 'checkpoint.created'
  | 'error.occurred'
  | 'metric.recorded';

// Monitoring types
export interface WorkflowHealth {
  workflowId: string;
  status: 'healthy' | 'degraded' | 'critical';
  lastCheck: number;
  metrics: {
    successRate: number;
    averageExecutionTime: number;
    errorRate: number;
    throughput: number;
  };
  issues: HealthIssue[];
}

export interface HealthIssue {
  type: 'performance' | 'error_rate' | 'timeout' | 'resource';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  affectedComponents: string[];
  recommendation?: string;
}

// Coordination types
export interface CoordinationMessage {
  id: string;
  type: 'task_assignment' | 'status_update' | 'resource_request' | 'completion_notice';
  source: string;
  target: string;
  timestamp: number;
  payload: any;
  requiresAck: boolean;
  ackReceived?: boolean;
}

export interface ResourcePool {
  agents: Map<string, AgentResource>;
  locks: Map<string, ResourceLock>;
  queue: ResourceRequest[];
}

export interface AgentResource {
  agentId: string;
  type: AgentType;
  status: 'available' | 'busy' | 'reserved' | 'offline';
  currentTasks: string[];
  capacity: number;
  utilization: number;
  lastAssigned?: number;
}

export interface ResourceLock {
  resourceId: string;
  ownerId: string;
  type: 'exclusive' | 'shared';
  acquiredAt: number;
  expiresAt: number;
}

export interface ResourceRequest {
  id: string;
  requesterId: string;
  resourceType: AgentType;
  priority: WorkflowPriority;
  requirements: AgentRequirement;
  requestedAt: number;
  fulfilled?: boolean;
}