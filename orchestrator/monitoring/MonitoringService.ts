/**
 * Monitoring Service
 * 
 * Comprehensive monitoring and health checking for workflow orchestration
 */

import { 
  WorkflowHealth, 
  HealthIssue,
  WorkflowMetrics,
  WorkflowEvent
} from '../types';
import { EventBus } from '../events/EventBus';
import { Logger } from '../utils/Logger';

interface MetricPoint {
  timestamp: number;
  value: number;
  labels?: Record<string, string>;
}

interface HealthCheck {
  name: string;
  checker: () => Promise<boolean>;
  critical: boolean;
  interval: number;
}

interface Alert {
  id: string;
  type: 'performance' | 'error_rate' | 'resource' | 'health';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  resolved: boolean;
  metadata?: Record<string, any>;
}

export class MonitoringService {
  private eventBus: EventBus;
  private logger: Logger;
  private metrics: Map<string, MetricPoint[]> = new Map();
  private healthChecks: Map<string, HealthCheck> = new Map();
  private healthStatus: Map<string, WorkflowHealth> = new Map();
  private alerts: Map<string, Alert> = new Map();
  private checkIntervals: Map<string, NodeJS.Timeout> = new Map();
  private metricsRetention: number = 3600000; // 1 hour
  private isRunning: boolean = false;

  constructor(eventBus: EventBus) {
    this.eventBus = eventBus;
    this.logger = new Logger('MonitoringService');
    
    // Setup default health checks
    this.setupDefaultHealthChecks();
    
    // Subscribe to workflow events
    this.setupEventHandlers();
  }

  /**
   * Start monitoring service
   */
  public start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;
    this.logger.info('Starting monitoring service');

    // Start health checks
    this.healthChecks.forEach((check, name) => {
      this.startHealthCheck(name, check);
    });

    // Start metrics cleanup
    this.startMetricsCleanup();
  }

  /**
   * Stop monitoring service
   */
  public stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    this.logger.info('Stopping monitoring service');

    // Clear all intervals
    this.checkIntervals.forEach(interval => clearInterval(interval));
    this.checkIntervals.clear();
  }

  /**
   * Record a metric
   */
  public recordMetric(
    name: string, 
    value: number, 
    labels?: Record<string, string>
  ): void {
    const point: MetricPoint = {
      timestamp: Date.now(),
      value,
      labels
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    this.metrics.get(name)!.push(point);

    // Check for anomalies
    this.checkMetricAnomaly(name, value);
  }

  /**
   * Record workflow metrics
   */
  public recordWorkflowMetrics(
    workflowId: string,
    metrics: Partial<WorkflowMetrics>
  ): void {
    Object.entries(metrics).forEach(([key, value]) => {
      if (typeof value === 'number') {
        this.recordMetric(`workflow.${key}`, value, { workflowId });
      }
    });
  }

  /**
   * Add custom health check
   */
  public addHealthCheck(
    name: string,
    checker: () => Promise<boolean>,
    critical: boolean = false,
    interval: number = 30000
  ): void {
    const check: HealthCheck = {
      name,
      checker,
      critical,
      interval
    };

    this.healthChecks.set(name, check);

    if (this.isRunning) {
      this.startHealthCheck(name, check);
    }

    this.logger.info('Added health check', { name, critical, interval });
  }

  /**
   * Get workflow health status
   */
  public getWorkflowHealth(workflowId: string): WorkflowHealth | undefined {
    return this.healthStatus.get(workflowId);
  }

  /**
   * Get all health statuses
   */
  public getAllHealthStatuses(): Map<string, WorkflowHealth> {
    return new Map(this.healthStatus);
  }

  /**
   * Get metrics
   */
  public getMetrics(
    name: string,
    startTime?: number,
    endTime?: number
  ): MetricPoint[] {
    const points = this.metrics.get(name) || [];
    
    if (!startTime && !endTime) {
      return points;
    }

    return points.filter(point => {
      if (startTime && point.timestamp < startTime) return false;
      if (endTime && point.timestamp > endTime) return false;
      return true;
    });
  }

  /**
   * Get metric statistics
   */
  public getMetricStats(name: string, windowMs: number = 300000): {
    count: number;
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  } | null {
    const cutoff = Date.now() - windowMs;
    const points = this.getMetrics(name, cutoff);

    if (points.length === 0) {
      return null;
    }

    const values = points.map(p => p.value).sort((a, b) => a - b);
    const sum = values.reduce((a, b) => a + b, 0);

    return {
      count: values.length,
      min: values[0],
      max: values[values.length - 1],
      avg: sum / values.length,
      p50: this.percentile(values, 0.5),
      p95: this.percentile(values, 0.95),
      p99: this.percentile(values, 0.99)
    };
  }

  /**
   * Create alert
   */
  public createAlert(
    type: Alert['type'],
    severity: Alert['severity'],
    message: string,
    metadata?: Record<string, any>
  ): string {
    const alert: Alert = {
      id: `alert-${Date.now()}-${Math.random()}`,
      type,
      severity,
      message,
      timestamp: Date.now(),
      resolved: false,
      metadata
    };

    this.alerts.set(alert.id, alert);

    // Emit alert event
    this.eventBus.emit({
      id: `event-${Date.now()}`,
      type: 'error.occurred',
      workflowId: metadata?.workflowId || 'system',
      executionId: metadata?.executionId || 'system',
      timestamp: Date.now(),
      data: { alert },
      source: 'MonitoringService'
    });

    this.logger.warn('Alert created', { 
      alertId: alert.id,
      type,
      severity,
      message 
    });

    return alert.id;
  }

  /**
   * Resolve alert
   */
  public resolveAlert(alertId: string): void {
    const alert = this.alerts.get(alertId);
    if (alert) {
      alert.resolved = true;
      this.logger.info('Alert resolved', { alertId });
    }
  }

  /**
   * Get active alerts
   */
  public getActiveAlerts(): Alert[] {
    return Array.from(this.alerts.values())
      .filter(alert => !alert.resolved)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Setup default health checks
   */
  private setupDefaultHealthChecks(): void {
    // Event bus health
    this.addHealthCheck(
      'event_bus',
      async () => {
        const status = this.eventBus.getStatus();
        return !status.isPaused && status.queueSize < 1000;
      },
      true,
      10000
    );

    // Memory usage health
    this.addHealthCheck(
      'memory_usage',
      async () => {
        const usage = process.memoryUsage();
        const heapUsedPercent = (usage.heapUsed / usage.heapTotal) * 100;
        return heapUsedPercent < 90;
      },
      true,
      30000
    );

    // Metrics storage health
    this.addHealthCheck(
      'metrics_storage',
      async () => {
        let totalPoints = 0;
        this.metrics.forEach(points => {
          totalPoints += points.length;
        });
        return totalPoints < 100000; // Max 100k metric points
      },
      false,
      60000
    );
  }

  /**
   * Setup event handlers
   */
  private setupEventHandlers(): void {
    // Track workflow lifecycle events
    this.eventBus.on('workflow.started', (event) => {
      const workflowId = event.workflowId;
      this.healthStatus.set(workflowId, {
        workflowId,
        status: 'healthy',
        lastCheck: Date.now(),
        metrics: {
          successRate: 100,
          averageExecutionTime: 0,
          errorRate: 0,
          throughput: 0
        },
        issues: []
      });
    });

    // Track step completions
    this.eventBus.on('step.completed', (event) => {
      this.recordMetric('step.duration', event.data.duration, {
        workflowId: event.workflowId,
        stepId: event.data.stepId
      });
    });

    // Track errors
    this.eventBus.on('step.failed', (event) => {
      this.recordMetric('step.errors', 1, {
        workflowId: event.workflowId,
        stepId: event.data.stepId
      });
      
      // Update health status
      this.updateWorkflowHealth(event.workflowId);
    });

    // Track agent metrics
    this.eventBus.on('agent.completed', (event) => {
      if (event.data.metrics) {
        this.recordMetric('agent.tokens', event.data.metrics.tokensUsed, {
          agentId: event.data.agentId
        });
        this.recordMetric('agent.processing_time', event.data.metrics.processingTime, {
          agentId: event.data.agentId
        });
      }
    });
  }

  /**
   * Start a health check
   */
  private startHealthCheck(name: string, check: HealthCheck): void {
    const runCheck = async () => {
      try {
        const healthy = await check.checker();
        
        if (!healthy) {
          this.createAlert(
            'health',
            check.critical ? 'critical' : 'medium',
            `Health check failed: ${name}`
          );
        }
        
        this.recordMetric(`health.${name}`, healthy ? 1 : 0);
      } catch (error) {
        this.logger.error('Health check error', { name, error });
        this.recordMetric(`health.${name}`, 0);
      }
    };

    // Run immediately
    runCheck();

    // Schedule periodic runs
    const interval = setInterval(runCheck, check.interval);
    this.checkIntervals.set(name, interval);
  }

  /**
   * Update workflow health status
   */
  private updateWorkflowHealth(workflowId: string): void {
    const health = this.healthStatus.get(workflowId);
    if (!health) return;

    // Calculate metrics from recent data
    const recentWindow = 300000; // 5 minutes
    const stepDurations = this.getMetrics('step.duration', Date.now() - recentWindow)
      .filter(p => p.labels?.workflowId === workflowId);
    const stepErrors = this.getMetrics('step.errors', Date.now() - recentWindow)
      .filter(p => p.labels?.workflowId === workflowId);

    // Update metrics
    if (stepDurations.length > 0) {
      const totalDuration = stepDurations.reduce((sum, p) => sum + p.value, 0);
      health.metrics.averageExecutionTime = totalDuration / stepDurations.length;
    }

    const totalSteps = stepDurations.length;
    const errorCount = stepErrors.length;
    
    health.metrics.errorRate = totalSteps > 0 ? (errorCount / totalSteps) * 100 : 0;
    health.metrics.successRate = 100 - health.metrics.errorRate;
    health.metrics.throughput = totalSteps / (recentWindow / 1000 / 60); // Steps per minute

    // Determine health status
    const issues: HealthIssue[] = [];

    if (health.metrics.errorRate > 50) {
      issues.push({
        type: 'error_rate',
        severity: 'critical',
        message: `High error rate: ${health.metrics.errorRate.toFixed(1)}%`,
        affectedComponents: ['workflow'],
        recommendation: 'Investigate failing steps and agent errors'
      });
    } else if (health.metrics.errorRate > 20) {
      issues.push({
        type: 'error_rate',
        severity: 'high',
        message: `Elevated error rate: ${health.metrics.errorRate.toFixed(1)}%`,
        affectedComponents: ['workflow'],
        recommendation: 'Monitor error trends'
      });
    }

    if (health.metrics.averageExecutionTime > 300000) { // 5 minutes
      issues.push({
        type: 'performance',
        severity: 'medium',
        message: `Slow execution time: ${(health.metrics.averageExecutionTime / 1000).toFixed(1)}s`,
        affectedComponents: ['workflow'],
        recommendation: 'Consider optimizing slow steps'
      });
    }

    health.issues = issues;
    health.status = issues.some(i => i.severity === 'critical') ? 'critical' :
                   issues.some(i => i.severity === 'high') ? 'degraded' : 'healthy';
    health.lastCheck = Date.now();
  }

  /**
   * Check for metric anomalies
   */
  private checkMetricAnomaly(name: string, value: number): void {
    const stats = this.getMetricStats(name, 3600000); // 1 hour window
    if (!stats || stats.count < 10) return;

    // Simple anomaly detection using standard deviation
    const values = this.getMetrics(name, Date.now() - 3600000).map(p => p.value);
    const mean = stats.avg;
    const variance = values.reduce((sum, v) => sum + Math.pow(v - mean, 2), 0) / values.length;
    const stdDev = Math.sqrt(variance);

    // Check if value is outside 3 standard deviations
    if (Math.abs(value - mean) > 3 * stdDev) {
      this.createAlert(
        'performance',
        'medium',
        `Anomaly detected in metric ${name}: value ${value} is outside normal range`,
        { metric: name, value, mean, stdDev }
      );
    }
  }

  /**
   * Start metrics cleanup
   */
  private startMetricsCleanup(): void {
    setInterval(() => {
      const cutoff = Date.now() - this.metricsRetention;
      
      this.metrics.forEach((points, name) => {
        const filtered = points.filter(p => p.timestamp > cutoff);
        if (filtered.length !== points.length) {
          this.metrics.set(name, filtered);
        }
      });

      // Clean up old resolved alerts
      const alertCutoff = Date.now() - 86400000; // 24 hours
      this.alerts.forEach((alert, id) => {
        if (alert.resolved && alert.timestamp < alertCutoff) {
          this.alerts.delete(id);
        }
      });
    }, 60000); // Run every minute
  }

  /**
   * Calculate percentile
   */
  private percentile(values: number[], p: number): number {
    const index = Math.ceil(values.length * p) - 1;
    return values[Math.max(0, index)];
  }

  /**
   * Get monitoring dashboard data
   */
  public getDashboardData(): {
    workflows: Map<string, WorkflowHealth>;
    metrics: Record<string, any>;
    alerts: Alert[];
    healthChecks: Record<string, boolean>;
  } {
    const healthChecks: Record<string, boolean> = {};
    
    this.healthChecks.forEach((_, name) => {
      const metric = this.getMetrics(`health.${name}`, Date.now() - 60000);
      healthChecks[name] = metric.length > 0 && metric[metric.length - 1].value === 1;
    });

    const keyMetrics: Record<string, any> = {};
    ['step.duration', 'step.errors', 'agent.tokens', 'agent.processing_time'].forEach(metric => {
      const stats = this.getMetricStats(metric);
      if (stats) {
        keyMetrics[metric] = stats;
      }
    });

    return {
      workflows: this.healthStatus,
      metrics: keyMetrics,
      alerts: this.getActiveAlerts(),
      healthChecks
    };
  }
}