/**
 * Scheduler Type Definitions
 * 
 * Interfaces for the intelligent meeting scheduling system
 */

import { Id } from '../convex/_generated/dataModel';

// Meeting types and enums
export type MeetingType = 
  | 'tender_kickoff'
  | 'technical_review'
  | 'bid_review'
  | 'client_meeting'
  | 'internal_sync'
  | 'training'
  | 'workshop'
  | 'presentation';

export type MeetingStatus = 
  | 'draft'
  | 'scheduled'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'postponed';

export type LocationType = 
  | 'physical'
  | 'online'
  | 'hybrid';

export type ResponseStatus = 
  | 'pending'
  | 'accepted'
  | 'declined'
  | 'tentative';

export type NotificationChannel = 
  | 'email'
  | 'sms'
  | 'inApp'
  | 'push';

export type RecurrencePattern = 
  | 'daily'
  | 'weekly'
  | 'biweekly'
  | 'monthly'
  | 'custom';

export type ResourceType = 
  | 'meeting_room'
  | 'equipment'
  | 'catering'
  | 'parking'
  | 'virtual_license';

// Contact management
export interface SchedulerContact {
  id: Id<'scheduler_contacts'>;
  name: string;
  email: string;
  phone?: string;
  role: string;
  department: string;
  state?: string;
  timezone: string;
  notificationPreferences: NotificationPreferences;
  availability: ContactAvailability;
  expertise: string[];
  isActive: boolean;
  createdAt: number;
  lastModified: number;
  createdBy: string;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  inApp: boolean;
  preferredChannel: NotificationChannel;
}

export interface ContactAvailability {
  workingHours: WorkingHours;
  daysOfWeek: number[]; // 0 = Sunday, 6 = Saturday
  blockedTimes: BlockedTime[];
}

export interface WorkingHours {
  start: string; // "09:00"
  end: string;   // "17:00"
}

export interface BlockedTime {
  start: string;
  end: string;
  reason?: string;
}

// Meeting interfaces
export interface SchedulerMeeting {
  id: Id<'scheduler_meetings'>;
  title: string;
  description: string;
  type: MeetingType;
  tenderId?: Id<'tenders'>;
  projectContext?: ProjectContext;
  startTime: number;
  endTime: number;
  timezone: string;
  location: MeetingLocation;
  participants: MeetingParticipant[];
  recurrence?: RecurrenceSettings;
  reminders: MeetingReminder[];
  resources?: MeetingResource[];
  agenda?: AgendaItem[];
  status: MeetingStatus;
  organizer: string;
  createdAt: number;
  lastModified: number;
  conflicts: ConflictInfo[];
  meetingLinks: Record<string, any>;
  rsvpStats: RSVPStats;
}

export interface ProjectContext {
  projectName: string;
  projectType: string;
  priority: string;
  requirements: string[];
}

export interface MeetingLocation {
  type: LocationType;
  value: string;
  details?: string;
}

export interface MeetingParticipant {
  contactId: Id<'scheduler_contacts'>;
  role: string;
  isRequired: boolean;
  responseStatus: ResponseStatus;
}

export interface RecurrenceSettings {
  pattern: RecurrencePattern;
  interval: number;
  endDate?: number;
  exceptions: number[];
}

export interface MeetingReminder {
  type: string;
  minutes: number;
  channel: NotificationChannel;
}

export interface MeetingResource {
  type: ResourceType;
  name: string;
  quantity: number;
  status: string;
}

export interface AgendaItem {
  item: string;
  duration: number;
  presenter?: Id<'scheduler_contacts'>;
}

export interface ConflictInfo {
  contactId: Id<'scheduler_contacts'>;
  conflictingMeetings: ConflictingMeeting[];
}

export interface ConflictingMeeting {
  id: Id<'scheduler_meetings'>;
  title: string;
  start: number;
  end: number;
}

export interface RSVPStats {
  accepted: number;
  declined: number;
  tentative: number;
  pending: number;
}

// RSVP management
export interface SchedulerRSVP {
  id: Id<'scheduler_rsvps'>;
  meetingId: Id<'scheduler_meetings'>;
  contactId: Id<'scheduler_contacts'>;
  response: ResponseStatus;
  comment?: string;
  proposedTime?: ProposedTime;
  respondedAt: number;
  remindersSent: number;
  lastReminderAt?: number;
}

export interface ProposedTime {
  start: number;
  end: number;
}

// Resource management
export interface SchedulerResource {
  id: Id<'scheduler_resources'>;
  name: string;
  type: ResourceType;
  capacity: number;
  location: string;
  features: string[];
  availability: ResourceAvailability;
  bookingRules: BookingRules;
  isActive: boolean;
  createdAt: number;
  lastModified: number;
}

export interface ResourceAvailability {
  workingHours: WorkingHours;
  daysOfWeek: number[];
  blockedDates: BlockedDate[];
}

export interface BlockedDate {
  date: number;
  reason: string;
}

export interface BookingRules {
  minDuration: number;
  maxDuration: number;
  advanceBooking: number;
  requiresApproval: boolean;
  approvers: string[];
}

export interface ResourceBooking {
  id: Id<'scheduler_bookings'>;
  resourceId: Id<'scheduler_resources'>;
  meetingId: Id<'scheduler_meetings'>;
  startTime: number;
  endTime: number;
  status: string;
  bookedBy: string;
  bookedAt: number;
  approvedBy?: string;
  approvedAt?: number;
  notes?: string;
}

// Meeting templates
export interface MeetingTemplate {
  id: Id<'scheduler_templates'>;
  name: string;
  description: string;
  type: MeetingType;
  isActive: boolean;
  settings: TemplateSettings;
  createdBy: string;
  createdAt: number;
  lastUsed?: number;
  useCount: number;
}

export interface TemplateSettings {
  duration: number;
  location: MeetingLocation;
  participantRoles: ParticipantRole[];
  reminders: MeetingReminder[];
  agenda?: AgendaItem[];
}

export interface ParticipantRole {
  role: string;
  isRequired: boolean;
  department?: string;
  expertise?: string[];
}

// Analytics
export interface SchedulerAnalytics {
  id: Id<'scheduler_analytics'>;
  periodStart: number;
  periodEnd: number;
  metrics: SchedulerMetrics;
  generatedAt: number;
}

export interface SchedulerMetrics {
  totalMeetings: number;
  averageAttendance: number;
  rsvpResponseRate: number;
  averageLeadTime: number;
  conflictRate: number;
  cancellationRate: number;
  platformUsage: Record<string, number>;
  departmentBreakdown: Record<string, number>;
  meetingTypeBreakdown: Record<MeetingType, number>;
  peakTimes: PeakTime[];
  topOrganizers: TopOrganizer[];
  resourceUtilization: Record<string, number>;
}

export interface PeakTime {
  hour: number;
  day: string;
  count: number;
}

export interface TopOrganizer {
  userId: string;
  meetingCount: number;
}

// Participant suggestions
export interface ParticipantSuggestion {
  contact: SchedulerContact;
  score: number;
  reasons: string[];
  availability: AvailabilityCheck;
  isRecommended: boolean;
  isRequired: boolean;
}

export interface AvailabilityCheck {
  isAvailable: boolean;
  reason: string;
}

// Notification interfaces
export interface MeetingInvitation {
  meetingId: Id<'scheduler_meetings'>;
  meetingTitle: string;
  meetingDescription: string;
  startTime: number;
  endTime: number;
  timezone: string;
  location: MeetingLocation;
  organizer: string;
  rsvpUrl: string;
  calendarLinks: Record<string, string>;
}

export interface MeetingReminder {
  meetingTitle: string;
  startTime: number;
  location: MeetingLocation;
  joinLink: string;
}

export interface MeetingNudge {
  type: 'meeting_nudge';
  meetingId: Id<'scheduler_meetings'>;
  meetingTitle: string;
  meetingTime: number;
  organizerName: string;
  urgency: 'low' | 'medium' | 'high';
}

// Integration interfaces
export interface CalendarIntegration {
  provider: 'google' | 'outlook' | 'ical';
  calendarId: string;
  accessToken: string;
  refreshToken?: string;
  syncEnabled: boolean;
  lastSync?: number;
}

export interface MeetingPlatformConfig {
  platform: 'teams' | 'zoom' | 'meet' | 'webex';
  apiKey?: string;
  apiSecret?: string;
  webhookUrl?: string;
  defaultSettings: Record<string, any>;
}

export interface NotificationService {
  type: 'email' | 'sms' | 'push';
  provider: string;
  config: Record<string, any>;
  isActive: boolean;
  limits?: {
    daily: number;
    hourly: number;
  };
}

// Optimization interfaces
export interface SchedulingOptimization {
  targetDate: number;
  duration: number;
  requiredParticipants: Id<'scheduler_contacts'>[];
  optionalParticipants: Id<'scheduler_contacts'>[];
  constraints: SchedulingConstraints;
  preferences: SchedulingPreferences;
}

export interface SchedulingConstraints {
  earliestStart: number;
  latestEnd: number;
  excludeWeekends: boolean;
  excludeHolidays: boolean;
  minNoticeHours: number;
  maxAttendeesPerSlot: number;
}

export interface SchedulingPreferences {
  preferredTimes: TimePreference[];
  avoidBackToBack: boolean;
  bufferMinutes: number;
  prioritizeAllAttendance: boolean;
}

export interface TimePreference {
  dayOfWeek: number;
  startHour: number;
  endHour: number;
  weight: number;
}

export interface OptimizedTimeSlot {
  start: number;
  end: number;
  score: number;
  availableParticipants: Id<'scheduler_contacts'>[];
  conflicts: ConflictInfo[];
  recommendation: string;
}