/**
 * Tender and Bid Section Type Definitions
 * 
 * Core interfaces for tender management and bid section handling
 */

import { Id } from '../convex/_generated/dataModel';

// Status enums
export type TenderStatus = 
  | 'draft'
  | 'bid_writing'
  | 'review'
  | 'submitted'
  | 'won'
  | 'lost'
  | 'cancelled';

export type BidSectionStatus = 
  | 'draft'
  | 'in_progress'
  | 'review'
  | 'approved'
  | 'rejected'
  | 'needs_revision';

export type TenderPriority = 'low' | 'medium' | 'high' | 'urgent';

export type TenderType = 
  | 'cleaning_services'
  | 'maintenance'
  | 'facility_management'
  | 'security'
  | 'other';

export type BidSectionType = 
  | 'executive_summary'
  | 'company_profile'
  | 'technical_proposal'
  | 'methodology'
  | 'staffing'
  | 'pricing'
  | 'experience'
  | 'references'
  | 'compliance'
  | 'timeline'
  | 'other';

// Core tender interface
export interface Tender {
  _id: Id<'tenders'>;
  _creationTime: number;
  name: string;
  clientName: string;
  status: TenderStatus;
  dueDate: string;
  estimatedValue: number;
  contractDuration?: string;
  estimatedScore?: number;
  priority?: TenderPriority;
  projectLocation?: string;
  totalWordCount?: number;
  type?: TenderType;
  description?: string;
  submissionDate?: string;
  winProbability?: number;
  strategicImportance?: number;
  lastModified?: number;
  createdBy?: string;
  assignedTeam?: string[];
  tags?: string[];
  clientContact?: {
    name: string;
    email: string;
    phone?: string;
  };
  documents?: {
    rfpDocumentId?: string;
    additionalDocuments?: string[];
  };
}

// Bid section interface
export interface BidSection {
  _id: Id<'bidSections'>;
  _creationTime: number;
  tenderId: Id<'tenders'>;
  title: string;
  content: string;
  wordCount: number;
  wordLimit: number;
  scoreWeight: number;
  status: BidSectionStatus;
  assignedAgent?: string;
  lastModified?: number;
  priority?: TenderPriority;
  requirements?: string[];
  type?: BidSectionType;
  version?: number;
  dueDate?: string;
  reviewNotes?: string;
  qualityScore?: number;
  keywords?: string[];
  references?: string[];
  metadata?: {
    timeSpent?: number;
    revisionCount?: number;
    lastReviewer?: string;
    completionPercentage?: number;
  };
}

// Extended tender with sections
export interface TenderWithSections extends Tender {
  bidSections: BidSection[];
}

// Tender progress tracking
export interface TenderProgress {
  totalSections: number;
  completedSections: number;
  inProgressSections: number;
  totalWords: number;
  totalWeightedScore: number;
  completionPercentage: number;
  averageQualityScore?: number;
  sectionsNeedingReview: number;
  overdueSections: number;
  estimatedCompletion?: string;
}

// Tender creation inputs
export interface CreateTenderInput {
  name: string;
  clientName: string;
  dueDate: string;
  estimatedValue: number;
  contractDuration?: string;
  priority?: TenderPriority;
  projectLocation?: string;
  type?: TenderType;
  description?: string;
  clientContact?: {
    name: string;
    email: string;
    phone?: string;
  };
  sections: CreateBidSectionInput[];
}

export interface CreateBidSectionInput {
  title: string;
  wordLimit: number;
  scoreWeight: number;
  type?: BidSectionType;
  requirements?: string[];
  dueDate?: string;
  priority?: TenderPriority;
}

// Update interfaces
export interface UpdateTenderInput {
  name?: string;
  clientName?: string;
  status?: TenderStatus;
  dueDate?: string;
  estimatedValue?: number;
  contractDuration?: string;
  estimatedScore?: number;
  priority?: TenderPriority;
  projectLocation?: string;
  type?: TenderType;
  description?: string;
  winProbability?: number;
  strategicImportance?: number;
  assignedTeam?: string[];
  tags?: string[];
  clientContact?: {
    name: string;
    email: string;
    phone?: string;
  };
}

export interface UpdateBidSectionInput {
  title?: string;
  content?: string;
  wordLimit?: number;
  scoreWeight?: number;
  status?: BidSectionStatus;
  assignedAgent?: string;
  priority?: TenderPriority;
  requirements?: string[];
  type?: BidSectionType;
  dueDate?: string;
  reviewNotes?: string;
  qualityScore?: number;
  keywords?: string[];
  references?: string[];
}

// Tender analytics
export interface TenderAnalytics {
  totalTenders: number;
  activeTenders: number;
  wonTenders: number;
  lostTenders: number;
  winRate: number;
  averageValue: number;
  totalValue: number;
  upcomingDeadlines: TenderDeadline[];
  statusDistribution: Record<TenderStatus, number>;
  priorityDistribution: Record<TenderPriority, number>;
  typeDistribution: Record<TenderType, number>;
}

export interface TenderDeadline {
  tenderId: Id<'tenders'>;
  name: string;
  dueDate: string;
  priority: TenderPriority;
  status: TenderStatus;
  daysRemaining: number;
}

// Tender search and filtering
export interface TenderFilter {
  status?: TenderStatus[];
  priority?: TenderPriority[];
  type?: TenderType[];
  clientName?: string;
  dateRange?: {
    start: string;
    end: string;
  };
  valueRange?: {
    min: number;
    max: number;
  };
  searchTerm?: string;
  tags?: string[];
}

export interface TenderSort {
  field: 'name' | 'clientName' | 'dueDate' | 'estimatedValue' | 'priority' | 'status' | '_creationTime';
  direction: 'asc' | 'desc';
}

// Tender export formats
export interface TenderExport {
  format: 'pdf' | 'docx' | 'html';
  includeSections: boolean;
  includeMetadata: boolean;
  customSections?: Id<'bidSections'>[];
}

// Tender templates
export interface TenderTemplate {
  id: string;
  name: string;
  description: string;
  type: TenderType;
  sections: TenderTemplateSection[];
  createdBy: string;
  isPublic: boolean;
  usageCount: number;
}

export interface TenderTemplateSection {
  title: string;
  description: string;
  type: BidSectionType;
  suggestedWordLimit: number;
  scoreWeight: number;
  requirements: string[];
  contentTemplate?: string;
}