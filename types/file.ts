/**
 * File Management Type Definitions
 * 
 * Interfaces for file uploads, document processing, and storage management
 */

import { Id } from '../convex/_generated/dataModel';
import { BidSectionType } from './tender';

// File types and status
export type FileType = 
  | 'pdf'
  | 'docx'
  | 'doc'
  | 'txt'
  | 'rtf'
  | 'odt'
  | 'xlsx'
  | 'xls'
  | 'csv'
  | 'pptx'
  | 'ppt'
  | 'image'
  | 'audio'
  | 'video'
  | 'archive'
  | 'other';

export type FileStatus = 
  | 'uploading'
  | 'uploaded'
  | 'processing'
  | 'processed'
  | 'failed'
  | 'deleted'
  | 'quarantined';

export type FileCategory = 
  | 'tender_document'
  | 'reference_material'
  | 'template'
  | 'attachment'
  | 'export'
  | 'backup'
  | 'temporary';

export type FileAccessLevel = 
  | 'public'
  | 'internal'
  | 'restricted'
  | 'private'
  | 'confidential';

export type FileProcessingStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'cancelled';

// Core file interfaces
export interface FileUpload {
  id: string;
  name: string;
  originalName: string;
  type: FileType;
  mimeType: string;
  size: number;
  status: FileStatus;
  category: FileCategory;
  accessLevel: FileAccessLevel;
  url: string;
  thumbnailUrl?: string;
  previewUrl?: string;
  storageKey: string;
  checksum: string;
  metadata: {
    uploadedBy: string;
    uploadedAt: number;
    lastModified: number;
    lastAccessed?: number;
    downloadCount: number;
    viewCount: number;
    width?: number;
    height?: number;
    duration?: number;
    pages?: number;
    encoding?: string;
    language?: string;
    version?: string;
  };
  tags: string[];
  description?: string;
  expiresAt?: number;
  isVersioned: boolean;
  parentId?: string;
  versions: FileVersion[];
  permissions: FilePermission[];
  virusStatus: 'not_scanned' | 'clean' | 'infected' | 'suspicious';
  processing?: FileProcessingJob[];
}

export interface FileVersion {
  id: string;
  version: string;
  url: string;
  size: number;
  checksum: string;
  createdAt: number;
  createdBy: string;
  notes?: string;
  isActive: boolean;
}

export interface FilePermission {
  userId: string;
  permission: 'read' | 'write' | 'delete' | 'share' | 'admin';
  grantedBy: string;
  grantedAt: number;
  expiresAt?: number;
}

// Document analysis and processing
export interface DocumentAnalysis {
  id: string;
  fileId: string;
  status: FileProcessingStatus;
  type: DocumentAnalysisType;
  startedAt: number;
  completedAt?: number;
  results: DocumentAnalysisResult;
  error?: string;
  metadata: {
    processingTime: number;
    confidence: number;
    model: string;
    version: string;
  };
}

export type DocumentAnalysisType = 
  | 'text_extraction'
  | 'ocr'
  | 'structure_analysis'
  | 'content_classification'
  | 'entity_recognition'
  | 'requirement_extraction'
  | 'section_identification'
  | 'key_phrase_extraction'
  | 'sentiment_analysis'
  | 'language_detection'
  | 'duplicate_detection'
  | 'compliance_check';

export interface DocumentAnalysisResult {
  textContent?: string;
  structuredData?: DocumentStructure;
  entities?: DocumentEntity[];
  requirements?: DocumentRequirement[];
  sections?: DocumentSection[];
  keyPhrases?: string[];
  sentiment?: DocumentSentiment;
  language?: string;
  confidence?: number;
  duplicates?: string[];
  complianceIssues?: ComplianceIssue[];
  metadata?: Record<string, any>;
}

export interface DocumentStructure {
  title?: string;
  headers: DocumentHeader[];
  paragraphs: DocumentParagraph[];
  tables: DocumentTable[];
  images: DocumentImage[];
  footers: DocumentFooter[];
  metadata: {
    totalPages: number;
    wordCount: number;
    characterCount: number;
    lineCount: number;
    hasImages: boolean;
    hasTables: boolean;
    hasHeaders: boolean;
    hasFooters: boolean;
  };
}

export interface DocumentHeader {
  level: number;
  text: string;
  position: DocumentPosition;
  formatting?: DocumentFormatting;
}

export interface DocumentParagraph {
  text: string;
  position: DocumentPosition;
  formatting?: DocumentFormatting;
  type: 'normal' | 'bullet' | 'numbered' | 'quote';
}

export interface DocumentTable {
  rows: DocumentTableRow[];
  columns: number;
  position: DocumentPosition;
  title?: string;
  hasHeaders: boolean;
}

export interface DocumentTableRow {
  cells: DocumentTableCell[];
  isHeader: boolean;
}

export interface DocumentTableCell {
  text: string;
  colspan?: number;
  rowspan?: number;
  formatting?: DocumentFormatting;
}

export interface DocumentImage {
  url: string;
  alt?: string;
  caption?: string;
  position: DocumentPosition;
  dimensions: {
    width: number;
    height: number;
  };
}

export interface DocumentFooter {
  text: string;
  page: number;
  position: DocumentPosition;
}

export interface DocumentPosition {
  page: number;
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface DocumentFormatting {
  bold?: boolean;
  italic?: boolean;
  underline?: boolean;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
  backgroundColor?: string;
  alignment?: 'left' | 'center' | 'right' | 'justify';
}

export interface DocumentEntity {
  text: string;
  type: EntityType;
  confidence: number;
  position: DocumentPosition;
  metadata?: Record<string, any>;
}

export type EntityType = 
  | 'person'
  | 'organization'
  | 'location'
  | 'date'
  | 'time'
  | 'money'
  | 'percentage'
  | 'contact'
  | 'requirement'
  | 'deadline'
  | 'specification'
  | 'criteria';

export interface DocumentRequirement {
  id: string;
  text: string;
  type: RequirementType;
  priority: 'low' | 'medium' | 'high' | 'mandatory';
  section?: string;
  page: number;
  confidence: number;
  relatedRequirements: string[];
  complianceStatus?: 'compliant' | 'non_compliant' | 'partial' | 'unknown';
  metadata: {
    wordCount: number;
    keywords: string[];
    category: string;
    complexity: 'simple' | 'moderate' | 'complex';
  };
}

export type RequirementType = 
  | 'functional'
  | 'technical'
  | 'performance'
  | 'security'
  | 'compliance'
  | 'financial'
  | 'timeline'
  | 'resource'
  | 'quality'
  | 'other';

export interface DocumentSection {
  id: string;
  title: string;
  content: string;
  type: BidSectionType;
  level: number;
  position: DocumentPosition;
  wordCount: number;
  requirements: string[];
  subsections: DocumentSection[];
  metadata: {
    importance: number;
    complexity: number;
    keywords: string[];
    estimatedScore?: number;
  };
}

export interface DocumentSentiment {
  overall: 'positive' | 'neutral' | 'negative';
  score: number;
  confidence: number;
  breakdown: {
    positive: number;
    neutral: number;
    negative: number;
  };
  aspects: SentimentAspect[];
}

export interface SentimentAspect {
  aspect: string;
  sentiment: 'positive' | 'neutral' | 'negative';
  score: number;
  confidence: number;
}

export interface ComplianceIssue {
  id: string;
  type: 'missing_requirement' | 'invalid_format' | 'policy_violation' | 'security_risk';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: DocumentPosition;
  suggestion: string;
  requirementId?: string;
  metadata?: Record<string, any>;
}

// File processing jobs
export interface FileProcessingJob {
  id: string;
  fileId: string;
  type: FileProcessingType;
  status: FileProcessingStatus;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  queuedAt: number;
  startedAt?: number;
  completedAt?: number;
  progress: number;
  error?: string;
  retryCount: number;
  maxRetries: number;
  parameters: Record<string, any>;
  results?: Record<string, any>;
  metadata: {
    processingTime?: number;
    cpuUsage?: number;
    memoryUsage?: number;
    estimatedCompletion?: number;
  };
}

export type FileProcessingType = 
  | 'virus_scan'
  | 'thumbnail_generation'
  | 'preview_generation'
  | 'text_extraction'
  | 'ocr'
  | 'format_conversion'
  | 'compression'
  | 'watermarking'
  | 'encryption'
  | 'analysis'
  | 'backup';

// File storage and management
export interface FileStorage {
  id: string;
  name: string;
  type: StorageType;
  configuration: StorageConfiguration;
  isActive: boolean;
  isDefault: boolean;
  capacity: {
    total: number;
    used: number;
    available: number;
  };
  performance: {
    uploadSpeed: number;
    downloadSpeed: number;
    responseTime: number;
    uptime: number;
  };
  metadata: {
    createdAt: number;
    lastHealthCheck: number;
    fileCount: number;
    averageFileSize: number;
  };
}

export type StorageType = 
  | 'local'
  | 's3'
  | 'azure_blob'
  | 'google_cloud'
  | 'ftp'
  | 'sftp'
  | 'webdav';

export interface StorageConfiguration {
  endpoint?: string;
  region?: string;
  bucket?: string;
  accessKey?: string;
  secretKey?: string;
  basePath?: string;
  encryption?: boolean;
  compression?: boolean;
  redundancy?: 'none' | 'single' | 'multiple';
  retentionDays?: number;
  autoCleanup?: boolean;
}

// File search and organization
export interface FileSearch {
  query?: string;
  filters: {
    types?: FileType[];
    categories?: FileCategory[];
    status?: FileStatus[];
    uploadedBy?: string[];
    dateRange?: {
      start: number;
      end: number;
    };
    sizeRange?: {
      min: number;
      max: number;
    };
    tags?: string[];
    tenderId?: Id<'tenders'>;
    hasAnalysis?: boolean;
  };
  sort: {
    field: 'name' | 'size' | 'uploadedAt' | 'lastModified' | 'downloadCount';
    direction: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    limit: number;
  };
}

export interface FileSearchResult {
  files: FileUpload[];
  totalCount: number;
  hasMore: boolean;
  facets: {
    types: Record<FileType, number>;
    categories: Record<FileCategory, number>;
    sizes: Record<string, number>;
    uploadedBy: Record<string, number>;
  };
}

export interface FileFolder {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  path: string;
  isShared: boolean;
  permissions: FilePermission[];
  metadata: {
    createdBy: string;
    createdAt: number;
    lastModified: number;
    fileCount: number;
    totalSize: number;
  };
  children: FileFolder[];
  files: FileUpload[];
}

// File sharing and collaboration
export interface FileShare {
  id: string;
  fileId: string;
  shareToken: string;
  sharedBy: string;
  sharedWith?: string[];
  permissions: ('read' | 'write' | 'download' | 'comment')[];
  isPublic: boolean;
  requiresAuth: boolean;
  password?: string;
  expiresAt?: number;
  maxDownloads?: number;
  downloadCount: number;
  lastAccessed?: number;
  isActive: boolean;
  metadata: {
    createdAt: number;
    accessCount: number;
    ipWhitelist?: string[];
    allowedDomains?: string[];
  };
}

export interface FileComment {
  id: string;
  fileId: string;
  userId: string;
  content: string;
  type: 'general' | 'suggestion' | 'issue' | 'approval';
  position?: DocumentPosition;
  parentId?: string;
  isResolved: boolean;
  resolvedBy?: string;
  resolvedAt?: number;
  createdAt: number;
  editedAt?: number;
  reactions: {
    emoji: string;
    count: number;
    users: string[];
  }[];
  metadata: {
    priority?: 'low' | 'medium' | 'high';
    category?: string;
    tags?: string[];
  };
}

// File analytics and reporting
export interface FileAnalytics {
  period: {
    start: number;
    end: number;
  };
  metrics: {
    totalUploads: number;
    totalSize: number;
    uniqueUsers: number;
    downloadCount: number;
    viewCount: number;
    averageFileSize: number;
    storageGrowth: number;
  };
  breakdown: {
    byType: Record<FileType, FileTypeMetrics>;
    byCategory: Record<FileCategory, number>;
    byUser: Record<string, FileUserMetrics>;
    byDay: Record<string, number>;
  };
  insights: {
    popularFiles: string[];
    largestFiles: string[];
    mostActiveUsers: string[];
    storageHotspots: string[];
  };
}

export interface FileTypeMetrics {
  count: number;
  totalSize: number;
  averageSize: number;
  downloadCount: number;
  viewCount: number;
}

export interface FileUserMetrics {
  uploadCount: number;
  downloadCount: number;
  totalSize: number;
  lastActivity: number;
}

// File backup and recovery
export interface FileBackup {
  id: string;
  name: string;
  description?: string;
  type: 'full' | 'incremental' | 'differential';
  status: 'pending' | 'running' | 'completed' | 'failed';
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone: string;
  };
  files: string[];
  destination: string;
  encryption: boolean;
  compression: boolean;
  metadata: {
    createdAt: number;
    startedAt?: number;
    completedAt?: number;
    duration?: number;
    fileCount: number;
    totalSize: number;
    compressedSize?: number;
    retentionDays: number;
  };
  verification: {
    checksum: string;
    verified: boolean;
    verifiedAt?: number;
  };
}