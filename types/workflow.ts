/**
 * Workflow Management Type Definitions
 * 
 * Interfaces for workflow orchestration and process management
 */

import { Id } from '../convex/_generated/dataModel';
import { TenderPriority, BidSectionType } from './tender';
import { AgentTaskType, AgentPriority } from './agent';

// Workflow status and types
export type WorkflowStatus = 
  | 'draft'
  | 'active'
  | 'paused'
  | 'completed'
  | 'cancelled'
  | 'failed'
  | 'suspended';

export type WorkflowType = 
  | 'tender_processing'
  | 'bid_writing'
  | 'review_approval'
  | 'quality_assurance'
  | 'submission'
  | 'custom';

export type WorkflowStepType = 
  | 'start'
  | 'task'
  | 'review'
  | 'approval'
  | 'condition'
  | 'parallel'
  | 'end';

export type WorkflowStepStatus = 
  | 'pending'
  | 'ready'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'skipped'
  | 'blocked';

export type WorkflowTrigger = 
  | 'manual'
  | 'scheduled'
  | 'event'
  | 'condition'
  | 'webhook';

// Core workflow interface
export interface Workflow {
  id: string;
  name: string;
  description: string;
  type: WorkflowType;
  status: WorkflowStatus;
  version: string;
  steps: WorkflowStep[];
  triggers: WorkflowTriggerConfig[];
  variables: Record<string, any>;
  metadata: {
    createdBy: string;
    createdAt: number;
    lastModified: number;
    lastModifiedBy: string;
    executionCount: number;
    successRate: number;
    averageExecutionTime: number;
  };
  settings: {
    timeout: number;
    maxRetries: number;
    errorHandling: 'stop' | 'continue' | 'rollback';
    notifications: WorkflowNotification[];
    isActive: boolean;
  };
}

// Workflow step definition
export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  type: WorkflowStepType;
  order: number;
  status: WorkflowStepStatus;
  dependencies: string[];
  conditions?: WorkflowCondition[];
  configuration: WorkflowStepConfig;
  timeouts: {
    execution: number;
    wait: number;
  };
  retryPolicy: {
    maxAttempts: number;
    backoffStrategy: 'linear' | 'exponential' | 'fixed';
    delay: number;
  };
  metadata: {
    createdAt: number;
    lastExecuted?: number;
    executionCount: number;
    successCount: number;
    failureCount: number;
    averageExecutionTime: number;
  };
}

export interface WorkflowStepConfig {
  taskType?: AgentTaskType;
  agentId?: string;
  agentType?: string;
  parameters?: Record<string, any>;
  inputMapping?: Record<string, string>;
  outputMapping?: Record<string, string>;
  validationRules?: ValidationRule[];
  approvers?: string[];
  parallelTasks?: ParallelTask[];
  conditionLogic?: string;
}

export interface ParallelTask {
  id: string;
  name: string;
  agentId?: string;
  taskType: AgentTaskType;
  parameters: Record<string, any>;
  required: boolean;
}

export interface ValidationRule {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'not_empty' | 'regex';
  value: string | number | boolean;
  message: string;
}

// Workflow execution
export interface WorkflowExecution {
  id: string;
  workflowId: string;
  status: WorkflowStatus;
  triggeredBy: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  steps: WorkflowStepExecution[];
  variables: Record<string, any>;
  context: WorkflowContext;
  errors: WorkflowError[];
  logs: WorkflowLog[];
  metadata: {
    executionMode: 'normal' | 'debug' | 'test';
    priority: WorkflowPriority;
    tags: string[];
  };
}

export interface WorkflowStepExecution {
  stepId: string;
  status: WorkflowStepStatus;
  startTime: number;
  endTime?: number;
  duration?: number;
  input?: Record<string, any>;
  output?: Record<string, any>;
  errors?: string[];
  retryCount: number;
  agentId?: string;
  taskId?: string;
}

export interface WorkflowContext {
  tenderId: Id<'tenders'>;
  sectionId?: Id<'bidSections'>;
  userId: string;
  priority: TenderPriority;
  deadline?: number;
  metadata: Record<string, any>;
}

export interface WorkflowError {
  stepId: string;
  code: string;
  message: string;
  timestamp: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  stack?: string;
  context?: Record<string, any>;
}

export interface WorkflowLog {
  level: 'debug' | 'info' | 'warn' | 'error';
  message: string;
  timestamp: number;
  stepId?: string;
  data?: Record<string, any>;
}

// Workflow progress tracking
export interface WorkflowProgress {
  workflowId: string;
  executionId: string;
  totalSteps: number;
  completedSteps: number;
  currentStep: string;
  progressPercentage: number;
  estimatedCompletion?: number;
  timeElapsed: number;
  stepsRemaining: number;
  blockedSteps: string[];
  errors: number;
  warnings: number;
}

// Workflow templates
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  type: WorkflowType;
  version: string;
  steps: WorkflowStepTemplate[];
  variables: WorkflowVariable[];
  tags: string[];
  metadata: {
    createdBy: string;
    createdAt: number;
    lastModified: number;
    usageCount: number;
    rating: number;
    isPublic: boolean;
  };
}

export interface WorkflowStepTemplate {
  id: string;
  name: string;
  description: string;
  type: WorkflowStepType;
  order: number;
  dependencies: string[];
  configTemplate: Record<string, any>;
  requiredFields: string[];
  optionalFields: string[];
}

export interface WorkflowVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description: string;
  defaultValue?: any;
  required: boolean;
  validation?: ValidationRule[];
}

// Workflow scheduling
export interface WorkflowSchedule {
  id: string;
  workflowId: string;
  name: string;
  description: string;
  schedule: ScheduleConfig;
  isActive: boolean;
  lastExecution?: number;
  nextExecution?: number;
  variables: Record<string, any>;
  metadata: {
    createdAt: number;
    executionCount: number;
    successCount: number;
    failureCount: number;
  };
}

export interface ScheduleConfig {
  type: 'cron' | 'interval' | 'once';
  expression: string;
  timezone?: string;
  startDate?: number;
  endDate?: number;
  maxExecutions?: number;
}

// Workflow triggers
export interface WorkflowTriggerConfig {
  id: string;
  name: string;
  type: WorkflowTrigger;
  isActive: boolean;
  conditions: WorkflowCondition[];
  configuration: TriggerConfiguration;
  metadata: {
    createdAt: number;
    lastTriggered?: number;
    triggerCount: number;
  };
}

export interface TriggerConfiguration {
  eventType?: string;
  scheduleExpression?: string;
  webhookUrl?: string;
  conditions?: Record<string, any>;
  parameters?: Record<string, any>;
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than' | 'in' | 'not_in';
  value: any;
  logicalOperator?: 'and' | 'or';
}

// Workflow monitoring
export interface WorkflowMonitoring {
  workflowId: string;
  status: WorkflowStatus;
  activeExecutions: number;
  queuedExecutions: number;
  performance: {
    averageExecutionTime: number;
    successRate: number;
    errorRate: number;
    throughput: number;
  };
  alerts: WorkflowAlert[];
  healthStatus: 'healthy' | 'warning' | 'critical';
  lastHealthCheck: number;
}

export interface WorkflowAlert {
  id: string;
  type: 'execution_failed' | 'timeout' | 'high_error_rate' | 'resource_limit' | 'custom';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  acknowledged: boolean;
  executionId?: string;
  stepId?: string;
  metadata?: Record<string, any>;
}

// Workflow analytics
export interface WorkflowAnalytics {
  workflowId: string;
  period: {
    start: number;
    end: number;
  };
  metrics: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    minExecutionTime: number;
    maxExecutionTime: number;
    throughput: number;
    errorRate: number;
  };
  breakdown: {
    byStatus: Record<WorkflowStatus, number>;
    byTrigger: Record<WorkflowTrigger, number>;
    byStep: Record<string, WorkflowStepAnalytics>;
    byTimeOfDay: Record<string, number>;
  };
  trends: {
    executionTime: number[];
    successRate: number[];
    errorRate: number[];
  };
}

export interface WorkflowStepAnalytics {
  stepId: string;
  executionCount: number;
  successCount: number;
  failureCount: number;
  averageExecutionTime: number;
  errorRate: number;
  bottleneck: boolean;
}

// Workflow optimization
export interface WorkflowOptimization {
  workflowId: string;
  recommendations: OptimizationRecommendation[];
  performance: {
    currentScore: number;
    potentialScore: number;
    improvementPercentage: number;
  };
  bottlenecks: WorkflowBottleneck[];
  lastAnalysis: number;
}

export interface OptimizationRecommendation {
  type: 'parallel_execution' | 'step_removal' | 'timeout_adjustment' | 'resource_allocation' | 'condition_simplification';
  description: string;
  impact: 'low' | 'medium' | 'high';
  effort: 'low' | 'medium' | 'high';
  estimatedImprovement: number;
  stepIds: string[];
}

export interface WorkflowBottleneck {
  stepId: string;
  reason: string;
  impact: number;
  suggestedFix: string;
  frequency: number;
}

// Workflow types
export type WorkflowPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface WorkflowNotification {
  type: 'email' | 'slack' | 'webhook' | 'sms';
  recipients: string[];
  events: ('start' | 'complete' | 'error' | 'timeout')[];
  template?: string;
  isActive: boolean;
}