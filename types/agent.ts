/**
 * AI Agent Type Definitions
 * 
 * Interfaces for AI agents that handle bid writing tasks
 */

import { Id } from '../convex/_generated/dataModel';
import { BidSectionType, TenderPriority } from './tender';

// Agent status and types
export type AgentStatus = 
  | 'active'
  | 'busy'
  | 'idle'
  | 'offline'
  | 'maintenance'
  | 'error';

export type AgentType = 
  | 'writer'
  | 'reviewer'
  | 'analyst'
  | 'coordinator'
  | 'specialist';

export type AgentCapability = 
  | 'content_generation'
  | 'content_review'
  | 'technical_writing'
  | 'executive_summary'
  | 'pricing_analysis'
  | 'compliance_check'
  | 'quality_assurance'
  | 'research'
  | 'formatting'
  | 'translation';

export type AgentPriority = 'low' | 'medium' | 'high' | 'critical';

export type AgentModel = 
  | 'gpt-4'
  | 'gpt-4-turbo'
  | 'gpt-4o'
  | 'gpt-4o-mini'
  | 'claude-3-opus'
  | 'claude-3-sonnet'
  | 'claude-3-haiku';

// Core agent interface
export interface BidWritingAgent {
  id: string;
  name: string;
  description: string;
  type: AgentType;
  status: AgentStatus;
  capabilities: AgentCapability[];
  specializations: BidSectionType[];
  model: AgentModel;
  temperature: number;
  maxTokens: number;
  systemPrompt: string;
  isActive: boolean;
  createdAt: number;
  lastActiveAt?: number;
  version: string;
  metadata: {
    tasksCompleted: number;
    averageQualityScore: number;
    averageResponseTime: number;
    successRate: number;
    totalWords: number;
    currentLoad: number;
    maxConcurrentTasks: number;
  };
  settings: {
    autoAssign: boolean;
    priority: AgentPriority;
    workingHours?: {
      start: string;
      end: string;
      timezone: string;
    };
    maxWordCount?: number;
    qualityThreshold: number;
  };
}

// Agent task assignment
export interface AgentTask {
  id: string;
  agentId: string;
  type: AgentTaskType;
  status: AgentTaskStatus;
  priority: AgentPriority;
  tenderId: Id<'tenders'>;
  sectionId?: Id<'bidSections'>;
  assignedAt: number;
  startedAt?: number;
  completedAt?: number;
  dueDate?: number;
  estimatedDuration?: number;
  actualDuration?: number;
  input: AgentTaskInput;
  output?: AgentTaskOutput;
  progress: number;
  metadata: {
    retryCount: number;
    errorMessage?: string;
    qualityScore?: number;
    reviewNotes?: string;
    tokens?: {
      input: number;
      output: number;
      total: number;
    };
  };
}

export type AgentTaskType = 
  | 'generate_content'
  | 'improve_content'
  | 'review_content'
  | 'analyze_requirements'
  | 'generate_outline'
  | 'fact_check'
  | 'format_content'
  | 'translate_content'
  | 'extract_keywords'
  | 'generate_summary';

export type AgentTaskStatus = 
  | 'pending'
  | 'assigned'
  | 'in_progress'
  | 'completed'
  | 'failed'
  | 'cancelled'
  | 'requires_review';

export interface AgentTaskInput {
  content?: string;
  instructions: string;
  context: {
    tenderName: string;
    clientName: string;
    sectionTitle: string;
    wordLimit?: number;
    scoreWeight?: number;
    requirements?: string[];
    existingContent?: string;
  };
  parameters?: {
    tone?: 'professional' | 'formal' | 'persuasive' | 'technical';
    style?: 'concise' | 'detailed' | 'bullet_points' | 'narrative';
    focus?: string[];
    keywords?: string[];
  };
}

export interface AgentTaskOutput {
  content: string;
  wordCount: number;
  confidence: number;
  suggestions?: string[];
  warnings?: string[];
  metadata: {
    processingTime: number;
    revisionsNeeded?: boolean;
    qualityIndicators: {
      clarity: number;
      relevance: number;
      completeness: number;
      persuasiveness: number;
    };
  };
}

// Agent coordination
export interface AgentCoordinator {
  id: string;
  name: string;
  description: string;
  agents: string[];
  workflowRules: WorkflowRule[];
  isActive: boolean;
  statistics: {
    tasksCoordinated: number;
    averageCompletionTime: number;
    successRate: number;
    activeAgents: number;
  };
}

export interface WorkflowRule {
  id: string;
  name: string;
  condition: WorkflowCondition;
  action: WorkflowAction;
  priority: number;
  isActive: boolean;
}

export interface WorkflowCondition {
  type: 'section_type' | 'word_count' | 'priority' | 'deadline' | 'agent_availability';
  operator: 'equals' | 'greater_than' | 'less_than' | 'contains' | 'not_equals';
  value: string | number | boolean;
}

export interface WorkflowAction {
  type: 'assign_agent' | 'escalate' | 'notify' | 'set_priority' | 'add_reviewer';
  parameters: Record<string, any>;
}

// Agent performance tracking
export interface AgentPerformance {
  agentId: string;
  period: {
    start: number;
    end: number;
  };
  metrics: {
    tasksCompleted: number;
    averageQualityScore: number;
    averageResponseTime: number;
    successRate: number;
    totalWords: number;
    errorRate: number;
    customerSatisfaction?: number;
  };
  breakdown: {
    byTaskType: Record<AgentTaskType, AgentPerformanceMetric>;
    bySectionType: Record<BidSectionType, AgentPerformanceMetric>;
    byPriority: Record<AgentPriority, AgentPerformanceMetric>;
  };
}

export interface AgentPerformanceMetric {
  count: number;
  averageScore: number;
  averageTime: number;
  successRate: number;
}

// Agent training and improvement
export interface AgentTraining {
  agentId: string;
  trainingData: TrainingExample[];
  feedback: AgentFeedback[];
  performance: AgentPerformance[];
  recommendations: string[];
  nextTrainingDate?: number;
}

export interface TrainingExample {
  input: AgentTaskInput;
  expectedOutput: string;
  qualityScore: number;
  feedback: string;
  tags: string[];
}

export interface AgentFeedback {
  taskId: string;
  rating: number;
  comment: string;
  category: 'quality' | 'relevance' | 'accuracy' | 'timeliness';
  providedBy: string;
  timestamp: number;
}

// Agent collaboration
export interface AgentCollaboration {
  id: string;
  name: string;
  description: string;
  agents: string[];
  tasks: string[];
  strategy: CollaborationStrategy;
  status: 'active' | 'paused' | 'completed';
  createdAt: number;
  completedAt?: number;
  results: {
    totalTasks: number;
    completedTasks: number;
    averageQuality: number;
    timeToCompletion: number;
  };
}

export type CollaborationStrategy = 
  | 'sequential'
  | 'parallel'
  | 'review_chain'
  | 'consensus'
  | 'specialization';

// Agent configuration
export interface AgentConfig {
  id: string;
  name: string;
  description: string;
  type: AgentType;
  capabilities: AgentCapability[];
  model: AgentModel;
  parameters: {
    temperature: number;
    maxTokens: number;
    topP?: number;
    frequencyPenalty?: number;
    presencePenalty?: number;
  };
  systemPrompt: string;
  tools: AgentTool[];
  constraints: {
    maxConcurrentTasks: number;
    maxWordCount?: number;
    qualityThreshold: number;
    timeoutMinutes: number;
  };
  specializations: BidSectionType[];
  isActive: boolean;
  version: string;
}

export interface AgentTool {
  name: string;
  description: string;
  type: 'function' | 'api' | 'database' | 'file';
  endpoint?: string;
  parameters?: Record<string, any>;
  isRequired: boolean;
}

// Agent monitoring
export interface AgentMonitoring {
  agentId: string;
  status: AgentStatus;
  currentTasks: AgentTask[];
  queuedTasks: AgentTask[];
  performance: {
    cpu: number;
    memory: number;
    responseTime: number;
    errorRate: number;
  };
  alerts: AgentAlert[];
  lastHealthCheck: number;
}

export interface AgentAlert {
  id: string;
  type: 'error' | 'warning' | 'info';
  message: string;
  timestamp: number;
  acknowledged: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}