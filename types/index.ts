/**
 * Type Definitions Index
 * 
 * Centralized export of all type definitions for the Bid Writing Tender Studio
 */

// Tender and bid section types
export * from './tender';
export type {
  Tender,
  BidSection,
  TenderWithSections,
  TenderProgress,
  TenderStatus,
  BidSectionStatus,
  TenderPriority,
  TenderType,
  BidSectionType,
  CreateTenderInput,
  CreateBidSectionInput,
  UpdateTenderInput,
  UpdateBidSectionInput,
  TenderAnalytics,
  TenderDeadline,
  TenderFilter,
  TenderSort,
  TenderExport,
  TenderTemplate,
  TenderTemplateSection,
} from './tender';

// AI Agent types
export * from './agent';
export type {
  BidWritingAgent,
  AgentTask,
  AgentCoordinator,
  AgentPerformance,
  AgentTraining,
  AgentFeedback,
  AgentCollaboration,
  AgentConfig,
  AgentTool,
  AgentMonitoring,
  AgentAlert,
  AgentStatus,
  AgentType,
  AgentCapability,
  AgentPriority,
  AgentModel,
  AgentTaskType,
  AgentTaskStatus,
  AgentTaskInput,
  AgentTaskOutput,
  WorkflowRule,
  WorkflowAction,
  AgentPerformanceMetric,
  TrainingExample,
  CollaborationStrategy,
} from './agent';

// Workflow management types
export * from './workflow';
export type {
  Workflow,
  WorkflowStep,
  WorkflowExecution,
  WorkflowStepExecution,
  WorkflowProgress,
  WorkflowTemplate,
  WorkflowStepTemplate,
  WorkflowVariable,
  WorkflowSchedule,
  WorkflowTriggerConfig,
  WorkflowMonitoring,
  WorkflowAlert,
  WorkflowAnalytics,
  WorkflowStepAnalytics,
  WorkflowOptimization,
  WorkflowBottleneck,
  WorkflowStatus,
  WorkflowType,
  WorkflowStepType,
  WorkflowStepStatus,
  WorkflowTrigger,
  WorkflowStepConfig,
  WorkflowContext,
  WorkflowError,
  WorkflowLog,
  WorkflowCondition,
  WorkflowPriority,
  WorkflowNotification,
  ParallelTask,
  ValidationRule,
  ScheduleConfig,
  TriggerConfiguration,
  OptimizationRecommendation,
} from './workflow';

// Chat and communication types
export * from './chat';
export type {
  ChatMessage,
  ChatThread,
  ChatParticipant,
  ChatAttachment,
  ChatReaction,
  ChatActivity,
  ChatNotification,
  ChatSearch,
  ChatSearchResult,
  ChatAnalytics,
  ChatAIAssistant,
  ChatAIResponse,
  ChatTemplate,
  ChatShortcut,
  ChatModerationRule,
  ChatModerationAction,
  ChatExport,
  ChatMessageType,
  ChatMessageStatus,
  ChatParticipantType,
  ChatThreadType,
  ChatThreadStatus,
  ChatPermission,
  ChatActivityType,
  ChatNotificationType,
  ChatAICapability,
  ChatShortcutAction,
} from './chat';

// File management types
export * from './file';
export type {
  FileUpload,
  FileVersion,
  FilePermission,
  DocumentAnalysis,
  DocumentAnalysisResult,
  DocumentStructure,
  DocumentEntity,
  DocumentRequirement,
  DocumentSection,
  DocumentSentiment,
  ComplianceIssue,
  FileProcessingJob,
  FileStorage,
  FileSearch,
  FileSearchResult,
  FileFolder,
  FileShare,
  FileComment,
  FileAnalytics,
  FileBackup,
  FileType,
  FileStatus,
  FileCategory,
  FileAccessLevel,
  FileProcessingStatus,
  DocumentAnalysisType,
  EntityType,
  RequirementType,
  FileProcessingType,
  StorageType,
  StorageConfiguration,
  DocumentHeader,
  DocumentParagraph,
  DocumentTable,
  DocumentTableRow,
  DocumentTableCell,
  DocumentImage,
  DocumentFooter,
  DocumentPosition,
  DocumentFormatting,
  SentimentAspect,
  FileTypeMetrics,
  FileUserMetrics,
} from './file';

// Common utility types
export type ID = string;
export type Timestamp = number;
export type Optional<T> = T | undefined;
export type Nullable<T> = T | null;
export type Dictionary<T> = Record<string, T>;
export type Partial<T> = {
  [P in keyof T]?: T[P];
};

// API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: number;
}

export interface ApiError {
  code: string;
  message: string;
  details?: Record<string, any>;
  timestamp: number;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

// User and authentication types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRole;
  permissions: string[];
  preferences: UserPreferences;
  metadata: {
    createdAt: number;
    lastLogin?: number;
    loginCount: number;
    isActive: boolean;
  };
}

export type UserRole = 
  | 'admin'
  | 'manager'
  | 'writer'
  | 'reviewer'
  | 'viewer'
  | 'client';

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  timezone: string;
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
    inApp: boolean;
  };
  dashboard: {
    layout: 'grid' | 'list';
    widgets: string[];
    refreshInterval: number;
  };
}

// System configuration types
export interface SystemConfig {
  version: string;
  environment: 'development' | 'staging' | 'production';
  features: {
    [key: string]: boolean;
  };
  limits: {
    maxFileSize: number;
    maxFilesPerUpload: number;
    maxAgentTasks: number;
    maxConcurrentWorkflows: number;
  };
  integrations: {
    openai: boolean;
    anthropic: boolean;
    azure: boolean;
    slack: boolean;
    email: boolean;
  };
}

// Audit and logging types
export interface AuditLog {
  id: string;
  userId: string;
  action: string;
  resource: string;
  resourceId?: string;
  timestamp: number;
  ipAddress: string;
  userAgent: string;
  metadata?: Record<string, any>;
  changes?: {
    before: Record<string, any>;
    after: Record<string, any>;
  };
}

export interface SystemLog {
  id: string;
  level: 'debug' | 'info' | 'warn' | 'error' | 'fatal';
  message: string;
  timestamp: number;
  source: string;
  category: string;
  metadata?: Record<string, any>;
  stackTrace?: string;
}

// Dashboard and reporting types
export interface Dashboard {
  id: string;
  name: string;
  description?: string;
  layout: DashboardLayout;
  widgets: DashboardWidget[];
  isPublic: boolean;
  permissions: string[];
  metadata: {
    createdBy: string;
    createdAt: number;
    lastModified: number;
    viewCount: number;
  };
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  gap: number;
}

export interface DashboardWidget {
  id: string;
  type: string;
  title: string;
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  configuration: Record<string, any>;
  dataSource: string;
  refreshInterval: number;
  isVisible: boolean;
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  readAt?: number;
  createdAt: number;
  expiresAt?: number;
  actions?: NotificationAction[];
}

export type NotificationType = 
  | 'info'
  | 'success'
  | 'warning'
  | 'error'
  | 'reminder'
  | 'update'
  | 'invitation';

export interface NotificationAction {
  id: string;
  label: string;
  type: 'button' | 'link';
  action: string;
  data?: Record<string, any>;
}

// Search and indexing types
export interface SearchIndex {
  id: string;
  name: string;
  type: 'tenders' | 'sections' | 'files' | 'messages' | 'users';
  configuration: SearchConfiguration;
  isActive: boolean;
  metadata: {
    createdAt: number;
    lastUpdated: number;
    documentCount: number;
    size: number;
  };
}

export interface SearchConfiguration {
  fields: string[];
  analyzers: Record<string, any>;
  filters: Record<string, any>;
  scoring: Record<string, any>;
}

export interface SearchQuery {
  query: string;
  filters?: Record<string, any>;
  sort?: Array<{
    field: string;
    direction: 'asc' | 'desc';
  }>;
  pagination?: {
    page: number;
    size: number;
  };
  highlight?: boolean;
  facets?: string[];
}

export interface SearchResult<T = any> {
  hits: SearchHit<T>[];
  total: number;
  facets?: Record<string, SearchFacet>;
  took: number;
  maxScore: number;
}

export interface SearchHit<T = any> {
  id: string;
  score: number;
  source: T;
  highlight?: Record<string, string[]>;
}

export interface SearchFacet {
  buckets: Array<{
    key: string;
    count: number;
  }>;
}

// Validation utilities
export * from './validators';
export type { ValidationResult } from './validators';

// Export everything as a single namespace for convenience
export namespace BidWritingStudio {
  export type {
    Tender,
    BidSection,
    BidWritingAgent,
    Workflow,
    ChatMessage,
    FileUpload,
    User,
    SystemConfig,
    AuditLog,
    Dashboard,
    Notification,
    SearchIndex,
  };
}