/**
 * Type Validation Utilities
 * 
 * Runtime validation functions for type safety at API boundaries
 */

import { 
  TenderStatus, 
  BidSectionStatus, 
  TenderPriority, 
  TenderType, 
  BidSectionType,
  AgentStatus,
  AgentType,
  AgentCapability,
  WorkflowStatus,
  WorkflowStepStatus,
  FileType,
  FileStatus,
  ChatMessageType,
  ChatThreadType
} from './index';

// Utility type for validation results
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Enum validation helpers
export const isValidTenderStatus = (status: string): status is TenderStatus => {
  return ['draft', 'bid_writing', 'review', 'submitted', 'won', 'lost', 'cancelled'].includes(status);
};

export const isValidBidSectionStatus = (status: string): status is BidSectionStatus => {
  return ['draft', 'in_progress', 'review', 'approved', 'rejected', 'needs_revision'].includes(status);
};

export const isValidTenderPriority = (priority: string): priority is TenderPriority => {
  return ['low', 'medium', 'high', 'urgent'].includes(priority);
};

export const isValidTenderType = (type: string): type is TenderType => {
  return ['cleaning_services', 'maintenance', 'facility_management', 'security', 'other'].includes(type);
};

export const isValidBidSectionType = (type: string): type is BidSectionType => {
  return [
    'executive_summary', 'company_profile', 'technical_proposal', 'methodology',
    'staffing', 'pricing', 'experience', 'references', 'compliance', 'timeline', 'other'
  ].includes(type);
};

export const isValidAgentStatus = (status: string): status is AgentStatus => {
  return ['active', 'busy', 'idle', 'offline', 'maintenance', 'error'].includes(status);
};

export const isValidAgentType = (type: string): type is AgentType => {
  return ['writer', 'reviewer', 'analyst', 'coordinator', 'specialist'].includes(type);
};

export const isValidWorkflowStatus = (status: string): status is WorkflowStatus => {
  return ['draft', 'active', 'paused', 'completed', 'cancelled', 'failed', 'suspended'].includes(status);
};

export const isValidFileType = (type: string): type is FileType => {
  return [
    'pdf', 'docx', 'doc', 'txt', 'rtf', 'odt', 'xlsx', 'xls', 'csv',
    'pptx', 'ppt', 'image', 'audio', 'video', 'archive', 'other'
  ].includes(type);
};

export const isValidChatMessageType = (type: string): type is ChatMessageType => {
  return [
    'text', 'file', 'image', 'code', 'suggestion', 'review', 
    'approval', 'system', 'notification'
  ].includes(type);
};

// Object validation functions
export const validateTender = (tender: any): ValidationResult => {
  const errors: string[] = [];

  if (!tender.name || typeof tender.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (!tender.clientName || typeof tender.clientName !== 'string') {
    errors.push('Client name is required and must be a string');
  }

  if (!isValidTenderStatus(tender.status)) {
    errors.push('Invalid tender status');
  }

  if (!tender.dueDate || typeof tender.dueDate !== 'string') {
    errors.push('Due date is required and must be a string');
  }

  if (typeof tender.estimatedValue !== 'number' || tender.estimatedValue < 0) {
    errors.push('Estimated value must be a non-negative number');
  }

  if (tender.priority && !isValidTenderPriority(tender.priority)) {
    errors.push('Invalid tender priority');
  }

  if (tender.type && !isValidTenderType(tender.type)) {
    errors.push('Invalid tender type');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateBidSection = (section: any): ValidationResult => {
  const errors: string[] = [];

  if (!section.title || typeof section.title !== 'string') {
    errors.push('Title is required and must be a string');
  }

  if (typeof section.content !== 'string') {
    errors.push('Content must be a string');
  }

  if (typeof section.wordCount !== 'number' || section.wordCount < 0) {
    errors.push('Word count must be a non-negative number');
  }

  if (typeof section.wordLimit !== 'number' || section.wordLimit < 0) {
    errors.push('Word limit must be a non-negative number');
  }

  if (typeof section.scoreWeight !== 'number' || section.scoreWeight < 0 || section.scoreWeight > 100) {
    errors.push('Score weight must be a number between 0 and 100');
  }

  if (!isValidBidSectionStatus(section.status)) {
    errors.push('Invalid bid section status');
  }

  if (section.type && !isValidBidSectionType(section.type)) {
    errors.push('Invalid bid section type');
  }

  if (section.priority && !isValidTenderPriority(section.priority)) {
    errors.push('Invalid priority');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateAgent = (agent: any): ValidationResult => {
  const errors: string[] = [];

  if (!agent.name || typeof agent.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (!agent.description || typeof agent.description !== 'string') {
    errors.push('Description is required and must be a string');
  }

  if (!isValidAgentType(agent.type)) {
    errors.push('Invalid agent type');
  }

  if (!isValidAgentStatus(agent.status)) {
    errors.push('Invalid agent status');
  }

  if (!Array.isArray(agent.capabilities)) {
    errors.push('Capabilities must be an array');
  }

  if (!Array.isArray(agent.specializations)) {
    errors.push('Specializations must be an array');
  }

  if (typeof agent.temperature !== 'number' || agent.temperature < 0 || agent.temperature > 2) {
    errors.push('Temperature must be a number between 0 and 2');
  }

  if (typeof agent.maxTokens !== 'number' || agent.maxTokens < 1) {
    errors.push('Max tokens must be a positive number');
  }

  if (!agent.systemPrompt || typeof agent.systemPrompt !== 'string') {
    errors.push('System prompt is required and must be a string');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateWorkflow = (workflow: any): ValidationResult => {
  const errors: string[] = [];

  if (!workflow.name || typeof workflow.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (!workflow.description || typeof workflow.description !== 'string') {
    errors.push('Description is required and must be a string');
  }

  if (!isValidWorkflowStatus(workflow.status)) {
    errors.push('Invalid workflow status');
  }

  if (!Array.isArray(workflow.steps)) {
    errors.push('Steps must be an array');
  } else {
    workflow.steps.forEach((step: any, index: number) => {
      if (!step.name || typeof step.name !== 'string') {
        errors.push(`Step ${index + 1}: Name is required and must be a string`);
      }

      if (typeof step.order !== 'number' || step.order < 0) {
        errors.push(`Step ${index + 1}: Order must be a non-negative number`);
      }

      if (!Array.isArray(step.dependencies)) {
        errors.push(`Step ${index + 1}: Dependencies must be an array`);
      }
    });
  }

  if (!Array.isArray(workflow.triggers)) {
    errors.push('Triggers must be an array');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateFileUpload = (file: any): ValidationResult => {
  const errors: string[] = [];

  if (!file.name || typeof file.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (!file.originalName || typeof file.originalName !== 'string') {
    errors.push('Original name is required and must be a string');
  }

  if (!isValidFileType(file.type)) {
    errors.push('Invalid file type');
  }

  if (!file.mimeType || typeof file.mimeType !== 'string') {
    errors.push('MIME type is required and must be a string');
  }

  if (typeof file.size !== 'number' || file.size < 0) {
    errors.push('Size must be a non-negative number');
  }

  if (!file.url || typeof file.url !== 'string') {
    errors.push('URL is required and must be a string');
  }

  if (!file.checksum || typeof file.checksum !== 'string') {
    errors.push('Checksum is required and must be a string');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

export const validateChatMessage = (message: any): ValidationResult => {
  const errors: string[] = [];

  if (!message.threadId || typeof message.threadId !== 'string') {
    errors.push('Thread ID is required and must be a string');
  }

  if (!message.senderId || typeof message.senderId !== 'string') {
    errors.push('Sender ID is required and must be a string');
  }

  if (!isValidChatMessageType(message.type)) {
    errors.push('Invalid message type');
  }

  if (!message.content || typeof message.content !== 'string') {
    errors.push('Content is required and must be a string');
  }

  if (typeof message.timestamp !== 'number' || message.timestamp < 0) {
    errors.push('Timestamp must be a non-negative number');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Validation utility functions
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validateUrl = (url: string): boolean => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

export const validateDateString = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

export const validatePhoneNumber = (phone: string): boolean => {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/;
  return phoneRegex.test(phone);
};

export const validatePositiveNumber = (value: number): boolean => {
  return typeof value === 'number' && value > 0;
};

export const validateNonNegativeNumber = (value: number): boolean => {
  return typeof value === 'number' && value >= 0;
};

export const validateStringLength = (str: string, min: number, max: number): boolean => {
  return typeof str === 'string' && str.length >= min && str.length <= max;
};

export const validateArrayLength = (arr: any[], min: number, max: number): boolean => {
  return Array.isArray(arr) && arr.length >= min && arr.length <= max;
};

// Compound validation functions
export const validateCreateTenderInput = (input: any): ValidationResult => {
  const errors: string[] = [];

  if (!input.name || typeof input.name !== 'string') {
    errors.push('Name is required and must be a string');
  }

  if (!input.clientName || typeof input.clientName !== 'string') {
    errors.push('Client name is required and must be a string');
  }

  if (!input.dueDate || !validateDateString(input.dueDate)) {
    errors.push('Due date is required and must be a valid date string');
  }

  if (!validatePositiveNumber(input.estimatedValue)) {
    errors.push('Estimated value must be a positive number');
  }

  if (!Array.isArray(input.sections) || input.sections.length === 0) {
    errors.push('Sections must be a non-empty array');
  } else {
    input.sections.forEach((section: any, index: number) => {
      if (!section.title || typeof section.title !== 'string') {
        errors.push(`Section ${index + 1}: Title is required and must be a string`);
      }

      if (!validatePositiveNumber(section.wordLimit)) {
        errors.push(`Section ${index + 1}: Word limit must be a positive number`);
      }

      if (!validateNonNegativeNumber(section.scoreWeight)) {
        errors.push(`Section ${index + 1}: Score weight must be a non-negative number`);
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

// Export all validation functions
export const validators = {
  tender: validateTender,
  bidSection: validateBidSection,
  agent: validateAgent,
  workflow: validateWorkflow,
  fileUpload: validateFileUpload,
  chatMessage: validateChatMessage,
  createTenderInput: validateCreateTenderInput,
  email: validateEmail,
  url: validateUrl,
  dateString: validateDateString,
  phoneNumber: validatePhoneNumber,
  positiveNumber: validatePositiveNumber,
  nonNegativeNumber: validateNonNegativeNumber,
  stringLength: validateStringLength,
  arrayLength: validateArrayLength
};

// Type guard factory
export const createTypeGuard = <T>(validator: (obj: any) => ValidationResult) => {
  return (obj: any): obj is T => {
    return validator(obj).isValid;
  };
};

// Common type guards
export const isTender = createTypeGuard(validateTender);
export const isBidSection = createTypeGuard(validateBidSection);
export const isAgent = createTypeGuard(validateAgent);
export const isWorkflow = createTypeGuard(validateWorkflow);
export const isFileUpload = createTypeGuard(validateFileUpload);
export const isChatMessage = createTypeGuard(validateChatMessage);