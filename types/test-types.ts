/**
 * Type Definition Tests
 * 
 * This file serves as both documentation and validation for the type definitions.
 * It demonstrates proper usage and ensures types work correctly.
 */

import type {
  // Tender types
  Tender,
  BidSection,
  TenderWithSections,
  CreateTenderInput,
  TenderStatus,
  BidSectionStatus,
  TenderPriority,
  
  // Agent types
  BidWritingAgent,
  AgentTask,
  AgentTaskInput,
  AgentStatus,
  AgentCapability,
  
  // Workflow types
  Workflow,
  WorkflowStep,
  WorkflowExecution,
  WorkflowStatus,
  
  // Chat types
  ChatMessage,
  ChatThread,
  ChatParticipant,
  ChatMessageType,
  
  // File types
  FileUpload,
  DocumentAnalysis,
  DocumentStructure,
  FileType,
  
  // Common types
  User,
  ApiResponse,
  PaginatedResponse,
  
  // Validation
  ValidationResult,
  validators
} from './index';

import { Id } from '../convex/_generated/dataModel';

// Test: Tender type usage
const testTender: Tender = {
  _id: "tender_123" as Id<'tenders'>,
  _creationTime: Date.now(),
  name: "Office Cleaning Services - 500 Bourke Street",
  clientName: "Property Management Corp",
  status: "bid_writing",
  dueDate: "2024-01-15",
  estimatedValue: 250000,
  contractDuration: "12 months",
  priority: "high",
  type: "cleaning_services",
  projectLocation: "Melbourne, VIC",
  description: "Comprehensive cleaning services for a premium office building",
  winProbability: 75,
  strategicImportance: 85,
  tags: ["cleaning", "office", "melbourne"],
  clientContact: {
    name: "John Smith",
    email: "<EMAIL>",
    phone: "+61 3 9123 4567"
  }
};

// Test: Bid Section type usage
const testBidSection: BidSection = {
  _id: "section_456" as Id<'bidSections'>,
  _creationTime: Date.now(),
  tenderId: testTender._id,
  title: "Executive Summary",
  content: "ARA Property Services is pleased to submit this proposal...",
  wordCount: 245,
  wordLimit: 500,
  scoreWeight: 20,
  status: "in_progress",
  type: "executive_summary",
  priority: "high",
  requirements: [
    "Highlight company experience",
    "Emphasize value proposition",
    "Include key personnel"
  ],
  keywords: ["professional", "reliable", "experienced"],
  qualityScore: 85,
  metadata: {
    timeSpent: 120,
    revisionCount: 2,
    completionPercentage: 70
  }
};

// Test: Agent type usage
const testAgent: BidWritingAgent = {
  id: "agent_writer_01",
  name: "Executive Summary Writer",
  description: "Specialized in creating compelling executive summaries",
  type: "writer",
  status: "active",
  capabilities: ["content_generation", "executive_summary"],
  specializations: ["executive_summary", "company_profile"],
  model: "gpt-4o",
  temperature: 0.7,
  maxTokens: 2000,
  systemPrompt: "You are an expert bid writer specializing in executive summaries...",
  isActive: true,
  createdAt: Date.now(),
  version: "1.0",
  metadata: {
    tasksCompleted: 150,
    averageQualityScore: 87.5,
    averageResponseTime: 45000,
    successRate: 95.2,
    totalWords: 75000,
    currentLoad: 2,
    maxConcurrentTasks: 5
  },
  settings: {
    autoAssign: true,
    priority: "high",
    qualityThreshold: 80,
    maxWordCount: 1000
  }
};

// Test: Agent Task type usage
const testAgentTask: AgentTask = {
  id: "task_789",
  agentId: testAgent.id,
  type: "generate_content",
  status: "in_progress",
  priority: "high",
  tenderId: testTender._id,
  sectionId: testBidSection._id,
  assignedAt: Date.now(),
  startedAt: Date.now(),
  progress: 65,
  input: {
    instructions: "Generate an executive summary highlighting ARA's experience",
    context: {
      tenderName: testTender.name,
      clientName: testTender.clientName,
      sectionTitle: testBidSection.title,
      wordLimit: testBidSection.wordLimit,
      scoreWeight: testBidSection.scoreWeight,
      requirements: testBidSection.requirements
    },
    parameters: {
      tone: "professional",
      style: "persuasive",
      focus: ["experience", "reliability", "value"],
      keywords: ["cleaning", "maintenance", "professional"]
    }
  },
  metadata: {
    retryCount: 0,
    tokens: {
      input: 450,
      output: 580,
      total: 1030
    }
  }
};

// Test: Workflow type usage
const testWorkflow: Workflow = {
  id: "workflow_bid_writing",
  name: "Standard Bid Writing Process",
  description: "Automated workflow for processing tender documents and generating bid responses",
  type: "bid_writing",
  status: "active",
  version: "1.2",
  steps: [
    {
      id: "step_analysis",
      name: "Document Analysis",
      description: "Analyze RFP document and extract requirements",
      type: "task",
      order: 1,
      status: "completed",
      dependencies: [],
      configuration: {
        taskType: "analyze_requirements",
        agentType: "analyst",
        parameters: {
          extractRequirements: true,
          identifySections: true
        }
      },
      timeouts: {
        execution: 300000,
        wait: 60000
      },
      retryPolicy: {
        maxAttempts: 3,
        backoffStrategy: "exponential",
        delay: 5000
      },
      metadata: {
        createdAt: Date.now(),
        executionCount: 1,
        successCount: 1,
        failureCount: 0,
        averageExecutionTime: 45000
      }
    }
  ],
  triggers: [],
  variables: {
    tenderId: testTender._id,
    priority: testTender.priority
  },
  metadata: {
    createdBy: "system",
    createdAt: Date.now(),
    lastModified: Date.now(),
    lastModifiedBy: "admin",
    executionCount: 25,
    successRate: 96.0,
    averageExecutionTime: 1800000
  },
  settings: {
    timeout: 3600000,
    maxRetries: 3,
    errorHandling: "stop",
    notifications: [],
    isActive: true
  }
};

// Test: Chat type usage
const testChatMessage: ChatMessage = {
  id: "msg_001",
  threadId: "thread_tender_123",
  senderId: "user_001",
  senderType: "user",
  type: "text",
  content: "Can we review the executive summary draft?",
  timestamp: Date.now(),
  status: "sent",
  mentions: ["agent_writer_01"],
  metadata: {
    tenderId: testTender._id,
    sectionId: testBidSection._id,
    priority: "medium",
    tags: ["review", "executive-summary"]
  },
  reactions: [
    {
      emoji: "👍",
      count: 2,
      users: ["user_002", "user_003"],
      addedAt: Date.now()
    }
  ]
};

// Test: File type usage
const testFileUpload: FileUpload = {
  id: "file_001",
  name: "RFP-500-Bourke-Street.pdf",
  originalName: "Request for Proposal - 500 Bourke Street Office Cleaning.pdf",
  type: "pdf",
  mimeType: "application/pdf",
  size: 2457600,
  status: "processed",
  category: "tender_document",
  accessLevel: "internal",
  url: "https://storage.example.com/files/file_001.pdf",
  thumbnailUrl: "https://storage.example.com/thumbnails/file_001.jpg",
  storageKey: "tenders/2024/file_001.pdf",
  checksum: "sha256:abc123def456...",
  metadata: {
    uploadedBy: "user_001",
    uploadedAt: Date.now(),
    lastModified: Date.now(),
    downloadCount: 5,
    viewCount: 12,
    pages: 45,
    encoding: "UTF-8",
    language: "en"
  },
  tags: ["rfp", "cleaning", "bourke-street"],
  description: "Request for Proposal document for office cleaning services",
  isVersioned: true,
  versions: [
    {
      id: "v1",
      version: "1.0",
      url: "https://storage.example.com/files/file_001_v1.pdf",
      size: 2457600,
      checksum: "sha256:abc123def456...",
      createdAt: Date.now(),
      createdBy: "user_001",
      isActive: true
    }
  ],
  permissions: [
    {
      userId: "user_001",
      permission: "admin",
      grantedBy: "system",
      grantedAt: Date.now()
    }
  ],
  virusStatus: "clean",
  processing: []
};

// Test: Document Analysis type usage
const testDocumentAnalysis: DocumentAnalysis = {
  id: "analysis_001",
  fileId: testFileUpload.id,
  status: "completed",
  type: "requirement_extraction",
  startedAt: Date.now() - 300000,
  completedAt: Date.now(),
  results: {
    textContent: "Extracted text content from the RFP document...",
    requirements: [
      {
        id: "req_001",
        text: "Cleaning services must be provided 5 days per week",
        type: "functional",
        priority: "mandatory",
        section: "Service Requirements",
        page: 5,
        confidence: 0.95,
        relatedRequirements: ["req_002"],
        complianceStatus: "unknown",
        metadata: {
          wordCount: 10,
          keywords: ["cleaning", "5 days", "weekly"],
          category: "schedule",
          complexity: "simple"
        }
      }
    ],
    entities: [
      {
        text: "500 Bourke Street",
        type: "location",
        confidence: 0.98,
        position: {
          page: 1,
          x: 100,
          y: 200,
          width: 150,
          height: 20
        }
      }
    ],
    sentiment: {
      overall: "neutral",
      score: 0.05,
      confidence: 0.85,
      breakdown: {
        positive: 0.3,
        neutral: 0.65,
        negative: 0.05
      },
      aspects: []
    }
  },
  metadata: {
    processingTime: 45000,
    confidence: 0.92,
    model: "gpt-4",
    version: "1.0"
  }
};

// Test: API Response types
const testApiResponse: ApiResponse<Tender> = {
  success: true,
  data: testTender,
  message: "Tender retrieved successfully",
  timestamp: Date.now()
};

const testPaginatedResponse: PaginatedResponse<BidSection> = {
  data: [testBidSection],
  pagination: {
    page: 1,
    limit: 10,
    total: 1,
    hasMore: false
  }
};

// Test: Validation functions
const validationTests = () => {
  // Test tender validation
  const tenderValidation: ValidationResult = validators.tender(testTender);
  console.log('Tender validation:', tenderValidation);

  // Test bid section validation
  const sectionValidation: ValidationResult = validators.bidSection(testBidSection);
  console.log('Bid section validation:', sectionValidation);

  // Test agent validation
  const agentValidation: ValidationResult = validators.agent(testAgent);
  console.log('Agent validation:', agentValidation);

  // Test email validation
  const emailValid: boolean = validators.email("<EMAIL>");
  console.log('Email validation:', emailValid);

  // Test URL validation
  const urlValid: boolean = validators.url("https://example.com");
  console.log('URL validation:', urlValid);
};

// Test: Type constraints and relationships
const testTypeConstraints = () => {
  // Test that tender ID matches in bid section
  const isValidRelation = testBidSection.tenderId === testTender._id;
  console.log('Valid tender-section relationship:', isValidRelation);

  // Test status type constraints
  const validStatuses: TenderStatus[] = ['draft', 'bid_writing', 'review'];
  const validSectionStatuses: BidSectionStatus[] = ['draft', 'in_progress', 'approved'];
  const validAgentStatuses: AgentStatus[] = ['active', 'busy', 'idle'];

  console.log('Status types defined correctly');
};

// Test: Union types and discriminated unions
const testUnionTypes = () => {
  // Test message type discrimination
  const textMessage: ChatMessage = {
    ...testChatMessage,
    type: "text"
  };

  const fileMessage: ChatMessage = {
    ...testChatMessage,
    type: "file",
    attachments: [
      {
        id: "att_001",
        type: "file",
        name: "document.pdf",
        url: "https://example.com/doc.pdf",
        size: 1024,
        mimeType: "application/pdf",
        uploadedAt: Date.now(),
        uploadedBy: "user_001",
        metadata: {}
      }
    ]
  };

  console.log('Union types work correctly');
};

// Test: Generic types and utility types
const testGenericTypes = () => {
  // Test API response with different data types
  const stringResponse: ApiResponse<string> = {
    success: true,
    data: "Hello World",
    timestamp: Date.now()
  };

  const numberResponse: ApiResponse<number> = {
    success: true,
    data: 42,
    timestamp: Date.now()
  };

  // Test paginated response with different data types
  const userPaginatedResponse: PaginatedResponse<User> = {
    data: [],
    pagination: {
      page: 1,
      limit: 10,
      total: 0,
      hasMore: false
    }
  };

  console.log('Generic types work correctly');
};

// Export test functions for potential use
export {
  validationTests,
  testTypeConstraints,
  testUnionTypes,
  testGenericTypes,
  testTender,
  testBidSection,
  testAgent,
  testWorkflow,
  testChatMessage,
  testFileUpload,
  testDocumentAnalysis
};

// Type assertion tests (compile-time only)
type AssertEqual<T, U> = T extends U ? (U extends T ? true : false) : false;

// Test that our types extend the expected base types correctly
type TenderIdIsCorrect = AssertEqual<Tender['_id'], Id<'tenders'>>;
type BidSectionIdIsCorrect = AssertEqual<BidSection['_id'], Id<'bidSections'>>;
type TenderStatusIsUnion = AssertEqual<TenderStatus, 'draft' | 'bid_writing' | 'review' | 'submitted' | 'won' | 'lost' | 'cancelled'>;

// These should all be true at compile time
const typeTests: [TenderIdIsCorrect, BidSectionIdIsCorrect, TenderStatusIsUnion] = [true, true, true];

console.log('All type definitions are working correctly!');
export default typeTests;