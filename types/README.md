# Type Definitions for Bid Writing Tender Studio

This directory contains comprehensive TypeScript type definitions for the ARA Property Services Bid Writing Tender Studio platform.

## Overview

The type definitions are organized into several modules, each focusing on a specific domain of the application:

- **`tender.ts`** - Core tender and bid section types
- **`agent.ts`** - AI agent definitions and task management
- **`workflow.ts`** - Workflow orchestration and process management
- **`chat.ts`** - Chat and communication interfaces
- **`file.ts`** - File management and document processing
- **`index.ts`** - Main export file with common utility types

## Module Descriptions

### 1. Tender Types (`tender.ts`)

**Purpose**: Defines the core data structures for tender management and bid writing.

**Key Interfaces**:
- `Tender` - Main tender document with metadata
- `BidSection` - Individual sections within a tender
- `TenderWithSections` - Tender with associated bid sections
- `TenderProgress` - Progress tracking for tender completion
- `TenderAnalytics` - Analytics and reporting data

**Key Features**:
- Comprehensive status tracking
- Priority and type categorization
- Template system for reusable tender structures
- Advanced filtering and search capabilities
- Export functionality for multiple formats

### 2. Agent Types (`agent.ts`)

**Purpose**: Defines AI agent architecture for automated bid writing tasks.

**Key Interfaces**:
- `BidWritingAgent` - Core agent definition with capabilities
- `AgentTask` - Task assignment and execution tracking
- `AgentCoordinator` - Multi-agent coordination system
- `AgentPerformance` - Performance metrics and analytics
- `AgentTraining` - Training data and feedback loops

**Key Features**:
- Multi-model support (GPT-4, Claude, etc.)
- Task specialization by section type
- Performance monitoring and optimization
- Collaborative agent workflows
- Training and continuous improvement

### 3. Workflow Types (`workflow.ts`)

**Purpose**: Orchestrates complex business processes and agent coordination.

**Key Interfaces**:
- `Workflow` - Complete workflow definition
- `WorkflowStep` - Individual steps in a workflow
- `WorkflowExecution` - Runtime execution tracking
- `WorkflowTemplate` - Reusable workflow templates
- `WorkflowAnalytics` - Performance and optimization data

**Key Features**:
- Visual workflow designer support
- Conditional logic and parallel execution
- Error handling and retry mechanisms
- Scheduling and triggers
- Performance optimization recommendations

### 4. Chat Types (`chat.ts`)

**Purpose**: Enables real-time communication and collaboration.

**Key Interfaces**:
- `ChatMessage` - Individual chat messages
- `ChatThread` - Conversation threads
- `ChatParticipant` - Users and agents in conversations
- `ChatAIAssistant` - AI-powered chat assistants
- `ChatAnalytics` - Communication analytics

**Key Features**:
- Multi-participant conversations
- File attachments and rich media
- AI assistant integration
- Message search and analytics
- Moderation and compliance tools

### 5. File Types (`file.ts`)

**Purpose**: Manages file uploads, processing, and document analysis.

**Key Interfaces**:
- `FileUpload` - File metadata and storage
- `DocumentAnalysis` - AI-powered document analysis
- `DocumentStructure` - Extracted document structure
- `FileProcessingJob` - Background processing tasks
- `FileStorage` - Storage provider management

**Key Features**:
- Multi-format document support
- OCR and text extraction
- Requirement extraction from RFP documents
- Version control and collaboration
- Advanced search and organization

## Usage Examples

### Basic Tender Creation

```typescript
import { CreateTenderInput, TenderType, TenderPriority } from './types';

const newTender: CreateTenderInput = {
  name: "Office Cleaning Services - 500 Bourke Street",
  clientName: "Property Management Corp",
  dueDate: "2024-01-15",
  estimatedValue: 250000,
  type: "cleaning_services",
  priority: "high",
  sections: [
    {
      title: "Executive Summary",
      wordLimit: 500,
      scoreWeight: 20,
      type: "executive_summary"
    },
    {
      title: "Technical Approach",
      wordLimit: 1000,
      scoreWeight: 30,
      type: "technical_proposal"
    }
  ]
};
```

### Agent Task Assignment

```typescript
import { AgentTask, AgentTaskType, AgentTaskInput } from './types';

const task: AgentTask = {
  id: "task_123",
  agentId: "agent_writer_01",
  type: "generate_content",
  status: "pending",
  priority: "high",
  tenderId: "tender_456",
  sectionId: "section_789",
  assignedAt: Date.now(),
  input: {
    instructions: "Generate executive summary for cleaning services tender",
    context: {
      tenderName: "Office Cleaning Services",
      clientName: "Property Management Corp",
      sectionTitle: "Executive Summary",
      wordLimit: 500,
      scoreWeight: 20
    }
  },
  progress: 0,
  metadata: {
    retryCount: 0
  }
};
```

### Workflow Definition

```typescript
import { Workflow, WorkflowStep, WorkflowType } from './types';

const bidWritingWorkflow: Workflow = {
  id: "workflow_bid_writing",
  name: "Standard Bid Writing Process",
  type: "bid_writing",
  status: "active",
  version: "1.0",
  steps: [
    {
      id: "step_1",
      name: "Document Analysis",
      type: "task",
      order: 1,
      dependencies: [],
      configuration: {
        taskType: "analyze_requirements",
        agentType: "analyst"
      }
    },
    {
      id: "step_2",
      name: "Content Generation",
      type: "parallel",
      order: 2,
      dependencies: ["step_1"],
      configuration: {
        parallelTasks: [
          {
            id: "generate_executive_summary",
            name: "Executive Summary",
            taskType: "generate_content",
            required: true
          }
        ]
      }
    }
  ],
  triggers: [],
  variables: {},
  metadata: {
    createdBy: "system",
    createdAt: Date.now(),
    lastModified: Date.now(),
    lastModifiedBy: "system",
    executionCount: 0,
    successRate: 0,
    averageExecutionTime: 0
  },
  settings: {
    timeout: 3600000,
    maxRetries: 3,
    errorHandling: "stop",
    notifications: [],
    isActive: true
  }
};
```

## Type Alignment with Convex Schema

All type definitions are carefully aligned with the existing Convex schema:

- **Database IDs**: Uses `Id<'tableName'>` from Convex
- **Field Names**: Matches exactly with schema field names
- **Data Types**: Consistent with Convex value types
- **Relationships**: Properly typed foreign key relationships

## Extension Points

The type system is designed to be extensible:

1. **Custom Agent Types**: Add new agent capabilities and specializations
2. **Workflow Steps**: Define custom workflow step types
3. **Document Processors**: Add new document analysis types
4. **File Storage**: Support additional storage providers
5. **Chat Integrations**: Add new communication channels

## Best Practices

1. **Import Specific Types**: Use named imports for better tree-shaking
2. **Type Guards**: Implement runtime type checking for API boundaries
3. **Generic Constraints**: Use generic types for reusable components
4. **Documentation**: Add JSDoc comments for complex types
5. **Validation**: Use schema validation libraries like Zod

## Contributing

When adding new types:

1. Place them in the appropriate module file
2. Export them in the main `index.ts` file
3. Update this README with usage examples
4. Ensure alignment with Convex schema
5. Add JSDoc documentation

## Type Safety

The type definitions provide compile-time safety for:

- **API Endpoints**: Request/response types
- **Database Operations**: Query and mutation parameters
- **Component Props**: React component interfaces
- **State Management**: Store and reducer types
- **Event Handlers**: Event and callback types

This comprehensive type system ensures type safety across the entire application while maintaining flexibility for future enhancements.