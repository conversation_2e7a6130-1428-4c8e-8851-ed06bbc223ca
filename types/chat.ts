/**
 * Chat and Communication Type Definitions
 * 
 * Interfaces for chat functionality and communication between users and agents
 */

import { Id } from '../convex/_generated/dataModel';
import { BidSectionType } from './tender';

// Chat message types
export type ChatMessageType = 
  | 'text'
  | 'file'
  | 'image'
  | 'code'
  | 'suggestion'
  | 'review'
  | 'approval'
  | 'system'
  | 'notification';

export type ChatMessageStatus = 
  | 'sending'
  | 'sent'
  | 'delivered'
  | 'read'
  | 'failed'
  | 'deleted';

export type ChatParticipantType = 
  | 'user'
  | 'agent'
  | 'system'
  | 'reviewer'
  | 'admin';

export type ChatThreadType = 
  | 'general'
  | 'tender_discussion'
  | 'section_review'
  | 'agent_collaboration'
  | 'support'
  | 'announcement';

export type ChatThreadStatus = 
  | 'active'
  | 'archived'
  | 'closed'
  | 'pinned'
  | 'private';

// Core chat interfaces
export interface ChatMessage {
  id: string;
  threadId: string;
  senderId: string;
  senderType: ChatParticipantType;
  type: ChatMessageType;
  content: string;
  timestamp: number;
  status: ChatMessageStatus;
  editedAt?: number;
  replyToId?: string;
  mentions?: string[];
  attachments?: ChatAttachment[];
  reactions?: ChatReaction[];
  metadata: {
    tenderId?: Id<'tenders'>;
    sectionId?: Id<'bidSections'>;
    agentTaskId?: string;
    workflowId?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    tags?: string[];
    context?: Record<string, any>;
  };
  formatting?: {
    bold?: boolean;
    italic?: boolean;
    code?: boolean;
    markdown?: boolean;
  };
  ai?: {
    generated: boolean;
    model?: string;
    confidence?: number;
    tokens?: number;
  };
}

export interface ChatThread {
  id: string;
  name: string;
  description?: string;
  type: ChatThreadType;
  status: ChatThreadStatus;
  participants: ChatParticipant[];
  messages: ChatMessage[];
  createdAt: number;
  lastActivity: number;
  lastMessageId?: string;
  metadata: {
    tenderId?: Id<'tenders'>;
    sectionId?: Id<'bidSections'>;
    projectId?: string;
    createdBy: string;
    messageCount: number;
    unreadCount: number;
    isNotificationEnabled: boolean;
    tags?: string[];
  };
  settings: {
    isPrivate: boolean;
    allowFileUploads: boolean;
    allowAgentAccess: boolean;
    retentionDays?: number;
    moderationLevel: 'none' | 'basic' | 'strict';
  };
}

export interface ChatParticipant {
  id: string;
  type: ChatParticipantType;
  name: string;
  avatar?: string;
  role: 'owner' | 'admin' | 'member' | 'guest';
  permissions: ChatPermission[];
  joinedAt: number;
  lastSeen?: number;
  isOnline: boolean;
  status: 'active' | 'away' | 'busy' | 'offline';
  metadata: {
    messageCount: number;
    unreadCount: number;
    mutedUntil?: number;
    preferences: {
      notifications: boolean;
      soundEnabled: boolean;
      mentionOnly: boolean;
    };
  };
}

export type ChatPermission = 
  | 'read_messages'
  | 'send_messages'
  | 'send_files'
  | 'delete_messages'
  | 'edit_messages'
  | 'manage_participants'
  | 'manage_settings'
  | 'moderate_content';

export interface ChatAttachment {
  id: string;
  type: 'file' | 'image' | 'document' | 'audio' | 'video' | 'link';
  name: string;
  url: string;
  size: number;
  mimeType: string;
  thumbnail?: string;
  metadata: {
    width?: number;
    height?: number;
    duration?: number;
    pages?: number;
    description?: string;
  };
  uploadedAt: number;
  uploadedBy: string;
}

export interface ChatReaction {
  emoji: string;
  count: number;
  users: string[];
  addedAt: number;
}

// Chat activity and notifications
export interface ChatActivity {
  id: string;
  threadId: string;
  userId: string;
  type: ChatActivityType;
  timestamp: number;
  metadata?: Record<string, any>;
}

export type ChatActivityType = 
  | 'message_sent'
  | 'message_edited'
  | 'message_deleted'
  | 'file_uploaded'
  | 'user_joined'
  | 'user_left'
  | 'thread_created'
  | 'thread_archived'
  | 'reaction_added'
  | 'reaction_removed';

export interface ChatNotification {
  id: string;
  userId: string;
  threadId: string;
  messageId?: string;
  type: ChatNotificationType;
  title: string;
  message: string;
  timestamp: number;
  isRead: boolean;
  readAt?: number;
  metadata: {
    priority: 'low' | 'medium' | 'high' | 'urgent';
    category: string;
    actionable: boolean;
    deepLink?: string;
  };
}

export type ChatNotificationType = 
  | 'mention'
  | 'reply'
  | 'new_message'
  | 'thread_update'
  | 'assignment'
  | 'review_request'
  | 'approval_needed'
  | 'deadline_reminder'
  | 'system_alert';

// Chat search and filtering
export interface ChatSearch {
  query: string;
  filters: {
    threadIds?: string[];
    senderIds?: string[];
    messageTypes?: ChatMessageType[];
    dateRange?: {
      start: number;
      end: number;
    };
    hasAttachments?: boolean;
    tags?: string[];
    tenderId?: Id<'tenders'>;
    sectionId?: Id<'bidSections'>;
  };
  sort: {
    field: 'timestamp' | 'relevance' | 'sender';
    direction: 'asc' | 'desc';
  };
  pagination: {
    page: number;
    limit: number;
  };
}

export interface ChatSearchResult {
  messages: ChatMessage[];
  threads: ChatThread[];
  totalCount: number;
  hasMore: boolean;
  facets: {
    senders: Record<string, number>;
    threads: Record<string, number>;
    types: Record<ChatMessageType, number>;
    dates: Record<string, number>;
  };
}

// Chat analytics
export interface ChatAnalytics {
  threadId?: string;
  period: {
    start: number;
    end: number;
  };
  metrics: {
    totalMessages: number;
    totalThreads: number;
    activeParticipants: number;
    averageResponseTime: number;
    messagesByType: Record<ChatMessageType, number>;
    messagesByParticipant: Record<string, number>;
    peakActivity: {
      hour: number;
      day: string;
      count: number;
    };
  };
  trends: {
    dailyMessages: number[];
    participantGrowth: number[];
    responseTime: number[];
  };
  insights: {
    mostActiveParticipants: string[];
    popularTopics: string[];
    averageThreadLifespan: number;
    engagementScore: number;
  };
}

// Chat AI integration
export interface ChatAIAssistant {
  id: string;
  name: string;
  description: string;
  avatar?: string;
  capabilities: ChatAICapability[];
  model: string;
  systemPrompt: string;
  isActive: boolean;
  settings: {
    autoRespond: boolean;
    responseDelay: number;
    maxResponseLength: number;
    temperature: number;
    contextWindow: number;
    mentionTrigger: boolean;
    keywordTriggers: string[];
  };
  metadata: {
    createdAt: number;
    lastActive: number;
    conversationCount: number;
    averageRating: number;
    totalTokens: number;
  };
}

export type ChatAICapability = 
  | 'question_answering'
  | 'content_generation'
  | 'document_analysis'
  | 'translation'
  | 'summarization'
  | 'code_assistance'
  | 'scheduling'
  | 'task_creation'
  | 'data_lookup';

export interface ChatAIResponse {
  messageId: string;
  assistantId: string;
  prompt: string;
  response: string;
  confidence: number;
  tokensUsed: number;
  processingTime: number;
  sources?: string[];
  suggestions?: string[];
  metadata: {
    model: string;
    temperature: number;
    context: string[];
    intent: string;
    sentiment: 'positive' | 'neutral' | 'negative';
  };
}

// Chat templates and shortcuts
export interface ChatTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  content: string;
  variables: string[];
  type: 'message' | 'thread' | 'response';
  tags: string[];
  usageCount: number;
  createdBy: string;
  createdAt: number;
  isPublic: boolean;
}

export interface ChatShortcut {
  id: string;
  trigger: string;
  action: ChatShortcutAction;
  isActive: boolean;
  scope: 'global' | 'thread' | 'personal';
  createdBy: string;
  createdAt: number;
}

export type ChatShortcutAction = 
  | 'insert_text'
  | 'create_task'
  | 'mention_user'
  | 'upload_file'
  | 'create_thread'
  | 'run_command'
  | 'generate_content';

// Chat moderation
export interface ChatModerationRule {
  id: string;
  name: string;
  description: string;
  type: 'content_filter' | 'rate_limit' | 'spam_detection' | 'profanity_filter';
  condition: string;
  action: 'warn' | 'delete' | 'mute' | 'ban' | 'flag';
  severity: 'low' | 'medium' | 'high' | 'critical';
  isActive: boolean;
  autoApply: boolean;
  metadata: {
    createdBy: string;
    createdAt: number;
    triggerCount: number;
    lastTriggered?: number;
  };
}

export interface ChatModerationAction {
  id: string;
  ruleId: string;
  messageId: string;
  userId: string;
  action: string;
  reason: string;
  timestamp: number;
  moderatorId: string;
  isReversed: boolean;
  metadata?: Record<string, any>;
}

// Chat export and backup
export interface ChatExport {
  id: string;
  name: string;
  description: string;
  type: 'full' | 'thread' | 'user' | 'date_range';
  format: 'json' | 'csv' | 'pdf' | 'html';
  filters: {
    threadIds?: string[];
    userIds?: string[];
    dateRange?: {
      start: number;
      end: number;
    };
    includeAttachments: boolean;
    includeDeleted: boolean;
  };
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdBy: string;
  createdAt: number;
  completedAt?: number;
  downloadUrl?: string;
  expiresAt?: number;
  metadata: {
    messageCount: number;
    fileSize: number;
    error?: string;
  };
}