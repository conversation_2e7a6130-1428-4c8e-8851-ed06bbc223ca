/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable experimental features for better performance
  experimental: {
    // Enable React 19 features
    ppr: false,
  },
  
  // Configure environment variables
  env: {
    NEXT_PUBLIC_CONVEX_URL: process.env.NEXT_PUBLIC_CONVEX_URL || 'https://your-convex-deployment.convex.cloud',
  },
  
  // Optimize for production
  reactStrictMode: true,
  
  // Configure webpack for better performance
  webpack: (config, { isServer }) => {
    // Optimize bundle size
    config.optimization.splitChunks = {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    };
    
    return config;
  },
  
  // Configure TypeScript
  typescript: {
    // Dangerously allow production builds to successfully complete even if
    // your project has TypeScript errors.
    // !! WARN !!
    // Uncomment the next line if you want to ignore TypeScript errors during build
    // ignoreBuildErrors: true,
  },
  
  // Configure ESLint
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    // ignoreDuringBuilds: true,
  },
  
  // Configure images
  images: {
    domains: [],
  },
  
  // Configure redirects if needed
  async redirects() {
    return [];
  },
  
  // Configure rewrites if needed
  async rewrites() {
    return [];
  },
};

export default nextConfig;