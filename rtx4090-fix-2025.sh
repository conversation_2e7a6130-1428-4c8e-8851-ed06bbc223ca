#!/bin/bash

echo "🚀 RTX 4090 Fix Script - 2024/2025 Latest Best Practices"
echo "========================================================"
echo ""

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if running as root
if [ "$EUID" -ne 0 ]; then 
    echo -e "${RED}❌ Please run as root${NC}"
    exit 1
fi

echo -e "${BLUE}🔍 Detecting system...${NC}"
OS_ID=$(grep ^ID= /etc/os-release | cut -d= -f2 | tr -d '"')
OS_VERSION=$(grep ^VERSION_ID= /etc/os-release | cut -d= -f2 | tr -d '"')
KERNEL_VERSION=$(uname -r)

echo "System: $OS_ID $OS_VERSION"
echo "Kernel: $KERNEL_VERSION"
echo ""

# 1. Check for RTX 4090 hardware presence
echo -e "${BLUE}1️⃣ Checking RTX 4090 Hardware...${NC}"
RTX4090_DETECTED=$(lspci | grep -i "NVIDIA Corporation Device 2684\|RTX 4090")
if [ -z "$RTX4090_DETECTED" ]; then
    echo -e "${RED}❌ RTX 4090 not detected in PCIe slots${NC}"
    echo "Hardware checks:"
    echo "1. Ensure GPU is properly seated in PCIe x16 slot"
    echo "2. Check power connections (4x PCIe 8-pin OR 450W+ PCIe Gen 5)"
    echo "3. Verify PSU minimum 850W capacity"
    echo "4. BIOS settings: Enable Resizable BAR, Above 4G Decoding, Disable CSM"
    lspci | grep -i nvidia
    exit 1
else
    echo -e "${GREEN}✅ RTX 4090 detected:${NC}"
    echo "$RTX4090_DETECTED"
fi

# 2. Remove old/incompatible drivers (Critical for RTX 4090)
echo ""
echo -e "${BLUE}2️⃣ Removing Old Drivers...${NC}"
echo "Purging old NVIDIA drivers..."
apt-get purge -y nvidia-*
apt-get autoremove -y
apt-get autoclean

# Remove old kernel modules
rmmod nvidia_uvm nvidia_drm nvidia_modeset nvidia 2>/dev/null || true

# 3. Install latest kernel headers (Required for RTX 4090)
echo ""
echo -e "${BLUE}3️⃣ Installing Kernel Headers...${NC}"
apt-get update
apt-get install -y linux-headers-$(uname -r) build-essential dkms

# 4. Install NVIDIA driver 535+ with open kernel module (2024/2025 requirement)
echo ""
echo -e "${BLUE}4️⃣ Installing Latest NVIDIA Driver (535+ with Open Kernel)...${NC}"

if [ "$OS_ID" = "ubuntu" ]; then
    # Ubuntu method
    add-apt-repository -y ppa:graphics-drivers/ppa
    apt-get update
    
    # Critical: Use open kernel module for RTX 4090
    apt-get install -y nvidia-driver-535 nvidia-open-kernel-dkms
    
    # Remove standard kernel module if installed
    apt-get remove -y nvidia-kernel-dkms
    
elif [ "$OS_ID" = "debian" ]; then
    # Debian method
    echo "deb http://deb.debian.org/debian/ bookworm main contrib non-free non-free-firmware" >> /etc/apt/sources.list
    apt-get update
    apt-get install -y nvidia-driver nvidia-open-kernel-dkms
    
else
    # Generic method using NVIDIA's repository
    echo "Using NVIDIA's official repository..."
    
    # Add NVIDIA repository
    wget https://developer.download.nvidia.com/compute/cuda/repos/ubuntu2204/x86_64/cuda-keyring_1.0-1_all.deb
    dpkg -i cuda-keyring_1.0-1_all.deb
    apt-get update
    
    # Install driver with open kernel module
    apt-get install -y nvidia-driver-535 nvidia-open-kernel-dkms
fi

# 5. Install NVIDIA Container Toolkit (Latest 2024/2025)
echo ""
echo -e "${BLUE}5️⃣ Installing NVIDIA Container Toolkit...${NC}"

# Add NVIDIA Container Toolkit repository
curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg
curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
    sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
    tee /etc/apt/sources.list.d/nvidia-container-toolkit.list

apt-get update
apt-get install -y nvidia-container-toolkit

# Configure Docker for GPU
if command -v docker &> /dev/null; then
    nvidia-ctk runtime configure --runtime=docker
    systemctl restart docker
else
    echo "Docker not found. Installing..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sh get-docker.sh
    nvidia-ctk runtime configure --runtime=docker
    systemctl restart docker
fi

# 6. Configure Docker daemon for RTX 4090 (2024/2025 optimizations)
echo ""
echo -e "${BLUE}6️⃣ Configuring Docker for RTX 4090...${NC}"
cat > /etc/docker/daemon.json <<EOF
{
    "default-runtime": "nvidia",
    "runtimes": {
        "nvidia": {
            "path": "nvidia-container-runtime",
            "runtimeArgs": []
        }
    },
    "default-shm-size": "1g",
    "default-ulimits": {
        "memlock": {
            "Name": "memlock",
            "Hard": -1,
            "Soft": -1
        },
        "stack": {
            "Name": "stack",
            "Hard": 67108864,
            "Soft": 67108864
        }
    }
}
EOF

systemctl restart docker

# 7. Set optimal GPU configuration for RTX 4090
echo ""
echo -e "${BLUE}7️⃣ Configuring RTX 4090 Settings...${NC}"

# Wait for driver to load
sleep 5

# Enable persistence mode (critical for stability)
nvidia-smi -pm 1

# Set power limit to maximum (RTX 4090 = 450W)
nvidia-smi -pl 450

# Set performance mode
nvidia-smi -ac 1215,2610  # Memory,Graphics clock for RTX 4090

# Configure GPU performance mode
cat > /etc/modprobe.d/nvidia.conf <<EOF
# RTX 4090 optimizations
options nvidia NVreg_RestrictProfilingToAdminUsers=0
options nvidia NVreg_TemporaryFilePath=/tmp
options nvidia NVreg_UsePageAttributeTable=1
options nvidia NVreg_EnableGpuFirmware=1
EOF

# 8. Create systemd service for GPU initialization
echo ""
echo -e "${BLUE}8️⃣ Creating GPU Initialization Service...${NC}"
cat > /etc/systemd/system/nvidia-init.service <<EOF
[Unit]
Description=NVIDIA GPU Initialization for RTX 4090
After=graphical.target

[Service]
Type=oneshot
ExecStart=/usr/bin/nvidia-smi -pm 1
ExecStart=/usr/bin/nvidia-smi -pl 450
RemainAfterExit=yes

[Install]
WantedBy=graphical.target
EOF

systemctl enable nvidia-init.service

# 9. Install monitoring tools
echo ""
echo -e "${BLUE}9️⃣ Installing GPU Monitoring Tools...${NC}"
apt-get install -y nvtop htop iotop

# 10. Final verification
echo ""
echo -e "${BLUE}🔟 Final Verification...${NC}"

# Check if driver loaded
if ! command -v nvidia-smi &> /dev/null; then
    echo -e "${RED}❌ Driver installation failed. Reboot required.${NC}"
    echo "Run: reboot && /tmp/rtx4090-fix-2025.sh"
    exit 1
fi

# Test basic functionality
echo "Testing basic GPU functionality..."
nvidia-smi

# Test Docker GPU access
echo ""
echo "Testing Docker GPU access..."
DOCKER_TEST=$(timeout 30 docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi 2>&1)
if echo "$DOCKER_TEST" | grep -q "RTX 4090"; then
    echo -e "${GREEN}✅ Docker GPU access working!${NC}"
else
    echo -e "${YELLOW}⚠️  Docker GPU test failed. May need reboot.${NC}"
    echo "$DOCKER_TEST"
fi

# Performance test
echo ""
echo "Running quick performance test..."
timeout 10 docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 \
    sh -c 'echo "GPU Compute Capability:" && nvidia-smi --query-gpu=compute_cap --format=csv,noheader'

# 11. Display final status
echo ""
echo -e "${GREEN}🎉 RTX 4090 Setup Complete!${NC}"
echo "========================================="
echo ""
echo -e "${BLUE}System Status:${NC}"
nvidia-smi --query-gpu=name,driver_version,power.limit,temperature.gpu,memory.total --format=csv

echo ""
echo -e "${BLUE}Docker Test Command:${NC}"
echo "docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi"

echo ""
echo -e "${BLUE}Monitoring Commands:${NC}"
echo "nvtop           - Interactive GPU monitor"
echo "nvidia-smi -l  - Continuous GPU status"
echo "watch -n 1 'nvidia-smi --query-gpu=temperature.gpu,power.draw --format=csv'"

echo ""
echo -e "${YELLOW}⚠️  Important Notes for RTX 4090:${NC}"
echo "1. Power limit is ~350W on Linux (vs 500W on Windows) - this is normal"
echo "2. P2P communication disabled for consumer cards - use single GPU workflows"
echo "3. If 'GPU fallen off bus' errors occur, check power supply capacity"
echo "4. Performance ~90% of Windows due to Linux driver limitations"

echo ""
echo -e "${GREEN}✅ Ready for AI workloads!${NC}"

# Create a quick test script
cat > /usr/local/bin/gpu-test <<EOF
#!/bin/bash
echo "🧪 RTX 4090 Quick Test"
echo "====================="
nvidia-smi
echo ""
echo "Docker GPU Test:"
docker run --rm --gpus all nvidia/cuda:12.1.0-base-ubuntu22.04 nvidia-smi --query-gpu=name,memory.used,memory.total --format=csv
EOF
chmod +x /usr/local/bin/gpu-test

echo "Created 'gpu-test' command for quick testing"