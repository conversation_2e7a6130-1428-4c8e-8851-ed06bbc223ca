# 🎭 ProHeadshots Clone with Convex & Resend - Development Prompt

## Project Overview

Create a professional AI headshot generation web application similar to CVPhoto.app, but using modern stack with Convex for backend/database and Resend for email services.

## 🛠️ Tech Stack Requirements

### Core Framework
- **Frontend**: Next.js 14+ (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn/ui components
- **State Management**: Zustand or React Context

### Backend & Database
- **Backend**: Convex (replacing Supabase)
  - Real-time database
  - File storage
  - Authentication
  - Serverless functions
  - WebSocket subscriptions

### Email Service
- **Email**: Resend (replacing SendGrid)
  - Transactional emails
  - React Email templates
  - Domain verification
  - Analytics

### AI Services (Multi-Provider Support)
- **Primary**: Fal AI (recommended for performance/cost)
- **Alternative**: Replicate API
- **Options**: Together AI, Modal, or custom Flux deployment

### Payment Processing (Multi-Provider)
- **Primary**: Polar (20% lower fees, developer-friendly)
- **Alternative**: Stripe
- **Cryptocurrency**: Optional - Coinbase Commerce

### Additional Services
- **Image Storage**: Convex File Storage or Cloudflare R2
- **Analytics**: Vercel Analytics + PostHog
- **Error Tracking**: Sentry
- **Rate Limiting**: Upstash Redis

## 📋 Core Features to Implement

### 1. Authentication & User Management
```typescript
// Convex schema
export const users = defineTable({
  email: v.string(),
  name: v.optional(v.string()),
  avatarUrl: v.optional(v.string()),
  credits: v.number(),
  subscription: v.optional(v.object({
    status: v.string(),
    plan: v.string(),
    expiresAt: v.optional(v.number()),
  })),
  createdAt: v.number(),
  lastLoginAt: v.number(),
});

export const sessions = defineTable({
  userId: v.id("users"),
  token: v.string(),
  expiresAt: v.number(),
});
```

### 2. AI Headshot Generation Pipeline
```typescript
// Convex function for generation
export const generateHeadshot = mutation({
  args: {
    userId: v.id("users"),
    sourceImageId: v.id("files"),
    style: v.string(),
    options: v.object({
      gender: v.optional(v.string()),
      age: v.optional(v.string()),
      ethnicity: v.optional(v.string()),
      background: v.optional(v.string()),
    }),
  },
  handler: async (ctx, args) => {
    // Check user credits
    // Queue generation job
    // Call AI provider (Fal/Replicate)
    // Store results
    // Send completion email
  },
});
```

### 3. File Upload & Management
```typescript
// File handling with Convex
export const uploadSourceImage = mutation({
  args: {
    userId: v.id("users"),
    storageId: v.string(),
    metadata: v.object({
      fileName: v.string(),
      fileSize: v.number(),
      mimeType: v.string(),
    }),
  },
  handler: async (ctx, args) => {
    // Validate file
    // Store reference
    // Process for AI input
  },
});
```

### 4. Credit System & Payments
```typescript
// Credit management
export const purchaseCredits = mutation({
  args: {
    userId: v.id("users"),
    packageId: v.string(),
    paymentProvider: v.union(v.literal("polar"), v.literal("stripe")),
    paymentIntentId: v.string(),
  },
  handler: async (ctx, args) => {
    // Verify payment
    // Add credits
    // Send receipt email
    // Log transaction
  },
});
```

### 5. Email Templates with Resend & React Email
```typescript
// Welcome email template
import { Html, Button, Text, Container } from '@react-email/components';

export const WelcomeEmail = ({ userName, verificationUrl }) => (
  <Html>
    <Container>
      <Text>Welcome to ProHeadshots, {userName}!</Text>
      <Button href={verificationUrl}>Verify Email</Button>
    </Container>
  </Html>
);

// Send with Resend
await resend.emails.send({
  from: 'ProHeadshots <<EMAIL>>',
  to: user.email,
  subject: 'Welcome to ProHeadshots',
  react: WelcomeEmail({ userName: user.name, verificationUrl }),
});
```

## 🎨 UI/UX Requirements

### Landing Page
- Hero section with AI-generated examples
- Before/after slider
- Pricing cards
- Social proof (testimonials, stats)
- FAQ section

### User Dashboard
- Upload interface with drag-and-drop
- Style selection gallery
- Generation history
- Credit balance display
- Download center

### Generation Flow
1. Upload source photo
2. Select style presets
3. Customize options
4. Review & confirm (show credit cost)
5. Real-time progress updates
6. Gallery of results
7. Download options (individual/bulk)

## 🔧 Implementation Details

### Project Structure
```
proheadshots-convex/
├── app/
│   ├── (auth)/
│   │   ├── login/
│   │   └── signup/
│   ├── (dashboard)/
│   │   ├── generate/
│   │   ├── gallery/
│   │   ├── settings/
│   │   └── billing/
│   ├── api/
│   │   ├── webhooks/
│   │   │   ├── polar/
│   │   │   └── stripe/
│   │   └── generate/
│   └── layout.tsx
├── components/
│   ├── ui/           # Shadcn components
│   ├── dashboard/
│   ├── generation/
│   └── marketing/
├── convex/
│   ├── schema.ts
│   ├── auth.ts
│   ├── users.ts
│   ├── generation.ts
│   ├── payments.ts
│   └── files.ts
├── emails/           # React Email templates
│   ├── welcome.tsx
│   ├── generation-complete.tsx
│   └── receipt.tsx
├── lib/
│   ├── ai-providers/
│   │   ├── fal.ts
│   │   └── replicate.ts
│   ├── payment-providers/
│   │   ├── polar.ts
│   │   └── stripe.ts
│   └── resend.ts
└── public/
    └── examples/     # Sample headshots
```

### Environment Variables
```env
# Convex
CONVEX_DEPLOYMENT=
NEXT_PUBLIC_CONVEX_URL=

# AI Providers
FAL_API_KEY=
REPLICATE_API_TOKEN=

# Payment Providers
POLAR_ACCESS_TOKEN=
POLAR_WEBHOOK_SECRET=
STRIPE_SECRET_KEY=
STRIPE_WEBHOOK_SECRET=

# Email (Resend)
RESEND_API_KEY=
RESEND_FROM_EMAIL=<EMAIL>

# App Config
NEXT_PUBLIC_APP_URL=http://localhost:3000
AI_PROVIDER=fal # or replicate
PAYMENT_PROVIDER=polar # or stripe
```

### Key Convex Schemas
```typescript
// Main tables
export default defineSchema({
  users: defineTable({
    email: v.string(),
    name: v.optional(v.string()),
    credits: v.number(),
    // ... user fields
  }).index("by_email", ["email"]),

  generations: defineTable({
    userId: v.id("users"),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("completed"),
      v.literal("failed")
    ),
    sourceImageId: v.id("_storage"),
    resultImageIds: v.array(v.id("_storage")),
    style: v.string(),
    options: v.any(),
    aiProvider: v.string(),
    cost: v.number(),
    processingTime: v.optional(v.number()),
    createdAt: v.number(),
    completedAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
        .index("by_status", ["status"]),

  transactions: defineTable({
    userId: v.id("users"),
    type: v.union(v.literal("purchase"), v.literal("subscription")),
    amount: v.number(),
    credits: v.number(),
    provider: v.string(),
    providerTransactionId: v.string(),
    status: v.string(),
    createdAt: v.number(),
  }).index("by_user", ["userId"])
    .index("by_provider_id", ["providerTransactionId"]),
});
```

### Real-time Updates with Convex
```typescript
// Client-side subscription
const generations = useQuery(api.generations.listByUser, {
  userId: currentUser?.id,
});

// Real-time progress updates
const currentGeneration = useQuery(api.generations.getCurrent, {
  userId: currentUser?.id,
});

// Update UI based on status
useEffect(() => {
  if (currentGeneration?.status === 'completed') {
    toast.success('Your headshots are ready!');
    router.push('/gallery');
  }
}, [currentGeneration]);
```

## 🚀 Deployment Architecture

### Recommended Deployment
- **Frontend**: Vercel (optimal for Next.js)
- **Convex**: Automatic deployment with Vercel
- **AI Processing**: Background jobs via Convex actions
- **File Storage**: Convex built-in or Cloudflare R2
- **Email**: Resend with custom domain

### Performance Optimizations
- Image optimization with Next.js Image
- Lazy loading for galleries
- Convex query caching
- CDN for generated images
- Progressive image loading

## 🎯 MVP Features Priority

### Phase 1 (Core MVP)
1. ✅ User authentication (email/password)
2. ✅ Single image upload
3. ✅ Basic style selection (5-10 presets)
4. ✅ AI generation with Fal
5. ✅ Download single images
6. ✅ Basic credit system
7. ✅ Polar payment integration

### Phase 2 (Enhanced)
1. 📱 Mobile-responsive design
2. 🎨 More style options
3. 📦 Bulk download (ZIP)
4. 💳 Stripe integration
5. 📧 Email notifications
6. 📊 Basic analytics

### Phase 3 (Scale)
1. 🔄 Subscription plans
2. 🎭 Custom training (Dreambooth)
3. 🌍 Multi-language support
4. 🤝 Team accounts
5. 🔌 API access
6. 🎨 Style marketplace

## 💰 Monetization Strategy

### Credit Packages
- Starter: $9 for 20 headshots
- Professional: $29 for 100 headshots
- Business: $99 for 500 headshots

### Subscriptions
- Monthly Pro: $49/month (200 headshots)
- Monthly Business: $199/month (1000 headshots)

### Enterprise
- Custom pricing
- API access
- Priority processing
- Custom styles

## 🔒 Security Considerations

1. **File Upload Validation**
   - Max file size: 10MB
   - Allowed types: JPG, PNG, WEBP
   - Virus scanning with ClamAV

2. **Rate Limiting**
   - API endpoints: 100 req/min
   - Generation: 10 concurrent per user
   - Uploads: 50 per day

3. **Data Privacy**
   - Auto-delete source images after 30 days
   - GDPR compliance
   - User data export

## 📝 Additional Notes

### Why Convex + Resend?
- **Convex**: Real-time updates, simpler than Supabase, better DX
- **Resend**: Modern email API, React Email support, better analytics

### AI Provider Recommendations
- **Fal AI**: Fastest, most cost-effective
- **Replicate**: More model options, good for experimentation
- **Together AI**: Best for custom models

### Cost Optimization
- Cache generated images
- Compress before storage
- Use webhook queues
- Implement smart retry logic

## 🎬 Getting Started Commands

```bash
# Create Next.js app with TypeScript
npx create-next-app@latest proheadshots-convex --typescript --tailwind --app

# Install dependencies
cd proheadshots-convex
npm install convex @convex-dev/auth @resend/node @fal-ai/client
npm install @radix-ui/react-avatar @radix-ui/react-dialog lucide-react
npm install react-dropzone react-hot-toast zustand

# Setup Convex
npx convex dev

# Install Shadcn UI
npx shadcn-ui@latest init
npx shadcn-ui@latest add button card dialog avatar toast

# Setup email templates
npm install @react-email/components resend

# Payment providers
npm install @polar-sh/sdk @stripe/stripe-js
```

This prompt provides a complete blueprint for building a ProHeadshots clone with modern technologies. The Convex + Resend stack offers better real-time capabilities and developer experience compared to the original Supabase + SendGrid setup.