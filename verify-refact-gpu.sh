#!/bin/bash

echo "🤖 Refact GPU Verification Script"
echo "================================="
echo ""

# Configuration
TAILSCALE_IP="*************"
GPU_PORT="9092"
SSH_TIMEOUT="30"

echo "🔍 Testing GPU Machine Connectivity..."

# Test 1: Tailscale connectivity
echo "1️⃣ Testing Tailscale connectivity..."
if command -v tailscale &> /dev/null; then
    PING_RESULT=$(timeout 10s tailscale ping $TAILSCALE_IP 2>/dev/null | head -1)
    if [[ $PING_RESULT == *"pong"* ]]; then
        echo "✅ Tailscale connectivity working"
    else
        echo "❌ Tailscale ping failed"
    fi
else
    echo "ℹ️  Tailscale CLI not found"
fi

echo ""

# Test 2: SSH connectivity
echo "2️⃣ Testing SSH connectivity..."
if timeout ${SSH_TIMEOUT}s ssh -o ConnectTimeout=10 -o BatchMode=yes root@$TAILSCALE_IP "echo 'SSH connected'" 2>/dev/null; then
    echo "✅ SSH connection successful"
    
    # Test Refact installation
    echo ""
    echo "3️⃣ Checking Refact installation..."
    REFACT_CHECK=$(timeout 30s ssh root@$TAILSCALE_IP "
        if [ -d '/root/refact-setup' ]; then
            echo 'REFACT_DIR_EXISTS'
            cd /root/refact-setup
            if [ -f 'venv/bin/activate' ]; then
                echo 'VENV_EXISTS'
                source venv/bin/activate
                if command -v refact &> /dev/null; then
                    echo 'REFACT_INSTALLED'
                    refact --version 2>/dev/null || echo 'VERSION_UNKNOWN'
                else
                    echo 'REFACT_NOT_FOUND'
                fi
            else
                echo 'VENV_NOT_FOUND'
            fi
        else
            echo 'REFACT_DIR_NOT_FOUND'
        fi
    " 2>/dev/null)
    
    if [[ $REFACT_CHECK == *"REFACT_INSTALLED"* ]]; then
        echo "✅ Refact is installed"
        echo "   $REFACT_CHECK"
    else
        echo "❌ Refact installation issue"
        echo "   $REFACT_CHECK"
    fi
    
    # Test PyTorch CUDA
    echo ""
    echo "4️⃣ Testing PyTorch CUDA support..."
    PYTORCH_CHECK=$(timeout 30s ssh root@$TAILSCALE_IP "
        cd /root/refact-setup && source venv/bin/activate
        python3 -c '
import torch
print(f\"PyTorch: {torch.__version__}\")
print(f\"CUDA Available: {torch.cuda.is_available()}\")
if torch.cuda.is_available():
    print(f\"CUDA Version: {torch.version.cuda}\")
    print(f\"GPU Count: {torch.cuda.device_count()}\")
    print(f\"GPU Name: {torch.cuda.get_device_name(0)}\")
    print(f\"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB\")
else:
    print(\"No CUDA devices available\")
        ' 2>/dev/null
    " 2>/dev/null)
    
    if [[ $PYTORCH_CHECK == *"RTX 4090"* ]]; then
        echo "✅ PyTorch CUDA working with RTX 4090"
        echo "$PYTORCH_CHECK"
    else
        echo "❌ PyTorch CUDA issue"
        echo "$PYTORCH_CHECK"
    fi
    
    # Test Refact configuration
    echo ""
    echo "5️⃣ Checking Refact configuration..."
    CONFIG_CHECK=$(timeout 30s ssh root@$TAILSCALE_IP "
        if [ -f ~/.cache/refact/cli.yaml ]; then
            echo 'CONFIG_EXISTS'
            cat ~/.cache/refact/cli.yaml
        else
            echo 'CONFIG_NOT_FOUND'
        fi
    " 2>/dev/null)
    
    echo "$CONFIG_CHECK"
    
else
    echo "❌ SSH connection failed"
    echo "   Possible issues:"
    echo "   - SSH daemon not running"
    echo "   - Tailscale connectivity issue"
    echo "   - Machine may be down or rebooting"
fi

echo ""

# Test 3: GPU Backend API (should still work)
echo "6️⃣ Testing GPU Backend API..."
HEALTH_RESPONSE=$(curl -s --connect-timeout 10 "http://$TAILSCALE_IP:$GPU_PORT/health" 2>/dev/null)
if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ GPU backend API working"
    echo "   Response: $HEALTH_RESPONSE"
else
    echo "❌ GPU backend API not responding"
    echo "   Response: $HEALTH_RESPONSE"
fi

echo ""

# Generate Refact setup commands
echo "🚀 Refact GPU Setup Commands"
echo "============================="
echo ""
echo "If Refact needs setup/repair, run these commands:"
echo ""
echo "# Connect to GPU machine"
echo "ssh root@$TAILSCALE_IP"
echo ""
echo "# Create/activate Python environment"
echo "cd /root/refact-setup"
echo "source venv/bin/activate"
echo ""
echo "# Test CUDA availability"
echo "python3 -c 'import torch; print(f\"CUDA: {torch.cuda.is_available()}\")'"
echo ""
echo "# Configure Refact for local models"
echo "cat > ~/.cache/refact/cli.yaml << 'EOF'"
echo "# Refact CLI Configuration"
echo "model: \"Refact/1.6b\""
echo "device: \"cuda\""
echo "endpoint_url: null"
echo "api_key: null"
echo "EOF"
echo ""
echo "# Create bring-your-own-key config"
echo "cat > ~/.cache/refact/bring-your-own-key.yaml << 'EOF'"
echo "# Bring Your Own Key Configuration"
echo "models:"
echo "  \"Refact/1.6b\":"
echo "    endpoint: \"http://localhost:8008\""
echo "    api_key: \"not-required\""
echo "    device: \"cuda\""
echo "    model_path: \"/root/.cache/refact/models/Refact--1.6b\""
echo "EOF"
echo ""
echo "# Test Refact"
echo "refact --help"
echo "refact --model \"Refact/1.6b\" --device cuda"
echo ""

# Summary
echo "📊 Verification Summary"
echo "======================"
if [[ $HEALTH_RESPONSE == *"healthy"* ]]; then
    echo "✅ GPU backend monitoring system operational"
else
    echo "❌ GPU backend monitoring system offline"
fi

echo ""
echo "🎯 Next Steps:"
echo "1. If SSH is working, complete Refact configuration"
echo "2. If SSH is down, check machine status and reboot if needed"
echo "3. Test Refact code completion functionality"
echo "4. Integrate Refact with your development workflow"
echo ""
echo "🔗 GPU Monitor Dashboard: http://$TAILSCALE_IP:$GPU_PORT"
echo "📊 Real-time GPU Status: curl http://$TAILSCALE_IP:$GPU_PORT/api/gpu/status"