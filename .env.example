# Convex deployment
CONVEX_DEPLOYMENT=

# OpenAI Configuration (for document analysis)
CONVEX_OPENAI_API_KEY=your-openai-api-key
CONVEX_OPENAI_BASE_URL=https://api.openai.com/v1

# Gmail API Configuration (for email listener)
GMAIL_CLIENT_ID=your-gmail-client-id
GMAIL_CLIENT_SECRET=your-gmail-client-secret
GMAIL_REDIRECT_URI=http://localhost:3000/api/gmail/callback
GMAIL_PUBSUB_TOPIC=projects/your-project/topics/gmail-webhook
GMAIL_WEBHOOK_SECRET=your-webhook-secret

# Temporary tokens (in production, store in secure database)
GMAIL_ACCESS_TOKEN=
GMAIL_REFRESH_TOKEN=

# Next.js Configuration
NEXT_PUBLIC_CONVEX_URL=

# Authentication
AUTH_SECRET=your-auth-secret

# Other API Keys
ANTHROPIC_API_KEY=your-anthropic-api-key
GOOGLE_GENERATIVE_AI_API_KEY=your-google-ai-api-key

# Feature Flags
ENABLE_OCR=true
ENABLE_EMAIL_LISTENER=true
ENABLE_AUTO_PROCESSING=true

# Processing Limits
MAX_FILE_SIZE_MB=100
MAX_CONCURRENT_OCR_JOBS=5
OCR_CONFIDENCE_THRESHOLD=0.7

# Storage Configuration
STORAGE_PROVIDER=convex
MAX_STORAGE_GB=50